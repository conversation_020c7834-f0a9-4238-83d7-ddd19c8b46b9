These files are included in the Blockly Games repo rather than fetched since we
only need two octaves from each set.  Also, the piano doesn't come pre-rendered
with every note.

Soundfont Sources

banjo:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/banjo-mp3/

choir:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/choir_aahs-mp3/

drum:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/melodic_tom-mp3/

flute:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/flute-mp3/

guitar:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/acoustic_guitar_nylon-mp3/

piano:
<PERSON><PERSON><PERSON> Grand Piano
Version V3+2016-12-09
Author: <PERSON>lm, axeldenstore (at) gmail (dot) com.
Creative Commons Attribution 3.0 license.
https://freepats.zenvoid.org/Piano/acoustic-grand-piano.html

trumpet:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/trumpet-mp3/

violin:
commit 44cf273 on Oct 2, 2013
Creative Commons Attribution 3.0 license.
https://github.com/gleitz/midi-js-soundfonts/trunk/FluidR3_GM/violin-mp3/
