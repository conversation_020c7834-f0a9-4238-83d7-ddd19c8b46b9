#tabarea {
  position: fixed !important;
}

#about {
  border: 1px solid #ddd;
  bottom: 10px;
  overflow-y: scroll;
  position: fixed;
}

#about>* {
  margin: 1em;
}

#docsButton:disabled {
  color: #ccc;
}

/* Tabs */
.tab {
  background: #fff;
  border: 1px solid #ddd;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  color: #333;
  cursor: default;
  padding: 4px 8px;
  position: relative;
  user-select: none;
}

.tab-bar>div {
  border-bottom: 0;
  float: left;
  margin: 1px 4px 0 0;
}

.tab:hover:not(.tab-disabled) {
  background: #eee;
}

.tab-disabled {
  background: #fff;
  border-color: #ccc;
  color: #ccc;
  cursor: not-allowed;
}

.tab-selected {
  background: #ddd !important; /* Selected trumps hover. */
}

/*
 * Shift selected tabs 1px towards the contents (and compensate via margin and
 * padding) to visually merge the borders of the tab with the borders of the
 * content area.
 */
.tab-bar>.tab-selected {
  margin-top: 0;
  padding-bottom: 5px;
  top: 1px;
}

.tab-bar {
  border: 0;
  cursor: default;
  list-style: none;
  margin: 0;
  outline: none;
  padding: 0;
  position: relative;
}

.tab-bar-clear {
  clear: both;
  height: 0;
  overflow: hidden;
}

#avatar-select {
  margin-top: 2px;
  color: #fff;
}
