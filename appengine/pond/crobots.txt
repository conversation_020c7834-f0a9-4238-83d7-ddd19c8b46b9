Blockly Games' Pond is a reimplementation of <PERSON>'s 1985 game CROBOTS.
Google has (with the author's permission) updated the game to be playable by a
new generation of programmers.

Differences between Pond and CROBOTS:

* Theme
CROBOTS features robots fighting in a battlefield.  Given today's diversity
priorities, a different theme was needed.  The reimplementation features
rubber ducks in a pond.

* Battlefield size
Height and width of battlefield is 100m x 100m instead of 1000m x 1000m.
Since speed and damage/health go to 100, scaling the pond's size to the same
magnitude makes for a cleaner API.  This was not possible in CROBOTS since
integers were the only data type, whereas JavaScript has floats.

* Scan resolution
CROBOTS scans in a direction with a resolution of +/- n degrees.  <PERSON> scans
with a total resolution of n degrees.  The only difference is that <PERSON>'s
resolution number is double that specified in CROBOTS.

* Failed scan
CROBOTS returns 0 for a scan that failed to detect another player.
<PERSON> returns Infinity.

* Impact damage
CROBOTS gives a flat 2% damage to robots colliding with a wall or another robot.
<PERSON> gives up to 3% damage, proportional to the speed at impact.

* Blast damage
CROBOTS gives 3%/5%/10% damage to robots within 40m/20m/5m of an explosion.
<PERSON> uses a proportional formula ((1 - r / 4) * 10) which is similar (after
accounting for the 10x decrease in battlefield size), but slightly increases
the value of accuracy.

* Swim
Because of the rubber duck theme, 'swim()' has been added as an alias to
CROBOT's 'drive()'.  Pond supports 'drive()' as an undocumented function.

* Stop
Pond introduces a new function 'stop()' that is the equivalent of
'drive(0, d)', where d is the current driving direction.

* Health
People are more used to games having 'health' (more is better) rather than
'damage' (less is better).  Pond introduces 'health()', with 'damage()' being
an undocumented function.

* Log
The 'log()' function prints a value to the browser's console.  This is great
for debugging.  The printed value is restricted to a number for precautionary
legal reasons.

* Trigonometry
CROBOTS has top-level sin/cos/tan/atan functions that use degrees.
Pond adds these to the JavaScript Math object as Math.sin_deg/Math.cos_deg/etc.

* Constants
Various constants such as acceleration rate, robot-missile speed ratio, etc are
tuned differently.  These differences are not intentional.

Google is indebted to Tom Poindexter for inspiring so many young people to
learn about programming through his game.  We hope to continue this success
with Pond.  As with CROBOTS, all the code for Pond is open source.

The original DOS-based CROBOTS game may be found here:
  https://crobots.deepthought.it/home.php
  https://github.com/tpoindex/crobots/

--
Neil Fraser
Google, California
August 2014
