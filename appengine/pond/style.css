#blockly {
  position: fixed;
  bottom: 10px;
}

#editor {
  font-size: 16pt;
  position: fixed !important;
  height: auto !important;
  bottom: 10px;
  border: 1px solid #ddd;
}

#visualization {
  position: relative;
}

#display {
  border: 1px solid #ddd;
  vertical-align: bottom;
}

#avatarStatTable {
  width: 406px;
  max-width: 406px;
  margin-left: -2px;
  height: 20px;
}

#avatarStatTable td {
  padding: 0;
  border: 1px solid;
  background: #ccc;
  border-radius: 2px;
}

#avatarStatTable td.asButton:hover {
  opacity: 0.8;
  user-select: none;
}

#avatarStatTable div:not(.avatarStatHealth) {
  padding: 1px;
}

.avatarStatName {
  position: absolute;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.avatarStatHealth {
  position: absolute;
}

/* Documentation frame. */
#dialogDocs {
  visibility: hidden;
  background-color: #fff;
  border: 1px solid #ccc;
  position: absolute;
  border-radius: 8px;
  box-shadow: 5px 5px 5px #888;
  padding: 15px 0 5px;
  top: 55px;
  left: 5px;
  width: 406px;
  bottom: 10px;
}

html[dir="RTL"] #dialogDocs {
  left: auto;
  right: 5px;
}

#frameDocs {
  width: 100%;
  height: 100%;
  border: none;
}

#closeDocs {
  position: absolute;
  top: -5px;
  left: 389px;
  cursor: pointer;
  opacity: .7;
}

html[dir="RTL"] #closeDocs {
  left: auto;
  right: 389px;
}

#closeDocs:hover {
  opacity: 1;
}

.edit {
  vertical-align: bottom;
  opacity: .6;
  transition: opacity .2s;
  transition-timing-function: ease;
}
.grey {
  -webkit-filter: grayscale(1);
  filter: grayscale(1);
}

.edit:hover {
  opacity: 1;
  -webkit-filter: grayscale(0);
  filter: grayscale(0);
}

.quote0,
.quote1 {
  height: 10px;
  width: 12px;
  margin: 0 2px;
  opacity: .5;
}

.quote0 {
  margin-bottom: 6px;
}

.quote1 {
  margin-bottom: -4px;
}
