body {
  overflow-x: hidden;
  overflow-y: scroll;
}

h2 {
  font-size: 120%;
  font-weight: normal;
}

h3 {
  font-weight: normal;
}

td, th {
  padding: 0 6px;
}

pre, code {
  font-size: large;
}

.spec {
  padding: 1ex;
  border-radius: 5px;
}

.pondSpec {
  border: 2px solid rgb(133, 65, 155);
  background: rgba(133, 65, 155, .2);
}

.logicSpec {
  border: 2px solid rgb(74, 107, 153);
  background: rgba(74, 107, 153, .2);
}

.loopsSpec {
  border: 2px solid rgb(77, 154, 61);
  background: rgba(77, 154, 61, .2);
}

.mathSpec {
  border: 2px solid rgb(73, 80, 155);
  background: rgba(73, 80, 155, .2);
}

.variablesSpec {
  border: 2px solid rgb(147, 68, 111);
  background: rgba(147, 68, 111, .2);
}

.functionsSpec {
  border: 2px solid rgb(133, 65, 155);
  background: rgba(133, 65, 155, .2);
}

.zippy-header-collapsed>img {
  background-image: url('zippy-plus.png');
}

.zippy-header-expanded>img {
  background-image: url('zippy-minus.png');
}

.zippy-header-collapsed,
.zippy-header-expanded {
  outline: 0;
  background-color: #eee;
  padding: 10px;
  border-radius: 20px;
}

.zippy-header-collapsed>img,
.zippy-header-expanded>img {
  height: 28px;
  margin-right: 1ex;
  width: 28px;
  vertical-align: bottom;
}

.zippy-content-collapsed,
.zippy-content-expanded {
  max-height: 0;
  overflow: hidden;
  transition: all .5s ease;
}

.zippy-content-collapsed {
  opacity: 0;
}

.zippy-content-expanded {
  opacity: 1;
}

.blocklyPathLight {
  fill: none;
  stroke-linecap: round;
  stroke-width: 1;
}

.blocklyText {
  cursor: default;
  fill: #fff;
  font-family: sans-serif;
  font-size: 11pt;
}

.blocklyEditableText>rect {
  fill: #fff;
  fill-opacity: .6;
}

.blocklyEditableText>text {
  fill: #000;
}

.blocklyIconGroup {
  opacity: .6;
}

.blocklyIconShape {
  fill: #00f;
  stroke: #fff;
  stroke-width: 1px;
}

.blocklyIconSymbol {
  fill: #fff;
}

body.hideBlocks .blocks {
  display: none;
}
body.hideJs .js {
  display: none;
}

body.hideLevel2 .level2 {
  display: none;
}
body.hideLevel3 .level3 {
  display: none;
}
body.hideLevel4 .level4 {
  display: none;
}
body.hideLevel5 .level5 {
  display: none;
}
body.hideLevel6 .level6 {
  display: none;
}
body.hideLevel7 .level7 {
  display: none;
}
body.hideLevel8 .level8 {
  display: none;
}
body.hideLevel9 .level9 {
  display: none;
}
body.hideLevel10 .level10 {
  display: none;
}
body.hideLevel11 .level11 {
  display: none;
}
