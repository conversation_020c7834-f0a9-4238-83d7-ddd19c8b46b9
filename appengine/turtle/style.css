button.primary {
  /* Can't use "text-align: start" due to IE. */
  text-align: left;
}
html[dir="RTL"] button.primary {
  text-align: right;
}

#blockly {
  position: fixed;
  bottom: 10px;
}
#display {
  border: 1px solid #ccc;
}
#downloadImageLink {
  display: none;
}
#sampleHelp3, #sampleHelp4 {
  height: 50px;
  width: 250px;
}

/* Slider. */
.sliderTrack {
  stroke: #aaa;
  stroke-width: 6px;
  stroke-linecap: round;
}
.sliderKnob {
  fill: #ddd;
  stroke: #bbc;
  stroke-width: 1px;
  stroke-linejoin: round;
}
.sliderKnob:hover {
  fill: #eee;
}
