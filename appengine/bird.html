<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="google" value="notranslate">
  <meta name="viewport" content="target-densitydpi=device-dpi, width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="description" content="<PERSON> is a deep-dive into conditionals. Control-flow is explored with increasingly complex conditions.">
  <title>Blockly Games : Bird</title>
  <link rel="stylesheet" href="common/common.css">
  <link rel="stylesheet" href="bird/style.css">
  <script src="common/boot.js"></script>
</head>
<body>
  <noscript>Blockly Games requires JavaScript.</noscript>
</body>
</html>
