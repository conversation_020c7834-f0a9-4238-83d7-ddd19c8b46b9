#blockly {
  position: fixed;
  bottom: 10px;
}
#visualization {
  position: relative;
}
.edges, .edgeX, .edgeY {
  stroke-width: 1;
  stroke: #BBA;
  fill: none;
  font-family: sans-serif;
  font-size: 10pt;
  font-weight: 100;
}
.edgeX {
  text-anchor: start;
}

#svgBird {
  overflow: hidden;
  direction: ltr;
}

img[src$="help_up.png"] {
  margin: -20px 10px 0;
  height: 64px;
  width: 23px;
}

@keyframes eat {
  from {
    transform: translate(0, 0) scale(1);
  }
  to {
    transform: translate(50px, 50px) scale(0);
  }
}

#worm.eaten>image {
  animation: eat .2s linear forwards;
}

/* Buttons */
button.primary {
  /* Can't use "text-align: start" due to IE. */
  text-align: left;
}
html[dir="RTL"] button.primary {
  text-align: right;
}
