#blockly {
  position: fixed;
  bottom: 10px;
}
#visualization {
  position: relative;
}
#capacityBubble {
  position: absolute;
  margin-top: -0.5em;
  left: 10px;
  top: 375px;
  width: 380px;
}
html[dir="RTL"] #capacityBubble {
  left: auto;
  right: 10px;
}
#capacity {
  display: none;
  color: #fff;
  padding: 5px 1em;
  border-radius: 15px;
  background-color: rgba(64,64,64,.7);
  font-size: large;
}
.capacityNumber {
  font-weight: bold;
}
#sampleOneTopBlock {
  height: 80px;
  width: 250px;
}
#pegSpin {
  height: 50px;
  width: 49px;
  background-position: -343px 0;
  margin-top: 10px;
  position: absolute;
}
html[dir="RTL"] #pegSpin {
  background-position: -441px 0;
}

.helpMenuFake {
  border-radius: 4px;
  background-color: #bfcdde;
  color: #000;
  padding: 0 1ex;
  cursor: default;
}
img[src$="help_up.png"] {
  margin: -20px 10px 0;
  height: 64px;
  width: 23px;
}
img[src$="help_down.png"] {
  margin: 0 10px -20px;
  height: 64px;
  width: 23px;
}

#svgMaze {
  overflow: hidden;
}

/* 'Look' icon */
#look>path {
  display: none;
  fill: none;
  stroke-width: 10;
  stroke-linecap: round;
  stroke-opacity: .7;
}

/* Buttons */
button.primary {
  /* Can't use "text-align: start" due to IE. */
  text-align: left;
}
html[dir="RTL"] button.primary {
  text-align: right;
}

#pegmanButton {
  padding: 0;
  position: relative;
}
#pegmanButton:active {
  border: 1px solid #ddd !important;
}
#pegmanButton>img {
  margin-left: -0.5ex;
  margin-right: .5ex;
  height: 38px;
  width: 49px;
  background-position: -980px 0;
  opacity: .8;
}
html[dir="RTL"] #pegmanButton>img {
  margin-left: .5ex;
  margin-right: -0.5ex;
}
#pegmanButton>span {
  position: absolute;
  top: .8ex;
  margin-left: -15px;
}
html[dir="RTL"] #pegmanButton>span {
  margin-right: -15px;
}
#pegmanMenu {
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 2px 2px 5px #888;
  display: none;
  position: absolute;
}
#pegmanMenu>div {
  background-color: #fff;
}
#pegmanMenu>div:hover {
  background-color: #57e;
}
#pegmanMenu>div>img {
  height: 38px;
  width: 49px;
  background-position: -980px 0;
}
