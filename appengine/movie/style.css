button.primary {
  /* Can't use "text-align: start" due to IE. */
  text-align: left;
}
html[dir="RTL"] button.primary {
  text-align: right;
}

#blockly {
  position: fixed;
  bottom: 10px;
}
#display {
  border: 1px solid #ccc;
}
#downloadImageLink {
  display: none;
}
#sampleHelp2 {
  height: 50px;
  width: 250px;
}

code {
  font-size: 150%; margin: 0 1em;
}

html code.rtl {
  display: none;
}
html code.ltr {
  display: inline;
}
html[dir="RTL"] code.rtl {
  display: inline;
}
html[dir="RTL"] code.ltr {
  display: none;
}

#coordinates {
  display: none;
  font-size: 10pt;
  margin-top: 5px;
  position: absolute;
  width: 400px;
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}
#x {
  position: absolute;
  left: 300px;
}
#y {
  position: absolute;
  left: 350px;
}

/* Slider. */
.sliderTrack {
  stroke: #aaa;
  stroke-width: 6px;
  stroke-linecap: round;
}
.sliderKnob {
  fill: #ddd;
  stroke: #bbc;
  stroke-width: 1px;
  stroke-linejoin: round;
}
.sliderKnob:hover {
  fill: #eee;
}
