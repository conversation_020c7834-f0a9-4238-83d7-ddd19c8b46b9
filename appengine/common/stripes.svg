<?xml version="1.0" encoding="UTF-8"?>
<svg width="50" height="50" version="1.1" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="fade" x1="-1" x2="51" y1="22" y2="22" gradientUnits="userSpaceOnUse">
      <stop stop-color="#fff" stop-opacity="0" offset=".02"/>
      <stop stop-color="#fff" offset=".98"/>
    </linearGradient>
  </defs>
  <rect x="-1" y="-1" width="52" height="52"/>
  <rect transform="rotate(45)" x="-.71" y="0" width="75" height="18" fill="#ffff00"/>
  <rect transform="rotate(45)" y="-35.3" width="75" height="18" fill="#ffff00"/>
  <rect x="-1" y="-1" width="52" height="52" fill="url(#fade)"/>
</svg>