runtime: python27
api_version: 1
threadsafe: no
# App Engine default is 10m.
default_expiration: "12h"

handlers:
# Storage API.
- url: /storage
  script: storage.py
  secure: always

# Error reporting.
- url: /errorReporter
  script: errorReporter.py
  secure: always

# Index page.
- url: /
  static_files: index.html
  upload: index\.html
  secure: always
- url: /index/
  static_dir: index
  secure: always

# About page.
- url: /about
  static_files: about.html
  upload: about\.html
  secure: always

# Admin page.
- url: /admin
  static_files: admin.html
  upload: admin\.html
  login: admin
  secure: always

# Bird app.
- url: /bird
  static_files: bird.html
  upload: bird\.html
  secure: always
- url: /bird/
  static_dir: bird
  secure: always

# Maze app.
- url: /maze
  static_files: maze.html
  upload: maze\.html
  secure: always
- url: /maze/
  static_dir: maze
  secure: always

# Movie app.
- url: /movie
  static_files: movie.html
  upload: movie\.html
  secure: always
- url: /movie/
  static_dir: movie
  secure: always

# Music app.
- url: /music
  static_files: music.html
  upload: music\.html
  secure: always
- url: /music/
  static_dir: music
  secure: always

# Puzzle app.
- url: /puzzle
  static_files: puzzle.html
  upload: puzzle\.html
  secure: always
- url: /puzzle/
  static_dir: puzzle
  secure: always

# Turtle app.
- url: /turtle
  static_files: turtle.html
  upload: turtle\.html
  secure: always
- url: /turtle/
  static_dir: turtle
  secure: always

# Pond apps.
- url: /pond-tutor
  static_files: pond-tutor.html
  upload: pond-tutor\.html
  secure: always
- url: /pond-duck
  static_files: pond-duck.html
  upload: pond-duck\.html
  secure: always
- url: /pond/
  static_dir: pond
  secure: always

# Gallery.
- url: /gallery
  static_files: gallery.html
  upload: gallery\.html
- url: /gallery/
  static_dir: gallery
  secure: always
- url: /gallery-api/submit
  script: gallery_api/submit.py
  secure: always
- url: /gallery-api/view
  script: gallery_api/view.py
  secure: always
- url: /gallery-api/expire
  script: gallery_api/expire.py
  login: admin
  secure: always
- url: /gallery-api/admin
  script: gallery_api/admin.py
  login: admin
  secure: always

# Shared files.
- url: /common
  static_dir: common
  secure: always
- url: /src
  static_dir: src
  secure: always
- url: /generated
  static_dir: generated
  secure: always
- url: /third-party/
  static_dir: third-party
  secure: always
- url: /favicon\.ico
  static_files: favicon.ico
  upload: favicon\.ico
  secure: always
  expiration: "30d"
- url: /apple-touch-icon\.png
  static_files: apple-touch-icon.png
  upload: apple-touch-icon\.png
  secure: always
  expiration: "30d"
- url: /robots\.txt
  static_files: robots.txt
  upload: robots\.txt
  secure: always

# Legacy Reddit redirects.
# Obsolete as of May 2018.
- url: /turtle-reddit
  script: reddit.py
  secure: always
- url: /movie-reddit
  script: reddit.py
  secure: always

# Source files and uncompiled versions.
skip_files:
# App Engine default patterns.
- ^(.*/)?#.*#$
- ^(.*/)?.*~$
- ^(.*/)?.*\.py[co]$
- ^(.*/)?.*/RCS/.*$
- ^(.*/)?\..*$
# Custom skip patterns.
- ^third-party/ace/snippets/.*$
- ^third-party/blockly/\.github/.*$
- ^third-party/blockly/externs/.*$
- ^third-party/blockly/msg/json/.*$
- ^third-party/JS-Interpreter/demos.*$
- ^third-party/JS-Interpreter/[^c].*$  # Only serve compressed.js.
- ^.+\.md$
