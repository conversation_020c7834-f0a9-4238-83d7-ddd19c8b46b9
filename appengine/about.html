<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>About Blockly Games</title>
  <link rel="stylesheet" href="common/common.css">
  <style>
body {
  margin: 0;
}
header,section {
  padding: 1em 10%;
}
td {
  padding: 0 1em;
}
footer {
  background: #f8f9fa;
  color: #666;
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 14px;
  padding: 40px 32px 30px;
}
#google-link {
  margin-right: 55px;
}
#google-link svg {
  margin-bottom: -7px;
  opacity: 0.58;
}
#google-link>a {
  color: #666;
  text-decoration: none;
  padding: 0 10px;
}
#footer-links>a {
  color: #666;
  margin: 15px;
  text-decoration: none;
}
#footer-links>a:hover {
  color: #999;
}
.uppercase {
  text-transform: uppercase;
}
.text-large {
  font-family: 'Google Sans', 'Roboto', Arial, Helvetica, sans-serif;
  font-size: x-large;
}
  </style>
</head>
<body>
<section>
<h1><a id="back" href="/">Blockly Games</a> : About</h1>

<p>Blockly Games is a series of educational games that teach programming. It is
designed for children who have not had prior experience with computer programming.
By the end of these games, one is ready to use conventional text-based languages.</p>

<table>
  <tr>
    <td>
      <img src="index/puzzle.png" height=100 width=100>
    </td>
    <td>
<p>Puzzle is a quick introduction to Blockly's shapes and how the pieces snap together.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/maze.png" height=100 width=100>
    </td>
    <td>
<p>Maze is an introduction to loops and conditionals. It starts simply, but
every level is more challenging than the last.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/bird.png" height=100 width=100>
    </td>
    <td>
<p>Bird is a deep-dive into conditionals. Control-flow is explored with
increasingly complex conditions.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/turtle.png" height=100 width=100>
    </td>
    <td>
<p>Turtle is a deep-dive into loops. Use nested loops to paint a picture.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/movie.png" height=100 width=100>
    </td>
    <td>
<p>Movie is an introduction to mathematical equations. Use math to animate a movie.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/music.png" height=100 width=100>
    </td>
    <td>
<p>Music is an introduction to functions. Use functions to compose music.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/pond-tutor.png" height=100 width=100>
    </td>
    <td>
<p>Pond Tutor introduces text-based programming. Levels switch back and forth
between blocks and actual JavaScript in a text editor.</p>
    </td>
  </tr>

  <tr>
    <td>
      <img src="index/pond-duck.png" height=100 width=100>
    </td>
    <td>
<p>Pond is an open-ended contest to program the smartest duck.
Use either blocks or JavaScript.</p>
    </td>
  </tr>
</table>

<p>Computer science skills help students collaborate, create and make nearly
every subject seem more relevant. Blockly Games encourages the development of
tomorrow's programmers. Designed to be self-paced, Blockly Games can be
<a href="https://github.com/google/blockly-games/wiki/Offline">downloaded for offline&nbsp;use</a>,
ensuring accessibility for all students and technology. All code is open source,
meaning it is free and customizable to meet your needs.  See the
<a href="https://github.com/google/blockly-games/wiki">developer's&nbsp;website</a>
for more information. Developers, teachers and parents are welcome to
<a href="https://groups.google.com/forum/#!forum/blockly-games">give&nbsp;feedback</a>
as we continue to grow.</p>

<p>If you are an educator and interested in exploring CS curriculum in the classroom, check out these other Code with Google programs.</p>

<ul>
  <li><a href="https://csfirst.withgoogle.com/s/en/home?utm_source=bg&utm_medium=referral&utm_campaign=20191120-bg-about--all-all-&src=re-bg-20191120-bg-about--all-all-">CS First</a>:
  Core CS concepts for 10-14 year old students featuring activities, hands-on
  lessons, lesson supplements, and digital materials for educators.</li>
  <li><a href="https://ghop.page.link/blockly-games">Grasshopper</a>:
  Students of all ages can learn to code for free with this fun app for beginners.</li>
</ul>

<p>For more CS education resources from Google, visit <a href="https://edu.google.com/code-with-google">Code with Google</a>.
Code with Google is dedicated to closing equity gaps in CS education by
providing the tools, resources and inspiration to help every educator and
student unlock their potential with code.</p>
</section>

<footer>
  <span id="google-link">
    <span class="uppercase">A</span>
    <a href="https://edu.google.com/code-with-google" target="_blank">
      <span class="text-large">Code with</span>
      <svg width=86 height=28 viewBox="0 0 86 28.21">
        <title>Google</title>
        <path d="m11.11 21.92c-6.035 0-11.11-4.919-11.11-10.96-0.00119-6.041 5.073-10.96 11.11-10.96 3.338 0 5.715 1.31 7.503 3.02l-2.11 2.112c-1.282-1.203-3.017-2.139-5.394-2.139-4.406 0-7.851 3.555-7.851 7.966s3.445 7.966 7.851 7.966c2.858 0 4.486-1.149 5.528-2.192 0.8548-0.8557 1.415-2.085 1.628-3.769h-7.155v-2.993h10.07c0.1073 0.5346 0.1597 1.177 0.1597 1.871 0 2.246-0.614 5.025-2.591 7.004-1.923 2.004-4.379 3.073-7.637 3.073z"/>
        <path d="m36.45 14.81c0 4.063-3.177 7.057-7.077 7.057-3.898 0-7.077-2.994-7.077-7.057 0-4.09 3.178-7.058 7.077-7.058 3.9 0 7.077 2.968 7.077 7.058zm-3.097 0c0-2.54-1.843-4.277-3.979-4.277-2.136 0-3.979 1.738-3.979 4.277 0 2.512 1.843 4.277 3.979 4.277 2.136 0 3.979-1.765 3.979-4.277z"/>
        <path d="m51.82 14.81c0 4.063-3.177 7.057-7.077 7.057-3.898 0-7.077-2.994-7.077-7.057 0-4.09 3.178-7.058 7.077-7.058 3.9 0 7.077 2.968 7.077 7.058zm-3.097 0c0-2.54-1.843-4.277-3.979-4.277s-3.979 1.738-3.979 4.277c0 2.512 1.843 4.277 3.979 4.277s3.979-1.765 3.979-4.277z"/>
        <path d="m66.56 8.197v12.66c0 5.213-3.071 7.351-6.702 7.351-3.418 0-5.474-2.298-6.249-4.17l2.697-1.123c0.4804 1.149 1.656 2.512 3.551 2.512 2.323 0 3.765-1.444 3.765-4.143v-1.016h-0.1061c-0.6938 0.8557-2.03 1.604-3.712 1.604-3.525 0-6.756-3.074-6.756-7.03 0-3.984 3.231-7.084 6.756-7.084 1.682 0 3.017 0.7483 3.712 1.578h0.1061v-1.137zm-2.724 6.644c0-2.486-1.656-4.303-3.765-4.303-2.136 0-3.926 1.818-3.926 4.303 0 2.46 1.789 4.251 3.926 4.251 2.109 0 3.765-1.791 3.765-4.251z"/>
        <path d="m71.76 0.716v20.74h-3.097v-20.74z"/>
        <path d="m83.46 17.14 2.403 1.604c-0.7749 1.149-2.644 3.128-5.875 3.128-4.006 0-6.914-3.1-6.914-7.057 0-4.197 2.935-7.058 6.568-7.058 3.659 0 5.448 2.914 6.035 4.491l0.3207 0.802-9.426 3.902c0.7212 1.417 1.843 2.139 3.418 2.139 1.576 0 2.67-0.7757 3.472-1.951zm-7.397-2.54 6.302-2.619c-0.3469-0.8819-1.389-1.497-2.617-1.497-1.575-0.001193-3.765 1.389-3.685 4.116z"/>
      </svg>
    </a>
    <span class="uppercase">Program</span>
  </span>

  <span id="footer-links">
    <a href="https://www.google.com/policies/privacy/">Privacy</a>&nbsp;<a href="https://www.google.com/policies/terms/">Terms</a>&nbsp;<a href="https://www.google.com/about/">About&nbsp;Google</a>&nbsp;<a href="https://www.google.com/about/products/">Google&nbsp;Products</a>
  </span>
</footer>

<script src="common/back.js"></script>

<!-- Google Analytics 3 -->
<!-- Delete this in July 1, 2023. -->
<script>
if (window.location.pathname.substr(-5) != '.html'){
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-50448074-1', 'auto');
  ga('send', 'pageview');
}
</script>
<!-- End Google Analytics 3 -->

</body>
</html>
