{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Умар"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Корта хьовзориг", "Games.maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Олхазар", "Games.turtle": "УьнтӀепхьид", "Games.movie": "Фильм", "Games.music": "Музыка", "Games.pondTutor": "Де<PERSON><PERSON><PERSON><PERSON>н Ӏам", "Games.pond": "Ӏам", "Games.linesOfCode1": "Ахьа йиний оцу тӀегӀанера Ӏалашо цхьаьна JavaScript могӀанна.", "Games.linesOfCode2": "Ахьа йина и тӀегӀа. Кодан могӀанийн барам JavaScript тӀехь - %1", "Games.nextLevel": "Хьо кийча вуй тӀегӀанна %1?", "Games.finalLevel": "Хьо кийча вуй кхечу зиеран?", "Games.submitTitle": "Корта:", "Games.linkTooltip": "Ӏалашйе, гайта хьажорг блокан", "Games.runTooltip": "Латайе айхьа йазйина программа.", "Games.runProgram": "Латайе прогамма", "Games.resetTooltip": "Сацайо программа, охьакхуссу йуьхьанцара хьоле.", "Games.resetProgram": "Кхоссар", "Games.help": "ГӀо", "Games.catLogic": "Логика", "Games.catLoops": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Математика", "Games.catText": "Йоза", "Games.catLists": "МогӀамаш", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Хийцаме", "Games.catProcedures": "<PERSON>ун<PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "Йехарх бала хилла", "Games.linkAlert": "Дагара дийца шайн блокашца хӀокху хьажоргехула: %1", "Games.hashError": "%1 цхьана файлца йогӀуш йац", "Games.xmlError": "Аьтту ца баьлла шу Ӏалашйина файл чуйаккха. Хила тарло иза Блокли кхечу версехь йина.", "Games.submitted": "Баркалла оцу программина! Нагахь вайн Ӏамийна маймалашна иза хазахетахь, иза зорба тухур йу галерейхь масех дийнахь.", "Games.listVariable": "могӀам", "Games.textVariable": "йоза", "Games.breakLink": "Аша JavaScript редакци йешшехь, шу йухадерзалур дац блокийн редакце. Кхин дӀа йой?", "Games.blocks": "Б<PERSON><PERSON><PERSON><PERSON>ш", "Games.congratulations": "Декъала во хьо!", "Games.helpAbort": "ХӀара тӀегӀа чӀогӀа чолхе йу. Иза а йитина, хьо дехьаволи кхечунна? Хьо йухаверза таро йу кхуза тӀаьхьа.", "Index.clear": "ДӀайаккхий ша йерг?", "Index.subTitle": "<PERSON><PERSON>н болчу программистийн ловзарш", "Index.moreInfo": "Хь<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на хаамаш...", "Index.startOver": "Йуха йоло лаьий хьуна?", "Index.clearData": "ЦӀанбе хаамаш", "Puzzle.animal1": "Бад", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "ЗӀок", "Puzzle.animal1HelpUrl": "https://ce.wikipedia.org/wiki/Бад", "Puzzle.animal2": "Цициг", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Лежиг", "Puzzle.animal2HelpUrl": "https://ru.wikipedia.org/wiki/Цициг", "Puzzle.animal3": "Накхармоза", "Puzzle.animal3Trait1": "Моз", "Puzzle.animal3Trait2": "Йу", "Puzzle.animal3HelpUrl": "https://ru.wikipedia.org/wiki/Накхарш", "Puzzle.animal4": "ЭтмаьӀиг", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Маза", "Puzzle.animal4HelpUrl": "https://ru.wikipedia.org/wiki/ЭтмаьӀиг_(дахаран_кеп)", "Puzzle.picture": "Сурт", "Puzzle.legs": "когаш:", "Puzzle.legsChoose": "харжа...", "Puzzle.traits": "башхаллаш:", "Puzzle.error0": "Ша йолу блокаш (%1) нийса лаьтташ йу", "Puzzle.error1": "Герга! Цхьа блок нийса лаьтташ йац.", "Puzzle.error2": "%1 масех блок нийса лаьтташ йац", "Puzzle.tryAgain": "Къа<PERSON>тийна блок нийса лаьтташ йац. Кхин цкъа а хьажа.", "Puzzle.checkAnswers": "Талла жамӀ", "Puzzle.helpText": "ХӀора дийнатан (баьццара блок), тӀечӀагӀде цуьнан сурт, харжа когийн барам, харжа цуьнан башха аматаш.", "Maze.moveForward": "хьалха гӀулч йаккха", "Maze.turnLeft": "Аьрру агӀор верза", "Maze.turnRight": "Аьтту агӀор верза", "Maze.doCode": "кхочушде", "Maze.helpIfElse": "Омар \"нагахь-кхечу агӀор\" кхочушдан цхьаъ йа кхин гӀуллакх.", "Maze.pathAhead": "нагахь некъ хьалха белахь", "Maze.pathLeft": "нагахь некъ аьрру агӀор белахь", "Maze.pathRight": "нагахь некъ аьтту агӀор белахь", "Maze.repeatUntil": "<PERSON>у<PERSON><PERSON><PERSON>а<PERSON>, цкъачунна ца оьшу", "Maze.moveForwardTooltip": "Хьалха воккху новкъахо цхьа гӀулч хьалха", "Maze.turnTooltip": "Хьовзаван новкъахо 90 градус аьрру йа аьтту агӀор", "Maze.ifTooltip": "Хьалха болу некъ дӀабиллина белахь, тӀаккха цхьадолу гӀуллакхаш де", "Maze.ifelseTooltip": "Нагахь цу агӀор биллина белахь, тӀаккха кхочуш де хьалхара гӀуллакхийн блок. Кхечу агӀор делахь, шолгӀа гӀуллакхийн блок йе", "Maze.whileTooltip": "Блок чудоьхкина гӀуллакхаш йухадан, тӀаьххьара меттиге кхаччалц.", "Maze.capacity0": "Шун йисина %0 блокаш.", "Maze.capacity1": "Шун йисина %1блок", "Maze.capacity2": "Шун йисина %2 блокаш", "Maze.runTooltip": "Новкъахочо блокаш аьлларг дийр ду", "Maze.resetTooltip": "Йухаверзаве новкъахо лабирин<PERSON><PERSON>н йуьххье", "Maze.helpStack": "Тобане тоха масех блок \"гӀулч йаккха хьалха\". суна Ӏалашоне кхочуш гӀо дан.", "Maze.helpOneTopBlock": "Кху тӀегӀанехь шун оьшу цхьаьнатоха йерриг блокаш кӀайчу белхан аренахь.", "Maze.helpRun": "ДӀайелла программа, хӀун хилла хьажархьама.", "Maze.helpReset": "Шун программо ца йина Ӏалашо кхочуш. ТӀетаӀайе кнопка \"Охьакхосса\", йуха хьажа.", "Turtle.submitTooltip": "Reddit чудакха хьайн сурт.", "Movie.y": "y", "Movie.y2": "чада<PERSON><PERSON>е y", "Movie.rectDraw": "нийса саберг", "Pond.documentation": "Документаци", "Pond.targetName": "Ӏалашо", "Gallery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}