{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "AnupamM", "Bl707", "Phoenix303", "<PERSON><PERSON><PERSON>", "Sachinkatiyar", "Sfic"]}, "Games.name": "ब्लोक्ली गेम", "Games.puzzle": "पहे<PERSON>ी", "Games.maze": "भूलभुलैया", "Games.bird": "पक्षी", "Games.turtle": "कछुआ", "Games.movie": "चाल", "Games.music": "संगीत", "Games.pondTutor": "तालाब अनुशिक्षक", "Games.pond": "तालाब", "Games.linesOfCode1": "आपने 1 पंक्ति जावास्क्रिप्ट के साथ इस स्तर को हल किया:", "Games.linesOfCode2": "आपने %1 स्तर जावास्क्रिप्ट पंक्ति के साथ इस स्तर को हल किया:", "Games.nextLevel": "क्या आप स्तर %1 के लिए तैयार हैं?", "Games.finalLevel": "क्या आप अगली चुनौती के लिए तैयार हैं?", "Games.submitTitle": "शीर्षक:", "Games.linkTooltip": "सेव करें और ब्लॉक से लिंक करें।", "Games.runTooltip": "आपने जो लिखा है, उसे रन करें।", "Games.runProgram": "प्रोग्राम चलाएँ", "Games.resetTooltip": "कार्यक्रम को बंद करें और स्तर रीसेट करें।", "Games.resetProgram": "रीसेट करें", "Games.help": "सहायता", "Games.catLogic": "तर्क", "Games.catLoops": "लूप", "Games.catMath": "गणित", "Games.catText": "टेक्स्ट", "Games.catLists": "सूचियाँ", "Games.catColour": "रंग", "Games.catVariables": "चर", "Games.catProcedures": "प्रोसीजर", "Games.httpRequestError": "अनुरोध के साथ समस्या हुई।", "Games.linkAlert": "इस लिंक के साथ का अपने ब्लॉक का साझा करें:\n\n %1", "Games.hashError": "खेद है, '%1' किसी सेव किए गए प्रोग्राम से संबंधित नहीं है।", "Games.xmlError": "आपकी सेव की गई फ़ाइल लोड नहीं हो सकी।  शायद यह ब्लॉकली के किसी भिन्न संस्करण के साथ बनाई गयी थी?", "Games.listVariable": "सूची", "Games.textVariable": "टेक्स्ट", "Games.breakLink": "एक बार जब आप जावास्क्रिप्ट को शुरू करते हैं, तो आप संपादन ब्लॉकों पर वापस नहीं जा सकते। क्या यह ठीक है?", "Games.blocks": "अवरोध", "Games.congratulations": "बधाई हो!", "Games.helpAbort": "यह स्तर बेहद मुश्किल है। क्या आप इसे छोड़ना चाहते हैं और अगले खेल पर जाना चाहेंगे? आप हमेशा बाद में वापस आ सकते हैं।", "Index.clear": "आपके सारे हल मिटा दें?", "Index.subTitle": "भविष्य के प्रोग्रामरों के लिए बना खेल", "Index.moreInfo": "अधिक जानकारी...", "Index.startOver": "फिर से शुरू करना चाहते हैं?", "Index.clearData": "डाटा साफ करें", "Puzzle.animal1": "पंख", "Puzzle.animal1Trait1": "पंख", "Puzzle.animal1Trait2": "चोंच", "Puzzle.animal1HelpUrl": "https://hi.wikipedia.org/wiki/बत्तख", "Puzzle.animal2": "बिल्ली", "Puzzle.animal2Trait1": "मूंछ", "Puzzle.animal2Trait2": "फर", "Puzzle.animal2HelpUrl": "https://hi.wikipedia.org/wiki/बिल्ली", "Puzzle.animal3": "मधुमक्खी", "Puzzle.animal3Trait1": "श<PERSON><PERSON>", "Puzzle.animal3Trait2": "डंक", "Puzzle.animal3HelpUrl": "https://hi.wikipedia.org/wiki/मधुमक्खी", "Puzzle.animal4": "घोंघा", "Puzzle.animal4Trait1": "खोल", "Puzzle.animal4Trait2": "कीचड़", "Puzzle.animal4HelpUrl": "https://hi.wikipedia.org/wiki/स्थलीय_घोंघा", "Puzzle.picture": "तस्वीर:", "Puzzle.legs": "पैर:", "Puzzle.legsChoose": "चुनें...", "Puzzle.traits": "लक्षण:", "Puzzle.error0": "बहुत बढ़िया!\nसभी %1 ब्लॉक सही हैं।", "Puzzle.error1": "लगभग हो गया! एक ब्लॉक गलत है।", "Puzzle.error2": "%1 ब्लॉक गलत हैं।", "Puzzle.tryAgain": "हाइलाइट किए गया ब्लॉक सही नहीं है।\nकोशिश करते रहें।", "Puzzle.checkAnswers": "उत्तर जांच करें", "Puzzle.helpText": "प्रत्येक जानवर (हरा) के लिए, इसकी तस्वीर संलग्न करें, इसकी पैरों की संख्या चुनें, और अपने गुणों का एक ढेर बनाएं।", "Maze.moveForward": "आगे बढ़ें", "Maze.turnLeft": "बाएँ मुड़ें", "Maze.turnRight": "दाएँ मुड़ें", "Maze.doCode": "करें", "Maze.helpIfElse": "यदि अन्य ब्लाक एक काम या अन्य काम करेंगे।", "Maze.pathAhead": "यदि पथ आगे है तो", "Maze.pathLeft": "यदि पथ बाएं है तो", "Maze.pathRight": "यदि पथ दाएँ है तो", "Maze.repeatUntil": "जब तक दोहराएँ", "Maze.moveForwardTooltip": "खिलाड़ी को एक स्थान आगे ले जाता है।", "Maze.turnTooltip": "खिलाड़ी को 90 डिग्री बाईं या दाईं तरफ घुमाता है।", "Maze.ifTooltip": "यदि निर्दिष्ट दिशा में एक पथ है, तो कुछ क्रियाएं करें।", "Maze.ifelseTooltip": "अगर निर्दिष्ट दिशा में एक पथ है, तो कार्य का पहला ब्लॉक करें। अन्यथा, कार्रवाई के दूसरे ब्लॉक करें।", "Maze.whileTooltip": "समापन बिंदु को दोहराएं जब तक पूरा बिंदु तक पहुंच नहीं हो।", "Maze.capacity0": "आपके पास %0 ब्लॉक बचें हैं।", "Maze.capacity1": "आपके पास %1 ब्लॉक बचें हैं।", "Maze.capacity2": "आपके पास %2 ब्लॉक बचें हैं।", "Maze.runTooltip": "ब्लॉक जो बतातें हैं उसे खिलाड़ी से करवाता है।", "Maze.resetTooltip": "खिलाड़ी को भूलभुलैया के शुरू में वापस रखें।", "Maze.helpStack": "लक्ष्य पर पहुंचने में मेरी मदद करने के लिए एक साथ 'आगे बढ़ने' के एक-एक ब्लॉक को ढेर कर दें।", "Maze.helpOneTopBlock": "इस स्तर पर, आपको सफेद कार्यक्षेत्र में सभी ब्लॉकों को एक साथ ढेर करने की आवश्यकता है।", "Maze.helpRun": "क्या होगा देखने के लिए अपने प्रोग्राम को चलाएँ।", "Maze.helpReset": "आपके प्रोग्राम ने भूलभुलैया का समाधान नहीं किया। 'रीसेट' दबाएं और फिर से प्रयास करें।", "Maze.helpRepeat": "केवल दो ब्लॉकों का उपयोग करके इस रास्ते के अंत तक पहुंचें। एक बार से अधिक एक बार चलाने के लिए 'दोहराने' का प्रयोग करें", "Maze.helpCapacity": "आपने इस स्तर के सभी ब्लॉकों का इस्तेमाल किया है। एक नया ब्लॉक बनाने के लिए, आपको पहले एक मौजूदा ब्लॉक को हटाना होगा।", "Maze.helpRepeatMany": "आप 'दोहराने' ब्लॉक के अंदर एक से अधिक ब्लॉक फिट कर सकते हैं।", "Maze.helpSkins": "इस मेन्यू से अपना पसंदीदा खिलाड़ी चुनें।", "Maze.helpIf": "एक 'अगर' ब्लॉक केवल तभी कुछ करेगा जब स्थिति सही हो। यदि बाईं तरफ एक पथ है तो बाएं मुड़ने का प्रयास करें।", "Maze.helpMenu": "इसकी स्थिति बदलने के लिए 'if' ब्लॉक में %1 पर क्लिक करें।", "Maze.helpWallFollow": "क्या आप इस जटिल भूलभुलैया को हल कर सकते हैं? बाईं ओर की दीवार का पालन करने का प्रयास करें। उन्नत प्रोग्रामर केवल!", "Bird.noWorm": "कीड़ा नहीं है", "Bird.heading": "शीर्षक", "Bird.noWormTooltip": "स्थिति जब पक्षी को कीड़ा नहीं मिला है।", "Bird.headingTooltip": "दिए गए कोण की दिशा में आगे बढ़ें: 0 सीधे रास्ते पर है, 90 सीधे ऊपर है, आदि।", "Bird.positionTooltip": "एक्स और वाई, पक्षी की स्थिति को चिह्नित करें। जब एक्स = 0 पक्षी बायीं किनार के पास होता है, जब एक्स = 100 यह दाहिने किनारे के पास है जब y = 0 पक्षी नीचे होता है, जब y = 100 शीर्ष पर होता है", "Bird.helpHeading": "पक्षी को अपने घोंसले में भूमिगत कीड़े प्राप्त करने के लिए शीर्षक कोण को बदले।", "Bird.helpHasWorm": "यदि आपके पास कीट है, या एक अलग शीर्षक अगर आपके पास कीट नहीं है, तो एक ब्लॉक में जाने के लिए इस ब्लॉक का उपयोग करें।", "Turtle.moveForward": "इतने तक आगे बढ़ें", "Turtle.moveBackward": "इतने तक पीछे हटें", "Turtle.turnRight": "इतने तक दाएँ मुड़ें", "Turtle.turnLeft": "इतने तक बाएँ मुड़ें", "Turtle.widthTooltip": "पेन की चौड़ाई बदलता है।", "Turtle.setWidth": "चौड़ाई को इतना निर्धारित करें", "Turtle.colourTooltip": "पेन का रंग बदलता है।", "Turtle.setColour": "रंग को इतना निर्धारित करें", "Turtle.penTooltip": "ड्रॉइंग को बंद या चालू करने के लिए पेन को उपर या नीचे करता है।", "Turtle.penUp": "पेन उठाएँ", "Turtle.penDown": "पेन नीचे करें", "Turtle.turtleVisibilityTooltip": "कछुए को (सर्कल और तीर) दृश्य या अदृश्य बनाता है।", "Turtle.hideTurtle": "कछुआ छुपाएँ", "Turtle.showTurtle": "कछुआ दिखाएँ", "Turtle.print": "प्रिंट करें", "Turtle.fontTooltip": "प्रिंट ब्लॉक के द्वारा इस्तेमाल होने वाला फ़ॉन्ट सेट करता है।", "Turtle.font": "फ़ॉन्ट", "Turtle.fontSize": "फ़ॉन्ट का माप", "Turtle.fontNormal": "सामान्य", "Turtle.fontBold": "गहरा", "Turtle.fontItalic": "तिरछा", "Turtle.galleryMsg": "गैलरी देखें", "Turtle.submitTooltip": "अपना चित्र रेडिट में भेजें", "Turtle.submitMsg": "गैलरी में भेजें", "Turtle.helpUseLoop": "आपका हल काम कर रहा है, लेकिन आप उससे अच्छा भी कर सकते हैं।", "Turtle.helpUseLoop4": "केवल चार खानों से एक सितारा बनाएँ।", "Turtle.helpText1": "चतुर्भुज बनाने के लिए प्रोग्राम बनायें।", "Turtle.helpText2": "चतुर्भुज के जगह पंचभुज बनाने के लिए अपने प्रोग्राम में बदलाव करें।", "Turtle.helpText3b": "पीला सितारा बनाएँ", "Turtle.helpText6": "तीन पीले सितारे और एक सफ़ेद रेखा बनाएँ", "Turtle.helpText7": "सितारा बना कर चार सफेद रेखा भी बना दें।", "Turtle.helpText8": "सफ़ेद रेखा से 360 में चित्र बनाने से वो एक पूरे चाँद के जैसा दिखेगा।", "Turtle.helpText9": "क्या आप काला घेरा जोड़ सकते हैं, जिससे चाँद आधा कटा हुआ दिखे?", "Movie.width": "चौड़ाई", "Movie.height": "ऊँचाई", "Movie.circleDraw": "घेरा", "Movie.lineDraw": "रेखा", "Movie.galleryMsg": "गैलरी देखें", "Movie.submitMsg": "गैलरी में भेजें", "Movie.helpText1": "इस व्यक्ति का चित्र बनाने के लिए सामान्य आकार का उपयोग करें।", "Music.piano": "पियानो", "Music.trumpet": "तुरही", "Music.banjo": "बैंजो", "Music.violin": "वायोलिन", "Music.guitar": "गिटार", "Music.flute": "बांसुरी", "Music.drum": "ड्रम", "Music.choir": "गायक-मंडली", "Music.submitDisabled": "अपने प्रोग्राम को तब तक चलाएं जब तक वह रुक न जाए। तब आप अपने संगीत को गैलरी में जमा कर सकते हैं।", "Music.galleryTooltip": "संगीत की गैलरी खोलें।", "Music.galleryMsg": "गैलरी देखें", "Music.submitTooltip": "अपने संगीत को गैलरी में जमा करें।", "Pond.playerName": "खिलाड़ी", "Pond.targetName": "लक्ष्य", "Gallery": "गैलरी"}