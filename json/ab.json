{"@metadata": {"authors": ["Pupsik-ipa", "Абухба Андрей", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Blockly Ахәмаррақәа", "Games.puzzle": "Ахыԥҽыга", "Games.maze": "<PERSON>л<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Аҵыс", "Games.turtle": "Акәуа", "Games.movie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.music": "Амузыка", "Games.pondTutor": "Аӡҭачы Tutor", "Games.pond": "Аӡҭачы", "Games.nextLevel": "Шәара шәазыхиоума %1 аҩаӡара?", "Games.linkTooltip": "Еиқәырхатәуп иагьаарԥштәуп аблокқәа рахь азхьарԥш.", "Games.runTooltip": "Идәықәҵа еиқәуршәаз апрограмма", "Games.runProgram": "Идәықәҵатәуп апрограмма", "Games.resetProgram": "Иқәгатәуп", "Games.help": "Ацхыраара", "Games.catLogic": "Алогика", "Games.catLoops": "Ациклқәа", "Games.catMath": "Аматематика", "Games.catText": "Атеқст", "Games.catLists": "Ахьӡынҵақәа", "Games.catColour": "Аԥштәы", "Games.catVariables": "Аҽеиҭакқәа", "Games.catProcedures": "Афункциақәа", "Games.listVariable": "ахьӡынҵа", "Games.textVariable": "Атеқст", "Games.blocks": "Аблокқәа", "Games.congratulations": "Ишәыдаҳныҳәалоит!", "Index.clear": "Иныхәтәума аҳасабрақәа зегьы?", "Index.startOver": "Ишәҭахәума ҿыц алагара?", "Index.clearData": "Ианыхтәуп адырқәа", "Puzzle.animal1": "Акәата", "Puzzle.animal1Trait2": "Аԥсаатә аԥынҵа", "Puzzle.animal1HelpUrl": "https://ru.wikipedia.org/wiki/Акәата", "Puzzle.animal2": "Ацгәы", "Puzzle.animal2Trait1": "Аԥаҵақәа", "Puzzle.animal2HelpUrl": "https://ab.wikipedia.org/wiki/Ацгәы", "Puzzle.animal3": "Ашьхымӡа", "Puzzle.animal3Trait1": "Ацха", "Puzzle.animal3HelpUrl": "https://ab.wikipedia.org/wiki/Ашьхыц", "Puzzle.animal4": "Акамыршша", "Puzzle.animal4Trait1": "Аракушка", "Puzzle.animal4HelpUrl": "https://ab.wikipedia.org/wiki/Акамыршша", "Puzzle.picture": "асахьа:", "Puzzle.legs": "ашьапқәа:", "Puzzle.legsChoose": "иалышәх...", "Puzzle.traits": "аҷыдарақәа:", "Puzzle.error0": "Ибзиоуп!\nАблокқәа (%1) зегьы иахьрыхәҭоу иргылоуп.", "Puzzle.error1": "Хәыҷык агуп! Блокк ахьгыло аҭыԥ иашаӡам.", "Puzzle.error2": "Аблокқәа (%1) ԥыҭк иахьрыхәҭоу иргылам.", "Puzzle.tryAgain": "Иалкаау аблок иахьгыло аҭыԥ иашаӡам.", "Puzzle.checkAnswers": "Игәаҭатәуп Алҵшәа", "Maze.moveForward": "ицатәуп ԥхьаҟа", "Maze.turnLeft": "иргежьтәуп армарахь", "Maze.turnRight": "игьежьтәуп арӷьарахь", "Maze.doCode": "инагӡатәуп", "Maze.pathAhead": "амҩа аԥхьаҟа иҟазар", "Maze.pathLeft": "амҩа армарахь иҟазар", "Maze.pathRight": "амҩа арӷьарахь иҟазар", "Maze.repeatUntil": "инагӡалатәуп акәымзар", "Maze.turnTooltip": "Дыргьежьтәуп аныҟәаҩ 90 градус рыла армарахь ма арӷьарахь.", "Maze.capacity0": "Шәара ишәзынхеит %0 блокк.", "Maze.capacity1": "Шәара ишәзынхеит аблокқәа %1.", "Maze.capacity2": "Шәара ишәзынхеит аблокқәа %2.", "Maze.runTooltip": "Аныҟәаҩ иҟаиҵоит аблокқәа иарҳәо зегьы", "Maze.resetTooltip": "Дырхынҳәтәуп аныҟәаҩ алибиринт алагамҭахь.", "Bird.noWorm": "ахәаҷа кӡам", "Bird.heading": "ахырхарҭа", "Bird.helpMutator": "Ииага аблок \"акәымзар\" аблок \"акәзар\" ахь.", "Turtle.turnRight": "игьежьтәуп арӷьарахь ала", "Turtle.turnLeft": "иргежьтәуп армара<PERSON>ь ала", "Turtle.colourTooltip": "Иаԥсахуеит акалам аԥштәы.", "Turtle.setColour": "иқәыргылатәуп аԥштәы", "Turtle.penUp": "акалам иҩахатәуп", "Turtle.penDown": "акалам лашьҭтәуп", "Turtle.hideTurtle": "иҵәахтәуп акәуа", "Turtle.showTurtle": "иаарԥштәуп акәуа", "Turtle.print": "икьыԥхьтәуп", "Turtle.fontHelpUrl": "https://ab.wikipedia.org/wiki/Ашрифт", "Turtle.font": "Ашр<PERSON><PERSON>т", "Turtle.fontSize": "ашрифт ашәагаа", "Turtle.fontNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.fontBold": "ижәпоу", "Turtle.fontItalic": "акур<PERSON>ив", "Turtle.galleryTooltip": "Иаанартуеит асахьақәа ргалереиа Reddit аҟны.", "Turtle.galleryMsg": "Шәахәаԥш Агалереиа", "Turtle.submitTooltip": "Иҭагалатәуп асахьа Reddit аҟынтәи.", "Turtle.submitMsg": "Иҭагалатәуп Агалереиахь", "Turtle.helpText1": "Иаԥҵа апрограмма, аквадрат асахьа ҭызхуа.", "Turtle.helpText3b": "Иҭышәх аеҵәа ҩежь.", "Turtle.helpToolbox": "Иалышәх агәыԥ, аблокқәа шәбарцаз.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "алагамҭатә х", "Movie.y1": "алагамҭатә у", "Movie.x2": "аҵыхәтәантәи х", "Movie.y2": "аҵыхәтәантәи у", "Movie.radius": "Ара<PERSON><PERSON><PERSON><PERSON>", "Movie.width": "аура", "Movie.height": "аҭбаара", "Movie.circleDraw": "агьежь", "Movie.rectDraw": "акәакьҭаиаша", "Movie.lineDraw": "аҵәаӷәа", "Movie.timeTooltip": "Иҳанаҭоит уажәтәи аамҭа афильм(0-100) аҟны.", "Movie.colourTooltip": "Иаԥсахуеит акалам аԥштәы", "Movie.setColour": "иқәыргылатәуп аԥштәы", "Movie.galleryMsg": "Шәахәаԥш Агалереиа", "Movie.submitTooltip": "Иҭагалатәуп шәфильм Reddit ахь.", "Movie.submitMsg": "Иҭагалатәуп Агалереиахь", "Music.piano": "апианино", "Music.trumpet": "атруба", "Music.banjo": "банджо", "Music.violin": "аԥхьарца", "Music.guitar": "агитара", "Music.flute": "афлеита", "Music.drum": "адаул", "Music.choir": "ахор", "Pond.documentation": "Адокументация", "Pond.playerName": "<PERSON><PERSON>л<PERSON><PERSON><PERSON>", "Pond.targetName": "Ахықәкы", "Pond.pendulumName": "Азаза", "Pond.scaredName": "Иршәоу"}