{"@metadata": {"authors": ["Andibing", "Cedric31", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "Games.name": "Blockly Games", "Games.puzzle": "Puzzle", "Games.maze": "Laberint", "Games.bird": "<PERSON>", "Games.turtle": "Tortuga", "Games.movie": "Filme", "Games.music": "Musica", "Games.pondTutor": "Tutorial de Pond", "Games.pond": "Pond", "Games.linesOfCode1": "You solved this level with 1 line of JavaScript:", "Games.linesOfCode2": "You solved this level with %1 lines of JavaScript:", "Games.nextLevel": "Are you ready for level %1?", "Games.finalLevel": "Are you ready for the next challenge?", "Games.submitTitle": "Title:", "Games.linkTooltip": "Salva e liga als blòts.", "Games.runTooltip": "Run the program you wrote.", "Games.runProgram": "Executa lo programa", "Games.resetTooltip": "Stop the program and reset the level.", "Games.resetProgram": "Reïnicializar", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logic", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Math", "Games.catText": "Tèxte", "Games.catLists": "Listas", "Games.catColour": "Color", "Games.catVariables": "Variablas", "Games.catProcedures": "Foncions", "Games.httpRequestError": "I a agut un problèma amb la demanda.", "Games.linkAlert": "Partejatz vòstres blòts gràcia a aqueste ligam :\n\n%1", "Games.hashError": "O planhèm, '%1' correspond pas a un fichièr Blockly salvament.", "Games.xmlError": "Impossible de cargar lo fichièr de salvament.  Benlèu qu'es estat creat amb una autra version de Blockly ?", "Games.submitted": "Thank you for this program!  If our staff of trained monkeys like it, they will publish it to the gallery within a couple of days.", "Games.listVariable": "lista", "Games.textVariable": "tèxte", "Games.breakLink": "Once you start editing JavaScript, you can't go back to editing blocks. Is this OK?", "Games.blocks": "Blòts", "Games.congratulations": "Felicitacions !", "Games.helpAbort": "This level is extremely difficult. Would you like to skip it and go onto the next game? You can always come back later.", "Index.clear": "Delete all your solutions?", "Index.subTitle": "Games for tomorrow's programmers.", "Index.moreInfo": "Info for educators...", "Index.startOver": "Want to start over?", "Index.clearData": "Escafar las donadas", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Nıkıle", "Puzzle.animal1HelpUrl": "https://oc.wikipedia.org/wiki/<PERSON>uit", "Puzzle.animal2": "Gat", "Puzzle.animal2Trait1": "Mostachas", "Puzzle.animal2Trait2": "Forradura", "Puzzle.animal2HelpUrl": "https://oc.wikipedia.org/wiki/Gat", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://oc.wikipedia.org/wiki/Abelha", "Puzzle.animal4": "Caga<PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://oc.wikipedia.org/wiki/Cagaraula", "Puzzle.picture": "imatge :", "Puzzle.legs": "cambas :", "Puzzle.legsChoose": "causissètz...", "Puzzle.traits": "traits :", "Puzzle.error0": "Perfièit !  Totes los %1 blòts son corrèctes.", "Puzzle.error1": "Gaireben !  Un blòt es incorrècte.", "Puzzle.error2": "%1 blòts son incorrèctes.", "Puzzle.tryAgain": "Lo blòt en susbrilhança es incorrècte.  Tornatz ensajar.", "Puzzle.checkAnswers": "Verificatz las responsas", "Puzzle.helpText": "Per cada animal (en verd), estacar son imatge, causir son nombre de cambas, e far una pila de sos traits caracteristics.", "Maze.moveForward": "avança", "Maze.turnLeft": "vira a esquèrra", "Maze.turnRight": "vira a dreita", "Maze.doCode": "far", "Maze.helpIfElse": "Un blòt 'Se-Siquenon' executa una causa o una autra.", "Maze.pathAhead": "se camin davant", "Maze.pathLeft": "se camin cap a esquèrra", "Maze.pathRight": "se camin cap a dreita", "Maze.repeatUntil": "repetís fins a", "Maze.moveForwardTooltip": "<PERSON>a avançar s<PERSON><PERSON> en avant d'un espaci.", "Maze.turnTooltip": "<PERSON>a virar s<PERSON><PERSON> a esquèrra o a dreita de 90 grases.", "Maze.ifTooltip": "Se i a un camin dins la direccion especificada, alara efectua aquelas accions.", "Maze.ifelseTooltip": "Se i a un camin dins la direccion especificada, alara fasètz lo premièr blòt d'accions. Siquenon fasètz lo segond blòt d'accions.", "Maze.whileTooltip": "Repetíse los blòts que son a l'interior fins a aténher la tòca.", "Maze.capacity0": "Te demòra %0 blòt.", "Maze.capacity1": "Te demòra %1 blòt.", "Maze.capacity2": "Te demòran %2 blòts.", "Maze.runTooltip": "Far far al jogaire çò que los blòts dison.", "Maze.resetTooltip": "Replaçar lo jogaire al començament del laberint.", "Maze.helpStack": "Empilar dos blòts d'instruccions 'avança' per m'ajudar a aténher ma tòca.", "Maze.helpOneTopBlock": "Dins aqueste nivèl, av<PERSON><PERSON> besonh d'empilar los blòts los uns en dessús dels autres dins la zòna de trabalh blanca.", "Maze.helpRun": "Run your program to see what happens.", "Maze.helpReset": "Your program didn't solve the maze. Press 'Reset' and try again.", "Maze.helpRepeat": "Los ordenadors an pas gaire de memòria. Utilizatz solament dos blòts per aténher la tòca. Utilizatz lo blòt 'repetís' per executar un blòt mai d'un còp.", "Maze.helpCapacity": "You have used up all the blocks for this level. To create a new block, you first need to delete an existing block.", "Maze.helpRepeatMany": "Podètz metre mai d’un blòt dins un blòt « repetir ».", "Maze.helpSkins": "Choose your favourite player from this menu.", "Maze.helpIf": "An 'if' block will do something only if the condition is true. Try turning left if there is a path to the left.", "Maze.helpMenu": "Click on %1 in the 'if' block to change its condition.", "Maze.helpWallFollow": "Can you solve this complicated maze? Try following the left-hand wall. Advanced programmers only!", "Bird.noWorm": "does not have worm", "Bird.heading": "heading", "Bird.noWormTooltip": "The condition when the bird has not gotten the worm.", "Bird.headingTooltip": "Move in the direction of the given angle: 0 is to the right, 90 is straight up, etc.", "Bird.positionTooltip": "x and y mark the bird's position. When x = 0 the bird is near the left edge, when x = 100 it's near the right edge. When y = 0 the bird is at the bottom, when y = 100 it's at the top.", "Bird.helpHeading": "Change the heading angle to make the bird get the worm and land in her nest.", "Turtle.moveTooltip": "Desplaça la tortuga en avant o en arrièr de la quantitat indicada.", "Turtle.moveForward": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.moveBackward": "recuolar de", "Turtle.turnTooltip": "Far virar la tortuga a esquèrra o a dreita del nombre de grases indicat.", "Turtle.turnRight": "virar a dreita de", "Turtle.turnLeft": "virar a esquèrra de", "Turtle.widthTooltip": "Modificar la grossor de l'estilò.", "Turtle.setWidth": "metre la grossor a", "Turtle.colourTooltip": "Modificar la color de l'estilò.", "Turtle.setColour": "metre la color a", "Turtle.penTooltip": "Levar o pausar l'estilò, per arrestar o començar de dessenhar.", "Turtle.penUp": "le<PERSON> l'estil<PERSON>", "Turtle.penDown": "pausar l'estilò", "Turtle.turtleVisibilityTooltip": "Rend la tortuga (cercle e flècha) visibla o pas.", "Turtle.hideTurtle": "amagar la tortuga", "Turtle.showTurtle": "afichar la tortuga", "Turtle.printTooltip": "<PERSON><PERSON><PERSON> lo tèxte dins la direccion de la tortuga a son emplaçament.", "Turtle.print": "ecrire", "Turtle.fontTooltip": "Definís la poliça utilizada pel blòt d’escritura.", "Turtle.font": "poliça", "Turtle.fontSize": "talha de la poliça", "Turtle.fontNormal": "normal", "Turtle.fontBold": "gras", "Turtle.fontItalic": "italica", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON> la galariá", "Turtle.helpText3b": "<PERSON><PERSON><PERSON> una estela jauna.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x de despart", "Movie.y1": "y de despart", "Movie.x2": "x de fin", "Movie.y2": "y de fin", "Movie.radius": "rai", "Movie.width": "largor", "Movie.height": "nautor", "Movie.circleDraw": "cercle", "Movie.rectDraw": "rectangle", "Movie.lineDraw": "linha", "Movie.galleryMsg": "<PERSON><PERSON><PERSON> la galariá", "Movie.submitTooltip": "Publicar vòstra animacion sus Reddit.", "Movie.submitMsg": "Publicar sus la galariá", "Music.playNote": "jogar %1 nòta %2", "Music.restTooltip": "Espèra la durada especificada.", "Music.rest": "esperar %1", "Music.setInstrument": "fixar l’instrument a %1", "Music.firstPart": "primièra partida", "Music.piano": "piano", "Music.trumpet": "trompeta", "Music.banjo": "banjo", "Music.violin": "violon", "Music.guitar": "guitarra", "Music.flute": "fl<PERSON><PERSON><PERSON>", "Music.drum": "tambor", "Music.choir": "c<PERSON>r", "Music.galleryMsg": "<PERSON><PERSON><PERSON> la galariá", "Pond.documentation": "Documentacion", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "Cibla", "Pond.pendulumName": "<PERSON><PERSON><PERSON>"}