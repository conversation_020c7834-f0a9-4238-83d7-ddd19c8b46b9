{"@metadata": {"authors": ["Азат Хәлилов", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Танзиля Кутлугильдина"]}, "Games.name": "Blockly уйындары", "Games.puzzle": "Башватҡыс", "Games.maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Ҡош", "Games.turtle": "Ташбаҡа", "Games.movie": " Фильм", "Games.pondTutor": "Tutor быуаһы", "Games.pond": "Быуа", "Games.linesOfCode1": "Мәсьәләне был кимәлдә JavaScriptта бер юл менән систегеҙ", "Games.linesOfCode2": "Был кимәлде систегеҙ. JavaScriptта код юлдары һаны - %1:", "Games.nextLevel": " %1 кимәленә әҙерһеңме?", "Games.finalLevel": "Киләһе һынауҙарға әҙерһеңме?", "Games.linkTooltip": "Блоктарға һылтанманы һаҡларға һәм күрһәтергә", "Games.runTooltip": "Яҙған программаңды тоҡандыр.", "Games.runProgram": "Программаны ебәреү", "Games.resetTooltip": "Программаны өҙә һәм башланғыс торошона ырғыта.", "Games.resetProgram": "Таҙартырға", "Games.help": "Ярҙам", "Games.catLogic": " Танылыу", "Games.catLoops": " Циклдар", "Games.catMath": "Математика", "Games.catText": "Текст", "Games.catLists": "Исемлектәр", "Games.catColour": "Төҫ", "Games.catVariables": " Үҙгәреүсән дәүмәлдәр", "Games.catProcedures": " Функциялар", "Games.httpRequestError": "Һорау ваҡытында проблема тыуҙы.", "Games.linkAlert": "%1 һылтанмаһы буйынса булған блоктарығыҙ менән бүлешегеҙ", "Games.hashError": "\nҠыҙғанысҡа ҡаршы, %1 һаҡланған программаларҙың береһенә лә тура килмәй", "Games.xmlError": "Һаҡланған файлығыҙҙы тейәп булманы. Ул, моғайын, Блоктың башҡа версияһында эшләнгән булғандыр.", "Games.listVariable": "Исемлек.", "Games.textVariable": "тест", "Games.congratulations": " Ҡотлайбыҙ!", "Games.helpAbort": "Был кимәл бик ҡатмарлы. Бәлки һин уны ҡалдырып киләһе уйынға күсергә теләйһеңдер. Бында һин һуңғараҡ кире ҡайта алаһың.", "Index.clear": " Бөтә сиселештәрҙе лә юйырғамы?", "Index.subTitle": " Буласаҡ программистар өсөн уйындар.", "Index.moreInfo": "Ентекләберәк", "Index.startOver": "Яңынан башларға теләйһегеҙме?", "Index.clearData": " Мәғлүмәттәрҙе юйырға", "Puzzle.animal1": " Өйрәк", "Puzzle.animal1Trait1": "Ҡауырһын", "Puzzle.animal1Trait2": " Томшоҡ", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Бесәй", "Puzzle.animal2Trait1": "Мыйыҡтар", "Puzzle.animal2Trait2": "Йәнлек тиреһе", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Бесәй", "Puzzle.animal3": "Бал ҡорто", "Puzzle.animal3Trait1": "<PERSON>ал", "Puzzle.animal3Trait2": "Ҡаяу", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Бал ҡорто", "Puzzle.animal4": "Ҡусҡар", "Puzzle.animal4Trait1": " Ҡабырсаҡ", "Puzzle.animal4Trait2": " Лайла", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Ҡусҡар", "Puzzle.picture": "Рәсем", "Puzzle.legs": "аяҡтар:", "Puzzle.legsChoose": "һайла...", "Puzzle.traits": "үҙенсәлектәр:", "Puzzle.error0": "Өлгөлө!(%1) бөтә блоктар ҙа дөрөҫ урынлашҡан.", "Puzzle.error1": "Булды тиерлек!\nБер блок дөрөҫ урынлаштырылмаған", "Puzzle.error2": "Бер нисә блок (%1) дөрөҫ урынлаштырылмаған.", "Puzzle.tryAgain": " Билдәләнгән блок дөрөҫ урынлаштырылмаған. Яңынан эшләп ҡара.", "Puzzle.checkAnswers": "Һөҙөмтәне тикшереү", "Puzzle.helpText": "Һәр йәнлек өсөн (йәшел блок), һүрәтен беркет, аяҡтары һанын һайла һәм уның айырмалы һыҙаттарын йыйып ал.", "Maze.moveForward": "алға табан баҫыу", "Maze.turnLeft": "һулға боролоу", "Maze.turnRight": "уңға боролоу", "Maze.doCode": "үтәргә", "Maze.helpIfElse": "\"бәлки-башҡаса\" фарманы был йә башҡа эште башҡара.", "Maze.pathAhead": "әгәр юл алда булһа", "Maze.pathLeft": "әгәр юл һулда булһа", "Maze.pathRight": "әгәр юл уңда булһа", "Maze.repeatUntil": "булғансы ҡабатларға", "Maze.moveForwardTooltip": "Юлсыны бер аҙымға алға күсерә.", "Maze.turnTooltip": "Юлсыны һулға йәки уңға 90 градусҡа борорға.", "Maze.ifTooltip": "Әгәр күрһәтелгән йүнәлештә юл асыҡ булһа, ҡайһы бер эштәрҙе башҡарырға", "Maze.ifelseTooltip": "Әгәр күрһәтелгән йүнәлештә юл асыҡ булһа, эштәрҙең беренсе блогын башҡарырға. Ябыҡ булһа, эштәрҙең икенсе блогын башҡарырға.", "Maze.whileTooltip": "Блокта булған эштәрҙе һуңғы нөктәгә барып еткәнсе ҡабатларға", "Maze.capacity0": "Һеҙҙең %0 блогыгыҙ ҡалды.", "Maze.capacity1": "Һеҙҙең %1 блок ҡалды.", "Maze.capacity2": "Һеҙҙең %2 блок ҡалды.", "Maze.runTooltip": "Юлсы блоктар күрһәткәндең барыһын да эшләйәсәк.", "Maze.resetTooltip": "Юлсыны лабиринт башына ҡайтарырға.", "Maze.helpStack": "Маҡсатыма ирешеүгә ярҙам өсөн бер нисә \"алға атлау\" блогын берләштерегеҙ", "Maze.helpOneTopBlock": "Был баҫҡыста һеҙгә аҡ эш яланында бөтә блоктарҙы ла бергә ҡушыу мотлаҡ.", "Maze.helpRun": "Нимә барғанын белеү өсөн программаны асығыҙ.", "Maze.helpReset": "Һеҙҙең программа мәсьәләне сисмәне. \"Алып ташлау\" төймәһенә баҫығыҙ һәм яңынан эшләп ҡарағыҙ.", "Maze.helpRepeat": "Ике генә блокты ҡулланып был юлдың аҙағынаса үтегеҙ. Блокты бер тапҡырҙан артыҡ башҡарыу өсөн \"ҡабатларға\"  төймәһен ҡулланығыҙ.", "Maze.helpCapacity": "Был баҫҡыс өсөн бөтә блоктырҙы ҡулландығыҙ. Яңы блок өҫтәр өсөн, тәүҙә булғанын юйҙырырға кәрәк.", "Maze.helpRepeatMany": "\"Ҡабатлау\" блогы эсендә берҙән артыҡ блок урынлаштыра алаһығыҙ.", "Maze.helpSkins": "Был менюла үҙегеҙҙең яратҡан юлсыны һайлағыҙ.", "Maze.helpIf": " \"Әгәр ҙә\" блогы дөрөҫ шарттар булғанда ғына нимәлер башҡарасаҡ. Һулға юл асыҡ булһа, шул яҡҡа боролоп ҡарағыҙ.", "Maze.helpMenu": "\"Әгәр ҙә\" блогы шарттарын үҙгәртеү өсөн %1 баҫығыҙ", "Maze.helpWallFollow": "Был ҡатмарлы лабиринтты сисә алаһыңмы? Һул яҡтан барырға тырышығыҙ. Тәжрибәле программистар өсөн генә.", "Bird.noWorm": "ҡарышлауыҡ тотолманы", "Bird.heading": "йүнәлеш", "Bird.noWormTooltip": "Ҡоштоң ҡарышлауыҡты әле тотоп өлгөрмәгән сағы."}