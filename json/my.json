{"@metadata": {"authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ninjastrikers", "Tn.yoon", "<PERSON>on"]}, "Games.name": "Blockly Games", "Games.puzzle": "ပဟေဠိ", "Games.maze": "ဝင်္ကဘာ", "Games.bird": "ငှက်", "Games.turtle": "ပင်လယ်လိပ်", "Games.movie": "ရုပ်ရှင်", "Games.music": "ဂီတ", "Games.pondTutor": "ရေကန်", "Games.pond": "ရေကန်", "Games.linesOfCode1": "သင် ဤအဆင့်ကို JavaScript 1 line ဖြင့်ဖြေရှင်းလိုက်ပါပြီ။", "Games.linesOfCode2": "သင် ဤအဆင့်ကို JavaScript %1 lines ဖြင့်ဖြေရှင်းလိုက်ပါပြီ။", "Games.nextLevel": "အဆင့် %1 အတွက်အဆင်သင့်ဖြစ်ပြီလား။", "Games.finalLevel": "စိန်ခေါ်မှုနောက်တစ်ခုအတွက် သင်အဆင်သင့်ဖြစ်ပြီလား။", "Games.submitTitle": "ခေါင်းစဉ်:", "Games.linkTooltip": "သိမ်းဆည်းပြီး ဘလောက်များဖြင့်ချိတ်ဆက်ပါ။", "Games.runTooltip": "သင်ရေးထားသော program ကို run ပါ။", "Games.runProgram": "Program run ပါ။", "Games.resetTooltip": "ပရိုဂရမ်ကိုရပ်ပြီး ဤအဆင့်ကိုပြန်စမည်။", "Games.resetProgram": "ပြန်စမည်။", "Games.help": "အကူအညီ", "Games.catLogic": "ယုတ္တိ", "Games.catLoops": "ကွင်း", "Games.catMath": "သင်္ချာ", "Games.catText": "စာသား", "Games.catLists": "စာရင်းများ", "Games.catColour": "အရောင်", "Games.catVariables": "အမျိုးမျိုးပြောင်းလဲနိုင်သော", "Games.catProcedures": "လုပ်ဆောင်ချက်များ", "Games.httpRequestError": "တောင်းဆိုမှုတွင် ပြဿနာရှိနေပါသည်။", "Games.linkAlert": "ဤလင့်ခ်ဖြင့် သင့် block များကိုဝေမျှပါ။\n\n%1", "Games.hashError": "စိတ်မကောင်းပါဘူး '%1' နှင့် ဆင်တူသည့် သိမ်းဆည်းပြီးသား program မရှိပါ။", "Games.xmlError": "သင်သိမ်းဆည်းထားသော ဖိုင်ကိုမရယူနိုင်ပါ။ မတူညီသည့် Blockly ဗားရှင်းဖြင့်ဖန်တီးထားဖန်တီးခဲ့ပါသလား။", "Games.listVariable": "စာရင်း", "Games.textVariable": "စာသား", "Games.breakLink": "JavaScript ကိုစတင် edit လုပ်ပြီဆိုပါက block များကို ပြန်ပြီး edit လုပ်၍မရပါ။ အဆင်ပြေပါသလား။", "Games.blocks": "Blocks", "Games.congratulations": "ဂုဏ်ပြုပါတယ်။", "Games.helpAbort": "ဤအဆင့်သည် အလွန်အမင်းခက်ခဲပါသည်။ ဤအဆင့်ကိုကျော်သွားပြီးအခြားဂိမ်းတစ်ခုသွားလိုပါသလား။ နောင်တွင်လည်း ဤအဆင့်သို့ပြန်လာနိုင်ပါသည်။", "Index.clear": "သင်ဖြေရှင်းထားမှုများအားလုံး ဖျက်လိုပါသလား။", "Index.subTitle": "အနာဂတ်၏ ပရိုဂမ်မာများအတွက် ကစားနည်းများ။", "Index.moreInfo": "နောက်ထပ်သတင်းအချက်အလက်များ။", "Index.startOver": "အစကနေပြန်စလိုပါသလား။", "Index.clearData": "Data များရှင်းလင်းမည်။", "Puzzle.animal1": "ဘဲ", "Puzzle.animal1Trait1": "အမွေးအတောင်များ", "Puzzle.animal1Trait2": "နှုတ်သီး", "Puzzle.animal1HelpUrl": "https://my.wikipedia.org/wiki/ဘဲ", "Puzzle.animal2": "ကြောင်", "Puzzle.animal2Trait1": "နှုတ်ခမ်းမွေး", "Puzzle.animal2Trait2": "သားမွေး", "Puzzle.animal2HelpUrl": "https://my.wikipedia.org/wiki/ကြောင်_(တိရစ္ဆာန်)", "Puzzle.animal3": "ပျား", "Puzzle.animal3Trait1": "ပျားရည်", "Puzzle.animal3Trait2": "စူးချွန်", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "ခရု", "Puzzle.animal4Trait1": "အခွံ", "Puzzle.animal4Trait2": "အကျိအချွဲ", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "ရုပ်ပုံ", "Puzzle.legs": "ခြေထောက်များ", "Puzzle.legsChoose": "ရွေးချယ်သည်", "Puzzle.traits": "ဉာဉ်/ အကျင့်", "Puzzle.error0": "ပြီးပြည်စုံပါတယ်၊ \nAll %1 block များအားလုံးမှန်ကန်ပါသည်။", "Puzzle.error1": "ပြီးခါနီးပါပြီ။ Block တစ်ခုမမှန်ပါ။", "Puzzle.error2": "%1 block များမမှန်ပါ။", "Puzzle.tryAgain": "Highlight လုပ်ပြထားသော block မမှန်ပါ။ ဆက်လက်ကြိုးစားပါ။", "Puzzle.checkAnswers": "အဖြေများကို စစ်ဆေးပါ။", "Puzzle.helpText": "တိရိစ္ဆာန်တစ်ကောင်ချင်းစီအတွက် (အစိမ်းရောင်)၊ ၎င်း၏ပုံတွင်တွဲပါ၊ ၎င်း၏ခြေထောက်အရည်အတွက်ကိုရွေးချယ်ပါ၊ ထို့နောက် ၎င်းဉာဉ် အမြောက်အများပြုလုပ်ပါ။", "Maze.moveForward": "အရှေ့သို့ရွှေ့ပါ။", "Maze.turnLeft": "ဘယ်ဘက်လှည့်ပါ။", "Maze.turnRight": "ညာဘက်လှည့်ပါ။", "Maze.doCode": "လုပ်ဆောင်ပါ။", "Maze.helpIfElse": "အကယ်၍ block များ တစ်ခု သို့မဟုတ် အခြားအရာများ လုပ်ဆောင်မည်ဆိုပါက", "Maze.pathAhead": "အကယ်၍ လမ်းကြောင်းသည် ရှေ့တွင်ရှိပါက", "Maze.pathLeft": "အကယ်၍ လမ်းကြောင်းသည် ဘယ်ဘက်တွင်ရှိပါက", "Maze.pathRight": "အကယ်၍ လမ်းကြောင်းသည် ညာဘက်တွက်ရှိပါက", "Maze.repeatUntil": "တိုင်အောင် ဆက်လက်လုပ်ဆောင်ပါ။", "Maze.moveForwardTooltip": "ကစားသမားကို တစ်နေရာစာ ရှေ့သို့သွားခိုင်းပါ။", "Maze.turnTooltip": "ကစားသမားကို ၉၀ ဒီဂရီ ဘယ် သို့မဟုတ် ညာဘက် သို့လှည့်ခိုင်းပါ။", "Maze.ifTooltip": "အကယ်၍ အထူးပြုဦးတည်လားရာတွင် လမ်းကြောင်းတစ်ခုရှိပြီးပါက", "Maze.ifelseTooltip": "အကယ်၍ အထူးပြုဦးတည်လားရာတွင် လမ်းကြောင်းတစ်ခုရှိပြီးပါက၊ လုပ်ဆောင်ချက်များ၏ ပထမ Block ကိုပြုလုပ်ပါ။ ထိုသို့မဟုတ်ပါက လုပ်ဆောင်ချက်များ၏ ဒုတိယ Block ကိုပြုလုပ်ပါ။", "Maze.whileTooltip": "ဆုံးမှတ်ရောက်သည်အထိ လုပ်ဆောင်ချက်များကိုထည့်သွင်းပြီး ထပ်ခါထပ်ခါပြုလုပ်ပါ။", "Maze.capacity0": "သင့်တွင် %0 block များကျန်နေပါသေးသည်။", "Maze.capacity1": "သင့်တွင် %1 block များကျန်နေပါသေးသည်။", "Maze.capacity2": "သင့်တွင် %2 block များကျန်နေပါသေးသည်။", "Maze.runTooltip": "Block များပြောသည့်အတိုင်း ကစားသမားကို လိုက်လုပ်စေပါ။", "Maze.resetTooltip": "ဝင်္ကဘာ၏ အစသို့ကစားသမားကိုပြန်ပို့ပါ။", "Maze.helpStack": "ရှေ့သို့ရွှေ့ပါ block ၂ တုံးကိုအတူရွှေ့ခြင်းဖြင့် ပန်းတိုင်ရောက်ရန် ကျွန်ုပ်ကိုကူညီပေးပါ။", "Maze.helpOneTopBlock": "ဤအဆင့်တွင် block များအားလုံးကို တစ်စုတစ်စည်းထဲ အလုပ်လုပ်မည့် အဖြူရောင်အကွက်ထဲတွင် ထားရှိရမည်။", "Maze.helpRun": "မည်ကဲ့သို့ဖြစ်သွားမည်ကို သိရှိရန် သင့် ပရိုဂမ်ကို စတင်လည်ပါတ်ကြည့်ပါ။", "Maze.helpReset": "သင့် ပရိုဂမ် ဤဝင်္ကဘာကို မဖြေရှင်းနိုင်ပါ၊ ပြန်စမည် ကိုရွေးချယ်၍ နောက်တစ်ကြိမ်ထပ်ကြိုးစားပါ။", "Maze.helpRepeat": "Block ၂ ခုသာအသုံးပြုပြီး ဤလမ်းကြောင်း၏ အဆုံးကိုသွားပါ၊ ထပ်ခါထပ်ခါပြုလုပ်ပြီး block တစ်ခုကို တစ်ကြိမ်ထက်ပို၍ လည်ပါတ်ခိုင်းပါ။", "Maze.helpCapacity": "ဤအဆင့်အတွက် block များအားလုံးကို သင်အသုံးပြုလိုက်ပြီးပါပြီ၊ Block အသစ်တစ်ခုဖန်တီးရန်", "Maze.helpRepeatMany": "ထပ်ခါထပ်ခါပြုလုပ်သည့် block တစ်ခုထဲတွင် တစ်ခုထက်ပိုသော block များသင့်အနေဖြင့်ထည့်သွင်းနိုင်သည်။", "Maze.helpSkins": "ဤစာရင်းတွင်းမှ သင့်အကြိုက်ဆုံးကစားသမားကို ရွေးချယ်ပါ။", "Maze.helpIf": "အကယ်၍ block သည် အခြေအနေတစ်ခု မှန်ကန်သည့်အချိန်တွင်မှ အလုပ်လုပ်နိုင်သည်။ အကယ်၍ ဘယ်ဘက်တွင် လမ်းကြောင်းရှိပါက ဘယ်ဘက်သို့လှည့်ပါ။", "Maze.helpMenu": "အကယ်၍ block ၏ အခြေအနေကို ပြောင်းလဲရန်အတွက် %1 ကိုနှိပ်ပါ။", "Maze.helpWallFollow": "ဤရှုပ်ထွေးသော ဝင်္ကဘာကို သင်ဖြေရှင်းနိုင်ပါသလား၊ ဘယ်ဘက်အခြမ်းတွင်ရှိသည့် နံရံဘက်မှ ဖြေရှင်းကြည့်ပါ၊ အဆင့်မြင့် ပရိုဂမ်မာများအတွက် ဖြစ်သည်။", "Bird.noWorm": "တီကောင်မရှိပါ", "Bird.heading": "ဦးတည်", "Bird.noWormTooltip": "ငှက်အနေဖြင့် တီကောင်မရရှိထားသည့် အခြေအနေတွင်။", "Bird.headingTooltip": "ပေးထားသောထောင့်၏ လားရာမှရွေ့လျားပါ။ ၀ သည် ညာဘက်၊ ၉၀ သည် တည့်တည့်မတ်မတ်၊ စသည်ဖြင့်", "Bird.positionTooltip": "x နှင့် y အမှတ်သည် ငှက်၏ တည်နေရာကိုပြသည်။ x = ၀ ဖြစ်သည့်အချိန်တွင် ငှက်သည် ဘယ်ဘက်အစွန်းသို့နီးကပ်နေသည်၊ y = ၁၀၀ ဖြစ်သည့်အချိန်တွင် ငှက်သည် ညာဘက်အစွန်းသို့နီးကပ်နေသည်။  y = ၀ ဖြစ်သည့်အချိန်တွင် ငှက်သည်အောက်ခြေတွင်ရှိနေပြီး y = ၁၀၀ ဖြစ်သည့်အချိန်တွင် ၎င်းသည် ထိပ်သို့ရောက်နေသည်။", "Bird.helpHeading": "ငှက်ကို တီကောင်ရယူပေးရန်အတွက်နှင့် ၎င်းအသိုက်ထဲသို့ဝင်သွားစေရန် ဦးတည်ထောင့်ကို ပြောင်းလဲပါ။", "Bird.helpHasWorm": "အကယ်၍ တီကောင်ရရှိပြီးပါက ဤ block ကိုအသုံးပြုပြီး ဦးတည်ရာတစ်ခုတွင်းသို့ဝင်သွားပါ။ သို့မဟုတ် တီကောင်မရရှိသေးပါက မတူညီသည့် ဦးတည်ရာဘက်သို့သွားလိုက်ပါ။", "Bird.helpX": "x သည်သင်၏ လက်ရှိရေပြင်ညီအနေအထားဖြစ်သည်၊ အကယ်၍ x သည်ကိန်းကဏန်းတစ်ခုထက်ငယ်ပါက ဤ block ကိုအသုံးပြုပြီး ဦးတည်ရာတစ်ခုအတွင်းသို့ဝင်သွားပါ။ သို့မဟုတ် မတူညီသည့် ဦးတည်ရာသို့ဝင်ပါ။", "Bird.helpElse": "အကယ်၍ block ကို အထူးပြုလုပ်ရန်အတွက် icon ကိုနှိပ်ပါ။", "Bird.helpElseIf": "ဤအဆင့်တွင် အကယ်၍နောက်ထပ် နှင့် နောက်ထပ် block ၂ ခုလုံးလိုအပ်သည်။", "Bird.helpAnd": "နှင့် block များသည် ၎င်း၏ သွင်းအားစုများ မှန်ကန်မှသာလျှင် မှန်ကန်နိုင်သည်။", "Bird.helpMutator": "နောက်ထပ် block ကို အကယ်၍ block အတွင်းသို့ဆွဲယူထည့်ပါ။", "Turtle.moveTooltip": "တိကျသော ပမာဏတစ်ခုဖြင့် ပင်လယ်လိပ်ကို အရှေ့သို့မဟုတ် အနောက် ကိုရွှေ့ပါ။", "Turtle.moveForward": "ဖြင့် အရှေ့ကိုရွှေ့ပါ။", "Turtle.moveBackward": "ဖြင့် အနောက်ကိုရွှေ့ပါ။", "Turtle.turnTooltip": "တိကျသော ဒီဂရီကဏန်းတစ်ခုဖြင့် ပင်လယ်လိပ်ကို ဘယ်ဘက် သို့မဟုတ် ညာဘက်သို့လှည့်ပါ။", "Turtle.turnRight": "ဖြင့် ညာဘက်လှည့်ပါ။", "Turtle.turnLeft": "အားဖြင့် ဘယ်ဘက်လှည့်ပါ။", "Turtle.widthTooltip": "Pen ၏ အနံကို ပြောင်းလဲမှုများ။", "Turtle.setWidth": "အနံကို ကိုထားမည်", "Turtle.colourTooltip": "Pen ၏ အရောင်ကို ပြောင်းလဲမှုများ", "Turtle.setColour": "အရောင်ကိုထားမည်။", "Turtle.penTooltip": "ပုံဆွဲခြင်းရပ်ရန် သို့မဟုတ် စရန်အတွက် Pen ကို မြှင့်မည် သို့မဟုတ် နိမ့်မည်။", "Turtle.penUp": "ညွှန်ပြသည့်အတံ", "Turtle.penDown": "ညွှန်ပြသည့်အတံ", "Turtle.turtleVisibilityTooltip": "ပင်လယ်လိပ်ကို (စက်ဝိုင်းနှင့် မြှား) မြင်ရအောင် သို့မဟုတ် မမြင်ရအောင် ပြုလုပ်မည်။", "Turtle.hideTurtle": "ပင်လယ်လိပ်ကို ဖွက်ထားမည်။", "Turtle.showTurtle": "ပင်လယ်လိပ်ကို ပြမည်။", "Turtle.printTooltip": "ပင်လယ်လိပ်၏ လားရာကို ၎င်း၏ တည်နေရာမှ စာသားရေးဆွဲပါ။", "Turtle.print": "ပရင့်ထုတ်", "Turtle.fontTooltip": "ပုံနှိပ် block ကိုအသုံးပြုပြီး font ထည့်သွင်းပါ။", "Turtle.font": "ဖောင့်", "Turtle.fontSize": "font အရွယ်အစား", "Turtle.fontNormal": "ပုံမှန်", "Turtle.fontBold": "အထူ", "Turtle.fontItalic": "အစောင်း", "Turtle.submitDisabled": "သင်၏ ပရိုဂမ် ရပ်သွားသည်အထိ လည်ပါတ်နေပါစေ။ ထို့နောက် သင်ဆွဲထားသောပုံကို ပြခန်းတင်နိုင်ပါပြီ။", "Turtle.galleryTooltip": "Reddit ပေါ်တွင် ဆွဲထားသောပုံများကို ပြခန်းပေါ်တွင်ဖွင့်ပါ။", "Turtle.galleryMsg": "ပြခန်းကိုကြည့်မည်", "Turtle.submitTooltip": "သင်ဆွဲထားသောပုံကို Reddit ပေါ်တင်ပါ။", "Turtle.submitMsg": "ပြခန်းပေါ်တင်မည်။", "Turtle.helpUseLoop": "သင့်ဖြေရှင်းနည်းအလုပ်ဖြစ်ပါသည်၊ သို့သော်ယခုထက်ပိုကောင်းအောင်ပြုလုပ်နိုင်ပါသေးသည်။", "Turtle.helpUseLoop3": "Block သုံးခုကိုသာအသုံးပြုပြီး ပုံစံတစ်ခုဆွဲပါ။", "Turtle.helpUseLoop4": "Block လေးခုသာအသုံးပြုပြီး ကြယ်ပုံဆွဲပါ။", "Turtle.helpText1": "စတုရန်းတစ်ခုဆွဲရန်အတွက် ပရိုဂမ်တစ်ခု ဖန်တီးပါ။", "Turtle.helpText2": "စတုရန်းတစ်ခုဆွဲမည့်အစား ပဥ္စဂံတစ်ခုဆွဲရန်အတွက် သင့်၏ပရိုဂမ်ကိုပြောင်းလဲလိုက်ပါ။", "Turtle.helpText3a": "အရောင်ပြောင်းလဲခွင့်ပြုသည့် block အသစ်တစ်ခုရှိသည်။", "Turtle.helpText3b": "အဝါရောင်ကြယ်တစ်ခုဆွဲပါ။", "Turtle.helpText4a": "သင်ရွေ့လျားသည့်အခါတွင် သင်၏ pen ကိုမယူခွင့်ပေးသော block အသစ်တစ်ခုရှိသည်။", "Turtle.helpText4b": "အဝါရောင်ကြယ်အသေးလေးတစ်ခုဆွဲပါ။  ထို့နောက် ၎င်းအပေါ်တွင် မျဉ်းတစ်ကြောင်းဆွဲပါ။", "Turtle.helpText5": "ကြယ်တစ်ခုအစား စတုရန်းတစ်ခုထဲတွင်စီထားသော ကြယ်လေးခုဆွဲနိုင်ပါသလား။", "Turtle.helpText6": "အဝါရောင်ကြယ်သုံးခုဆွဲပါ၊ ထို့နောက်အဝါရောင်မျဥ်းတစ်ကြောင်းဆွဲပါ။", "Turtle.helpText7": "ကြယ်များဆွဲပါ၊ ထို့နောက် အဖြူရောင်မျဉ်းကြာင်းလေးခုဆွဲပါ။", "Turtle.helpText8": "လပြည့်တစ်ခုပုံသဏ္ဍန်ရှိသော အဖြူရောင်လိုင်းပေါင်း ၃၆၀ ခုဆွဲပါ။", "Turtle.helpText9": "အနက်ရောင်စက်ဝိုင်းတစ်ခုထည့်ခြင်းဖြင့် လတဖြည်းဖြည်းကွယ်လာအောင်ပြုလုပ်နိုင်ပါသလား။", "Turtle.helpText10": "သင်ကြိုက်သည့်အရာကိုဆွဲပါ။ သင်စမ်းသပ်ရှာဖွေနိုင်ရန်အတွက် block အသစ်ပေါင်းများစွာရရှိထားပါပြီ။ ပျော်ရွှင်ပါစေ။", "Turtle.helpText10Reddit": "\" ကိုအသုံးပြုပါ၊ \" ကိုပြခန်းထဲတွင်ကြည့်ပါ၊ အောက်ခြေတွင်အခြားသူများ ဆွဲထားသည်များကိုကြည့်နိုင်သည်။ အကယ်၍ စိတ်ဝင်စားစရာတစ်ခုခုသင်ဆွဲခဲ့ပါက \" ကိုအသုံးပြုပါ၊ \" ပြခန်းပေါ်တင်ပါ၊ ထုတ်ဝေသည့်ခလုတ်နှိပ်ပါ။", "Turtle.helpToolbox": "Block များကိုကြည့်ရှုရန်အတွက် အမျိုးအစားရွေးချယ်ပါ။", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x ဖြင့်စပါ။", "Movie.y1": "y ဖြင့်စပါ။", "Movie.x2": "x ဖြင့်ဆုံးပါ။", "Movie.y2": "y ဖြင့်ဆုံးပါ။", "Movie.radius": "အချင်းဝက်", "Movie.width": "အလျား", "Movie.height": "အမြင့်", "Movie.circleTooltip": "အထူးတည်နေရာတစ်ခုမှ အထူးအချင်းဝက်ဖြင့် စက်ဝိုင်းပုံတစ်ပုံဆွဲပါ။", "Movie.circleDraw": "စက်ဝိုင်း", "Movie.rectTooltip": "အထူးတည်နေရာတစ်ခုမှ အထူးအလျားနှင့်အမြင့်ဖြင့် စတုရန်းတစ်ခုဆွဲပါ။", "Movie.rectDraw": "စတုရန်း", "Movie.lineTooltip": "အမှတ်တစ်ခုမှတစ်ခုသို့အထူးအလျားဖြင့် မျဉ်းတစ်ကြောင်းဆွဲပါ။", "Movie.lineDraw": "မျဉ်းကြောင်း", "Movie.timeTooltip": "လှုပ်ရှားမှုရုပ်ပုံများထဲမှ လက်ရှိအချိန်ကိုပြန်သွားပါ။ (၀ - ၁၀၀)", "Movie.colourTooltip": "Pen ၏ အရောင်ကို ပြောင်းလဲမှုများ", "Movie.setColour": "အရောင်ကိုထားမည်။", "Movie.submitDisabled": "သင်၏ရုပ်ရှင်လှုပ်ရှားမှုမရှိပါ၊ Block များကိုအသုံးပြုပြီး စိတ်ဝင်စားစရာတစ်ခုခုပြုလုပ်ပါ၊ ထို့နောက် သင်၏ ရုပ်ရှင်ကို ပြခန်းပေါ်တွင်တင်နိုင်ပါပြီ။", "Movie.galleryTooltip": "ရုပ်ရှင်ပြခန်းကို ဖွင့်ပါ။", "Movie.galleryMsg": "ပြခန်းတွင်ကြည့်ပါ။", "Movie.submitTooltip": "ပြခန်းသို့ သင်၏ရုပ်ရှင်ကိုတင်ပါ။", "Movie.submitMsg": "ပြခန်းပေါ်တင်မည်။", "Movie.helpText1": "ဤလူ၏ပုံကိုဆွဲရန် ရိုးရှင်းသောရုပ်ပုံများကို အသုံးပြုပါ။", "Movie.helpText2a": "ဤအဆင့်သည် ရုပ်ရှင်တစ်ခုဖြစ်သည်။ သင်သည် လူပုဂ္ဂိုလ်၏လက်မောင်းကို မျက်နှာပြင်ပေါ်တွင် လှိမ့်နေစေချင်သည်။ Play ခလုတ်ကိုနှိပ်၍ အစမ်းကြည့်ရှုပါ။", "Movie.helpText2b": "ရုပ်ရှင်ပြသည့်အချိန်တွင် အချိန်၏ တန်ဖိုးသည် ၀ မှ ၁၀၀ ထိရေတွက်သည်။ သင့်အနေဖြင့်လက်မောင်းကို 'y' အနေအထားတွင်ထားရှိပြီး ၀ မှ ၁၀၀ ထိသွားစေချင်သည့်အတွက် လွယ်ကူစွာလုပ်ဆောင်နိုင်ပါလိမ့်မည်။", "Movie.helpText3": "အချိန် block သည် ၀ မှ ၁၀၀ ထိရေတွက်သည်။ သို့သော် ဤအချိန်တွင် သင်သည် 'y' အနေအထားတွင်ရှိနေသော လက်မောင်းကို ၁၀၀ မှ ၀ သို့သွားစေချင်သည်။ ဦးတည်ရာကို ပြောင်းလဲပေးနိုင်သော ရိုးရှင်းသည့် သင်္ချာဖြေရှင်းနည်း သင်စဉ်းစားနိုင်ပါသလား။", "Movie.helpText4": "ပြီးခဲ့သည့် အဆင့်တွင် သင်သင်ယူခဲ့သည့်အရာကို အသုံးပြုပြီး ခြေထောက်များ ကြက်ခြေခတ်ဖြစ်အောင် ပြုလုပ်ပါ။", "Movie.helpText5": "လက်မောင်းအတွက် သင်္ချာပုံသေနည်းမှာ ရှုပ်ထွေးပါသည်။ အဖြေက\nဒီမှာပါ:", "Movie.helpText6": "လူပုဂ္ဂိုလ်အား လက်နှစ်ချောင်း ပေးလိုက်ပါ။", "Movie.helpText7": "ရုပ်ရှင်၏ ပထမထက်ဝက်အတွက် ခေါင်းသေးတစ်ခုဆွဲရန် 'အကယ်၍' block ကိုသုံးပါ။ ပြီးပါက ရုပ်ရှင်၏ ဒုတိယထက်ဝက်အတွက် ခေါင်းကြီးတစ်ခုကို ဆွဲလိုက်ပါ။", "Movie.helpText8": "ရုပ်ရှင်ထက်ဝက်ခန့်တွင် ခြေထောက်များကို ဦးတည်ရာပြောင်းပြန် လုပ်လိုက်ပါ။", "Movie.helpText9": "လူပုဂ္ဂိုလ်နောက်ကျောတွင် ကျယ်ကားလာသည့်စက်ဝိုင်းတစ်ခုကို ဆွဲလိုက်ပါ။", "Movie.helpText10": "သင့်စိတ်ကြိုက်ရုပ်ရှင်တစ်ခုဖန်တီးပါ။ သင့်ထံတွင် စိတ်ကြိုက်လေ့လာစူးစမ်းနိုင်သော block အသစ်ပေါင်းများစွာရရှိထားပါပြီ။ ပျော်ရွှင်ပါစေ။", "Movie.helpText10Reddit": "အောက်ခြေတွင် အခြားသူများ ပြုလုပ်ထားသည့် ရုပ်ရှင်များကိုကြည့်ရန် \"ပြခန်းထဲတွင်ကြည့်ပါ\" ကိုအသုံးပြုပါ။ အကယ်၍ သင်သည် စိတ်ဝင်စားစရာ ရုပ်ရှင်တစ်ခုပြုလုပ်ထားပါက \"ပြခန်းသို့ ထည့်သွင်းပါ\" ကိုအသုံးပြု၍ လွှင့်တင်ပါ။", "Music.piano": "ပီယာနို", "Music.banjo": "ဘင်ဂျို", "Music.violin": "တယော", "Music.guitar": "ဂစ်တာ", "Music.flute": "ပလွေ", "Music.drum": "ဒရမ်", "Music.submitDisabled": "သင်၏ ပရိုဂရမ်ကို ရပ်သွားသည်အထိ လည်ပတ်ပါစေ။ ထို့နောက် သင်၏သီချင်းကို ပြခန်းတွင် တင်နိုင်ပါပြီ။", "Music.galleryTooltip": "ဂီတပြန်ခန်းကို ဖွင့်ပါ။", "Music.galleryMsg": "ပြခန်းတွင်ကြည့်ပါ။", "Music.submitMsg": "ပြခန်းပေါ်တင်မည်", "Pond.scanTooltip": "ရန်သူကိုရှာဖွေပါ၊ တိကျသော ဦးတည်ရာကိုသတ်မှတ်ပါ (၀ - ၃၆၀) ဦးတည်ရာကိုသတ်မှတ်ပါ။ ထိုဦးတည်ရာအတွင်းမှ အနီးဆုံးရန်သူထံချဉ်းကပ်ပါ။ အကယ်၍ရန်သူမတွေ့ပါက အဆုံးအစမရှိသည့်အခြေအနေသို့ပြန်သွားပါ။", "Pond.cannonTooltip": "အမြောက်ပစ်ပါ။ (၀ - ၃၆၀) နှင့် (၀ - ၇၀) ကြား ဦးတည်ရာကိုသတ်မှတ်ပါ။", "Pond.swimTooltip": "ရှေ့သို့ကူးခတ်ပါ။ ဦးတည်ရာသတ်မှတ်ပါ (၀ - ၃၆၀)", "Pond.stopTooltip": "ကူးခတ်တာရပ်ပါ။ ကစားသမားသည် ရပ်ရန်အတွက်အရှိန်လျှော့မည်။", "Pond.healthTooltip": "ကစားသမား၏ လက်ရှိကျန်းမာရေးအခြေအနေထံပြန်သွားပါ။ (၀ သည် သေဆုံးခြင်းဖြစ်သည်၊ ၁၀၀ သည် ကျန်းမာရေးကောင်းမွန်နေသည်။)", "Pond.speedTooltip": "ကစားသမား၏ လက်ရှိအရှိန်ထံပြန်သွားပါ။ (၀ သည်ရပ်နေခြင်းဖြစ်သည်၊ ၁၀၀ သည် အရှိန်အပြည့်ဖြစ်သည်။)", "Pond.locXTooltip": "လှုပ်ရှားမှုရုပ်ပုံများထဲမှ လက်ရှိအချိန်ကိုပြန်သွားပါ။ (၀ - ၁၀၀)", "Pond.locYTooltip": "ကစားသမား၏ Y ညွှန်းကိန်းထံသို့ပြန်ပါ။ (၀ သည် အောက်ခြေအစွန်တွင်ရှိသည်၊ ၁၀၀ သည် ထိပ်အစွန်တွင်ရှိသည်။)", "Pond.docsTooltip": "ဘာသာစကား သိမ်းဆည်းမှုများကိုပြပါ။", "Pond.documentation": "မှတ်တမ်းမှတ်ရာများ", "Pond.playerName": "သင့်ဖြေရှင်းချက်အလုပ်ဖြစ်ပါသည်။", "Pond.targetName": "ဦးတည်ချက်", "Pond.pendulumName": "နာရီချိန်သီး", "Pond.scaredName": "ကြောက်ရွံ့သော", "Pond.helpUseScan": "သင်၏ ဖြေရှင်းမှုအလုပ်ဖြစ်ပါသည်၊ သို့သော သင့်အနေဖြင့် ဤထက်ကောင်းမွန်အောင်ပြုလုပ်နိုင်ပါသေးသည်။ Scan ကိုအသုံးပြုပြီး အမြှောက်ကို မည်မျှဝေးဝေးသွားမည်ကို သတ်မှတ်ပါ။", "Pond.helpText1": "အမြောက် ကိုအမိန့်ပေးပြီး ပစ်မှတ်ကိုပစ်ပါ။ ပထမဦးဆုံးအကန့်အသတ်မှာ ထောင့်ဖြစ်သည်။ ဒုတိယ အကန့်အသတ်မှာ အကွာအဝေးဖြစ်သည်။ မှန်ကန်သော ပေါင်းစပ်မှုကိုရှာဖွေပါ။", "Pond.helpText2": "ပစ်မှတ်သည်အကြိမ်များစွာထိမှန်ရန်လိုအပ်ပါသည်။ while (true) ကိုအသုံးပြုပြီး မတိကျမသေချာသည့် အရာတစ်ခုပြုလုပ်ပါ။", "Pond.helpText3a": "ပြိုင်ဘက်သည် ရှေ့နောက်ရွေ့လျားပြီး ထိမှန်ရန်ခက်ခဲအောင်ပြုလုပ်နေသည်။ Scan ကိုအသုံးပြုခြင်းသည် ပြိုင်ဘက်၏ အကွာအဝေးအတိအကျကို သတ်မှတ်ပေးသည်။", "Pond.helpText3b": "ဤကြားအကွားအဝေးသည် သေချာတိကျစွာ အမြောက်ပစ်လွှတ်နိုင်ရန်ဖြစ်သည်။", "Pond.helpText4": "ပြိုင်ဘက်ကစားသမားသည် အမြောက်မှအလွန်ကွာဝေးစွာတည်ရှိနေသည် (မီတာ ၇၀ မှာအကန့်အသတ်ဖြစ်သည်) ထိုအစား ရေကူးခြင်း ကိုအသုံးပြီးပြိုင်ဘက်နှင့်နီးကပ်အောင်သွားပြီး တိုက်ခိုက်ပါ။", "Pond.helpText5": "အမြောက်ကိုအသုံးပြုရန်အတွက် ပြိုင်ဘက်သည်ဝေးလွန်းနေသည်။ သို့သော်လည်း တိုက်ခိုက်မှုတွင် သင်အသက်ရှင်ကျန်ရစ်ရန်အတွက် အားနည်းလွန်းနေသည်။ သင်၏ ရေပြင်ညီတည်နေရာ ၅ဝထက်လျော့ပါက ပြိုင်ဘက်ထံသို့ကူးခတ်သွားပါ။ ထို့နောက်ရပ်ပြီး အမြောက်ကိုအသုံးပြုပါ။", "Pond.helpText6": "တိုက်မိသည့်အချိန်တွင် ပြိုင်ဘက်ကစားသမားသည် ထွက်ပြေးသွားမည်ဖြစ်သည်။ အပိုင်းအခြားထက်ကျော်လွန်ပါက ၎င်းထံသို့ကူးခတ်သွားပါ။ (မီတာ ၇၀)", "Gallery": "ပြခန်း"}