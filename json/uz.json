{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Nodirbek", "Sociologist"]}, "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "Toshbaqa", "Games.movie": "Film", "Games.music": "Musi<PERSON>", "Games.pondTutor": "<PERSON><PERSON><PERSON>", "Games.pond": "Hovuz", "Games.linesOfCode1": "Siz ushbu darajani 1 qator JavaScript bilan yechdingiz:", "Games.linesOfCode2": "<PERSON>z <PERSON><PERSON>bu <PERSON> %1 qator Javascript yordamida yechdingiz:", "Games.nextLevel": "%1 darajaga tayyormisiz?", "Games.finalLevel": "Keyingi sinovga tayyormisiz?", "Games.submitTitle": "Sarlavha:", "Games.linkTooltip": "Sa<PERSON><PERSON> va bloklarga havola.", "Games.runTooltip": "Siz yozgan dasturni ishga tushiring.", "Games.runProgram": "<PERSON><PERSON><PERSON> ishga tushiring", "Games.resetTooltip": "<PERSON><PERSON><PERSON> to'x<PERSON> va darajani qayta o'r<PERSON>.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Man<PERSON><PERSON>", "Games.catLoops": "Tsikllar", "Games.catMath": "Matematika", "Games.catText": "Mat<PERSON>", "Games.catLists": "Roʻyxatlar", "Games.catColour": "<PERSON>ng", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "So'rov bilan bog'liq muammo yuz berdi.", "Games.linkAlert": "O'zingizning bloklaringizni ushbu havola orqali ulashing: %1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON>, '%1' hech qanday saqlangan dasturga mos kelmaydi.", "Games.xmlError": "<PERSON>q<PERSON><PERSON> fayling<PERSON>ni yuklab bo'<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, u <PERSON>ly-ning boshqa versiyasi bilan yaratilgandir?", "Games.submitted": "<PERSON><PERSON><PERSON> dastur uchun tashakkur! Agar dastur bizning o'rgatilgan  xodimlarimizga yoqsa, uni bir necha kun ichida galereyada nashr etishadi.", "Games.listVariable": "ro'yxat", "Games.textVariable": "matn", "Games.breakLink": "JavaScript-ni tahrir<PERSON>ni boshlaganingizdan so'ng, tahrir<PERSON> bloklariga qaytishingiz mumkin emas. Rozimisiz?", "Games.blocks": "Bloklar", "Games.congratulations": "Tabriklaymiz!", "Games.helpAbort": "Bu daraja juda qiyin. <PERSON><PERSON> <PERSON>'<PERSON><PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON>, keyingi o'y<PERSON> b<PERSON>hni xoh<PERSON><PERSON>? Siz har doim keyinroq qaytib keli<PERSON> mumkin.", "Index.clear": "<PERSON>cha ye<PERSON>'chiri<PERSON>mi?", "Index.subTitle": "Ertangi kun dasturchilari uchun o'yin<PERSON>.", "Index.moreInfo": "<PERSON>'qitu<PERSON><PERSON><PERSON> uchun ma'lumot...", "Index.startOver": "<PERSON><PERSON><PERSON> b<PERSON>?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o'chirish", "Puzzle.animal1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2": "Mushuk", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "Ari", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "rasm:", "Puzzle.legs": "oyoqlari:", "Puzzle.legsChoose": "tanlang...", "Puzzle.traits": "xususiyatlar:", "Puzzle.error0": "Ajoyib!\nBarcha %1 bloklar to'g'ri", "Puzzle.error1": "<PERSON><PERSON><PERSON>! Bitta blok noto'g'ri.", "Puzzle.error2": "%1 blok xato.", "Puzzle.tryAgain": "Belgilangan blok to'g'ri emas. <PERSON><PERSON> qilishni davom ettiring.", "Puzzle.checkAnswers": "Javoblar<PERSON> te<PERSON>", "Puzzle.helpText": "<PERSON>r bir <PERSON><PERSON> (yashi<PERSON>), r<PERSON><PERSON>, oyo<PERSON><PERSON> sonini tanlang va uning xususiyatlariga bir qator qilib qo'ying.", "Maze.moveForward": "<PERSON><PERSON> yurish", "Maze.turnLeft": "<PERSON><PERSON><PERSON> burilish", "Maze.turnRight": "<PERSON><PERSON><PERSON><PERSON> burilish", "Maze.doCode": "b<PERSON><PERSON>h", "Maze.helpIfElse": "If-else blo<PERSON>ri u yoki bu narsani bajaradi.", "Maze.pathAhead": "agar to'g'rida yo'l bo'lsa", "Maze.pathLeft": "agar chap tarafda yo'l bo'lsa", "Maze.pathRight": "agar o'ng tomonda yo'lda bo'lsa", "Maze.repeatUntil": "qadar takrorlang", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitta bo's<PERSON><PERSON> oldinga siljit<PERSON>.", "Maze.turnTooltip": "Qahramonni 90 daraja chapga yoki o'ngga burab qo'yadi.", "Maze.ifTooltip": "<PERSON>gar belgi<PERSON>an yo'na<PERSON>da yo'l bo'lsa, unda ba'zi amallarni bajaring.", "Maze.ifelseTooltip": "<PERSON><PERSON> bel<PERSON>an yo'na<PERSON>da yo'l bo'l<PERSON>, unda birinchi harakatlar blokini bajaring. <PERSON><PERSON> holda, harak<PERSON><PERSON>ning ikkinchi blokini bajaring.", "Maze.whileTooltip": "Tugatish nuqtasiga yetguncha ko'rsatilgan amallarni takrorlang.", "Maze.capacity0": "Sizda %0 ta blok qoldi.", "Maze.capacity1": "Sizda %1 ta blok qoldi.", "Maze.capacity2": "Sizda %2 ta blok qoldi.", "Maze.runTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> a<PERSON> baja<PERSON><PERSON>ga majbur qiladi.", "Maze.resetTooltip": "<PERSON>irint<PERSON> boshiga o'y<PERSON><PERSON>i qaytarib qo'ying.", "Maze.helpStack": "<PERSON><PERSON><PERSON><PERSON> erishishi<PERSON>da yordam berish uchun bir nechta \"oldinga siljish\" blo<PERSON><PERSON> yig'ing.", "Maze.helpOneTopBlock": "<PERSON><PERSON><PERSON> da<PERSON>ada siz oq rangdagi ish joyida barcha bloklarni to'plashing<PERSON> kerak.", "Maze.helpRun": "<PERSON><PERSON> bo'lishini ko'rish uchun dasturingizni ishga tushiring.", "Maze.helpReset": "<PERSON>z<PERSON> dasturingiz labirintga yechim topmadi. \"Qayta tiklash\" <PERSON><PERSON><PERSON> bosing va qaytadan urinib ko'ring.", "Maze.helpRepeat": "<PERSON><PERSON><PERSON> yo'lning oxiriga yetish uchun faqat ikkita blokdan foydalaning. Blokni bir necha marta ishlatish uchun \"takrorlash\" dan foydalaning.", "Maze.helpCapacity": "<PERSON>z ushbu da<PERSON>gi barcha bloklardan foydalandingiz. <PERSON>i blok yaratish uchun avval mavjud blokni o'chirib tashlashingiz kerak.", "Maze.helpRepeatMany": "<PERSON><PERSON> \"takro<PERSON><PERSON>\" blokiga bir nechta bloklarni joylash<PERSON><PERSON><PERSON><PERSON> mumkin.", "Maze.helpSkins": "<PERSON><PERSON><PERSON> yo<PERSON> qahramoningizni tanlang.", "Maze.helpIf": "\"Agar\" bloki faqat shart to'g'ri bo'lsa, biror ishni bajaradi. Agar chap tomonda yo'l bo'lsa, chapga burilishga harakat qiling.", "Maze.helpMenu": "Uning holatini o'zgartirish uchun 'if' blokidagi %1-ni bosing.", "Maze.helpWallFollow": "<PERSON><PERSON><PERSON> m<PERSON><PERSON>b labirintning ye<PERSON><PERSON> topa olasizmi? Chap tarafdagi devor bilan harakat qilib ko'ring. Faqat kuchli dasturchilar uchun!", "Bird.noWorm": "chu<PERSON><PERSON> yo'q", "Bird.heading": "sarl<PERSON>ha", "Bird.noWormTooltip": "<PERSON><PERSON> chu<PERSON> olmagan holat.", "Bird.headingTooltip": "<PERSON><PERSON><PERSON> burchak yo'na<PERSON>i bo'yicha <PERSON>: 0 o'ngga, 90 to'g'ri yuq<PERSON>ga va hk.", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Gallery": "<PERSON><PERSON><PERSON>"}