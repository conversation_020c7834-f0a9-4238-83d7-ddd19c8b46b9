{"@metadata": {"authors": ["<PERSON>"]}, "Games.puzzle": "Jigsaw puzzle", "Games.maze": "<PERSON><PERSON>", "Games.turtle": "Turtle", "Games.linkTooltip": "Hain n airt til blocks.", "Games.runProgram": "Rin <PERSON>", "Games.resetProgram": "Reset", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logeec", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Maths", "Games.catText": "Tex", "Games.catLists": "<PERSON><PERSON>", "Games.catColour": "Colour", "Games.catVariables": "Variables", "Games.catProcedures": "Functions", "Games.httpRequestError": "Thaur wis ae problem wi the request.", "Games.linkAlert": "Shair yer blocks wi this airtin:\n\n%1", "Games.hashError": "<PERSON><PERSON>, '%1' disna correspond wi onie hained program.", "Games.xmlError": "<PERSON><PERSON><PERSON> laid yer hained file.  Perhaps it wis makit wi ae deefferent version o Blockly?", "Games.listVariable": "leet", "Games.textVariable": "tex", "Puzzle.legsChoose": "chuise...", "Puzzle.error0": "Perfect!\nAw %1 blocks ar correct.", "Puzzle.error1": "Naerlie! yin block is oncorrect.", "Puzzle.error2": "%1 blocks ar oncorrect.", "Puzzle.tryAgain": "The heilichted block is no correct.\nKeep it up.", "Puzzle.checkAnswers": "Check Answers", "Puzzle.helpText": "Fer ilka kintrie (green), attach its banner, chuise its leid, an mak ae stack o its cities.", "Maze.moveForward": "muiv forewaird", "Maze.turnLeft": "turn cair", "Maze.turnRight": "turn richt", "Maze.doCode": "dae", "Maze.helpIfElse": "Gif-itherwise blocks will dae yin thing or the ither.", "Maze.pathAhead": "gif path is aheid", "Maze.pathLeft": "gif path is oan the cair", "Maze.pathRight": "gif path is oan the richt", "Maze.repeatUntil": "repeat ontil", "Maze.moveForwardTooltip": "<PERSON><PERSON>s the player forewaird yin space.", "Maze.turnTooltip": "Turns the player cair or richt bi 90 degrees.", "Maze.ifTooltip": "Gif thaur is ae path in the speceefied direction, than dae some aictions.", "Maze.ifelseTooltip": "Gif thaur is ae path in the speceefied direction, than dae the first block o aictions. Itherwise, dae the seicont block o aictions.", "Maze.whileTooltip": "Repeat the enclaised aictions ontil the finish point is reached.", "Maze.capacity0": "Ye hae %0 blocks left.", "Maze.capacity1": "Ye hae %1 block left.", "Maze.capacity2": "Ye hae %2 blocks left.", "Maze.runTooltip": "Maks the player dae whit the blocks say.", "Maze.resetTooltip": "Put the player back at the stairt o the maze.", "Maze.helpStack": "Stack some 'muiv forewaird' blocks thegather tae heelp me reach the goal.", "Maze.helpOneTopBlock": "Oan this level, ye need tae stack thegather aw o the blocks in the white warkspace.", "Maze.helpRun": "Rin yer program tae see whit happens.", "Maze.helpReset": "Yer program didna solve the maze.  Press 'Reset' n gie it anither shot.", "Maze.helpRepeat": "Reach the end o this path uisin yinlie twa blocks.  Uise 'repeat' tae rin ae block mair than yince.", "Maze.helpCapacity": "Ye'v uised up aw the blocks fer this level.  Tae mak ae new block, ye maun first delyte aen exeestin block.", "Maze.helpRepeatMany": "Ye can fit mair than yin block inside ae 'repeat' block.", "Maze.helpSkins": "Chuise yer favoorite player fae this menu.", "Maze.helpIf": "Ae 'gif' block will dae sommit yinlie gif the condeetion is true.  Gie turnin cair ae shot gif thaur is ae path oan the cair.", "Maze.helpMenu": "Clap oan %1 in the 'gif' block tae chynge its condeetion.", "Maze.helpWallFollow": "Can ye solve this compleecated maze?  <PERSON><PERSON> follaein the cair waa ae shot.  Advanced programmers yinlie!", "Turtle.moveTooltip": "Muives the turtle forewaird or backwaird bi the speceefied amoont.", "Turtle.moveForward": "muiv forewaird bi", "Turtle.moveBackward": "muiv backwaird bi", "Turtle.turnTooltip": "Turns the turtle cai or richt bi the speceefied nummer o degrees.", "Turtle.turnRight": "turn richt bi", "Turtle.turnLeft": "turn cair bi", "Turtle.widthTooltip": "Chynges the width o the pen.", "Turtle.setWidth": "set width tae", "Turtle.colourTooltip": "Chynges the colour o the pen.", "Turtle.setColour": "set colour tae", "Turtle.penTooltip": "Lifts or lowers the pen, tae stap or stairt drawin.", "Turtle.penUp": "pen up", "Turtle.penDown": "pen doon", "Turtle.turtleVisibilityTooltip": "Maks the turtle (circle n pointer) veesible or onveesible.", "Turtle.hideTurtle": "skauk turtle", "Turtle.showTurtle": "shaw turtle", "Turtle.printTooltip": "Draws tex in the turtle's direction at its location.", "Turtle.print": "prent", "Turtle.fontTooltip": "Sets the font uised bi the prent block.", "Turtle.font": "font", "Turtle.fontSize": "font size", "Turtle.fontNormal": "normal", "Turtle.fontBold": "baud", "Turtle.fontItalic": "italeec"}