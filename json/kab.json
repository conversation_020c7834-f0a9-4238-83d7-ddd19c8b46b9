{"@metadata": {"authors": ["Belkacem77", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "YubaWissin"]}, "Games.name": "Uraren n Blocky", "Games.puzzle": "Apuzzel", "Games.maze": "<PERSON><PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.turtle": "Tifkert", "Games.movie": "Asaru", "Games.music": "Aẓawan", "Games.pondTutor": "Tamsirt n Pond", "Games.pond": "Pond", "Games.linesOfCode1": "Tufiḍ-d aswir-a s 1 n yizerrig n JavaScript:", "Games.linesOfCode2": "Tufiḍ-d aswir-a s %1 n yizerrigen n JavaScript:", "Games.nextLevel": "Theyyaḍ i uswir %1?", "Games.finalLevel": "<PERSON><PERSON><PERSON> i wayen i d-it<PERSON>dun ?", "Games.submitTitle": "Azwel:", "Games.linkTooltip": "<PERSON><PERSON> teqq<PERSON>ḍ akk i<PERSON><PERSON> i yellan.", "Games.runTooltip": "<PERSON><PERSON><PERSON> ahil i turiḍ.", "Games.runProgram": "<PERSON><PERSON><PERSON> ahil", "Games.resetTooltip": "<PERSON><PERSON><PERSON> ahil tɛiwdeḍ-d s wadda i uswir.", "Games.resetProgram": "Ɛiwed-d s wadda", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Tameẓla", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Tusnakt", "Games.catText": "<PERSON><PERSON><PERSON>", "Games.catLists": "Tibdarin", "Games.catColour": "Ini", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "Yella-d wugur deg tuttra.", "Games.linkAlert": "Bḍu iḥedran-ik/im s useɣwen-a:\n\n\n%1", "Games.hashError": "<PERSON><PERSON>-a<PERSON>, « %1 » ur yettak ara anzi ula ɣer yiwen n wahil yet<PERSON>.", "Games.xmlError": "Azdam n ufaylu-yik/yim i teskelseḍ yugi. Ahat yettwasnulfa-d s lqem yem<PERSON>den n Blockly ?", "Games.submitted": "<PERSON><PERSON><PERSON><PERSON> ɣef wahil-a! Ma yeɛǧeb-as i terbaɛt-nneɣ n yibkan, ad t-id-ssufɣen seg sya ɣer sin n wussan.", "Games.listVariable": "tabdart", "Games.textVariable": "a<PERSON><PERSON>", "Games.breakLink": "Segmi ara tebdu<PERSON> asnifel n JavaScript, ur te<PERSON><PERSON><PERSON><PERSON>ḍ ara akk ad tuɣaleḍ ɣer usnifel n yiḥedran. Yelha?", "Games.blocks": "<PERSON><PERSON><PERSON><PERSON>", "Games.congratulations": "Ayyuz!", "Games.helpAbort": "Aswir-a yuɛeṛ aṭas aṭas. Tebɣiḍ ad tǧellbeḍ ad tɛeddiḍ ɣer wurar i d-itteddun? Ad tizmireḍ ad tuɣaleḍ alma d da umbeɛd.", "Index.clear": "Ad tsefḍeḍ akk ayen yellan?", "Index.subTitle": "Uraren i yimessihlen n uzekka.", "Index.moreInfo": "Isallen isensegmayen...", "Index.startOver": "Tebɣiḍ ad as-tɛiwdeḍ s wadda?", "Index.clearData": "<PERSON><PERSON><PERSON>", "Puzzle.animal1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "Aqamum", "Puzzle.animal1HelpUrl": "https://kab.wikipedia.org/wiki/Ab<PERSON>ik", "Puzzle.animal2": "Amcic", "Puzzle.animal2Trait1": "Icelɣumen", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://kab.wikipedia.org/wiki/Amcic", "Puzzle.animal3": "Tizizwit", "Puzzle.animal3Trait1": "Tam<PERSON>", "Puzzle.animal3Trait2": "Tisiqqest", "Puzzle.animal3HelpUrl": "https://kab.wikipedia.org/wiki/Tizizwit", "Puzzle.animal4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Alda", "Puzzle.animal4HelpUrl": "https://kab.wikipedia.org/wiki/Abelɛa<PERSON>us", "Puzzle.picture": "tugna:", "Puzzle.legs": "iḍarren:", "Puzzle.legsChoose": "fren...", "Puzzle.traits": "ije<PERSON><PERSON><PERSON>:", "Puzzle.error0": "Igerrez!\nAkk iḥedran %1 d imeɣta.", "Puzzle.error1": "Qrib! <PERSON>wen n yiḥder ma<PERSON>i d ameɣtu.", "Puzzle.error2": "%1 n yi<PERSON><PERSON><PERSON> ma<PERSON> d imeɣta.", "Puzzle.tryAgain": "<PERSON><PERSON><PERSON> s ub<PERSON><PERSON>eq ma<PERSON>i d ameɣtu. Ɛreḍ tikkelt-nniḍen.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON>n", "Puzzle.helpText": "I yal aɣersiw (a<PERSON>g<PERSON><PERSON>), sente<PERSON> tugna-s s ufran n wuṭṭun n yiḍarren-is, sakin sers ayen ixeddem awal ɣef wawal.", "Maze.moveForward": "<PERSON><PERSON><PERSON>", "Maze.turnLeft": "<PERSON><PERSON> <PERSON>", "Maze.turnRight": "<PERSON><PERSON> <PERSON> ayeffus", "Maze.doCode": "eg", "Maze.helpIfElse": "<PERSON><PERSON><PERSON> 'ma-neɣ' yesselkam taɣawsa neɣ tayeḍ.", "Maze.pathAhead": "ma d abrid n zdat", "Maze.pathLeft": "ma d abrid n u<PERSON>", "Maze.pathRight": "ma d abrid n uyeffus", "Maze.repeatUntil": "ales almi", "Maze.moveForwardTooltip": "Saẓ amyurar s yiwen n umkan.", "Maze.turnTooltip": "<PERSON><PERSON> amyurar s a<PERSON><PERSON><PERSON> neɣ s ayeffus s 90 n n tfesniwin.", "Maze.ifTooltip": "<PERSON><PERSON> yiwen n ubrid deg tnila d-it<PERSON><PERSON><PERSON>, eg tigawin-a.", "Maze.ifelseTooltip": "Ma yella yiwen n ubrid deg tnila d-it<PERSON><PERSON><PERSON>, eg tigawin n yiḥder amezwaru. Ma ulac eg tigawin n yiḥder wis sin.", "Maze.whileTooltip": "<PERSON><PERSON> i<PERSON><PERSON>ran n ugensu almi yewweḍ iswi.", "Maze.capacity0": "Qqimen-ak-d %0 n yiḥedran.", "Maze.capacity1": "Iqqim-ak-d %1 n yiḥder.", "Maze.capacity2": "Qqimen-ak-d %2 n yiḥedran.", "Maze.runTooltip": "Err amyurar ad yeg ayen i d-qqaren yiḥedran.", "Maze.resetTooltip": "Sers amyurar deg tazwara n tziba", "Maze.helpStack": "As<PERSON>bibb lwaḥid n sin n yiḥedran n tinadin ad 'isaẓ' ar zdat akken ad d-imudd tallelt ad awḍeɣ iswi.", "Maze.helpOneTopBlock": "Deg uswir-a, tesriḍ ad tsembibbeḍ iḥedran wa ɣef wa nnig n wiyaḍ deg temnaḍt tamellalt n umahil.", "Maze.helpRun": "<PERSON><PERSON><PERSON> ahil-ik akken ad twalid ayen  i<PERSON><PERSON>run.", "Maze.helpReset": "Ahil-ik ur ye<PERSON>ri ara taziba.\nSenned <PERSON><PERSON> <PERSON><PERSON><PERSON> awe<PERSON>' sakin ɛreḍ tikkelt-nniḍen.", "Maze.helpRepeat": "Seqdec kan sin n yiḥedran akken ad tawḍeḍ iswi.\nSeqdec iḥder 'ales' akken ad teslekmeḍ iḥder ugar n tikkelt.", "Maze.helpCapacity": "Tes<PERSON><PERSON><PERSON>ḍ akk iḥedran deg uswir-a. Akken ad ternuḍ iḥder amaynut, yessef ad tekkseḍ iḥder yellan.", "Maze.helpRepeatMany": "Tzermeḍ ad terreḍ ugar n yiwen n yiḥder ar yiḥder \"ales\".", "Maze.helpSkins": "<PERSON>en amyurar-ik anurif seg wumuɣ-a.", "Maze.helpIf": "I<PERSON><PERSON> 'ma' ad iselkem ayen yellan daxel-is ma yella tawtilit d tidettit. Ad yeɛreḍ ad yezzi s azelmaḍ ma yella ubrid azelmaḍ.", "Maze.helpMenu": "Sit ɣef %1 deg yiḥder 'ma' akken ad tbeddleḍ tawtilt-is.", "Maze.helpWallFollow": "Tzemreḍ ad tefruḍ taziba-a iweɛren?\nƐreḍ ad tḍefreḍ aɣrab seg yidis n ufus-ik azelmaḍ. I yimsihal kan imussnawen.", "Bird.noWorm": "ur <PERSON><PERSON>-s <PERSON>ra i<PERSON>", "Bird.heading": "i<PERSON><PERSON>", "Bird.noWormTooltip": "<PERSON>dad ticki agḍiḍ ur d-yeṭṭif ara ijirmeḍ.", "Bird.headingTooltip": "Ddu di tnila n teɣmert yettunefken. 0 deg uzelma<PERSON>, 90 deg wudem, atg.", "Bird.positionTooltip": "x akked y mmalen-d adig n ugḍiḍ. Ticki x = 0 agḍiḍ ad yili di rrif azelmaḍ, ticki x = 100 ad yili di rrif ayeffus. Ticki y = 0 agḍiḍ ad yili ukesser, ticki y = 100 ad yili ukessawen.", "Bird.helpHeading": "Beddel tiɣmert n uqerru akken agḍiḍ ad d-yeṭṭef ijermeḍ sakin ad yerṣ ɣef lɛec-is.", "Bird.helpHasWorm": "Seqdec iḥder-agi akken ad tedduḍ ar tnila ma <PERSON>ur-k ijerme<PERSON>, sakin dar tnila-nni<PERSON>en mayella ulac.", "Bird.helpX": "'x' d adig-ik aglawan. Seqde<PERSON> iḥder-agi akken ad tedduḍ di tnila ma yella 'x' yezga-d ddwa n n umiḍan, di tnila-nniḍen ma yugar-it.", "Bird.helpElse": "Senned ɣef tignit akken ad tesnifled iḥder 'ma'.", "Bird.helpElseIf": "As<PERSON>r-<PERSON>gi yesra lwaḥid iḥder 'ma' akked iḥder 'ma ulac'.", "Bird.helpAnd": "<PERSON><PERSON><PERSON> 'ak<PERSON>' d idetti ma yella sin n inekcam d idettiyen.", "Bird.helpMutator": "<PERSON><PERSON><PERSON><PERSON><PERSON> iḥder 'ma ulac' ar yiḥder 'ma'.", "Turtle.moveTooltip": "<PERSON><PERSON> a<PERSON> ar zdatneq ar deffir n tesmekta yettwammlen.", "Turtle.moveForward": "ar zdat s", "Turtle.moveBackward": "ar deffir s", "Turtle.turnTooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> s azel<PERSON><PERSON> neɣ s ayeffus s umḍan n tfesniwin d-ittunefken.", "Turtle.turnRight": "zzi s ayeffus s", "Turtle.turnLeft": "zzi s a<PERSON><PERSON><PERSON> s", "Turtle.widthTooltip": "Ad ibeddel tehri n yimru.", "Turtle.setWidth": "sbadu tuzert ar", "Turtle.colourTooltip": "Ad ibeddel ini n yimru.", "Turtle.setColour": "sbadu ini ar", "Turtle.penTooltip": "Rfed neɣ sers imru, akken ad tesḥebseḍ neɣ ad tkemmleḍ asuneɣ.", "Turtle.penUp": "rfed imru", "Turtle.penDown": "sers imru", "Turtle.turtleVisibilityTooltip": "Ad yerr a<PERSON> (tawinest neɣ aneccab) ad yettban neɣ ala.", "Turtle.hideTurtle": "ffer a<PERSON><PERSON>run", "Turtle.showTurtle": "sken afekrun", "Turtle.printTooltip": "<PERSON> isune<PERSON> a<PERSON>ris s tnilla n ufekrun deg wadig-is.", "Turtle.print": "aru", "Turtle.fontTooltip": "Ad isbadu tasefsit iseqdac iḥder n tira.", "Turtle.font": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontSize": "Teɣzi n tsefsit", "Turtle.fontNormal": "amagnu", "Turtle.fontBold": "zur", "Turtle.fontItalic": "uknan", "Turtle.submitDisabled": "Ad iselkem ahil arama yeḥbes. Sakin ad tizmired ad tessuffɣeḍ unuɣ-ik di tmidelt.", "Turtle.galleryTooltip": "<PERSON><PERSON> timidelt n wunuɣen.", "Turtle.galleryMsg": "<PERSON><PERSON> timidelt", "Turtle.submitTooltip": "Suffeɣ-f unuɣ-ik.", "Turtle.submitMsg": "Suffeɣ-d di tmidelt", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON>-<PERSON>k te<PERSON>, acu kan tzemreḍ ad teggeḍ ugar.", "Turtle.helpUseLoop3": "<PERSON> isuneɣ talɣa s kraḍ kan n iḥedran.", "Turtle.helpUseLoop4": "<PERSON> isuneɣ itri s kuẓ kan n iḥedran.", "Turtle.helpText1": "Ad yernu ahil isunuɣen amkuẓ.", "Turtle.helpText2": "Ad ibeddel ahil-ik akken ad isuneɣ asemmusdis deg umḍiq n umkuẓ.", "Turtle.helpText3a": "Wagi d iḥder amaynut ara k-yeǧǧen ad tbeddleḍ ini.", "Turtle.helpText3b": "Suneɣ itri awraɣ.", "Turtle.helpText4a": "Wagi d iḥder amaynut ara k-yeǧǧen ad trefdeḍ akeryun-ik n tferkit ticki tleḥḥuḍ.", "Turtle.helpText4b": "<PERSON>eɣ itri awraɣ, sakin suneɣ ajerriḍ nnig-s.", "Turtle.helpText5": "Deg umḍiq n yiwen n yitri, t<PERSON><PERSON><PERSON><PERSON> ad sunɣeḍ kraḍ n yitran yettwasersen am umkuẓ?", "Turtle.helpText6": "<PERSON> <PERSON><PERSON><PERSON> kraḍ n yitran iwraɣen, ak<PERSON> i<PERSON><PERSON> am<PERSON>.", "Turtle.helpText7": "<PERSON><PERSON><PERSON> itran iwraɣen, sakin kuẓ n ijerriḍen imellalen?", "Turtle.helpText8": "Suneɣ 360 n izirigen imellalen yettemcabin ar waggur ye<PERSON>.", "Turtle.helpText9": "Tzemreḍ ad ternuḍ tawinest taberkant akken aggur ad yuɣal razzirt?", "Turtle.helpText10": "Suneɣ ayen tebɣiḍ. Ɣur-k ddeqs n iḥedran ara twaliḍ. Zhu!", "Turtle.helpText10Reddit": "Seqdec taqeffalt  \"Wali timdelt\" akken ad twaliḍ unuɣen i gan wiyaḍ. Ma yella terniḍ unuɣen yelhan, seqdec taqeffalt \"Suffeɣ-d di tmidelt\" akken ad t-id-suffɣeḍ.", "Turtle.helpToolbox": "Fren taggayt akked ad twaliḍ iḥedran.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x n tazwara", "Movie.y1": "y n tazwara", "Movie.x2": "x n tagara", "Movie.y2": "y n tagara", "Movie.radius": "a<PERSON><PERSON><PERSON>", "Movie.width": "tehri", "Movie.height": "<PERSON><PERSON><PERSON>", "Movie.circleTooltip": "Ad isuneɣ tawinest deg um<PERSON>iq akked waqqar d-ittunefken.", "Movie.circleDraw": "tawinest", "Movie.rectTooltip": "<PERSON> is<PERSON><PERSON> asrem deg umḍiq d-ittunefken s tehri akked teɣzi yettunefken.", "Movie.rectDraw": "asrem", "Movie.lineTooltip": "<PERSON> isune<PERSON> izirig seg yiwet n tneqqiḍt ar tayeḍ s tuzert d-ittenefken.", "Movie.lineDraw": "<PERSON><PERSON><PERSON>", "Movie.timeTooltip": "Ad d-yerr tanzagt tamirant n umray (0-100).", "Movie.colourTooltip": "Beddel ini n ukeryun.", "Movie.setColour": "sbadu ini ar", "Movie.submitDisabled": "Amray-ik ur yet<PERSON><PERSON><PERSON><PERSON> ara. Seqdec iḥedran akken ad tegeḍ kra n wayen yelhan. Sakin ad tizmireḍ ad tessuffɣeḍ amray-ik di tmidelt.", "Movie.galleryTooltip": "<PERSON>di timidelt n yemrayen.", "Movie.galleryMsg": "<PERSON><PERSON> timidelt", "Movie.submitTooltip": "<PERSON><PERSON> amray-ik.", "Movie.submitMsg": "Suffeɣ-d di tmidelt", "Movie.helpLayer": "<PERSON><PERSON> tawinest n ugilal d asawn n wahil-ik. <PERSON><PERSON> ad d-iban dffir n umdan.", "Movie.helpText1": "<PERSON><PERSON><PERSON><PERSON> tilɣiwin tiḥerfiyin akken ad tseunɣeḍ amdan-agi.", "Movie.helpText2a": "<PERSON><PERSON><PERSON><PERSON>a<PERSON> d asaru. Tebɣiḍ ad tawiḍ iɣil n umdan deg ugdil. Senned ɣef tqeffalt n tɣuri akken ad twaliḍ taskant.", "Movie.helpText2b": "<PERSON><PERSON><PERSON> it<PERSON>u usaru, azal n yiḥder 'akud' ad yettaẓ si 0 ar 100. <PERSON><PERSON> tebɣiḍ d akken adig 'y' n yiɣil ad yebdu si 0 sakin ad yeddu arama 100, ayagi d ayen fessusen.", "Movie.helpText3": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' ad yeddu seg 0 arama d 100. Maca tura tebɣiḍ d akken adig 'y' nyiɣil-nniḍen ad yebdu seg 100 aram d 0. <PERSON><PERSON><PERSON><PERSON><PERSON> ad tafeḍ tanfalit n tusnakt ara yettin tanila?", "Movie.helpText4": "Seqdec ayen i tlemdeḍ deg uswir uezrin akken ad tkerrfeḍ iḍaṛṛen.", "Movie.helpText5": "Tan<PERSON>lit n tusnakt i yiɣil tewɛṛ. A-tt-a tririt.", "Movie.helpText6": "<PERSON>dd sin n yiɣalen i umdan.", "Movie.helpText7": "Sqdec iḥder 'if' akken ad tsunɣeḍ aqeṛṛu mẓẓiyen i uzgn anemzu n usaru. Sakin suneɣ aqeṛṛu mqqren i uzgen wis sin n usaru.", "Movie.helpText8": "<PERSON><PERSON> iḍaṛ<PERSON>en ɣr tnila-nniden deg uzgen n usaru.", "Movie.helpText9": "Sun<PERSON>ɣ tawinest ara y<PERSON> deffir n umdan.", "Movie.helpText10": "<PERSON>e<PERSON> amray tebɣiḍ. Ɣur-k ddeqs n iḥedran ara twaliḍ. Zhu!", "Movie.helpText10Reddit": "Seqdec taqeffalt  \"Wali timdelt\" akken ad twaliḍ imrayen i gan wiyaḍ. Ma yella terniḍ imrayen ye<PERSON>, seqdec taqeffalt \"Suffeɣ-d di tmidelt\" akken ad -t-id-suffɣeḍ.", "Music.playNoteTooltip": "<PERSON><PERSON> tazmili n uẓawan n tenzagt akked uwrir n yisefka.", "Music.playNote": "Urar %1 tazmilt %2", "Music.restTooltip": "Ad yerǧu tanzagt i d-ittufken.", "Music.restWholeTooltip": "Ad yerǧu tazmilt meṛṛa.", "Music.rest": "rǧu %1", "Music.setInstrumentTooltip": "<PERSON>du ar wallal i d-ituunefken s wurar n tezmilin tiẓawanin yeṭṭafaren.", "Music.setInstrument": "Sbadu allal ar %1", "Music.startTooltip": "Ad yesselkem iḥedran degugensu ticki taq<PERSON><PERSON>lt \"<PERSON><PERSON> ahil\" tettusenned.", "Music.start": "ticki %1 yettusenned", "Music.pitchTooltip": "<PERSON><PERSON><PERSON><PERSON>(C4 d 7).", "Music.firstPart": "a<PERSON><PERSON> amenzu", "Music.piano": "<PERSON><PERSON><PERSON>", "Music.trumpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Music.banjo": "Abanju", "Music.violin": "Imẓad", "Music.guitar": "Snitra", "Music.flute": "Ajewwaq", "Music.drum": "a<PERSON><PERSON>er", "Music.choir": "tarba<PERSON>t", "Music.submitDisabled": "Ad iselkem ahil arama yeḥbes. Sakin ad tizmired ad tessuffɣeḍ azaɛan-ik di tmidelt.", "Music.galleryTooltip": "<PERSON><PERSON> timidelt n uẓawan.", "Music.galleryMsg": "<PERSON><PERSON> timidelt", "Music.submitTooltip": "Azen aẓawan-ik ar tmidelt.", "Music.submitMsg": "Suffeɣ-d di tmidelt", "Music.helpUseFunctions": "Tifrat-<PERSON>k te<PERSON>, maca tzemreḍ ad txedmeḍ ugar. Seqdec tiwuriwin akken ad tesneɣseḍ tasmekta n tengalt yettwalsen.", "Music.helpUseInstruments": "Aẓawan ad yffeɣ akken iwata ma yella tsxedmeḍ allal-nniden deg yal iḥder n tnekra.", "Music.helpText1": "<PERSON>dd<PERSON> kra<PERSON> n tizmilin timezwura n 'Frère Jacques'.", "Music.helpText2a": "'tawuri' ad k-teǧǧ ad tesdukleḍ iḥedran, sakin ad ten-tsellekmeḍ deg yiwet n tikelt.", "Music.helpText2b": "<PERSON>nu tawuri akken ad turareḍ kraḍ n tizmilin timezwura n 'Frère Jacques'. <PERSON><PERSON> tawuri-agi snat n tikalt. Ur rennu ara iḥedran-nniḍen n tizmilin.", "Music.helpText3": "<PERSON>nu tawuri tis snat i yiḥder n 'Frère Jacques'. Tazmilt taneggarut meqqert ugar.", "Music.helpText4": "Rnu tawuri tis kraḍ i yiḥder yeṭṭafaṛen '<PERSON><PERSON>'. Kuẓ timezwura n tizmilin weizzilit ugar.", "Music.helpText5": "Fak aẓawan n 'Frère Jacques'.", "Music.helpText6a": "I<PERSON><PERSON>-agi amaynut ad k-yeǧǧ ad tedduḍ ar wallal-nniḍen.", "Music.helpText6b": "Urar aẓwan-ik s yimẓad", "Music.helpText7a": "<PERSON><PERSON><PERSON>-agi amaynut a dyernu akud n tsusmi.", "Music.helpText7b": "Rnu g̱hder wis sin yesɛan iḥder n tsusmi, sakin urar daɣen '<PERSON><PERSON>'.", "Music.helpText8": "Yal iḥder n beddu yessefk ad yura<PERSON> '<PERSON><PERSON>' snat n tikal.", "Music.helpText9": "Rnu kuẓ n yiḥedran n beddu anida yal yiwen ad yura<PERSON> '<PERSON><PERSON>' snat n tikal. rnu amḍan iwatan n yiḥedran n tsusmi.", "Music.helpText10": "Suddes ayen tebɣiḍ. Ɣur-k ddeqs n iḥedran imaynuten ara twaliḍ. Zhu!", "Music.helpText10Reddit": "Seqdec taqeffalt  \"Wali timdelt\" akken ad twaliḍ ayen i suddsen wiyaḍ. Ma yella terniḍ ayen ye<PERSON>han, seqdec taqeffalt \"Suffeɣ-d deg tmidelt\" akken ad t-id-suffɣeḍ.", "Pond.scanTooltip": "<PERSON><PERSON>. <PERSON><PERSON> (0-360). Ad d-yerr ameccaq n ucengu iqerben di tnila. Ad d-yerr ifeḍ ma ulac ula d yiwen n ucengu.", "Pond.cannonTooltip": "<PERSON>wet s umrud. <PERSON><PERSON> (0-360) akked ta<PERSON> tafellayt n umeccaq (0-70).", "Pond.swimTooltip": "<PERSON><PERSON><PERSON> (0-360).", "Pond.stopTooltip": "<PERSON><PERSON>es ardab. <PERSON><PERSON><PERSON> ad isenɣeṣ arured akken ad yeḥbes.", "Pond.healthTooltip": "Ad d-yerr tazmert tamirant n umyurar (0 yemmut, 100 yeǧǧhedd aṭas).", "Pond.speedTooltip": "Ad d-yerr arured amiran n umyurar (0 yeḥbes, 100 yettazzal aṭas).", "Pond.locXTooltip": "Ad d-yerr amsideg X n umyurar (0 d rrif azel<PERSON>, 100 d rrif ayeffus).", "Pond.locYTooltip": "Ad d-yerr amsideg y n umyurar (0 d rrif n ukesser, 100 d rrif n usawen).", "Pond.docsTooltip": "Ad d-yesken tasemlit ɣef tutlayt.", "Pond.documentation": "<PERSON><PERSON><PERSON><PERSON>", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.pendulumName": "Tamrilt", "Pond.scaredName": "Ugade<PERSON>", "Pond.helpUseScan": "Tifrat-<PERSON><PERSON> te<PERSON>, acu kan tzemreḍ ad tegeḍ ugar. Seqdec 'scan' akken ad temmleḍ i umrud ameccaq ansa ara yewwet.", "Pond.helpText1": "Seqdec taladna 'cannon' akken ad tḥazeḍ asaḍas. Aɣewwa<PERSON> amezwaru n tiɣmert, wis sin d ameccaq. Af-d tudd<PERSON> i<PERSON>an.", "Pond.helpText2": "Asaḍas-agi yessefk ad yettuḥaz ddeqs n tikal. Seqdec tineddict 'while (true)' akken ad tegeḍ taɣawsa s wudem ifed.", "Pond.helpText3a": "Afna-agi yettaẓ yettuɣal, ayen ara t-yerren yewɛeṛ iakken ad tḥazeḍ-t. <PERSON><PERSON><PERSON> 'scan' ad terr ameccaq yellan deg tnila yettunefken.", "Pond.helpText3b": "Ta<PERSON>-agi tafellayt n umeccaq d ayen tesra tladna 'cannon' akken ad tewwet s tseddi.", "Pond.helpText4": "Afna-agi yebɛed aṭas akken ad tesqedceḍ amrud (ibeɛden arama d 70 n ikilumitren). Deg umḍiq-is, seqdec taladna 'swim' akken ad tebduḍ ardab ar ufna akkenad tḥazeḍ-t.", "Pond.helpText5": "Afna-agi yebɛed aṭas akken ad tesqedceḍ amrud. <PERSON><PERSON>, ur tezmireḍ ara i umyewwat. Rdeb ar ufna skud adig-ik aglawan yekka-d ddaw n 50. I<PERSON> 'stop' sakin seqdec amrud.", "Pond.helpText6": "Afna-agi ad ireggel ticki tḥuzaḍ-t. <PERSON><PERSON>-s ma yella yebɛed ugar n talast (70 n ikilumitren).", "Gallery": "<PERSON><PERSON><PERSON>"}