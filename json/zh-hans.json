{"@metadata": {"authors": ["A Retired User", "Assoc", "Deathkon", "Hydra", "Hzy980512", "<PERSON><PERSON>", "Jiang123aa", "Liuxinyu970226", "Lu<PERSON><PERSON>cheng", "Qiyue2001", "She<PERSON>", "SkEy", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yfdyh000", "予弦", "沈澄心", "神樂坂秀吉"]}, "Games.name": "Blockly 游戏", "Games.puzzle": "拼图", "Games.maze": "迷宫", "Games.bird": "鸟", "Games.turtle": "乌龟", "Games.movie": "电影", "Games.music": "音乐", "Games.pondTutor": "池塘教学", "Games.pond": "池塘", "Games.linesOfCode1": "你通过一行JavaScript通过了这一关：", "Games.linesOfCode2": "你通过 %1 行JavaScript通过了这一关：", "Games.nextLevel": "你准备好开始第%1关了吗?", "Games.finalLevel": "你准备好下一个挑战了吗？", "Games.submitTitle": "标题：", "Games.linkTooltip": "保存进度，并创建链接。", "Games.runTooltip": "运行您编写的程序。", "Games.runProgram": "运行程序", "Games.resetTooltip": "停止程序运行并重置本关。", "Games.resetProgram": "重置", "Games.help": "帮助", "Games.catLogic": "逻辑", "Games.catLoops": "循环", "Games.catMath": "数学", "Games.catText": "文本", "Games.catLists": "列表", "Games.catColour": "颜色", "Games.catVariables": "变量", "Games.catProcedures": "函数", "Games.httpRequestError": "请求发生了问题。", "Games.linkAlert": "通过这个链接分享您编写的块：\n\n%1", "Games.hashError": "抱歉，没有任何已保存的程序对应“%1” 。", "Games.xmlError": "无法载入您保存的文件。可能您使用了其他版本的Blockly创建该文件？", "Games.submitted": "感谢您提供的程序！如果我们训练有素的猴子工作人员们喜欢它，它们会在几天之内把您的作品挂到画廊里去。", "Games.listVariable": "列表", "Games.textVariable": "文本", "Games.breakLink": "一旦您开始编辑JavaScript，您就不能再返回块编辑模式。确定要这样做吗？", "Games.blocks": "块", "Games.congratulations": "恭喜你！", "Games.helpAbort": "这一关非常困难。您也许希望跳过它并开始下一个游戏？您稍后可以再回到这关。", "Index.clear": "确认要删除您所有的解决方案吗？", "Index.subTitle": "为未来的程序员所设计的游戏。", "Index.moreInfo": "给教育者的信息……", "Index.startOver": "想要重新开始吗？", "Index.clearData": "清除数据", "Puzzle.animal1": "鸭子", "Puzzle.animal1Trait1": "羽毛", "Puzzle.animal1Trait2": "喙", "Puzzle.animal1HelpUrl": "https://zh.wikipedia.org/wiki/鸭", "Puzzle.animal2": "猫", "Puzzle.animal2Trait1": "胡须", "Puzzle.animal2Trait2": "皮毛", "Puzzle.animal2HelpUrl": "https://zh.wikipedia.org/wiki/猫", "Puzzle.animal3": "蜜蜂", "Puzzle.animal3Trait1": "蜂蜜", "Puzzle.animal3Trait2": "螫刺", "Puzzle.animal3HelpUrl": "https://zh.wikipedia.org/wiki/蜜蜂", "Puzzle.animal4": "蜗牛", "Puzzle.animal4Trait1": "壳", "Puzzle.animal4Trait2": "黏液", "Puzzle.animal4HelpUrl": "https://zh.wikipedia.org/wiki/蜗牛", "Puzzle.picture": "照片：", "Puzzle.legs": "腿：", "Puzzle.legsChoose": "请选择...", "Puzzle.traits": "特征：", "Puzzle.error0": "完美！所有%1个块都准确无误。", "Puzzle.error1": "差不多了！还有一个块没有填对。", "Puzzle.error2": "还有 %1 个块没有填对。", "Puzzle.tryAgain": "不正确的块已经高亮显示了，再试试吧。", "Puzzle.checkAnswers": "检查答案", "Puzzle.helpText": "对于每个动物（绿色），为其添加照片、选择腿的数量，并且选择它们各自的身体特征。", "Maze.moveForward": "向前走", "Maze.turnLeft": "向 左 转", "Maze.turnRight": "向 右 转", "Maze.doCode": "执行", "Maze.helpIfElse": "if-else块会根据条件是否满足，来决定执行这件事还是那件事。", "Maze.pathAhead": "如果 正前方 可以通行", "Maze.pathLeft": "如果 左侧 可以通行", "Maze.pathRight": "如果 右侧 可以通行", "Maze.repeatUntil": "重复直到", "Maze.moveForwardTooltip": "让角色向前走一步。", "Maze.turnTooltip": "让玩家向左或向右转90度", "Maze.ifTooltip": "如果在某一特定方向可以通行，那么执行特定操作。", "Maze.ifelseTooltip": "如果某方向可以通行，那么就执行第一个块内的动作。否则，执行第二个块内的动作。", "Maze.whileTooltip": "重复内部包含的动作直至到达终点。", "Maze.capacity0": "您还剩下 %0 块。", "Maze.capacity1": "您还剩下 %1 块。", "Maze.capacity2": "您还剩下 %2 块。", "Maze.runTooltip": "让角色按照块的指令来行动。", "Maze.resetTooltip": "把角色放回到迷宫的起始点。", "Maze.helpStack": "程序是由一系列的“块”组成的。将多个“向前走”的块叠加起来，帮我走到终点。", "Maze.helpOneTopBlock": "在这一关里，您需要将所有方块堆叠在白色工作区内。", "Maze.helpRun": "运行您的程序，看看会发生什么。", "Maze.helpReset": "您的程序没能解决这个谜题。点击“重置”按钮，再试一次。", "Maze.helpRepeat": "只用两个块来走到路的尽头。使用“重复”来让一个块运行不止一次。", "Maze.helpCapacity": "您在这关里已经用完了全部块。若要创建新块，您必须先删除一个已存在的块。", "Maze.helpRepeatMany": "您可以在“重复”块中放入多个块", "Maze.helpSkins": "从这个菜单中选择您最喜欢的角色吧。", "Maze.helpIf": "“if”块里的内容只会在条件为真时才会执行。试试当左侧可以通行时，向左转。", "Maze.helpMenu": "在“if”块中点击 %1 来改变它的判别条件。", "Maze.helpWallFollow": "您能解决这个复杂的迷宫吗？试着沿着左手边的墙一直走。仅限经验丰富的程序员！", "Bird.noWorm": "还没捉到虫子", "Bird.heading": "飞行方向", "Bird.noWormTooltip": "鸟还没有捉到虫子时，该条件成立。", "Bird.headingTooltip": "向着指定角度的方向飞行：0表示正右，90表示正上方，以此类推。", "Bird.positionTooltip": "横坐标x和纵坐标y记录鸟的当前位置。当 x = 0 时鸟贴着左侧边缘，当 x = 100 时贴着右侧边缘。当 y = 0 时在最底端，当 y = 100 时在最上端。", "Bird.helpHeading": "改变鸟的飞行方向，让鸟捉到虫子并且飞回它的巢。", "Bird.helpHasWorm": "使用这个块，让你能够在没抓到虫子时飞向一个方向，而抓到虫子之后飞向另一个方向。", "Bird.helpX": "“x”表示你当前的横坐标位置。使用这个块，让你在“x”小于某个数值时飞向一个方向，否则飞向另一个方向。", "Bird.helpElse": "点击图案来修改“if”块。", "Bird.helpElseIf": "这一关既需要“else if”块，又需要“else”块。", "Bird.helpAnd": "“and”方块只有当其输入都为真时，结果才为真。", "Bird.helpMutator": "将一个“else”块拖入“if”块里。", "Turtle.moveTooltip": "让乌龟向前或向后移动指定的距离。", "Turtle.moveForward": "前进 指定距离", "Turtle.moveBackward": "后退 指定距离", "Turtle.turnTooltip": "让乌龟向左或向右转动指定的角度。", "Turtle.turnRight": "右转", "Turtle.turnLeft": "左转", "Turtle.widthTooltip": "更改笔的粗细。", "Turtle.setWidth": "将笔粗设置为", "Turtle.colourTooltip": "更改笔的颜色。", "Turtle.setColour": "将笔的颜色设为", "Turtle.penTooltip": "提起或落下笔，来停下或开始绘画。", "Turtle.penUp": "提起笔", "Turtle.penDown": "落下笔", "Turtle.turtleVisibilityTooltip": "让乌龟（那个圆圈和箭头）可见或者隐藏。", "Turtle.hideTurtle": "隐藏乌龟", "Turtle.showTurtle": "显示乌龟", "Turtle.printHelpUrl": "https://zh.wikipedia.org/wiki/印刷", "Turtle.printTooltip": "在龟的朝向和位置处书写文本。", "Turtle.print": "书写", "Turtle.fontHelpUrl": "https://zh.wikipedia.org/wiki/字体", "Turtle.fontTooltip": "设置书写块使用的字体。", "Turtle.font": "字体", "Turtle.fontSize": "文字大小", "Turtle.fontNormal": "正常", "Turtle.fontBold": "粗体", "Turtle.fontItalic": "斜体", "Turtle.submitDisabled": "运行您的程序直到它停下来。之后您可以提交您的绘画作品到画廊里。", "Turtle.galleryTooltip": "打开绘画画廊。", "Turtle.galleryMsg": "浏览画廊", "Turtle.submitTooltip": "提交您的绘画作品到画廊里。", "Turtle.submitMsg": "提交至画廊", "Turtle.helpUseLoop": "您的解决方案是可行的，但您应该可以做得更好。", "Turtle.helpUseLoop3": "仅用三个块来绘制图形。", "Turtle.helpUseLoop4": "仅用四个块来绘制星形。", "Turtle.helpText1": "创建一个绘制正方形的程序。", "Turtle.helpText2": "修改您的程序，让它绘制一个五边形而不是正方形。", "Turtle.helpText3a": "现在有一个新的块，可以让您改变笔的颜色：", "Turtle.helpText3b": "画一个黄色的星形。", "Turtle.helpText4a": "现在有一个新的块，可以让您在移动的时候把笔从纸面上提起来，不再留下痕迹。", "Turtle.helpText4b": "画一个黄色的星形，然后在它的上方画一条线。", "Turtle.helpText5": "不仅仅画一颗星，你能画出排成方形的四颗星星吗？", "Turtle.helpText6": "画出三个黄色的星星，还有一条白色的线。", "Turtle.helpText7": "画出这些星星，然后画四条白色的线。", "Turtle.helpText8": "画出360条白色的线，它们看起来像是一轮满月。", "Turtle.helpText9": "您能再加上一个黑色的圆，使月亮变成新月吗？", "Turtle.helpText10": "想画什么就画什么吧。现在您拥有了大量的新块可供探索。玩得愉快！", "Turtle.helpText10Reddit": "点击\"浏览画廊\"按钮来浏览其他人的作品。如果您画出了一些有趣的作品，可以点击\"提交至画廊\"按钮来发布它。", "Turtle.helpToolbox": "选择一个分类以查看这些块。", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "起点x", "Movie.y1": "起点y", "Movie.x2": "终点x", "Movie.y2": "终点y", "Movie.radius": "半径", "Movie.width": "宽度", "Movie.height": "高度", "Movie.circleTooltip": "在指定位置画一个指定半径的圆形。", "Movie.circleDraw": "圆", "Movie.rectTooltip": "在指定位置画一个指定宽度和高度的矩形。", "Movie.rectDraw": "矩形", "Movie.lineTooltip": "在一个点和另一个点之间画一条指定粗细的线", "Movie.lineDraw": "线", "Movie.timeTooltip": "返回动画中的当前时间(范围0-100)。", "Movie.colourTooltip": "更改笔的颜色。", "Movie.setColour": "设置颜色为", "Movie.submitDisabled": "您的电影里没有东西在运动。用这些块来制作一些有趣的东西。然后您可以提交您的电影作品到画廊里。", "Movie.galleryTooltip": "打开电影画廊。", "Movie.galleryMsg": "浏览画廊", "Movie.submitTooltip": "提交您的电影作品到画廊里。", "Movie.submitMsg": "提交至画廊", "Movie.helpLayer": "将作为背景的圆放在你程序的最开头绘制，它就会显示在人的身后。", "Movie.helpText1": "使用简单的图形来画出这个人。", "Movie.helpText2a": "这一关是一个电影。您希望移动人物的手臂横穿屏幕。请按下播放键来观看。", "Movie.helpText2b": "当电影正在播放时，“time”块的值会从0增加到100。要是您希望手臂的“y”坐标从0开始运动至100，这应该会很容易办到。", "Movie.helpText3": "“time”块的值从0增长到100。但是现在您希望另一条手臂的“y”坐标从0开始运动至100。您可以用一个简单的数学公式来让运动方向相反吗？", "Movie.helpText4": "使用您在上一关学到的东西来让腿交叉。", "Movie.helpText5": "手臂运动的数学公式比较复杂。这里有答案：", "Movie.helpText6": "给人物加一双手。", "Movie.helpText7": "使用“if”块以在电影前半部分画一个小头。然后在电影后半部分画一个大头。", "Movie.helpText8": "在电影中使腿朝相反方向走一半路。", "Movie.helpText9": "在人物身后画一个渐渐变大的圆形。", "Movie.helpText10": "按你的想法来创作电影吧。现在您拥有了大量的新块可供探索。玩得愉快！", "Movie.helpText10Reddit": "点击\"浏览画廊\"按钮来浏览其他人的作品。如果您拍出了一些有趣的电影，可以点击\"提交至画廊\"按钮来发布它。", "Music.playNoteTooltip": "演奏一个特定持续时间和特定音高的音符。", "Music.playNote": "演奏%1 音高为%2", "Music.restTooltip": "等待一段特定的时长。", "Music.restWholeTooltip": "等待一个全音符。", "Music.rest": "休止 %1", "Music.setInstrumentTooltip": "当演奏后续音符时，切换到特定乐器。", "Music.setInstrument": "设置乐器为%1", "Music.startTooltip": "当点击“运行程序”按钮时，执行块内的代码。", "Music.start": "当点击%1时", "Music.pitchTooltip": "一个音高（C4用7表示）。", "Music.firstPart": "第一部分", "Music.piano": "钢琴", "Music.trumpet": "小号", "Music.banjo": "班卓琴", "Music.violin": "小提琴", "Music.guitar": "吉他", "Music.flute": "长笛", "Music.drum": "鼓", "Music.choir": "合唱", "Music.submitDisabled": "运行您的程序直到它停下来。之后您可以提交您的音乐作品到画廊里。", "Music.galleryTooltip": "打开音乐画廊。", "Music.galleryMsg": "浏览画廊", "Music.submitTooltip": "提交您的音乐作品到画廊里。", "Music.submitMsg": "提交至画廊", "Music.helpUseFunctions": "您的解决方案可以工作，但您可以做的更好。使用函数减少重复代码数量。", "Music.helpUseInstruments": "如果您在每个起始块使用不同的乐器，音乐演奏效果会更佳。", "Music.helpText1": "组合出乐曲“两只老虎”的前四个音符。", "Music.helpText2a": "一个“函数”允许您将块组合在一起，然后多次运行它们。", "Music.helpText2b": "创造一个函数来演奏乐曲“两只老虎”的前四个音符。运行这个函数两遍。不要增加新的音符块。", "Music.helpText3": "为乐曲“两只老虎”的下一部分创建第二个函数。最后一个音符会更长一点。", "Music.helpText4": "为乐曲“两只老虎”的下一部分创建第三个函数。前四个音符更短。", "Music.helpText5": "完成“两只老虎”的整个曲子。", "Music.helpText6a": "这个新块让您能够改成另一乐器。", "Music.helpText6b": "用小提琴来演奏您的曲子。", "Music.helpText7a": "这个新块添加了一个无声的延迟。", "Music.helpText7b": "创建第二个开始块，它带有两个延时块，然后同样演奏“两只老虎”。", "Music.helpText8": "让每个开始块演奏“两只老虎”两遍。", "Music.helpText9": "创建四个开始块，每个都会演奏“两只老虎”两遍。添加正确的延时块数量。", "Music.helpText10": "按您的兴趣来谱曲吧。现在您拥有了大量的新块可供探索。玩得愉快！", "Music.helpText10Reddit": "点击\"浏览画廊\"按钮来浏览其他人的作品。如果您创作出了一些有趣的音乐作品，可以点击\"提交至画廊\"按钮来发布它。", "Pond.scanTooltip": "搜寻敌人。指定一个方向（范围是0-360）。返回在该方向上离你最近敌人的距离。如果在该方向上没有发现敌人的话，就返回无限大。", "Pond.cannonTooltip": "发射大炮。指定开炮的方向（范围0-360）和距离（范围0-70）。", "Pond.swimTooltip": "向前游动，指定游动方向（范围是0-360）。", "Pond.stopTooltip": "停止游动。角色会缓慢地停下来。", "Pond.healthTooltip": "返回角色的当前的生命值（0是死了，100是满血）。", "Pond.speedTooltip": "返回角色的当前的速度（0表示正停着，100表示正全速前进）。", "Pond.locXTooltip": "返回角色的X坐标（0表示左侧边缘，100表示右侧边缘）。", "Pond.locYTooltip": "返回玩家的Y坐标（0表示在最底端，100表示在最顶端）。", "Pond.logTooltip": "向您浏览器的控制台输出一个数字", "Pond.docsTooltip": "显示语言文档。", "Pond.documentation": "文档", "Pond.playerName": "玩家", "Pond.targetName": "目标", "Pond.pendulumName": "来回摇摆", "Pond.scaredName": "胆小鬼", "Pond.helpUseScan": "您的解决方案能够工作，但您可以做的更好。使用“scan”来得知大炮应该射多远。", "Pond.helpText1": "使用“cannon”命令来攻击目标。第一个参数是开炮角度，第二个参数是开炮距离。找到正确的组合。", "Pond.helpText2": "这个目标需要射击很多轮。使用“while (true)”循环以便无限次射击。", "Pond.helpText3a": "这个对手会来回移动，使其很难被击中。“scan”表达式能返回在特定方向上目标的距离。", "Pond.helpText3b": "这个距离就是“cannon”命令需要的精确的发射距离。", "Pond.helpText4": "这个对手离得太远了，无法使用大炮（射程只有70米）。但是，可以使用“swim”命令开始向对手游过去，然后击溃它。", "Pond.helpText5": "这个对手也离得太远了，不能使用大炮。但是您现在血量太少，直接冲上去很难存活。所以，好的办法是向对手游过去，直到您的横坐标小于50。然后使用“stop”命令停止，并开炮射击。", "Pond.helpText6": "这个对手如果被击中的话就会逃跑。如果逃到超出射程（70米）的话，您需要继续游向它。", "Gallery": "画廊"}