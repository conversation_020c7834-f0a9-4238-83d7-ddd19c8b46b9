{"@metadata": {"authors": ["Mechanizatar", "Plaga med", "SimondR", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Г<PERSON><PERSON><PERSON><PERSON><PERSON>ly", "Games.puzzle": "Галаваломка", "Games.maze": "Лаб<PERSON><PERSON><PERSON>нт", "Games.bird": "Птушка", "Games.turtle": "Чарапашка", "Games.movie": "Фільм", "Games.music": "Музыка", "Games.pondTutor": "Сажалка Tutor", "Games.pond": "Сажалка", "Games.linesOfCode1": "Ты вырашыў задачу на гэтым узроўні адным радком на JavaScript:", "Games.linesOfCode2": "Ты вырашыў гэты ўзровень. Лік радкоў кода на JavaScript — %1:", "Games.nextLevel": "Ты гатовы да ўзроўня %1?", "Games.finalLevel": "Ты гатовы да наступнага выпрабавання?", "Games.submitTitle": "Загаловак:", "Games.linkTooltip": "Захаваць і паказаць спасылку на блокі.", "Games.runTooltip": "Запусці напісаную табой праграму.", "Games.runProgram": "Запусціць праграму", "Games.resetTooltip": "Перапыняе праграму і скідае ў зыходны стан.", "Games.resetProgram": "Скінуць", "Games.help": "Даведка", "Games.catLogic": "Логіка", "Games.catLoops": "Цыклы", "Games.catMath": "Матэматыка", "Games.catText": "Тэкст", "Games.catLists": "Спісы", "Games.catColour": "<PERSON>о<PERSON><PERSON><PERSON>", "Games.catVariables": "Пераменныя", "Games.catProcedures": "Функцыі", "Games.httpRequestError": "Адбылася праблема пры запыце.", "Games.linkAlert": "Падзяліцеся сваімі блоками па гэтай ссылке:\n\n%1", "Games.hashError": "На жаль, '%1' не адпавядае ніводнаму захаванаму файлу Блоклі.", "Games.xmlError": "Не атрымалася загрузіць Ваш захаваны файл. Магчыма, ён быў створаны ў іншай версіі Блоклі?", "Games.listVariable": "спіс", "Games.textVariable": "тэкст", "Games.breakLink": "Як толькі Вы пачнеце рэдагаваць JavaScript, Вы не зможаце вярнуцца да рэдагавання блокаў. Працягнуць?", "Games.blocks": "Блокі", "Games.congratulations": "Віншуем!", "Games.helpAbort": "Гэты ўзровень вельмі складаны. Можа, Вы хочаце прапусціць яго і перайсці да наступнай гульні? Ві заўсёды можаце вярнуцца сюды пазней.", "Index.clear": "Выдаліць усе вырашэння?", "Index.subTitle": "Гульні для будучых праграмістаў.", "Index.moreInfo": "Звесткі для настаўнікаў...", "Index.startOver": "Хочаце пачаць нанова?", "Index.clearData": "Ачысціць даныя", "Puzzle.animal1": "Качка", "Puzzle.animal1Trait1": "Пёры", "Puzzle.animal1Trait2": "Дзюба", "Puzzle.animal1HelpUrl": "https://be.wikipedia.org/wiki/Качка", "Puzzle.animal2": "Котка", "Puzzle.animal2Trait1": "Вусы", "Puzzle.animal2Trait2": "Футра", "Puzzle.animal2HelpUrl": "https://be.wikipedia.org/wiki/Котка", "Puzzle.animal3": "Пчала", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "Джала", "Puzzle.animal3HelpUrl": "https://be.wikipedia.org/wiki/Пчала", "Puzzle.animal4": "Слімак", "Puzzle.animal4Trait1": "Ракавінка", "Puzzle.animal4Trait2": "Слізь", "Puzzle.animal4HelpUrl": "https://be.wikipedia.org/wiki/Слімак", "Puzzle.picture": "карцінка:", "Puzzle.legs": "нагі:", "Puzzle.legsChoose": "абяры...", "Puzzle.traits": "асаблівасці:", "Puzzle.error0": "Ідэальна!\nЎсе блокі (%1) размешчаны правільна.", "Puzzle.error1": "Амаль! Адзін блок размешчаны няправільна.", "Puzzle.error2": "Некалькі блокаў (%1) размешчаныя няправільна.", "Puzzle.tryAgain": "Выдзелены блок размешчаны няправільна.\nПаспрабуй яшчэ.", "Puzzle.checkAnswers": "Праверыць вынік", "Puzzle.helpText": "Для кожнай жывёлы (зялёны блок), прымацуй яе выяву, абяры лік ног і збяры яе адметныя рысы.", "Maze.moveForward": "ступіць наперад", "Maze.turnLeft": "павярнуць налева", "Maze.turnRight": "павярнуць направа", "Maze.doCode": "выконваць", "Maze.helpIfElse": "Каманда \"калі-інакш\" выканае адно ці другое дзеянне.", "Maze.pathAhead": "калі шлях наперадзе", "Maze.pathLeft": "калі шлях злева", "Maze.pathRight": "калі шлях cправа", "Maze.repeatUntil": "паўтараць, пакуль не", "Maze.moveForwardTooltip": "Прасоўвае вандроўніка наперад на адзін крок.", "Maze.turnTooltip": "Павярнуць вандроўніка на 90 градусаў налева або направа.", "Maze.ifTooltip": "Калі шлях у згаданым кірунку адкрыты, то выканаць некаторыя дзеянні.", "Maze.ifelseTooltip": "Калі шлях у згаданым кірунку адкрыты, то выканаць першы блок дзеянняў. Інакш, выканаць другі блок дзеянняў.", "Maze.whileTooltip": "Паўтараць дзеянні, складзеныя ў блоку, да дасягнення канчатковай кропкі.", "Maze.capacity0": "У Вас засталося %0 блокаў.", "Maze.capacity1": "У Вас застаўся %1 блок.", "Maze.capacity2": "У Вас засталося %2 блока.", "Maze.runTooltip": "Вандроўнік зробіць усё, што скажуць яму блокі.", "Maze.resetTooltip": "Вярнуць вандроўніка ў пачатак лабірынта.", "Maze.helpStack": "Згрупуйце некалькі блокаў «ступіць наперад», каб дапамагчы мне дасягнуць мэты.", "Maze.helpOneTopBlock": "На дадзеным узроўні вам неабходна скласці разам усе блокі на белым працоўным полі.", "Maze.helpRun": "Запусціце праграму, каб паглядзець што адбываецца.", "Maze.helpReset": "Ваша праграма не вырашыла задачу. Націсніце кнопку «Скінуць» і паспрабуйце зноў.", "Maze.helpRepeat": "Прайдзіце да канца гэтага шляху, выкарыстоўваючы толькі два блокі. Для выканання блока больш за адзін раз выкарыстоўвайце «паўтараць».", "Maze.helpCapacity": "Вы выкарыстоўвалі ўсе блокі для гэтага ўзроўню. Каб дадаць новы блок, спачатку неабходна выдаліць існуючы.", "Maze.helpRepeatMany": "Вы можаце размясціць больш аднаго блока ўнутры блока «паўтараць».", "Maze.helpSkins": "Выберыце ў гэтым меню свайго любімага вандроўніка.", "Maze.helpIf": "Блок \"калі\" выканае нешта толькі ў выпадку вернай ўмовы. Паспрабуйце звярнуць налева, калі шлях налева даступны.", "Maze.helpMenu": "Націсніце на %1 у блоку 'калі' для змены яго ўмовы.", "Maze.helpWallFollow": "Ці можаш ты вырашыць гэты складаны лабірынт? Паспрабуй прытрымлівацца левай сцяны. Толькі для дасведчаных праграмістаў!", "Bird.noWorm": "чарвяк не злоўлены", "Bird.heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "Стан, калі птушка яшчэ не злавіла чарвяка.", "Bird.headingTooltip": "Рухацца ў кірунку, зададзеным кутом: 0 — направа, 90 — уверх і г.д.", "Bird.positionTooltip": "x і y адзначаюць становішча птушкі. Пры x = 0 птушка знаходзіцца ў левага краю, пры x = 100 яна ў правага краю. Пры y = 0 птушка знаходзіцца ў ніжняй частцы, пры y = 100 — на самым версе.", "Bird.helpHeading": "Змяні курс, каб птушка злавіла чарвяка і села ў сваё гняздо.", "Bird.helpHasWorm": "Выкарыстоўвай гэты блок для руху ў адным кірунку, калі чарвяк злоўлены, і ў іншым — калі не злоўлены.", "Bird.helpX": "'x' — гэта бягучае гарызантальнае становішча. Выкарыстоўвай гэты блок, каб рухацца ў адным кірунку, калі 'x' меншая за колькасць, або ў іншым кірунку, у адваротным выпадку.", "Bird.helpElse": "Націсніце на значок, каб змяніць блок «if».", "Bird.helpElseIf": "На гэтым узроўні патрабуюцца блокі 'інакш калі' і 'інакш'.", "Bird.helpAnd": "Блок 'і' мае значэння ісціна, калі абодва параметра ісцінныя.", "Bird.helpMutator": "Перацягні блок 'інакш' у блок 'калі'.", "Turtle.moveTooltip": "Перамясціць чарапашку наперад або назад на зададзеную адлегласць.", "Turtle.moveForward": "перамясціць наперад на", "Turtle.moveBackward": "перамясціць назад на", "Turtle.turnTooltip": "Павярнуць чарапашку налева або направа на зададзены кут у градусах.", "Turtle.turnRight": "павярнуць направа на", "Turtle.turnLeft": "павярнуць налева на", "Turtle.widthTooltip": "Змяніць шырыню пяра.", "Turtle.setWidth": "вызначыць шырыню", "Turtle.colourTooltip": "Мяняе колер пяра.", "Turtle.setColour": "вызначыць колер", "Turtle.penTooltip": "Падымае або апускае пяро, каб ставіць або пачаць маляванне.", "Turtle.penUp": "падняць пяро", "Turtle.penDown": "апусціць пяро", "Turtle.turtleVisibilityTooltip": "Зрабіць чарапашку (акружнасць і стрэлку) бачнай або нябачнай.", "Turtle.hideTurtle": "схаваць чарапашку", "Turtle.showTurtle": "паказаць чарапашку", "Turtle.printHelpUrl": "https://be.wikipedia.org/wiki/Кнігадрукаванне", "Turtle.printTooltip": "Намаляваць тэкст у становішчы і кірунку чарапашкі.", "Turtle.print": "надрукаваць", "Turtle.fontHelpUrl": "https://be.wikipedia.org/wiki/Шрыфт", "Turtle.fontTooltip": "Усталяваць шрыфт для блока друку.", "Turtle.font": "шрыфт", "Turtle.fontSize": "памер шрыфта", "Turtle.fontNormal": "звычайны", "Turtle.fontBold": "тлусты", "Turtle.fontItalic": "курсіў", "Turtle.submitDisabled": "Запусці праграму і чакай яе завяршэнне. Затым можна змясціць атрыманы малюнак у галерэю.", "Turtle.galleryTooltip": "Адкрывае галерэю малюнкаў на Reddit.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.submitTooltip": "Спампаваць малюнак на Reddit.", "Turtle.submitMsg": "Спампаваць у Галерэю", "Turtle.helpUseLoop": "Тваё рашэнне працуе, але можна зрабіць лепш.", "Turtle.helpUseLoop3": "Намалюй фігуру, выкарыстоўваючы ўсяго тры блока.", "Turtle.helpUseLoop4": "Намалюй зорачку, выкарыстоўваючы ўсяго чатыры блока.", "Turtle.helpText1": "Ствары праграму, якая намалюе квадрат.", "Turtle.helpText2": "Змяні праграму, каб замест квадрата намаляваць пяцiвугольнiк.", "Turtle.helpText3a": "Гэта новы блок, які дазваляе змяніць колер.", "Turtle.helpText3b": "Намалюй жоўтую зорку.", "Turtle.helpText4a": "Ёсць новы блок, які дазваляе ўзняць пяро ад паперы пры руху:", "Turtle.helpText4b": "Намалюй невялікую жоўтую зорачку, потым лінію над ёй.", "Turtle.helpText5": "Замест адной зоркі, можаш намаляваць чатыры зоркі ў кутах квадрата?", "Turtle.helpText6": "Намалюй тры жоўтыя зоркі і адну белую лінію.", "Turtle.helpText7": "Намалюй зоркі, потым чатыры белыя лініі.", "Turtle.helpText8": "Прамалёўка 360-і белых ліній будзе выглядаць як поўны месяц.", "Turtle.helpText9": "Можаш дадаць чорны круг, каб месяц ператварыўся ў паўмесяц?", "Turtle.helpText10": "Намалюй, што пажадаеш. У цябе з'явілася шмат новых блокаў, якія варта вывучыць. Прыемна правесці час!", "Turtle.helpText10Reddit": "Выкарыстай кнопку \"Глядзець Галерэю\", каб паглядзець малюнкі іншых. Калі ты намаляваў нешта цікавае — выкарыстай кнопку \"Захаваць ў Галерэі\", каб падзяліцца малюнкам з іншымі.", "Turtle.helpToolbox": "Абяры групу, каб убачыць блокі.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "пачатковы x", "Movie.y1": "пачатковы y", "Movie.x2": "канечны x", "Movie.y2": "канечны y", "Movie.radius": "радыус", "Movie.width": "даўжыня", "Movie.height": "шырыня", "Movie.circleTooltip": "Малюе круг у зададзеным месцы з зададзеным радыусам.", "Movie.circleDraw": "круг", "Movie.rectTooltip": "Малюе прамавугольнік ў зададзеным месцы з зададзенай даўжынёй і шырынёй.", "Movie.rectDraw": "прамавугольнік", "Movie.lineTooltip": "Малюе адрэзак зададзенай шырыні паміж адзначанымі кропкамі.", "Movie.lineDraw": "адрэзак", "Movie.timeTooltip": "Вяртае бягучы час у фільме (0-100).", "Movie.colourTooltip": "Мяняе колер пяра.", "Movie.setColour": "вызначыць колер", "Movie.submitDisabled": "Твой фільм нерухомы. Выкарыстоўвай блокі, каб стварыць нешта цікавае. Пасля гэтага ты можаш змясціць свой фільм у галерэю.", "Movie.galleryTooltip": "Адкрыць галерэю фільмаў на Reddit.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.submitTooltip": "Прадставіць свой фільм на Reddit.", "Movie.submitMsg": "Прадстав<PERSON>ць у Галерэю", "Movie.helpText1": "Выкарыстоўвай простыя фігуры, каб намаляваць гэтага чалавечка.", "Movie.helpText2a": "Гэты ўзровень уяўляе сабою кіно. Націсні кнопку Старт, каб паглядзець.", "Movie.helpText2b": "Падчас паказу фільма значэнне блока \"час\" змяняецца ад нуля да 100. Паколькі гарызантальная каардыната чырвонага шара таксама павінна пачынацца с 0 і дайсці да 100, гэта павінна быць проста.", "Movie.helpText3": "Блок \"час\" адлічвае ад 0 да 100. Але на гэты раз патрабуецца змяняць гарызантальнае становішча чырвонага шара са 100 да 0. Мож<PERSON><PERSON> знайсці простую матэматычную формулу, каб змяніць кірунак руху?", "Movie.helpText4": "Выкарыстоўвай тое, што ты даведаўся на папярэднім узроўні, каб запусціць чатыры зялёных шара ва ўсіх чатырох кірунках.", "Movie.helpText5": "Перасоўваць галаву мышы няцяжка. Можаш зрабіць разлікі, каб вушы таксама рухаліся?", "Movie.helpText6": "Два простых адрэзка. Паспрабуй зразумець, што адбываецца з канцамі адрэзкаў.", "Movie.helpText7": "Матэматычная формула для гэтага падальнага шара няпростая. Вось рашэнне:", "Movie.helpText8": "Выкарыстоўвай блок \"калі\", каб намаляваць чырвоны і белы шары для першай частцы фільма. Потым намалюй зялёны шар для другой частцы фільма.", "Movie.helpText9": "Можаш перасунуць шар па дроце? Дрот ужо намаляваны. Калі ты зможаш гэта зрабіць — ты зможаш усё.", "Movie.helpText10": "Зрабі фільм, які пажадаеш. У цябе з'явілася шмат новых блокаў, якія варта вывучыць. Прыемна правесці час!", "Movie.helpText10Reddit": "Выкарыстай кнопку \"Глядзець Галерэю\", каб паглядзець фільмы іншых. Калі ты стварыў нешта цікавае — выкарыстай кнопку \"Захаваць у Галерэі\", каб падзяліцца фільмам з іншымі.", "Music.playNoteTooltip": "Грае адну музычную ноту вызначанай працягласці і вышыні.", "Pond.scanTooltip": "Шукае ворагаў. <PERSON><PERSON><PERSON><PERSON><PERSON> кірунак (0-360). Вяртае адлегласць да бліжэйшага суперніка ў гэтым кірунку. Вяртае бясконцасць, калі супернік не знойдзены.", "Pond.cannonTooltip": "Страляць з гарматы. Паз<PERSON><PERSON> кірунак (0-360) і адлегласць (0-70).", "Pond.swimTooltip": "Плыць наперад. Пакажы кірунак (0-360).", "Pond.stopTooltip": "Спыніць плыць. Гулец будзе запавольвацца да прыпынку.", "Pond.healthTooltip": "Вяртае бягучае здароўе гульца (0 – труп, 100 – здаровы).", "Pond.speedTooltip": "Вяртае бягу<PERSON><PERSON>ю хуткасць гульца (0 – нерухомасць, 100 – максімальная хуткасць).", "Pond.locXTooltip": "Вяртае каардынату X гульца (0 – левы край, 100 – правы край).", "Pond.locYTooltip": "Вярт<PERSON><PERSON> каардынату Y гульца (0 – ніжні край, 100 – верхні край).", "Pond.docsTooltip": "Адкрывае дакументацыю па мове.", "Pond.documentation": "Дакументацыя", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Мэта", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Пужаны", "Pond.helpUseScan": "Тваё рашэнне працуе, але можна зрабіць лепш. Выкарыстоўвай 'scan', каб паказаць адлегласць для стральбы гарматы.", "Pond.helpText1": "Выкарыстоўвайце каманду 'cannon', каб ўразіць мэту. Першы параметр — кут, другі — адлегласць. Знайдзіце правільнае спалучэнне.", "Pond.helpText2": "Гэтая мэта павінна быць уражаная шмат разоў. Выкарыстоўвай цыкл 'while (true)', каб рабіць нешта бясконца.", "Pond.helpText3a": "Гэты супернік рухаецца ўзад-уперад, з-за чаго ў яго цяжка патрапіць. Выраз 'scan' вяртае дакладнае адлегласць да суперніка ў паказаным кірунку.", "Pond.helpText3b": "Адлегласць — менавіта тое, што трэба камандзе 'cannon' для дакладнай стральбы.", "Pond.helpText4": "Гэты супернік занадта далёка, каб выкарыстоўваць гармату (якая мае абмежаванне 70 метраў). Замест гэтага, выкарыстоўвай каманду 'swim', каб плыць у бок суперніка і ўрэзацца ў яго.", "Pond.helpText5": "Гэты супернік таксама занадта далёка, каб выкарыстоўваць гарматы. Але ты занадта слабы, каб выжыць у сутыкненні. Плыві ў бок суперніка, пакуль тваё гарызантальнае становішча менш 50. Затым \"stop\" і выкарыстоўвай гарматы.", "Pond.helpText6": "Гэты супернік будзе адыходзіць, калі ў яго патрапілі. Плыві ўперад, калі ён знаходзіцца па-за дыяпазону (70 метраў).", "Gallery": "Галер<PERSON>я"}