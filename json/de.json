{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Dan-yell", "<PERSON> Schäfchen", "Flow", "<PERSON><PERSON><PERSON>", "Metalhead64", "Sillyfreak", "<PERSON><PERSON>", "TomatoCake", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "Labyrinth", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "Schildkröte", "Games.movie": "Film", "Games.music": "Mu<PERSON>", "Games.pondTutor": "Teichübung", "Games.pond": "<PERSON><PERSON>", "Games.linesOfCode1": "Du hast dieses Level mit einer Zeile JavaScript gelöst:", "Games.linesOfCode2": "Du hast dieses Level mit %1 Zeilen JavaScript gelöst:", "Games.nextLevel": "Bist du bereit für Level %1?", "Games.finalLevel": "Bist du bereit für die nächste Herausforderung?", "Games.submitTitle": "Titel:", "Games.linkTooltip": "Speichern und Link erstellen.", "Games.runTooltip": "Das geschriebene Programm ausführen.", "Games.runProgram": "Programm ausführen", "Games.resetTooltip": "Stoppt das Programm und setzt das Level zurück.", "Games.resetProgram": "Ausgangsposition", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logik", "Games.catLoops": "Schleifen", "Games.catMath": "Mathematik", "Games.catText": "Text", "Games.catLists": "Listen", "Games.catColour": "Farbe", "Games.catVariables": "Variablen", "Games.catProcedures": "Funktionen", "Games.httpRequestError": "Es gab ein Problem mit der Anfrage.", "Games.linkAlert": "Teile deine Blöcke mit diesem Link:\n\n%1", "Games.hashError": "Le<PERSON> stimmt „%1“ mit keinem gespeicherten Programm überein.", "Games.xmlError": "Deine gespeicherte Datei konnte nicht geladen werden. Vielleicht wurde sie mit einer anderen <PERSON>ly-Version erstellt?", "Games.submitted": "Vielen Dank für dieses Programm! Falls unsere Belegschaft aus trainierten Affen es mag, werden sie es innerhalb weniger Tage in der Galerie veröffentlichen.", "Games.listVariable": "Liste", "Games.textVariable": "Text", "Games.breakLink": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> zu bearbeiten, kannst du nicht zurückgehen, um Blöcke zu bearbeiten. Ist das in Ordnung?", "Games.blocks": "<PERSON><PERSON><PERSON><PERSON>", "Games.congratulations": "Glückwunsch!", "Games.helpAbort": "Dieses Level ist sehr schwierig. Möchtest du es überspringen und zum nächsten Spiel gehen? Du kannst später immer noch zurückkehren.", "Index.clear": "Alle deine Lösungen löschen?", "Index.subTitle": "Spiele für die Programmierer von morgen.", "Index.moreInfo": "Informationen für Lehrpersonen", "Index.startOver": "Neu anfangen?", "Index.clearData": "Daten löschen", "Puzzle.animal1": "Ente", "Puzzle.animal1Trait1": "Federn", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://de.wikipedia.org/wiki/Entenvögel", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Fell", "Puzzle.animal2HelpUrl": "https://de.wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3": "Biene", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://de.wikipedia.org/wiki/Bienen", "Puzzle.animal4": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "Gehäuse", "Puzzle.animal4Trait2": "Schleim", "Puzzle.animal4HelpUrl": "https://de.wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>", "Puzzle.picture": "Bild:", "Puzzle.legs": "Beine:", "Puzzle.legsChoose": "auswählen …", "Puzzle.traits": "Eigenschaften:", "Puzzle.error0": "Perfekt! \nAlle %1 Bausteine sind richtig.", "Puzzle.error1": "Fast! Ein Baustein ist falsch.", "Puzzle.error2": "%1 Bausteine sind falsch.", "Puzzle.tryAgain": "Der hervorgehobene Baustein ist falsch. Versuche es noch einmal.", "Puzzle.checkAnswers": "Antworten überprüfen", "Puzzle.helpText": "Hänge für jedes Tier (grün) sein Bild an, wähle seine Anzahl der Beine aus und mache einen Stapel mit seinen Eigenschaften.", "Maze.moveForward": "gera<PERSON><PERSON> bewegen", "Maze.turnLeft": "links drehen", "Maze.turnRight": "rechts drehen", "Maze.doCode": "ausführen", "Maze.helpIfElse": "Wenn-Sonst-Bausteine führen das eine oder das andere aus.", "Maze.pathAhead": "wenn Pfad gera<PERSON>us ist", "Maze.pathLeft": "wenn Pfad nach links ist", "Maze.pathRight": "wenn Pfad nach rechts ist", "Maze.repeatUntil": "wiederholen bis", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> den Spieler ein Feld vor.", "Maze.turnTooltip": "Dreht den Spieler um 90 Grad nach links oder rechts.", "Maze.ifTooltip": "Falls es einen Pfad in der angegebenen Richtung gibt, dann einige Aktionen ausführen.", "Maze.ifelseTooltip": "Falls es einen Pfad in der angegebenen Richtung gibt, dann den ersten Abschnitt ausführen. Ansonsten den zweiten Abschnitt ausführen.", "Maze.whileTooltip": "Die beigefügten Aktionen wiederholen, bis das Ziel erreicht wurde.", "Maze.capacity0": "Du hast keine <PERSON> mehr.", "Maze.capacity1": "Du hast noch %1 Baustein.", "Maze.capacity2": "Du hast noch %2 Bausteine.", "Maze.runTooltip": "<PERSON> Spieler macht das, was die Bausteine sagen.", "Maze.resetTooltip": "Setzt den Spieler an den Start des Labyrinths zurück.", "Maze.helpStack": "Verbinde einige \"geradeaus bewegen\"-<PERSON><PERSON><PERSON><PERSON>, um das Ziel zu erreichen.", "Maze.helpOneTopBlock": "In diesem Level musst du die ganzen Bausteine in dem weißen Arbeitsbereich zusammenstapeln.", "Maze.helpRun": "<PERSON><PERSON><PERSON><PERSON> dein Programm aus, um zu sehen, was passiert.", "Maze.helpReset": "Dein Programm löst das Labyrinth nicht. Dr<PERSON><PERSON> „Ausgangsposition“ und versuche es erneut.", "Maze.helpRepeat": "<PERSON><PERSON><PERSON> das Ende dieses Pfads mit nur zwei Bausteinen. <PERSON><PERSON><PERSON> „Wiederholen“, um einen Baustein mehr als einmal auszuführen.", "Maze.helpCapacity": "Du hast für dieses Level alle Bausteine aufgebraucht. Um einen neuen Baustein zu erstellen, musst du zuerst einen vorhandenen Baustein löschen.", "Maze.helpRepeatMany": "Du kannst mehr als einen Baustein in einen \"Wiederholungs\"-Baustein platzieren.", "Maze.helpSkins": "Wähle deinen Lieblingsspieler vom Menü aus.", "Maze.helpIf": "Ein „Wenn“-<PERSON><PERSON><PERSON> macht etwas, falls die Bedingung wahr ist. Versuche links abzubiegen, falls es einen Pfad nach links gibt.", "Maze.helpMenu": "Klicke auf %1 im „Falls“-Baustein, um die Bedingung zu ändern.", "Maze.helpWallFollow": "Kannst du dieses komplizierte Labyrinth lösen? Folge der linken Wand. Nur für fortgeschrittene Programmierer!", "Bird.noWorm": "hat keinen <PERSON>", "Bird.heading": "Steuerkurs", "Bird.noWormTooltip": "Die Bedingung, wenn der Vogel den Wurm nicht bekommen hat.", "Bird.headingTooltip": "Bewege dich in die Richtung des angegebenen Winkels: 0 ist nach rechts, 90 ist geradeaus etc.", "Bird.positionTooltip": "x und y markieren die Position des Vogels. Wenn x = 0, ist der Vogel in der Nähe der linken Kante, wenn x = 100, ist er in der Nähe der rechten Kante. Wenn y = 0, ist der Vogel unten, wenn y = 100, ist er oben.", "Bird.helpHeading": "<PERSON><PERSON><PERSON> den Winkel des Steuerkurses, damit der Vogel den Wurm bekommt und in seinem Nest landet.", "Bird.helpHasWorm": "Verwende diesen Block, um in einen Steuerkurs zu gehen, falls du den Wurm hast, oder in einen anderen Steuerkurs, falls du den Wurm nicht hast.", "Bird.helpX": "„x“ ist deine aktuelle horizontale Position. Verwende diesen Block, um in einen Steuerkurs zu gehen, falls „x“ weniger ist als eine Zahl, oder anderenfalls in einen anderen Steuerkurs.", "Bird.helpElse": "<PERSON>licke auf das Symbol, um den „if“-Block zu ändern.", "Bird.helpElseIf": "Dieses Level benötigt sowohl einen „sonst falls“- als auch einen „sonst“-Block.", "Bird.helpAnd": "Der „und“-Block ist nur wahr, wenn beide Eingaben wahr sind.", "Bird.helpMutator": "Zieht einen „sonst“-Block in den „falls“-Block.", "Turtle.moveTooltip": "Bewegt die Schildkröte um den angegebenen Wert vorwärts oder rückwärts.", "Turtle.moveForward": "vorwärts bewegen um", "Turtle.moveBackward": "rückwärts bewegen um", "Turtle.turnTooltip": "Dreht die Schildkröte nach links oder rechts um die angegebene Gradanzahl.", "Turtle.turnRight": "nach rechts drehen um", "Turtle.turnLeft": "nach links drehen um", "Turtle.widthTooltip": "Ändert die Breite des Stiftes.", "Turtle.setWidth": "Breite festlegen auf", "Turtle.colourTooltip": "Ändert die Farbe des Stiftes.", "Turtle.setColour": "<PERSON><PERSON><PERSON> die Farbe in", "Turtle.penTooltip": "Hebt oder senkt den Stift zum Stoppen oder Starten der Zeichnung.", "Turtle.penUp": "Stift nach oben", "Turtle.penDown": "Stift nach unten", "Turtle.turtleVisibilityTooltip": "Macht die Schildkröte (Kreis und Pfeil) sichtbar oder unsichtbar.", "Turtle.hideTurtle": "Schildkröte ausblenden", "Turtle.showTurtle": "Schildkröte anzeigen", "Turtle.printHelpUrl": "https://de.wikipedia.org/wiki/Buchdruck", "Turtle.printTooltip": "Zeichnet Text in der Richtung der Schildkröte bei ihrem Standort.", "Turtle.print": "drucken", "Turtle.fontHelpUrl": "https://de.wikipedia.org/wiki/Schriftschnitt", "Turtle.fontTooltip": "Legt die Schriftart fest, die vom Druck-Baustein verwendet wird.", "Turtle.font": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontSize": "Schriftgröße", "Turtle.fontNormal": "normal", "Turtle.fontBold": "fett", "Turtle.fontItalic": "kursiv", "Turtle.submitDisabled": "Führt dein Programm aus, bis es stoppt. Dann kannst du deine Zeichnung zur Galerie abschicken.", "Turtle.galleryTooltip": "Öffnet die Galerie der Zeichnungen.", "Turtle.galleryMsg": "Galerie ansehen", "Turtle.submitTooltip": "<PERSON><PERSON> in der Galerie speichern.", "Turtle.submitMsg": "In der Galerie speichern", "Turtle.helpUseLoop": "<PERSON><PERSON>, aber du kannst es besser.", "Turtle.helpUseLoop3": "<PERSON>ei<PERSON>ne die Form mit nur drei Blöcken.", "Turtle.helpUseLoop4": "Zeichne den Stern mit nur vier Blöcken.", "Turtle.helpText1": "<PERSON><PERSON><PERSON> ein Programm, das ein Quadrat zeichnet.", "Turtle.helpText2": "<PERSON>nder<PERSON> dein Programm, um ein Fünfeck anstatt eines Quadrats zu zeichnen.", "Turtle.helpText3a": "<PERSON>s gibt einen neuen Block, der es dir ermöglicht, die Farbe zu ändern:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON><PERSON> einen gelben Stern.", "Turtle.helpText4a": "Es gibt einen neuen Block, der es dir ermöglicht, deinen Stift von dem Pa<PERSON>r zu heben, wenn du dich bewegst:", "Turtle.helpText4b": "<PERSON><PERSON>chne einen kleinen gelben <PERSON>, dann zeichne darüber eine Linie.", "Turtle.helpText5": "Kannst du anstatt eines Sterns vier Sterne, angeordnet in einem Quadrat, zeichnen?", "Turtle.helpText6": "Zeichne drei gelbe Sterne und eine weiße Linie.", "Turtle.helpText7": "<PERSON><PERSON><PERSON><PERSON> die Sterne, dann zeichne vier weiße Linien.", "Turtle.helpText8": "Zeichne 360 weiße Linien, die wie der Vollmond aussehen.", "Turtle.helpText9": "Kannst du einen schwarzen Kreis hinzufügen, sodass der Mond eine Sichel bekommt?", "Turtle.helpText10": "<PERSON><PERSON><PERSON><PERSON>, was du willst. Du hast eine hohe Anzahl neuer Blöcke, die du entdecken kannst. <PERSON><PERSON>!", "Turtle.helpText10Reddit": "<PERSON>erwen<PERSON> <PERSON> Button „Galerie ansehen“, um zu sehen, was andere Menschen gezeichnet haben. Falls du etwas Interessantes gezeichnet hast, verwende den Button „In der Galerie speichern“, um sie zu veröffentlichen.", "Turtle.helpToolbox": "<PERSON><PERSON>hle eine Kategorie aus, um die Blöcke anzusehen.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "Start-x", "Movie.y1": "Start-y", "Movie.x2": "End-x", "Movie.y2": "End-y", "Movie.radius": "<PERSON><PERSON>", "Movie.width": "Breite", "Movie.height": "<PERSON><PERSON><PERSON>", "Movie.circleTooltip": "Zeichnet einen Kreis am angegebenen Ort und mit dem angegebenen Radius.", "Movie.circleDraw": "Kreis", "Movie.rectTooltip": "Zeichnet ein Rechteck am angegebenen Ort und mit der angegebenen Breite und Höhe.", "Movie.rectDraw": "<PERSON><PERSON><PERSON>", "Movie.lineTooltip": "<PERSON><PERSON><PERSON>net eine Linie von einem Punkt zum anderen mit der angegebenen Breite.", "Movie.lineDraw": "<PERSON><PERSON>", "Movie.timeTooltip": "Gibt die aktuelle Zeit in der Animation zurück (0–100).", "Movie.colourTooltip": "Ändert die Farbe des Stiftes.", "Movie.setColour": "Farbe festlegen auf", "Movie.submitDisabled": "Dein Film bewegt sich nicht. Verwende Blöcke, um etwas interessant zu machen. Dann kannst du dein Film zur Galerie abschicken.", "Movie.galleryTooltip": "Öffne die Galerie der Filme.", "Movie.galleryMsg": "Galerie ansehen", "Movie.submitTooltip": "Reiche deinen Film in die Galerie ein.", "Movie.submitMsg": "In der Galerie speichern", "Movie.helpLayer": "Verschiebe den Hintergrundkreis an den Anfang deines Programms. Dann wird er hinter der Person erscheinen.", "Movie.helpText1": "<PERSON>utze einfache Formen, um diese Person zu zeichnen.", "Movie.helpText2a": "Dieses Level ist ein Film. Du möchtest den Arm der Person durch den Bildschirm verschieben. Drücke den Wiedergabe-Knopf, um eine Vorschau anzu<PERSON>hen.", "Movie.helpText2b": "Während der Film abgespielt wird, zählt der Wert des „time“-Blocks von 0 bis 100. Wenn der Arm bei der Y-Position 0 starten und bis 100 gehen soll, sollte dies einfach sein.", "Movie.helpText3": "Der „time“-<PERSON> zählt von 0 bis 100. <PERSON>r jetzt möchtest du, dass die Y-Position des anderen Arms bei 100 beginnen und nach 0 gehen soll. Kannst du eine einfache mathematische Formel herausfinden, die die Richtung ändert?", "Movie.helpText4": "<PERSON><PERSON><PERSON>, was du im vorherigen Level gelernt hast, um kreuzende Beine zu erstellen.", "Movie.helpText5": "Die mathematische Formel für den Arm ist kompliziert. Hier ist die Antwort:", "Movie.helpText6": "<PERSON><PERSON> der Person ein <PERSON>.", "Movie.helpText7": "Verwende den „if“-Block, um einen kleinen Kopf für die erste Hälfte des Films zu zeichnen. Z<PERSON>chne dann einen großen Kopf für die zweite Hälfte des Films.", "Movie.helpText8": "Lege die Beine in die entgegengesetzte Richtung auf dem halben Weg durch den Film.", "Movie.helpText9": "Zeichne einen expandierenden Kreis hinter der Person.", "Movie.helpText10": "<PERSON><PERSON><PERSON> einen Film über etwas, was du willst. Du hast eine hohe Anzahl neuer Blöcke, die du entdecken kannst. <PERSON><PERSON>!", "Movie.helpText10Reddit": "Verwende den Button \"Galerie ansehen\", um Filme von anderen Menschen anzusehen. Falls du einen interessanten Film erstellt hast, verwende den Button \"Zur Galerie abschicken\", um ihn zu veröffentlichen.", "Music.playNoteTooltip": "Spielt eine Musiknote für die angegebene Dauer und Höhe.", "Music.playNote": "Note %2 %1 lang abspielen", "Music.restTooltip": "Wartet die angegebene Dauer ab.", "Music.restWholeTooltip": "Wartet auf eine ganze Note.", "Music.rest": "ruhen für %1", "Music.setInstrumentTooltip": "Wechselt auf das angegebene Instrument, wenn anschließend Musiknoten abgespielt werden.", "Music.setInstrument": "Instrument auf %1 festlegen", "Music.startTooltip": "Führt die Blöcke innerhalb aus, wenn die Schaltfläche „Programm ausführen“ angeklickt wird.", "Music.start": "wenn %1 angeklickt wird", "Music.pitchTooltip": "Eine Note (C4 ist 7).", "Music.firstPart": "<PERSON>ster <PERSON>", "Music.piano": "<PERSON><PERSON><PERSON>", "Music.trumpet": "Trompet<PERSON>", "Music.banjo": "<PERSON><PERSON>", "Music.violin": "Geige", "Music.guitar": "<PERSON><PERSON><PERSON>", "Music.flute": "Flöte", "Music.drum": "<PERSON><PERSON><PERSON>", "Music.choir": "Chor", "Music.submitDisabled": "Führe dein Programm aus, bis es aufhört. Dann kannst du deine Musik in der Galerie speichern.", "Music.galleryTooltip": "Öffnet die Galerie der Musik.", "Music.galleryMsg": "Galerie ansehen", "Music.submitTooltip": "Speichert deine Musik in der Galerie.", "Music.submitMsg": "In der Galerie speichern", "Music.helpUseFunctions": "<PERSON><PERSON> funk<PERSON>, aber du kannst es besser machen. Benutze Funktionen, um die Menge des wiederholten Codes zu reduzieren.", "Music.helpUseInstruments": "Die Musik wird besser klingen, wenn du in jedem Startblock ein anderes Instrument verwendest.", "Music.helpText1": "Komponiere die ersten vier Noten von „Frère Jacques“.", "Music.helpText2a": "<PERSON>e „Funktion“ erlaubt dir die Gruppierung von Blöcken. Führe sie dann mehr als einmal aus.", "Music.helpText2b": "<PERSON><PERSON><PERSON> eine Funktion, um die ersten vier Noten von „Frère Jacques“ abzuspielen. Führe diese Funktion zweimal aus. Füge keine neuen Notenblöcke hinzu.", "Music.helpText3": "<PERSON><PERSON><PERSON> eine zweite Funktion für den nächsten Teil von „Frère Jacques“. Die letzte Note ist länger.", "Music.helpText4": "<PERSON><PERSON><PERSON> eine dritte Funktion für den nächsten Teil von „Frère Jacques“. Die ersten vier Noten sind kürzer.", "Music.helpText5": "Vervollständige die Melodie von „Frère Jacques“.", "Music.helpText6a": "Dieser neue Block lässt dich ein anderes Instrument auswählen.", "Music.helpText6b": "Spiele deine Melodie mit einer Geige.", "Music.helpText7a": "Dieser neue Block fügt eine stille Verzögerung hinzu.", "Music.helpText7b": "<PERSON><PERSON><PERSON> einen zweiten Startblock, der zwei Verzögerungsblöcke hat, spiele dann auch „<PERSON><PERSON> Jacques“.", "Music.helpText8": "<PERSON><PERSON> Startblock sollte „Fr<PERSON>“ zweimal abspielen.", "Music.helpText9": "<PERSON><PERSON><PERSON> v<PERSON> Startblöcke, die jeweils zweimal „Frère Jacques“ abspielen. Ergänze die richtige Anzahl an Verzögerungsblöcken.", "Music.helpText10": "<PERSON><PERSON><PERSON><PERSON>, was du willst. Du hast eine hohe Anzahl an neuen Blöcken bekommen, die du entdecken kannst. <PERSON><PERSON>!", "Music.helpText10Reddit": "Verwende die Schaltfläche \"Galerie ansehen\", um zu sehen, was andere Menschen komponiert haben. Falls du etwas Interessantes komponiert hast, verwende die Schaltfläche \"In Galerie speichern\", um es zu veröffentlichen.", "Pond.scanTooltip": "Sucht nach Feinden. Gib eine Richtung an (0–360). Gibt die Entfernung zum nächsten Feind in dieser Richtung zurück. Gibt „Unendlichkeit“ zurück, falls kein Feind gefunden wurde.", "Pond.cannonTooltip": "<PERSON><PERSON>t die Kanone ab. Gib eine Richtung (0–360) und eine Reichweite (0–70) an.", "Pond.swimTooltip": "Schwimme vorwärts. Gib eine Richtung an (0–360).", "Pond.stopTooltip": "<PERSON><PERSON>rt auf zu schwimmen. Der Spieler wird sich bis zum Stopp verlangsamen.", "Pond.healthTooltip": "Gibt die aktuelle Gesundheit des Spielers zurück (0 ist tot, 100 ist gesund).", "Pond.speedTooltip": "Gibt die aktuelle Geschwindigkeit des Spielers zurück (0 ist angehalten, 100 ist volle Geschwindigkeit).", "Pond.locXTooltip": "Gibt die X-Koordinate des Spielers zurück (0 ist die linke, 100 die rechte Kante).", "Pond.locYTooltip": "Gibt die Y-Koordinate des Spielers zurück (0 ist die untere, 100 die obere Kante).", "Pond.logTooltip": "Gibt eine Nummer auf deiner Browser-Konsole aus.", "Pond.docsTooltip": "Zeigt die Sprachdokumentation an.", "Pond.documentation": "Dokumentation", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON>", "Pond.pendulumName": "Pendel", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "<PERSON><PERSON>, aber du kannst es besser. Verwende „scan“, um der Kanone zu sagen, wie weit sie schießen soll.", "Pond.helpText1": "Verwende den Befehl „cannon“, um das Ziel zu treffen. Der erste Parameter ist der Winkel, der zweite die Reichweite. Finde die richtige Kombination heraus.", "Pond.helpText2": "Dieses Z<PERSON> braucht mehrere Treffer. Verwende eine „while (true)“-<PERSON><PERSON><PERSON><PERSON>, um etwas zeitlich unbegrenzt zu machen.", "Pond.helpText3a": "Dieser Gegner bewegt sich zurück und vor, was es schwer macht, ihn zu treffen. Der Ausdruck „scan“ gibt den exakten Bereich des Gegners in der angegebenen Richtung zurück.", "Pond.helpText3b": "<PERSON><PERSON> Bereich ist genau das, was der <PERSON><PERSON>hl „cannon“ braucht, um exakt abzufeuern.", "Pond.helpText4": "Die<PERSON> Gegner ist zu weit weg, um die Kanone zu verwenden (sie hat ein Limit von 70 Metern). Verwende stattdessen den Befehl „swim“, um mit dem Hinüberschwimmen zu beginnen und mit dem Gegner zusammenzustoßen.", "Pond.helpText5": "Die<PERSON> Gegner ist ebenfalls zu weit weg, um die Kanone zu verwenden. Aber du bist zu schwach, um einen Zusammenstoß zu überleben. Schwimme hinüber zum G<PERSON>ner, derweil ist dein horizontaler Standort weniger als 50. Dann „stop“ und verwende die Kanone.", "Pond.helpText6": "Dieser Gegner zieht fort, wenn er geschlagen wird. Schwimm hinüber, falls er außerhalb des Bereichs ist (70 Meter).", "Gallery": "Galerie"}