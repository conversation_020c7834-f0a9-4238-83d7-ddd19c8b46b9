{"@metadata": {"authors": ["Christian List", "Hein0170", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "RickiRung<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SimmeD", "Tjernob<PERSON>"]}, "Games.name": "Blockly-spil", "Games.puzzle": "P<PERSON>lespil", "Games.maze": "Labyrint", "Games.bird": "Fugl", "Games.turtle": "Skildpadde", "Games.movie": "Film", "Games.music": "Mu<PERSON>", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON>", "Games.pond": "Dam", "Games.linesOfCode1": "Du har løst dette niveau med 1 linje JavaScript:", "Games.linesOfCode2": "Du har løst dette niveau med %1 linjer JavaScript:", "Games.nextLevel": "Er du klar til niveau %1?", "Games.finalLevel": "Er du klar til den næste u<PERSON>ring?", "Games.submitTitle": "Titel:", "Games.linkTooltip": "Gem og link til blokkene.", "Games.runTooltip": "<PERSON><PERSON><PERSON> det program, du skrev.", "Games.runProgram": "Kør program", "Games.resetTooltip": "Stop programmet og nulstil niveauet.", "Games.resetProgram": "Nulstil", "Games.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Logik", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematik", "Games.catText": "Tekst", "Games.catLists": "Lister", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Variabler", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "Der var et problem med forespørgslen.", "Games.linkAlert": "Del dine blokke med dette link:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON>, '%1' passer ikke med nogen gemte programmer.", "Games.xmlError": "Kunne ikke hente din gemte fil.  Måske er den lavet med en anden version af Blockly?", "Games.submitted": "Tak for dette program! Hvis vores personale af dresserede aber kan lide det, så vil de udgive det i galleriet indenfor et par dage.", "Games.listVariable": "liste", "Games.textVariable": "tekst", "Games.breakLink": "<PERSON><PERSON><PERSON> du begynder at redigere JavaScript kan du ikke gå tilbage til redigeringblokke. Er det o.k.?", "Games.blocks": "Blokke", "Games.congratulations": "Tillykke!", "Games.helpAbort": "Dette niveau er yderst van<PERSON>ligt. Vil du springe det over og gå videre til det næste spil? Du kan altid komme tilbage senere.", "Index.clear": "Slette alle dine løsninger?", "Index.subTitle": "Spil for fremtidens programmører.", "Index.moreInfo": "Info til undervisere...", "Index.startOver": "Vil du starte forfra?", "Index.clearData": "Ryd data", "Puzzle.animal1": "And", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://da.wikipedia.org/wiki/And", "Puzzle.animal2": "<PERSON>", "Puzzle.animal2Trait1": "Knurhår", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://da.wikipedia.org/wiki/Kat", "Puzzle.animal3": "Bi", "Puzzle.animal3Trait1": "Honning", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://da.wikipedia.org/wiki/Bi", "Puzzle.animal4": "Snegl", "Puzzle.animal4Trait1": "Skal", "Puzzle.animal4Trait2": "<PERSON>", "Puzzle.animal4HelpUrl": "https://da.wikipedia.org/wiki/Snegl", "Puzzle.picture": "billede:", "Puzzle.legs": "ben:", "Puzzle.legsChoose": "vælg...", "Puzzle.traits": "træk:", "Puzzle.error0": "Perfekt!\nAlle %1 blokke er rigtige.", "Puzzle.error1": "Næsten! Én blok er forkert.", "Puzzle.error2": "%1 blokke er forkerte.", "Puzzle.tryAgain": "Den fremhævede blok er ikke rigtig.\nBliv ved med at prøve.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.helpText": "For hvert dyr (grøn), til<PERSON><PERSON><PERSON> dets billede, vælg dets antal ben, og lav en stak af dens træk.", "Maze.moveForward": "flyt fremad", "Maze.turnLeft": "drej til venstre", "Maze.turnRight": "drej til højre", "Maze.doCode": "<PERSON>d<PERSON><PERSON><PERSON>", "Maze.helpIfElse": "Hvis-ellers blokke vil gøre den ene eller den anden ting.", "Maze.pathAhead": "hvis sti foran", "Maze.pathLeft": "hvis sti til venstre", "Maze.pathRight": "hvis sti til højre", "Maze.repeatUntil": "gentag indtil", "Maze.moveForwardTooltip": "Flytter spilleren et felt frem.", "Maze.turnTooltip": "<PERSON><PERSON><PERSON> 90 grader til højre eller venstre.", "Maze.ifTooltip": "<PERSON><PERSON> der er en sti i den angivne retning, så udfør nogle handlinger.", "Maze.ifelseTooltip": "<PERSON>vis der er en sti i den angivne retning, så udfør den første handlingsblok. <PERSON><PERSON> ikke, så udfør den anden handlingsblok.", "Maze.whileTooltip": "Gentag gruppen af handlinger indtil slutpunktet er nået.", "Maze.capacity0": "Du har %0 blokke tilbage.", "Maze.capacity1": "Du har %1 blok tilbage.", "Maze.capacity2": "Du har %2 blokke tilbage.", "Maze.runTooltip": "<PERSON><PERSON><PERSON> spilleren til at gøre det blokkene siger.", "Maze.resetTooltip": "<PERSON><PERSON>tter spilleren tilbage til starten af labyrinten.", "Maze.helpStack": "<PERSON>æt et par 'flyt fremad' blokke sammen for at hjælpe mig til at nå målet,", "Maze.helpOneTopBlock": "<PERSON><PERSON> denne bane skal du sætte alle blokkene sammen i det hvide arbejdsfelt.", "Maze.helpRun": "<PERSON><PERSON><PERSON> dit program for at se, hvad der sker.", "Maze.helpReset": "Dit program løste ikke labyrinten. Tryk på 'Nulstil', og prøv igen.", "Maze.helpRepeat": "<PERSON><PERSON><PERSON><PERSON> at nå enden af denne sti ved kun at bruge to blokke. Brug 'gentag indtil' til at køre en blok mere end én gang.", "Maze.helpCapacity": "Du har opbrugt alle blokkene for dette niveau. Hvis du vil oprette en ny blok, skal du først slette en eksisterende blok.", "Maze.helpRepeatMany": "Der er plads til mere end én blok inde i en 'gentag indtil' blok.", "Maze.helpSkins": "Væ<PERSON>g din favoritspiller fra denne menu.", "Maze.helpIf": "En 'hvis'-blok vil kun gøre noget hvis betingelsen er sand. Prøv at dreje til venstre hvis der er en sti til venstre.", "Maze.helpMenu": "Klik på %1 i 'hvis' blokken for at ændre dens tilstand.", "Maze.helpWallFollow": "Kan du løse denne komplicerede labyrint? Prøv at følge væggen på venstre hånd. Kun for avancerede programmører!", "Bird.noWorm": "har ikke ormen", "Bird.heading": "retning", "Bird.noWormTooltip": "<PERSON>, når fuglen ikke har fået ormen.", "Bird.headingTooltip": "Bevæg i retning af den givne vinkel: 0 er til højre, 90 er lige op, osv.", "Bird.positionTooltip": "x og y markerer fuglens position. Når x = 0 er fuglen i nærheden af den venstre kant, når x = 100, er den tæt på den højre kant. Når y = 0 er fuglen på bunden, når y = 100, er den på toppen.", "Bird.helpHeading": "Skift vinklens retning så fuglen kan få ormen og lande i sin rede.", "Bird.helpHasWorm": "Brug denne blok for at gå i en retning hvis du har ormen, eller en anden retning, hvis du ikke har ormen.", "Bird.helpX": "'x' er din nuværende vandrette position. Brug denne blok til at gå i en retning, hvis 'x' er mindre end et tal, ellers en anden retning.", "Bird.helpElse": "Klik på ikonet for at ændre 'hvis'-blokken.", "Bird.helpElseIf": "<PERSON><PERSON> niveau har behov for både en 'ellers hvis'-blok og en 'ellers'-blok.", "Bird.helpAnd": "'og'-blokken er kun sand, hvis begge dens parametre er sande.", "Bird.helpMutator": "Træk en 'ellers'-blok ind i 'hvis'-blokken.", "Turtle.moveTooltip": "<PERSON><PERSON><PERSON><PERSON> skildpadden frem eller tilbage med den angivne værdi.", "Turtle.moveForward": "flyt fremad med", "Turtle.moveBackward": "flyt bagud med", "Turtle.turnTooltip": "<PERSON><PERSON><PERSON> skildpadden til venstre eller højre med det angivne antal grader.", "Turtle.turnRight": "drej til højre med", "Turtle.turnLeft": "drej til venstre med", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON> bredden på pennen.", "Turtle.setWidth": "sæt bredden til", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON> farven på pennen.", "Turtle.setColour": "sæt farve til", "Turtle.penTooltip": "<PERSON><PERSON><PERSON> eller sænker pennen, for at stoppe eller starte tegning.", "Turtle.penUp": "pennen op", "Turtle.penDown": "pennen ned", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON> skild<PERSON> (cirkel og pil) synlig eller usynlig.", "Turtle.hideTurtle": "skjul skildpadde", "Turtle.showTurtle": "vis skildpadde", "Turtle.printHelpUrl": "https://da.wikipedia.org/wiki/Bogtryk", "Turtle.printTooltip": "Skriver tekst i skildpaddens retning på dens placering.", "Turtle.print": "skriv", "Turtle.fontHelpUrl": "https://da.wikipedia.org/wiki/Skrifttype", "Turtle.fontTooltip": "Angiver den skrifttype, der bruges af skriveblokken.", "Turtle.font": "skrifttype", "Turtle.fontSize": "skriftstørrelse", "Turtle.fontNormal": "normal", "Turtle.fontBold": "fed", "Turtle.fontItalic": "kursiv", "Turtle.submitDisabled": "<PERSON><PERSON><PERSON> dit program, indtil det stopper. Derefter kan du føje din tegning til galleriet.", "Turtle.galleryTooltip": "Åbn galleriet med tegninger.", "Turtle.galleryMsg": "Se galleri", "Turtle.submitTooltip": "Del din tegning i galleriet.", "Turtle.submitMsg": "<PERSON><PERSON><PERSON> til galleri", "Turtle.helpUseLoop": "<PERSON> virk<PERSON>, men du kan gøre det bedre.", "Turtle.helpUseLoop3": "Tegn figuren med kun tre blokke.", "Turtle.helpUseLoop4": "Tegn stjernen med kun fire blokke.", "Turtle.helpText1": "Lav et program, der tegner en firkant.", "Turtle.helpText2": "<PERSON><PERSON><PERSON> dit program til at tegne en femkant i stedet for en firkant.", "Turtle.helpText3a": "Der er en ny blok, der giver dig mulighed for at ændre farve:", "Turtle.helpText3b": "Tegn en gul stjerne.", "Turtle.helpText4a": "Der er en ny blok, der giver dig mulighed for at løfte pennen fra papiret, n<PERSON>r du flytter:", "Turtle.helpText4b": "Tegn en lille gul stjerne, og tegn derefter en linje over den.", "Turtle.helpText5": "Kan du i stedet for en stjerne, tegne fire stjerner, i en firkant?", "Turtle.helpText6": "Tegn tre gule stjerner, og en hvid linje.", "Turtle.helpText7": "Tegn stjernerne, og tegn derefter fire hvide linjer.", "Turtle.helpText8": "<PERSON><PERSON> man tegner 360 hvide linjer, vil det ligne en fuldmåne.", "Turtle.helpText9": "Kan du tilføje en sort cirkel, så månen bliver en halvmåne?", "Turtle.helpText10": "Tegn lige hvad du vil. Du har et stort antal nye blokke, du kan udforske. Hav det sjovt!", "Turtle.helpText10Reddit": "Brug \"Se galleri\" knappen for at se, hvad andre mennesker har tegnet. <PERSON>vis du tegner noget interessant, så brug \"<PERSON>øj til galleri\" knappen for at udgive det.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON> en kategori for at se blokke.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "start-x", "Movie.y1": "start-y", "Movie.x2": "slut-x", "Movie.y2": "slut-y", "Movie.radius": "radius", "Movie.width": "bredde", "Movie.height": "<PERSON>ø<PERSON><PERSON>", "Movie.circleTooltip": "Tegner en cirkel på den angivne placering, og med den angivne radius.", "Movie.circleDraw": "cirkel", "Movie.rectTooltip": "<PERSON><PERSON>r en rektangel på den angivne placering, og med den angivne bredde og højde.", "Movie.rectDraw": "rektang<PERSON>", "Movie.lineTooltip": "Tegner en linje fra et punkt til et andet med den angivne bredde.", "Movie.lineDraw": "linje", "Movie.timeTooltip": "Returnerer den aktuelle tid i animationen (0-100).", "Movie.colourTooltip": "<PERSON>ft farven på pennen.", "Movie.setColour": "sæt farven til", "Movie.submitDisabled": "Din film bevæger sig ikke. Brug blokke til at lave noget interessant. Derefter kan du indsende din film til galleriet.", "Movie.galleryTooltip": "Åbn filmgalleriet.", "Movie.galleryMsg": "Se galleri", "Movie.submitTooltip": "Del din film med galleriet.", "Movie.submitMsg": "<PERSON><PERSON><PERSON> til galleri", "Movie.helpLayer": "Flyt baggrundscirklen øverst i dit program. Så vil den fremstå bag personen.", "Movie.helpText1": "<PERSON><PERSON> enkle former til at tegne denne person.", "Movie.helpText2a": "Dette niveau er en film. Du skal få personens arm til at bevæge sig på tværs af skærmen. Tryk på afspil for at se en forhåndsvisning.", "Movie.helpText2b": "<PERSON>s filmen spiller, vil værdien af »time«-blokken tælle fra 0 til 100. Da du vil have den vandrette position af armen til at starte ved 0 og gå til 100 bør dette være let.", "Movie.helpText3": "»time«-blokken tæller fra 0 til 100. Men nu ønsker du at »y«-positionen for den anden arm starter ved 100 og tæller til 0. Kan du finde ud af en simpel matematisk formel, som vender retningen?", "Movie.helpText4": "Brug hvad du har lært i det forrige niveau for at få krydsende ben.", "Movie.helpText5": "Den matematiske formel for armen er kompliceret. Her er svaret:", "Movie.helpText6": "<PERSON><PERSON> personen to hænder.", "Movie.helpText7": "Brug blokken »if« til at tegne et lille hoved for den første halvdel af filmen. Tegn så et stort hoved for den anden halvdel af filmen.", "Movie.helpText8": "Vend retningen for benene om halvvejs igennem filmen.", "Movie.helpText9": "Tegn en udadgående cirkel bag personen.", "Movie.helpText10": "Lav en film om lige hvad du vil. Du har et stort antal nye blokke, du kan udforske. Hav det sjovt!", "Movie.helpText10Reddit": "Brug \"Se galleri\" knappen for at se film andre mennesker har lavet. Hvis du laver en interessant film, så brug \"<PERSON>øj til galleri\" knappen for at udgive den.", "Music.playNoteTooltip": "Afspiller en musikalsk node med den angivne varighed og tonehøjde.", "Music.playNote": "afspil %1 node %2", "Music.restTooltip": "Venter i den angivne varighed.", "Music.restWholeTooltip": "Venter på en helnode.", "Music.rest": "pause %1", "Music.setInstrumentTooltip": "Skifter til det angivne instrument når der spilles efterfølgende musikalske noder.", "Music.setInstrument": "sæt instrument til %1", "Music.startTooltip": "<PERSON><PERSON><PERSON> blokkene inden i når knappen »Kør program« aktiveres.", "Music.start": "når %1 aktiveres", "Music.pitchTooltip": "En node (C4 er 7).", "Music.firstPart": "f<PERSON><PERSON><PERSON>", "Music.piano": "piano", "Music.trumpet": "trompet", "Music.banjo": "banjo", "Music.violin": "violin", "Music.guitar": "guitar", "Music.flute": "<PERSON><PERSON><PERSON><PERSON>", "Music.drum": "tromme", "Music.choir": "kor", "Music.submitDisabled": "Afvikler dit program indtil det stopper. Så kan du indsende din musik til galleriet.", "Music.galleryTooltip": "Åbn musikgalleriet.", "Music.galleryMsg": "Se galleri", "Music.submitTooltip": "Send din musik til galleriet.", "Music.submitMsg": "Send til galleriet", "Music.helpUseFunctions": "<PERSON> l<PERSON> fungerer, men du kan gøre det bedre. Brug funktioner til at reducere mængden af gentaget kode.", "Music.helpUseInstruments": "Musikken vil lyde bedre, hvis du bruger et andet instrument i hver startblok.", "Music.helpText1": "Komponer de første fire noder af »Mester Jakob«.", "Music.helpText2a": "En »funktion« gør at du kan gruppere blokke sammen og så køre dem mere end en gang.", "Music.helpText2b": "Opret en funktion at afspille de første fire noder af »<PERSON><PERSON>«. <PERSON><PERSON><PERSON> den funktion to gange. Tilføj ikke nye nodeblokke.", "Music.helpText3": "Opret en anden funktion for den næste del af »Mester Jakob«. Den sidste node er længere.", "Music.helpText4": "Opret en tredje funktion for den næste del af »Mester Jakob«. De første fire oder er kortere.", "Music.helpText5": "<PERSON><PERSON><PERSON><PERSON><PERSON> den fulde melodi for »Mester Jakob«.", "Music.helpText6a": "Denne nye blok lader dig ændre til et andet instrument.", "Music.helpText6b": "Spil din melodi med en violin.", "Music.helpText7a": "<PERSON>ne nye blok tilføjer en stille forsinkelse.", "Music.helpText7b": "Opret endnu en startblok som har to forsinkelsesblokke, og som så også afspiller »Mester Jakob«.", "Music.helpText8": "Hver startblok skal afspille »Mester Jakob« to gange.", "Music.helpText9": "Opret fire startblokke som hver afspiller »<PERSON><PERSON> Jakob« to gange. Tilføj det korrekte antal forsinkelsesblokke.", "Music.helpText10": "Komponer lige hvad du vil. Du har et stort antal nye blokke du kan undersøge. Gør det sjovt!", "Music.helpText10Reddit": "Brug knappen \"Se galleri\" for at se hvad andre har komponeret. Hvis du komponerer noget interessant, så brug knappen \"Send til galleri\" for at udgive det.", "Pond.scanTooltip": "<PERSON><PERSON>g efter fjender. Angiv en retning (0-360). Returnerer afstanden til den nærmeste fjende i den retning. Returnerer 'Infinity' (uendeligt), hvis den ikke fandt nogen fjender.", "Pond.cannonTooltip": "Affyrer kanonen. <PERSON><PERSON> re<PERSON> (0-360) og afstand (0-70).", "Pond.swimTooltip": "Svøm fremad. Angiv en retning (0-360).", "Pond.stopTooltip": "Stop med at svømme. Spilleren vil nedsætte hastigheden til den stopper.", "Pond.healthTooltip": "Returnerer spillerens nuværende helbred (0 er død, 100 er sundt).", "Pond.speedTooltip": "Returnerer spillerens aktuelle hastighed (0 er stoppet, 100 er fuld fart).", "Pond.locXTooltip": "Returnerer spillerens X-koordinat (0 er den venstre kant, 100 er den højre kant).", "Pond.locYTooltip": "Returnerer spillerens Y-koordinat (0 er den nederste kant, 100 er den øverste kant).", "Pond.logTooltip": "Udskriver et nummer til din browsers konsol.", "Pond.docsTooltip": "<PERSON>is sprogdo<PERSON>.", "Pond.documentation": "Dokumentation", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON>", "Pond.helpUseScan": "<PERSON> v<PERSON>, men du kan gøre det bedre. Brug 'scan'-kommandoen til at fortælle kanonen, hvor langt den skal skyde.", "Pond.helpText1": "Brug 'cannon'-kommandoen til at ramme målet. Den første parameter er vinklen, den anden parameter er afstanden. Find den rette kombination.", "Pond.helpText2": "<PERSON><PERSON> mål skal rammes mange gange. Brug en 'while (true)' l<PERSON><PERSON><PERSON> for at gøre noget uendeligt.", "Pond.helpText3a": "Denne modstander bevæger sig frem og tilbage, hvilket gør det svært at ramme. 'scan'-kommandoen returnerer den nøjagtige afstand til modstanderen i den angivne retning.", "Pond.helpText3b": "<PERSON>ne afstand er præcis hvad 'cannon'-kommandoen skal bruge for at skyde præcist.", "Pond.helpText4": "<PERSON><PERSON> modstander er for langt væk til at bruge kanonen (som har en grænse på 70 meter). Brug i stedet 'swim'-kommandoen til at svømme i modstanderens retning og ramme den.", "Pond.helpText5": "<PERSON>ne modstander er også for langt væk til at bruge kanonen. Men du er for svag til at overleve et sammenstød. Svøm mod din modstander, indtil din afstanden til modstanderen er mindre end 50. Brug så 'stop'-kommandoen og bruge kanonen.", "Pond.helpText6": "<PERSON><PERSON> modstander vil flytte sig væk, n<PERSON>r den er blevet ramt. Svø<PERSON> nærm<PERSON>, hvis den er udenfor rækkevidde (70 meter).", "Gallery": "Galleri"}