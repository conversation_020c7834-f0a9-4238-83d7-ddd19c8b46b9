{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON><PERSON> ë<PERSON>", "Games.puzzle": "S-ciapatesta", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON>ra", "Games.movie": "Film", "Games.music": "<PERSON><PERSON><PERSON><PERSON>", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON> ëd Pond", "Games.pond": "Baciass", "Games.linesOfCode1": "A l'ha arzolvù 's livel con 1 linia ëd JavaScript:", "Games.linesOfCode2": "A l'ha arzolvù 's livel con %1 linie ëd JavaScript:", "Games.nextLevel": "É-lo pront për <PERSON>l livel %1?", "Games.finalLevel": "É-lo pront për n'àutra dësfida?", "Games.submitTitle": "Tìtol:", "Games.linkTooltip": "Argistré e lijé ai blòch.", "Games.runTooltip": "<PERSON><PERSON> and<PERSON>l programa ch'a l'ha scrivù.", "Games.runProgram": "<PERSON><PERSON> andé <PERSON>a", "Games.resetTooltip": "Fërmé ël programa e inissialisé torna ël livel.", "Games.resetProgram": "Buté torna coma al prinsipi", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Lògica", "Games.catLoops": "Liasse", "Games.catMath": "Matemàtica", "Games.catText": "Test", "Games.catLists": "Liste", "Games.catColour": "Color", "Games.catVariables": "Variàbij", "Games.catProcedures": "Fonsion", "Games.httpRequestError": "A-i é staje un problema con l'arcesta.", "Games.linkAlert": "Ch'a partagia ij sò blòch grassie a sta liura: %1", "Games.hashError": "<PERSON> d<PERSON><PERSON><PERSON>, '%1 a corëspond a gnun programa salvà.", "Games.xmlError": "A l'é nen podusse carié so archivi salvà. Miraco a l'é stàit creà con na version diferenta ëd Blockly?", "Games.submitted": "<PERSON><PERSON><PERSON> p<PERSON> ës programa! S'a-j pias a nòstra echip ëd sumie anlenà, a lo publichëran da si dontré di.", "Games.listVariable": "lista", "Games.textVariable": "test", "Games.breakLink": "Na vira ch'a l'avrà ancaminà a modifiché ël JavaScript, a podrà pi nen torné a la modìfica dij blòch. A va bin?", "Games.blocks": "B<PERSON>ò<PERSON>", "Games.congratulations": "Compliment!", "Games.helpAbort": "Ës livel a l'é motobin difìcil. Veul-lo autelo e passer al gieugh apress? A peul sempe torné andré pi tard.", "Index.clear": "Dësca<PERSON>lé tute le soe solussion?", "Index.subTitle": "<PERSON><PERSON><PERSON> për ij programator ë<PERSON> do<PERSON>.", "Index.moreInfo": "Anformassion për j'educator...", "Index.startOver": "<PERSON><PERSON><PERSON>-lo ancaminé dal prinsipi?", "Index.clearData": "Dëscancelé ij dàit", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Gat", "Puzzle.animal2Trait1": "Barbis", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://pms.wikipedia.org/wiki/Gat", "Puzzle.animal3": "Avija", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://pms.wikipedia.org/wiki/Avija", "Puzzle.animal4": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "plancia:", "Puzzle.legs": "gambe:", "Puzzle.legsChoose": "sern...", "Puzzle.traits": "caraterìstiche:", "Puzzle.error0": "Përfet! Tuti ij %1 blòch a son giust.", "Puzzle.error1": "Scasi! Un blòch a va nen bin.", "Puzzle.error2": "%1 blòch a van nen bin.", "Puzzle.tryAgain": "Ël blòch evidensià a va nen bin. Preuva torna.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON><PERSON> le rispòste", "Puzzle.helpText": "<PERSON><PERSON><PERSON> minca n'animal (an verd), tacheje soa plancia, serne ël nùmer ëd soe piòte, e fé na colòna ëd soe caraterìstiche.", "Maze.moveForward": "andé drit", "Maze.turnLeft": "voltesse a snistra", "Maze.turnRight": "voltesse a drita", "Maze.doCode": "fé", "Maze.helpIfElse": "Un blòch si-opura a farà na còsa u l'àutra.", "Maze.pathAhead": "se sënté dë dnans", "Maze.pathLeft": "se sënté a snistra", "Maze.pathRight": "se sënté a drita", "Maze.repeatUntil": "arpete fin-a a", "Maze.moveForwardTooltip": "<PERSON>à andé <PERSON> giugador anans ëd në spassi.", "Maze.turnTooltip": "Fé volté ël giugador a snistra o a drita ëd 90 gré.", "Maze.ifTooltip": "S'a-i é na stra ant la diression ëspessificà, antlora fa chèiche assion.", "Maze.ifelseTooltip": "S'a-i é na stra ant la diression ëspessificà, antlora fa ël prim blòch d'assion. <PERSON>ë<PERSON><PERSON>ò, fà ël second blòch d'assion.", "Maze.whileTooltip": "Arpet j'assion contnùe fin-a a argionze ël pont final.", "Maze.capacity0": "At resto %0 blòch.", "Maze.capacity1": "At resta %1 blòch.", "Maze.capacity2": "At resto %2 blòch.", "Maze.runTooltip": "A fà fé al giugador lòn ch'a diso ij block.", "Maze.resetTooltip": "Buta torna ël giugador al prinsipi dël labirint.", "Maze.helpStack": "Ambaron-a un pàira ëd blòch 'va anans' për giuteme a argionze me but.", "Maze.helpOneTopBlock": "An cost livel, it deve ambaroné ij blòch un ansima a l'àutr ant la zòna ëd travaj bianca.", "Maze.helpRun": "Fà marcé tò programa për vëdde lòn ch'a-i suced.", "Maze.helpReset": "Tò programa a l'ha nen arzolvù ël labirint. Sgnaca 'Buté torna coma al prinsipi' e preuva torna.", "Maze.helpRepeat": "J'ordinator a l'han na memòria limità. Riva a la fin ëd sa stra dovrand mach doi blòch. Deuvra 'arpet' për eseguì un blòch pi 'd na vira.", "Maze.helpCapacity": "It l'has consumà tuti ij blòch për cost livel. Për creé un blòch neuv, tl'has prima damanca dë scancelé un blòch esistent.", "Maze.helpRepeatMany": "A peul buté pi d'un blòch andrinta a 'n blòch «arpete».", "Maze.helpSkins": "Ch'a serna sò giugador preferì da së mnù.", "Maze.helpIf": "Un blòch 'si' a farà cheicòs mach si la condission a l'é vera. Preuva a svolté a snistra s'a-i é na stra a snistra.", "Maze.helpMenu": "Sgnaca su %1 ant ël blòch 'si' për cangé soa condission.", "Maze.helpWallFollow": "Peulës-to arzòlve cost labirint complicà? Sërca d'andeje dapress a la muraja a snistra. Mach për programator coj barbis!", "Bird.noWorm": "a l'ha gnun verm", "Bird.heading": "diression", "Bird.noWormTooltip": "La condission ëd cand l'osel a l'ha nen ciapà ël verm.", "Bird.headingTooltip": "Bogé ant la diression ëd n'àngol dàit: 0 a l'é a drita, 90 drit an facia, e via fòrt.", "Bird.positionTooltip": "x e y a marco la posission ëd l'osel. Cand x = 0 l'osel a l'é tacà a la bordura snistra, cand x = 100 a l'é tacà a la bordura drita. Cand y = 0 l'osel a l'é an bass, cand y = 100 a l'é an àut.", "Bird.helpHeading": "Modifiché l'àngol dla diression për fé an manera che l'osel a ciapa ël verm e a atera an sò ni.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON> 's bl<PERSON><PERSON> për andé an na diression si chiel a l'ha ël verm, e an n'àutra s'a-l l'ha pa.", "Bird.helpX": "'x' e a l'é soa posission orisontal atual. <PERSON><PERSON><PERSON> 's bl<PERSON><PERSON> për and<PERSON> an na diression si 'x' a l'é pi ci che 'n nù<PERSON>, d<PERSON><PERSON><PERSON><PERSON> an n'àutra diression.", "Bird.helpElse": "Sgnaché an sla plancia për modifiché ël blòch 'si'.", "Bird.helpElseIf": "Ës livel a l'ha damanca sia d'un blòch 'si' che d'un blòch 'dësnò'.", "Bird.helpAnd": "Ël blòch 'e' a l'é ver mach si soe doe part a son vere.", "Bird.helpMutator": "Tiré un blòch 'dësn<PERSON>' ant ël blòch 'si'.", "Turtle.moveTooltip": "A bogia la bissa copera anans o andré dla quantità spessificà.", "Turtle.moveForward": "and<PERSON> drit ëd", "Turtle.moveBackward": "and<PERSON> and<PERSON>d", "Turtle.turnTooltip": "A fà volté la bissa copera a snistra o a drita dël nùmer ëd gré spessificà.", "Turtle.turnRight": "voltesse a drita ëd", "Turtle.turnLeft": "voltesse a snistra ëd", "Turtle.widthTooltip": "Modifiché la larghëssa dla piuma.", "Turtle.setWidth": "buté la larghëssa a", "Turtle.colourTooltip": "A cangia ël color ëd la piuma.", "Turtle.setColour": "buté ël color a", "Turtle.penTooltip": "A àussa o a sbassa la piuma, për chité o ancaminé a dissegné.", "Turtle.penUp": "aussé la piuma", "Turtle.penDown": "pogé la piuma", "Turtle.turtleVisibilityTooltip": "A rend la bissa copera (sercc e flecia) visìbil o invisìbil.", "Turtle.hideTurtle": "stë<PERSON>é la bissa copera", "Turtle.showTurtle": "mostré la bissa copera", "Turtle.printTooltip": "A dissegna ël test ant la diression dla bissa copera a soa posission.", "Turtle.print": "scrive", "Turtle.fontTooltip": "A definiss ël caràter dovrà dal blòch dë scritura.", "Turtle.font": "<PERSON><PERSON><PERSON>", "Turtle.fontSize": "dimension dij car<PERSON>ter", "Turtle.fontNormal": "normal", "Turtle.fontBold": "<PERSON><PERSON><PERSON>", "Turtle.fontItalic": "corsiv", "Turtle.submitDisabled": "<PERSON>é marcé sò programa fin-a che as fërma. Peui a peul publiché sò dissegn ant la galarìa.", "Turtle.galleryTooltip": "<PERSON><PERSON><PERSON> la galarìa dij dissegn.", "Turtle.galleryMsg": "<PERSON><PERSON>dd<PERSON> la Galarìa", "Turtle.submitTooltip": "<PERSON><PERSON>é sò dissegn ant la galarìa.", "Turtle.submitMsg": "Publiché ant la Galarìa", "Turtle.helpUseLoop": "Soa solussion a marcia, ma a peul fé mej.", "Turtle.helpUseLoop3": "Dissegné la forma con mach tre blòch.", "Turtle.helpUseLoop4": "Dissegné la stèila con mach quatr blòch.", "Turtle.helpText1": "Creé un programa ch'a dissegna un quadrà.", "Turtle.helpText2": "Modifiché sòprograma për dissegné un pentàgon nopà che un quadrà.", "Turtle.helpText3a": "Cost-sì a l'é un blòch neuv ch'a-j pë<PERSON>et ëd modifiché ël color:", "Turtle.helpText3b": "Dissegné na stèila giàuna.", "Turtle.helpText4a": "Cost-sì a l'é un blòch neuv ch'a-j pë<PERSON>et d'aussé sò crajon dal feuj cand a së spòsta:", "Turtle.helpText4b": "Dissegné na stèila giàuna, peui na linia dëdzora 'd chila.", "Turtle.helpText5": "Nopà che na stèila, peul-lo dissegné quatr ëstèile butà an quadrà?", "Turtle.helpText6": "Dissegné tre stèile g<PERSON>, e na linia bianca.", "Turtle.helpText7": "Dissegné je st<PERSON>ile, peui quatr linie bianche.", "Turtle.helpText8": "Dissegné 360 linie bianche ch'a smijo a na lun-a pien-a.", "Turtle.helpText9": "<PERSON><PERSON>l-lo gionté un sercc nèir për che la lun-a a dventa na lun-a chërsenta?", "Turtle.helpText10": "Ch'a dissegna lòn ch'a veul. A l'ha 'n baron ëd blòch neuv da esploré. Ch'as amusa!", "Turtle.helpText10Reddit": "Ch'a deuvra ël boton \"Vëdde la galarìa\" për vëdde lòn che j'àutri a l'han dissegnà. S'a dissegna cheicòs d'anteressant, ch'a deuvra ël boton \"Publiché ant la galarìa\" për publichelo.", "Turtle.helpToolbox": "Serne na categorìa për vëdde ij blòch.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x ëd partensa", "Movie.y1": "y ëd partensa", "Movie.x2": "x ëd fin", "Movie.y2": "y ëd fin", "Movie.radius": "raj", "Movie.width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.height": "autëssa", "Movie.circleTooltip": "A dissegna un sercc al pòst ëspessificà e ëd raj spessificà.", "Movie.circleDraw": "sercc", "Movie.rectTooltip": "A dissegna un retàngol ant ël pòst ëspessificà e con larghëssa e autëssa spessificà.", "Movie.rectDraw": "retàngol", "Movie.lineTooltip": "A dissegna na linia da 'n pont a n'àutr con la larghëssa spessificà.", "Movie.lineDraw": "linia", "Movie.timeTooltip": "A rëspond con la durà atual dl'animassion (0-100).", "Movie.colourTooltip": "A cangia ël color dël crajon.", "Movie.setColour": "buté ël color a", "Movie.submitDisabled": "Soa animassiona bogia nen. <PERSON><PERSON><PERSON> ij blòch për fé cheicòs d'anteressant. Peui a peul publiché soa animassion ant la galarìa.", "Movie.galleryTooltip": "<PERSON><PERSON><PERSON> la galarìa dj'animassion.", "Movie.galleryMsg": "Vëdde la galarìa", "Movie.submitTooltip": "Publiché soa animassion ant la galarìa.", "Movie.submitMsg": "Publiché an sla galarìa", "Movie.helpLayer": "Ch'a spòsta ël sercc dë sfond an cò a sò programa. Antlora a comparirà darera la përson-a.", "Movie.helpText1": "<PERSON><PERSON><PERSON> dle forme sempie për dissegné costa përson-a.", "Movie.helpText2a": "Ës livel a l'é n'animassion. Chiel a veul fé bogé ël brass ëd la përson-a a travers dlë scren. Ch'a sgnaca an sël boton ëd gieugh për na previsualisassion.", "Movie.helpText2b": "Antant che l'animassion as <PERSON><PERSON><PERSON><PERSON><PERSON>, ël valor dël blòch 'temp' a va anans da 0 a 100. Dagià che chiel a veul che la posission 'y' dël brass a parta da 0 e a vada fin-a a 100, sòn a dovrìa esse belfé.", "Movie.helpText3": "ël blòch 'temp' a va da 0 a 100. Ma adess chiel a veul che la posission 'y' ëd l'àutr brass a parta da 100 e a vada fin-a a 0. Peul-lo trové na fórmola matemàtica sempia ch'a anverta la diression?", "Movie.helpText4": "Ch'a deuvra lòn ch'a l'ha amprendù al livel precedent për fé dle gambe ch'a traverso.", "Movie.helpText5": "La fórmola matemàtica për ël brass a l'é complica. Costa a l'é la rispòsta:", "Movie.helpText6": "Ch'a-j buta doe man a la përson-a.", "Movie.helpText7": "Ch'a deuvra ël blòch 'si' për dissegné na cita testa për la prima mità dël filmà. Peui ch'a dissegna na gròssa testa për la sconda mità dël filmà.", "Movie.helpText8": "Ch'a fasa anvërtì la diression a le gambe a mità dël filmà.", "Movie.helpText9": "Ch'a dissegna un sercc ch'a së slarga darera la përson-a.", "Movie.helpText10": "Ch'a fasa n'animassion ëd lòn ch'a veul. A l'ha na caterva ëd neuv blòch da esploré. Ch'as amusa!", "Movie.helpText10Reddit": "Ch'a deuvra ël boton \"Vëdde la galarìa\" për vëdde j'animassion fàite da d'àutra gent. S'a l'ha creà n'animassion anteressanta, ch'a deuvra ël boton \"Publiché ant la galarìa\" për publichela.", "Music.playNoteTooltip": "A son-a mach un-a nòta musical ëd la durà e autëssa spessificà.", "Music.playNote": "soné %1 nòta %2", "Music.restTooltip": "A spetà për la durà spessificà.", "Music.restWholeTooltip": "A speta për na nòta antrega.", "Music.rest": "speté %1", "Music.setInstrumentTooltip": "A passa a lë strument ëspessificà an sonand le nòte musicaj ch'a ven-o apress.", "Music.setInstrument": "fissé lë strument a %1", "Music.startTooltip": "A fa marcé ij blòch and<PERSON><PERSON> cand a së sgnaca ël boton 'Fé parte ël programa'.", "Music.start": "cand %1 a l'é sgnacà", "Music.pitchTooltip": "Un-a nòta (C4 a l'é 7).", "Music.firstPart": "prima part", "Music.piano": "piano", "Music.trumpet": "tromba", "Music.banjo": "bangio", "Music.violin": "violin", "Music.guitar": "g<PERSON>ra", "Music.flute": "fluta", "Music.drum": "tamborn", "Music.choir": "c<PERSON><PERSON>", "Music.submitDisabled": "<PERSON>é marcé sò programa fin-a ch'as fërma. Peui a peul publiché soa mùsica ant la galarìa.", "Music.galleryTooltip": "<PERSON><PERSON><PERSON> la galarìa ëd mùsi<PERSON>.", "Music.galleryMsg": "Vëdde la galarìa", "Music.submitTooltip": "Publiché soa mùsica ant la galarìa.", "Music.submitMsg": "Publiché ant la galarìa", "Music.helpUseFunctions": "Soa solussion a marcia, ma a peul fé mej. Ch'a deuvra dle fonsion për diminuì la quatità ëd còdes arpetù.", "Music.helpUseInstruments": "La mùsica a vnirà mej s'a deuvra në strument diferent an minca 'n blòch ëd partensa.", "Music.helpText1": "Ch'a compon-e le prime quatr nòte ëd '<PERSON><PERSON>'.", "Music.helpText2a": "Na 'fonsion' a-j p<PERSON><PERSON>et d'argropé ansem dij blòch, peui ëd feje andé pi che na vira.", "Music.helpText2b": "Ch'a crea na fonsion për soné le prime quatr nòte ëd '<PERSON><PERSON>'. Ch'a fasa parte sta fonsion doe vire. Ch'a gionta gnun àutr blòch ëd nòte.", "Music.helpText3": "Ch'a crea na sconda fonsion për la part apress ëd '<PERSON><PERSON>'. L'ùltima nòta a l'é pi longa.", "Music.helpText4": "Ch'a crea na tersa fonsion për la part ch'a-i ven apress ëd '<PERSON><PERSON>'. Le prime quatr nòte a son pi curte.", "Music.helpText5": "Ch'a completa tuta l'aria ëd '<PERSON><PERSON>'.", "Music.helpText6a": "Ës neuv blòch a-j përmet ëd passé a n'àutr ëstrument.", "Music.helpText6b": "Soné soa aria con un violin.", "Music.helpText7a": "Ës neuv blòch a gionta un temp ëd silensi.", "Music.helpText7b": "Ch'a crea un second blòch ch'a l'ha doi blòch ëd silensi, peui a son-a ëd<PERSON><PERSON> '<PERSON><PERSON>'.", "Music.helpText8": "Minca 'n bl<PERSON>ch ëd partensa a dev soné 'Fr<PERSON> Jacques' doe vire.", "Music.helpText9": "Ch'a crea quatr blòch ëd partensa ch'a son-o mincadun '<PERSON><PERSON>' doe vire. Ch'a gionta ël nùmer giust ëd blòch ëd silensi.", "Music.helpText10": "Ch'a compon-a lòn ch'a veul. A l'ha un nùmer ëstragròss ëd neuv blòch da esploré. Ch'as amusa!", "Music.helpText10Reddit": "Ch'a deuvra ël boton \"Vëdde la galarìa\" për vëdde lòn che j'àutri a l'han componù. S'a compon cheicòs d'anteressant, ch'a deuvra ël boton \"Publiché ant la galarìa\" për publichelo.", "Pond.scanTooltip": "Sërché dij nemis. Spessifiché na diression (0-360). A rëspond con la distansa dal nemis pi davzin an cola diression. A rëspond Infinì s'a treuva gnun nemis.", "Pond.cannonTooltip": "Sparé con ël canon. Spessifiché na diression (0-360) e na portà (0-70).", "Pond.swimTooltip": "<PERSON><PERSON><PERSON> Spessifiché na diression (0-360).", "Pond.stopTooltip": "<PERSON><PERSON> ëd noé. Ël giugador a ralentrà për fërmesse.", "Pond.healthTooltip": "A rëspond con la salute atual dël giugador (0 a l'é mòrt, 100 a l'é an pien-a forma).", "Pond.speedTooltip": "A smon l'andi atual dël giugador (0 a l'é ferm, 100 a l'é a andi pien).", "Pond.locXTooltip": "A smon la coordinà X dël giugador (0 a l'é ël bòrd ësnistr, 100 a l'é ël bòrd drit).", "Pond.locYTooltip": "A smon la coordinà Y dël giugador (0 a l'é ël bass, 100 a l'é l'àut).", "Pond.logTooltip": "A stampa un nùmer an s<PERSON> crusc<PERSON>t ëd sò navigador.", "Pond.docsTooltip": "A smon la documentassion an sla lenga.", "Pond.documentation": "Documentassion", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Bërsaj", "Pond.pendulumName": "Pèndola", "Pond.scaredName": "Sbaruvà", "Pond.helpUseScan": "Soa solussion a marcia, ma a peul fé mej. Ch'a deuvra 'scan' për dì al canon a che distansa tiré.", "Pond.helpText1": "Ch'a deuvra ël comand 'cannon' për co<PERSON>pì ël bërsaj. Ël prim paràmeter a l'é l'àngol, ël second a l'é la distansa. Ch'a treuva la bon-a combinassion.", "Pond.helpText2": "Ës bërsaj a dev esse colpì vàire vire. Ch'a deuvra un sicl 'while (true)' për fé cheicòs indefinitivaman.", "Pond.helpText3a": "S'aversari a bogia anans e andré, lòn ch'a rend malfé colpilo. L'espression 'scan' a smon la distansa precisa da l'aversari ant la diression ëspessificà.", "Pond.helpText3b": "Sa distansa a l'é pròpe cola dont ël comand 'cannon' a l'ha damanca për tiré con precision.", "Pond.helpText4": "St'aversari a l'é tròp leugn për dovré ël canon (ch'a l'ha na limitassion dë 70 méter). <PERSON>, ch'a deuvra ël comand 'swim' për ancaminé a noé vers l'aversari e colpilo.", "Pond.helpText5": "ëd<PERSON><PERSON> st'aversari-sì a l'é tròp leugn për dovré ël canon. Ma chiel a l'é tròp débol për dzorvive a n'antruch. Ch'a noa vers l'aversari fin-a a che soa posission orisontal a sia anferior a 50. Peui 'stop' e ch'a deuvra ël canon.", "Pond.helpText6": "S'aversari a së slontanërà cand a sarà colpì. Ch'a noa vers ëd chiel s'a l'é fòra portà (70 méter).", "Gallery": "<PERSON><PERSON><PERSON><PERSON>"}