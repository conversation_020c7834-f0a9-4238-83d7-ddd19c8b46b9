{"@metadata": {"authors": ["Chairego apc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "Games.name": "Xogos do <PERSON>ly", "Games.puzzle": "Crebacabezas", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON>", "Games.turtle": "Tartaruga", "Games.movie": "Filme", "Games.music": "Música", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "Lagoa", "Games.linesOfCode1": "Resolviches este nivel con 1 liña de JavaScript:", "Games.linesOfCode2": "Resolviches este nivel con %1 liñas de JavaScript:", "Games.nextLevel": "Estás preparado para o nivel %1?", "Games.finalLevel": "Estás preparado para o próximo desafío?", "Games.submitTitle": "Título:", "Games.linkTooltip": "Gardar e crear unha ligazón aos bloques.", "Games.runTooltip": "Executa o programa que escribiches.", "Games.runProgram": "Executar o programa", "Games.resetTooltip": "Detén o programa e reinicia o nivel.", "Games.resetProgram": "Reiniciar", "Games.help": "Axuda", "Games.catLogic": "Lóxica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matemáticas", "Games.catText": "Texto", "Games.catLists": "Listas", "Games.catColour": "Cor", "Games.catVariables": "Variables", "Games.catProcedures": "Funcións", "Games.httpRequestError": "Houbo un problema coa solicitude.", "Games.linkAlert": "Comparte os teus bloques con esta ligazón:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, \"%1\" non se corresponde con ningún programa gardado.", "Games.xmlError": "Non se puido cargar o ficheiro gardado. Se cadra, foi creado cunha versión diferente de Blockly.", "Games.submitted": "Grazas por este programa! Se lle gusta ao noso persoal de monos adestrados, publicarano na galería nun par de días.", "Games.listVariable": "lista", "Games.textVariable": "texto", "Games.breakLink": "Unha vez que comeces a editar JavaScript, non poderás volver á edición de bloques. Queres continuar?", "Games.blocks": "Bloques", "Games.congratulations": "Parabéns!", "Games.helpAbort": "Este nivel é moi difícil. Gustaríache saltalo e pasar ao seguinte xogo? Sempre podes volver máis tarde.", "Index.clear": "Queres eliminar todas as túas solucións?", "Index.subTitle": "Xogos para os programadores do futuro.", "Index.moreInfo": "Información para educadores...", "Index.startOver": "Queres comezar de novo?", "Index.clearData": "<PERSON><PERSON><PERSON> os datos", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://gl.wikipedia.org/wiki/Pato", "Puzzle.animal2": "Gato", "Puzzle.animal2Trait1": "Bigoteira", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://gl.wikipedia.org/wiki/Gato", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://gl.wikipedia.org/wiki/<PERSON>la", "Puzzle.animal4": "Caracol", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Baba", "Puzzle.animal4HelpUrl": "https://gl.wikipedia.org/wiki/Caracol", "Puzzle.picture": "imaxe:", "Puzzle.legs": "patas:", "Puzzle.legsChoose": "escolle...", "Puzzle.traits": "trazos:", "Puzzle.error0": "Perfecto!\nOs %1 bloques son correctos.", "Puzzle.error1": "Por pouco! Un bloque é incorrecto.", "Puzzle.error2": "%1 bloques son incorrectos.", "Puzzle.tryAgain": "O bloque destacado non é correcto.\n<PERSON><PERSON>.", "Puzzle.checkAnswers": "Comprobar as respostas", "Puzzle.helpText": "A cada animal (en verde), faille corresponder a súa foto, sinala o seu número de patas e apiña os seus trazos.", "Maze.moveForward": "a<PERSON><PERSON>", "Maze.turnLeft": "xirar á esquerda", "Maze.turnRight": "xirar á dereita", "Maze.doCode": "executar", "Maze.helpIfElse": "Os bloques \"se/se non\" fan unha cousa ou a outra.", "Maze.pathAhead": "se hai camiño diante", "Maze.pathLeft": "se hai camiño á esquerda", "Maze.pathRight": "se hai camiño á dereita", "Maze.repeatUntil": "repetir ata", "Maze.moveForwardTooltip": "Fai que a figura avance un espazo.", "Maze.turnTooltip": "Fai que a figura xire á esquerda ou á dereita 90 graos.", "Maze.ifTooltip": "Se hai un camiño na dirección especificada, entón fanse algunhas accións.", "Maze.ifelseTooltip": "Se hai un camiño na dirección especificada, entón faise o primeiro bloque de accións. En caso contrario, faise o segundo bloque de accións.", "Maze.whileTooltip": "Repetir as accións do bloque ata chegar á meta.", "Maze.capacity0": "Restan %0 bloques.", "Maze.capacity1": "Resta %1 bloque.", "Maze.capacity2": "Restan %2 bloques.", "Maze.runTooltip": "Fai que a figura realice o que din os bloques.", "Maze.resetTooltip": "Coloca a figura ao inicio do labirinto.", "Maze.helpStack": "<PERSON><PERSON> unha parella de bloques \"avanzar\" para axudarme a chegar á meta.", "Maze.helpOneTopBlock": "Neste nivel, tes que amorear xuntos tódolos bloques no espazo de traballo.", "Maze.helpRun": "Executa o teu programa para comprobar o que acontece.", "Maze.helpReset": "O teu programa non resolveu o labirinto. Preme en \"Restablecer\" e inténtao de novo.", "Maze.helpRepeat": "Chega ao final deste camiño usando só dous bloques. Utiliza \"repetir\" para executar un bloque máis dunha vez.", "Maze.helpCapacity": "Xa usaches todos os bloques dispoñibles para este nivel. Para crear un novo bloque, primeiro debes borrar un dos existentes.", "Maze.helpRepeatMany": "Podes apilar máis dun bloque dentro dos bloques \"repetir\".", "Maze.helpSkins": "Escolle o teu xogador favorito neste menú.", "Maze.helpIf": "Un bloque \"se\" fará algo unicamente se a condición é certa. Intenta xirar á esquerda se hai camiño á esquerda.", "Maze.helpMenu": "Preme en %1 no bloque \"se\" para cambiar a súa condición.", "Maze.helpWallFollow": "Podes resolver este labirinto máis complicado? Intenta seguir a parede da esquerda. Só para programadores avanzados!", "Bird.noWorm": "non ten ningunha miñoca", "Bird.heading": "dirección", "Bird.noWormTooltip": "A condición cando o paxaro non consegue a miñoca.", "Bird.headingTooltip": "Móvese na dirección do ángulo dado: 0 é cara á dereita, 90 é cara a arriba etc.", "Bird.positionTooltip": "x e y marcan a posición do paxaro. Cando x = 0 o paxaro está preto do bordo esquerdo, cando x = 100 o paxaro está preto do bordo dereito. Cando y = 0 o paxaro está na parte inferior, cando y = 100 o paxaro está na parte superior.", "Bird.helpHeading": "Cambia o ángulo da dirección para que o paxaro consiga a miñoca e aterrice no seu niño.", "Bird.helpHasWorm": "Usa este bloque para ir nunha dirección se tes a miñoca, ou nunha dirección diferente se non tes a miñoca.", "Bird.helpX": "\"x\" é a túa posición horizontal actual. Usa este bloque para ir nunha dirección se \"x\" é menor que un número, ou nunha dirección diferente en caso contrario.", "Bird.helpElse": "Fai clic na icona para modificar o bloque \"se\".", "Bird.helpElseIf": "Este nivel necesita bloques \"mais se\" e \"se non\".", "Bird.helpAnd": "O bloque \"e\" só é verdadeiro se ambas as s<PERSON><PERSON> entradas son verdade<PERSON><PERSON>.", "Bird.helpMutator": "Arrastra un bloque \"se non\" ao bloque \"se\".", "Turtle.moveTooltip": "Move a tartaruga adiante ou atrás a cantidade indicada.", "Turtle.moveForward": "mover adiante", "Turtle.moveBackward": "mover atrás", "Turtle.turnTooltip": "Xira a tartaruga á esquerda ou á dereita o número especificado de graos.", "Turtle.turnRight": "xirar á dereita", "Turtle.turnLeft": "xirar á esquerda", "Turtle.widthTooltip": "Cambia o grosor do bolígrafo.", "Turtle.setWidth": "establecer o grosor en", "Turtle.colourTooltip": "Cambia a cor do bolígrafo.", "Turtle.setColour": "establecer a cor en", "Turtle.penTooltip": "<PERSON>eva ou baixa o bolígrafo, para deixar de debuxar ou empezar a debuxar.", "Turtle.penUp": "elevar o bolígrafo", "Turtle.penDown": "baixar o bolígrafo", "Turtle.turtleVisibilityTooltip": "Fai que a tartaruga (o círculo e a frecha) sexa visible ou invisible.", "Turtle.hideTurtle": "agochar a tartaruga", "Turtle.showTurtle": "amosar a tartaruga", "Turtle.printHelpUrl": "https://gl.wikipedia.org/wiki/Impresi%C3%B3n", "Turtle.printTooltip": "Escribe un texto na dirección da tartaruga e desde a súa posición.", "Turtle.print": "escribir", "Turtle.fontTooltip": "Define o tipo de letra empregado polo bloque de escritura.", "Turtle.font": "tipo de letra", "Turtle.fontSize": "<PERSON><PERSON><PERSON> da <PERSON>ra", "Turtle.fontNormal": "normal", "Turtle.fontBold": "negra", "Turtle.fontItalic": "cursiva", "Turtle.submitDisabled": "Executa o teu programa ata que se deteña. Despois podes enviar o teu debuxo á galería.", "Turtle.galleryTooltip": "Abre a galería de debuxos.", "Turtle.galleryMsg": "Ollar a galería", "Turtle.submitTooltip": "Enviar o teu debuxo á galería.", "Turtle.submitMsg": "Enviar á galería", "Turtle.helpUseLoop": "A túa solución funciona, pero podes facelo mellor.", "Turtle.helpUseLoop3": "Debuxa a forma con só tres bloques.", "Turtle.helpUseLoop4": "Debuxa a estrela con só catro bloques.", "Turtle.helpText1": "Crea un programa que debuxe un cadrado.", "Turtle.helpText2": "Cambia o teu programa para debuxar un pentágono en lugar dun cadrado.", "Turtle.helpText3a": "Hai un novo bloque que che permite cambiar a cor:", "Turtle.helpText3b": "Debuxa unha estrela amarela.", "Turtle.helpText4a": "Hai un bloque novo que che permite levantar o bolígrafo do papel cando te moves:", "Turtle.helpText4b": "Debuxa unha pequena estrela amarela e despois debuxa unha liña enriba dela.", "Turtle.helpText5": "En lugar dunha estrela, podes debuxar catro estrelas dispostas nun cadrado?", "Turtle.helpText6": "Debuxa tres estrelas amarelas e unha liña branca.", "Turtle.helpText7": "Debuxa as estrelas e despois debuxa catro liñas brancas.", "Turtle.helpText8": "Debuxar 360 liñas brancas semellará a lúa chea.", "Turtle.helpText9": "Podes engadir un círculo negro para que a lúa se converta en lúa crecente?", "Turtle.helpText10": "Debuxa o que queiras. Tes un gran número de bloques novos cos que podes explorar. Divírtete!", "Turtle.helpText10Reddit": "Usa o botón \"Ollar a galería\" para ver o que debuxaron outras persoas. Se debuxas algo interesante, usa o botón \"Enviar á galería\" para publicalo.", "Turtle.helpToolbox": "Escolle unha categoría para ver os bloques.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x de inicio", "Movie.y1": "y de inicio", "Movie.x2": "x de fin", "Movie.y2": "y de fin", "Movie.radius": "raio", "Movie.width": "ancho", "Movie.height": "altura", "Movie.circleTooltip": "Debuxa un círculo no lugar especificado e co raio especificado.", "Movie.circleDraw": "<PERSON><PERSON><PERSON><PERSON>", "Movie.rectTooltip": "Debuxa un rectángulo no lugar especificado e co ancho e a altura especificados.", "Movie.rectDraw": "rect<PERSON><PERSON><PERSON>", "Movie.lineTooltip": "Debuxa unha liña dun punto a outro co ancho especificado.", "Movie.lineDraw": "liña", "Movie.timeTooltip": "Devolve o tempo actual na animación (0-100).", "Movie.colourTooltip": "Cambia a cor do bolígrafo.", "Movie.setColour": "mudar a cor a", "Movie.submitDisabled": "O teu filme non se move. Usa os bloques para facer algo interesante. Despois podes enviar a túa película á galería.", "Movie.galleryTooltip": "Abre a galería de filmes.", "Movie.galleryMsg": "Ollar a galería", "Movie.submitTooltip": "Enviar o teu filme á galería.", "Movie.submitMsg": "Enviar á galería", "Movie.helpLayer": "Move o círculo do fondo á parte superior do programa. Entón aparecerá detrás da persoa.", "Movie.helpText1": "Usa formas sinxelas para debuxar esta persoa.", "Movie.helpText2a": "Este nivel é un filme. Queres que o brazo da persoa se mova pola pantalla. Preme no botón de reprodución para ollar unha vista previa.", "Movie.helpText2b": "A medida que se reproduce o filme, o valor do bloque \"tempo\" conta de 0 a 100. Como queres que a posición \"y\" do brazo comece en 0 e vaia ata 100, isto debería ser sinxelo.", "Movie.helpText3": "O bloque \"tempo\" conta de 0 a 100. Pero agora queres que a posición \"y\" do outro brazo comece en 100 e vaia a 0. Podes descubrir unha fórmula matemática sinxela que cambie a dirección?", "Movie.helpText4": "Usa o que aprendiches no nivel anterior para facer que as pernas se crucen.", "Movie.helpText5": "A fórmula matemática para o brazo é complicada. Aquí está a resposta:", "Movie.helpText6": "Dálle un par de mans á persoa.", "Movie.helpText7": "Usa o bloque \"se\" para debuxar unha cabeza pequena para a primeira metade do filme. Despois debuxa unha cabeza grande para a segunda metade do filme.", "Movie.helpText8": "<PERSON>ai que as pernas invertan a dirección á metade do filme.", "Movie.helpText9": "Debuxa un círculo en expansión detrás da persoa.", "Movie.helpText10": "Fai un filme do que queiras. Tes un gran número de bloques novos cos que podes explorar. Divírtete!", "Movie.helpText10Reddit": "Usa o botón \"Ollar a galería\" para ver os filmes doutras persoas. Se fas un filme interesante, usa o botón \"Enviar á galería\" para publicalo.", "Music.playNoteTooltip": "Toca unha nota musical coa duración e o ton especificados.", "Music.playNote": "reproducir %1 coa nota %2", "Music.restTooltip": "Agarda durante o tempo especificado.", "Music.restWholeTooltip": "A<PERSON>a unha nota completa.", "Music.rest": "agardar %1", "Music.setInstrumentTooltip": "Cambia ao instrumento especificado ao reproducir as notas musicais posteriores.", "Music.setInstrument": "establecer o instrumento a %1", "Music.startTooltip": "Executa os bloques interiores cando se fai clic no botón \"Executar o programa\".", "Music.start": "ao premer en %1", "Music.pitchTooltip": "<PERSON>ha nota (a nota dó é o 7).", "Music.firstPart": "primeiro anaco", "Music.piano": "piano", "Music.trumpet": "trompeta", "Music.banjo": "banxo", "Music.violin": "violín", "Music.guitar": "guitarra", "Music.flute": "frauta", "Music.drum": "tambor", "Music.choir": "coro", "Music.submitDisabled": "Executa o teu programa ata que se deteña. Despois podes enviar a túa música á galería.", "Music.galleryTooltip": "Abre a galería de música.", "Music.galleryMsg": "Ollar a galería", "Music.submitTooltip": "Enviar a túa música á galería.", "Music.submitMsg": "Enviar á galería", "Music.helpUseFunctions": "A túa solución funciona, pero podes facelo mellor. Usa funcións para reducir a cantidade de código repetido.", "Music.helpUseInstruments": "A música soará mellor se usas un instrumento diferente en cada bloque de inicio.", "Music.helpText1": "<PERSON><PERSON><PERSON> as catro primeiras notas de \"Frère Jacques\".", "Music.helpText2a": "Unha \"función\" permite agrupar varios bloques e executalos máis dunha vez.", "Music.helpText2b": "Crea unha función para tocar as catro primeiras notas de \"Frère Jacques\". Executa esa función dúas veces. Non engadas novos bloques de notas.", "Music.helpText3": "Crea unha segunda función para a seguinte parte de \"Frère Jacques\". A última nota é máis longa.", "Music.helpText4": "Crea unha terceira función para a seguinte parte de \"Frère Jacques\". As catro primeiras notas son máis curtas.", "Music.helpText5": "Completa a canción \"Frère Jacques\".", "Music.helpText6a": "Este novo bloque permíteche cambiar a outro instrumento.", "Music.helpText6b": "Toca a túa melodía cun violín.", "Music.helpText7a": "Este novo bloque engade unha pausa.", "Music.helpText7b": "Crea un segundo bloque de inicio que teña dous bloques de pausa e que logo reproduza tamén \"Frère Jacques\".", "Music.helpText8": "Cada bloque de inicio debería reproducir dúas veces \"Frère Jacques\".", "Music.helpText9": "<PERSON>rea catro bloques de inicio que reproduzan \"<PERSON><PERSON>\" dúas veces. Engade o número correcto de bloques de pausa.", "Music.helpText10": "Compón o que queiras. Tes un gran número de bloques novos cos que podes explorar. Divírtete!", "Music.helpText10Reddit": "Usa o botón \"Ollar a galería\" para ver o que compuxeron outras persoas. Se compós algo interesante, usa o botón \"Enviar á galería\" para publicalo.", "Pond.scanTooltip": "Busca inimigos. Especifica unha dirección (0-360). Devolve a distancia ao inimigo máis próximo nesa dirección. Devolve infinito se non se atopa ningún inimigo.", "Pond.cannonTooltip": "Dispara o canón. Especifica unha dirección (0-360) e un alcance (0-70).", "Pond.swimTooltip": "<PERSON><PERSON> cara a adiante. Especifica unha dirección (0-360).", "Pond.stopTooltip": "<PERSON><PERSON>a de nadar. O xogador freará ata deterse.", "Pond.healthTooltip": "Devolve a saúde actual do xogador (0 é morto, 100 é saudable).", "Pond.speedTooltip": "Devolve a velocidade actual do xogador (0 é parado, 100 é a velocidade máxima).", "Pond.locXTooltip": "Devolve a coordenada X do xogador (0 é o bordo esquerdo, 100 é o bordo dereito).", "Pond.locYTooltip": "Devolve a coordenada Y do xogador (0 é o bordo inferior, 100 é o bordo superior).", "Pond.logTooltip": "Imprime un número na consola do teu navegador.", "Pond.docsTooltip": "Mostra a documentación da linguaxe.", "Pond.documentation": "Documentación", "Pond.playerName": "Xogador", "Pond.targetName": "Obxectivo", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Agoirado", "Pond.helpUseScan": "A túa solución funciona, pero podes facelo mellor. Usa \"scan\" para dicirlle ao canón cara a onde disparar.", "Pond.helpText1": "Usa o comando \"cannon\" para darlle ao obxectivo. O primeiro parámetro é o ángulo, o segundo parámetro é o alcance. Atopa a combinación correcta.", "Pond.helpText2": "Cómpre alcanzar este obxectivo múltiples veces. Usa un bucle \"while (true)\" para facer algo indefinidamente.", "Pond.helpText3a": "Este opoñente móvese cara a atrás e cara a adiante, dificultando o golpe. A expresión \"scan\" devolve o rango exacto ata o opoñente na dirección especificada.", "Pond.helpText3b": "Este alcance é exactamente o que o comando \"cannon\" necesita para se disparar con precisión.", "Pond.helpText4": "Este oponente está demasiado lonxe para usar o canón (que ten un límite de 70 metros). En vez diso, usa o comando \"swim\" para comezar a nadar cara ao opoñente e chocar contra el.", "Pond.helpText5": "Este opoñente tamén está demasiado lonxe para usar o canón. Pero ti es demasiado débil para sobrevivir a unha colisión. Nada cara ao opoñente mentres a túa localización horizontal está a menos de 50. <PERSON><PERSON><PERSON>, usa \"stop\" e o canón.", "Pond.helpText6": "Este opoñente afastarase cando sexa golpeado. Nada cara a el se está fóra do alcance (70 metros).", "Gallery": "Galería"}