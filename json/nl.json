{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "KlaasZ4usV", "Lemondoge", "McDut<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Optilete", "<PERSON><PERSON>", "<PERSON>", "Robin0van0der0vliet", "<PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "<PERSON><PERSON><PERSON>", "Games.maze": "Doolhof", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Muziek", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.pond": "Vijver", "Games.linesOfCode1": "Je hebt dit niveau opgelost met 1 regel JavaScript:", "Games.linesOfCode2": "Je hebt dit niveau opgelost met %1 regels JavaScript:", "Games.nextLevel": "Ben je klaar voor niveau %1?", "Games.finalLevel": "Ben je klaar voor de volgende uitdaging?", "Games.submitTitle": "Naam:", "Games.linkTooltip": "Opslaan en koppelen naar blokken.", "Games.runTooltip": "Het door u geschreven programma uitvoeren.", "Games.runProgram": "Programma uitvoeren", "Games.resetTooltip": "Stop het programma en begin het niveau opnieuw.", "Games.resetProgram": "Opnieuw instellen", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logica", "Games.catLoops": "Lussen", "Games.catMath": "Formules", "Games.catText": "Tekst", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variabelen", "Games.catProcedures": "Functies", "Games.httpRequestError": "Er is een probleem opgetreden tijdens het verwerken van het verzoek.", "Games.linkAlert": "Deel uw blokken via deze koppeling:\n\n%1", "Games.hashError": "\"%1\" komt he<PERSON><PERSON> niet overeen met een opgeslagen bestand.", "Games.xmlError": "<PERSON>w opgeslagen bestand kan niet geladen worden. Is het misschien gemaakt met een andere vers<PERSON>?", "Games.submitted": "Dank je voor dit programma! Als onze staf van opgeleide apen het leuk vindt, nemen ze het binnen enkele dagen op in de galerij.", "Games.listVariable": "lijst", "Games.textVariable": "tekst", "Games.breakLink": "Zodra u begint met het bewerken van JavaScript, kunt u niet terug gaan naar het bewerken van blokken. Akkoord?", "Games.blocks": "Blokken", "Games.congratulations": "Van harte gefeliciteerd!", "Games.helpAbort": "Dit niveau is erg moeilijk. Wil je het overslaan en naar het volgende spel gaan? Je kunt altijd later terugkomen.", "Index.clear": "Al je oplossingen wissen?", "Index.subTitle": "Spelletjes voor de programmeurs van morgen.", "Index.moreInfo": "Informatie voor docenten...", "Index.startOver": "Wil je opnieuw beginnen?", "Index.clearData": "G<PERSON>vens wissen", "Puzzle.animal1": "Eend", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON>navel", "Puzzle.animal1HelpUrl": "https://nl.wikipedia.org/wiki/E<PERSON>en", "Puzzle.animal2": "<PERSON>", "Puzzle.animal2Trait1": "Snorharen", "Puzzle.animal2Trait2": "Vacht", "Puzzle.animal2HelpUrl": "https://nl.wikipedia.org/wiki/<PERSON>_(dier)", "Puzzle.animal3": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "Honing", "Puzzle.animal3Trait2": "Angel", "Puzzle.animal3HelpUrl": "https://nl.wikipedia.org/wiki/Bijen", "Puzzle.animal4": "<PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Slij<PERSON>", "Puzzle.animal4HelpUrl": "https://nl.wikipedia.org/wiki/Slakken", "Puzzle.picture": "afbeelding:", "Puzzle.legs": "benen:", "Puzzle.legsChoose": "kiezen...", "Puzzle.traits": "eigenschappen:", "Puzzle.error0": "Perfect\nAlle %1 blokken zijn correct.", "Puzzle.error1": "Bijna! <PERSON><PERSON> b<PERSON> is onjuist.", "Puzzle.error2": "%1 blokken zijn onjuist.", "Puzzle.tryAgain": "Het gema<PERSON><PERSON><PERSON> blok is niet correct.\nBlijf het proberen.", "Puzzle.checkAnswers": "Antwoorden controleren", "Puzzle.helpText": "<PERSON><PERSON> voor ieder dier (groen) een a<PERSON>bee<PERSON>, het aantal poten en maak een stapel van de eigenschappen van het dier.", "Maze.moveForward": "vooruit", "Maze.turnLeft": "linksaf", "Maze.turnRight": "recht<PERSON>f", "Maze.doCode": "uitvoeren", "Maze.helpIfElse": "\"als-dan\"-blokken doen het ene of het andere.", "Maze.pathAhead": "als er een pad rechtdoor is", "Maze.pathLeft": "als er een pad naar links is", "Maze.pathRight": "als er een pad naar rechts is", "Maze.repeatUntil": "<PERSON><PERSON>n to<PERSON>t", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> wordt Pegman één vakje vooruit verplaatst.", "Maze.turnTooltip": "<PERSON><PERSON><PERSON> 90 graden naar links of naar rechts.", "Maze.ifTooltip": "Als er een pad in de aangegeven richting is, voer dan wat handelingen uit.", "Maze.ifelseTooltip": "Als er een pad is in de aangegeven richting, voer dan het eerste blok van handelingen uit, anders het tweede blok.", "Maze.whileTooltip": "<PERSON><PERSON><PERSON> de ingesloten handelingen totdat het eindpunt is bereikt.", "Maze.capacity0": "U hebt %0 blokken over.", "Maze.capacity1": "U hebt %1 blok over.", "Maze.capacity2": "U hebt %2 blokken over.", "Maze.runTooltip": "<PERSON><PERSON>gt ervoor dat de speler doet wat de blokken voorschrijven.", "Maze.resetTooltip": "<PERSON>et de speler terug naar het begin van het do<PERSON>hof.", "Maze.helpStack": "Stapel een paar instructies van het type \"ga vooruit\" op elkaar en help me mijn doel te bereiken.", "Maze.helpOneTopBlock": "Op dit niveau moet u alle blokken uit de witte werkruimte opstapelen.", "Maze.helpRun": "<PERSON><PERSON>r uw programma uit en kijk wat er geb<PERSON>t.", "Maze.helpReset": "Uw programma heeft het doolhof niet opgelost. Klik op \"Opnieuw instellen\" en probeert het opnieuw.", "Maze.helpRepeat": "Computers hebben een beperkt geheugen. Ga naar het einde van dit pad door maximaal twee blokken te gebruiken. Gebruik \"herhalen\" om een blok meerdere keren uit te voeren.", "Maze.helpCapacity": "U hebt alle blokken voor dit niveau gebruikt. Om een nieuw blok te maken, moet u eerst een bestaand blok.", "Maze.helpRepeatMany": "U kunt meer dan <PERSON><PERSON> blok toevoegen binnen een \"herhalen\"-blok", "Maze.helpSkins": "Kies uw favoriete speler uit dit menu.", "Maze.helpIf": "Het blok \"als\" doet alleen iets als de voorwa<PERSON>e waar is. Probeer naar links te draaien als er een pad naar links is.", "Maze.helpMenu": "Klik op %1 in het blok \"als\" om de voorwaarde te wijzigen.", "Maze.helpWallFollow": "Kunt u dit ingewikkelde doolhof oplossen? Probeer de linkermuur te volgen. Dit is een opdracht voor gevorderde programmeurs!", "Bird.noWorm": "heeft geen worm", "Bird.heading": "richting", "Bird.noWormTooltip": "<PERSON> toestand als de vogel de worm niet heeft gekregen.", "Bird.headingTooltip": "Beweeg in de richting van de gegeven hoek: 0 is naar rechts, 90 is recht omhoog, enzovoort.", "Bird.positionTooltip": "x en y geven de positie van de vogel aan. Als x = 0, dan bevindt de vogel zich aan de linker rand, als x = 100, dan is deze aan de rechter rand. Als y = 0 is de vogel op de bodem, als y = 100 is deze bovenaan.", "Bird.helpHeading": "<PERSON><PERSON><PERSON><PERSON> de hoek van de richtingaanwijzer zo dat de vogel de worm pakt en in haar nest landt.", "Bird.helpHasWorm": "Gebruik dit blok om in één richting te gaan als de worm is gepakt, of een andere richting als de worm nog niet is gepakt.", "Bird.helpX": "\"x\" is je huidige horizontale positie. Met dit blok ga je in <PERSON><PERSON> richting als \"x\" kleiner is dan een getal, of anders in een andere richting.", "Bird.helpElse": "<PERSON><PERSON> op het pictogram om het \"als\"-blok aan te passen.", "Bird.helpElseIf": "Voor dit niveau is een \"anders-als\"-blok en een \"anders\"-blok nodig.", "Bird.helpAnd": "Het \"en\"-blok is alleen waar als beide invoeren waar zijn.", "Bird.helpMutator": "Sleep een \"anders\"-blok in het \"als\"-blok.", "Turtle.moveTooltip": "Verplaatst de schildpad vooruit of achteruit met het ingestelde aantal stappen.", "Turtle.moveForward": "aantal vooruit", "Turtle.moveBackward": "aantal achteruit", "Turtle.turnTooltip": "<PERSON><PERSON><PERSON> de schildpad naar links of naar rechts met het opgegeven aantal graden.", "Turtle.turnRight": "rechts draaien,", "Turtle.turnLeft": "links draaien", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> de breed<PERSON> van de <PERSON>.", "Turtle.setWidth": "breedte instellen op", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON> van de <PERSON>.", "Turtle.setColour": "kleur instellen op", "Turtle.penTooltip": "Tilt de pen op of zet die neer, om te stoppen of te beginnen met tekenen.", "Turtle.penUp": "pen omhoog", "Turtle.penDown": "pen omlaag", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON> de schild<PERSON> z<PERSON> of onzichtbaar (cirkel en pijl).", "Turtle.hideTurtle": "schildpad verbergen", "Turtle.showTurtle": "<PERSON><PERSON><PERSON>pad weergeven", "Turtle.printHelpUrl": "https://nl.wikipedia.org/wiki/Boekdrukkunst", "Turtle.printTooltip": "Tekent tekst in de richting van de schildpad vanaf de huidige positie.", "Turtle.print": "afdrukken", "Turtle.fontHelpUrl": "https://nl.wikipedia.org/wiki/Lettertype", "Turtle.fontTooltip": "St<PERSON>t het lettertype in voor tekst.", "Turtle.font": "lettertype", "Turtle.fontSize": "lettergrootte", "Turtle.fontNormal": "norm<PERSON>", "Turtle.fontBold": "vet", "Turtle.fontItalic": "cursief", "Turtle.submitDisabled": "Voor je programma uit tot het stopt. <PERSON><PERSON>na kan je je tekening opslaan in de galerij.", "Turtle.galleryTooltip": "<PERSON><PERSON><PERSON> met te<PERSON>en openen.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON>", "Turtle.submitTooltip": "<PERSON><PERSON> opslaan in de galerij.", "Turtle.submitMsg": "Opslaan in galerij", "Turtle.helpUseLoop": "Je oplossing werkt, maar je kunt nog tot een beter oplossing komen.", "Turtle.helpUseLoop3": "<PERSON><PERSON> de vorm met maxi<PERSON><PERSON> drie blok<PERSON>.", "Turtle.helpUseLoop4": "<PERSON><PERSON> met maxi<PERSON><PERSON> vier blokken.", "Turtle.helpText1": "Maak een programma dat een vierkant tekent.", "Turtle.helpText2": "<PERSON><PERSON><PERSON><PERSON> je programma zodat het een vijfhoek tekent in plaats van een vierkant.", "Turtle.helpText3a": "Er is een nieuw blok waarmee je de kleur kunt wijzigen:", "Turtle.helpText3b": "Teken een gele ster.", "Turtle.helpText4a": "Er is een nieuw blok waarmee je de pen van het papier kunt tillen als je beweegt:", "Turtle.helpText4b": "Teken een kleine gele ster en teken daar dan een lijn boven.", "Turtle.helpText5": "Kan je vier sterren in de vorm van een vierkant tekenen in plaats van één ster?", "Turtle.helpText6": "Teken drie gele sterren en één witte lijn.", "Turtle.helpText7": "Te<PERSON> de sterren en teken daarna vier witte lijnen.", "Turtle.helpText8": "360 witte lijnen zien eruit als een volle maan.", "Turtle.helpText9": "Kan je een zwarte cirkel toevoegen zodat de maan een halve maan wordt?", "Turtle.helpText10": "<PERSON><PERSON> wat je wilt. Je hebt een groot aantal nieuwe blokken die je kunt ontdekken. <PERSON>eel plezier!", "Turtle.helpText10Reddit": "Gebruik de knop \"<PERSON><PERSON><PERSON> bekijk<PERSON>\" om te bekijken wat anderen mensen hebben getekend. Als je iets interessants tekent, gebruik dan de knop \"Opslaan in galerij\" om je tekening te publiceren.", "Turtle.helpToolbox": "Kies een categorie om te blokken te bekijken.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "beginpunt x", "Movie.y1": "beginpunt y", "Movie.x2": "eindpunt x", "Movie.y2": "eindpunt y", "Movie.radius": "straal", "Movie.width": "breedte", "Movie.height": "hoogte", "Movie.circleTooltip": "Maakt een cirkel op de opgegeven plaats en met de opgegeven radius.", "Movie.circleDraw": "cirkel", "Movie.rectTooltip": "Maakt een rechthoek op de opgegeven plaats en met de opgegeven breedte en hoogte.", "Movie.rectDraw": "rechthoek", "Movie.lineTooltip": "Maakt een lijn van het ene punt naar het andere met de opgegeven breedte.", "Movie.lineDraw": "lijn", "Movie.timeTooltip": "<PERSON>ft de huidige tijd terug in de animatie (0-100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON> van de <PERSON>.", "Movie.setColour": "stel kleur in op", "Movie.submitDisabled": "Je filmpje beweegt niet. Gebruik blokken om iets interessants te maken. Daarna kan je je filmpje opslaan in de galerij.", "Movie.galleryTooltip": "Filmgalerij openen.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON>", "Movie.submitTooltip": "Sla je film op in de galerij.", "Movie.submitMsg": "Opslaan in galerij", "Movie.helpLayer": "Verplaats de achtergrondcirkel naar de bovenkant van je programma. Dan is het dan achter de persoon te zien.", "Movie.helpText1": "Gebruik eenvoudige vormen om deze persoon te tekenen.", "Movie.helpText2a": "Dit niveau is een film. Je wilt de arm van de persoon over het scherm laten bewegen. Klik op de afspeelknop om een voorvertoning te bekijken.", "Movie.helpText2b": "Te<PERSON><PERSON><PERSON><PERSON> de film speelt, telt de waarde van het \"tijd\"-blok van 0 tot 100. Omdat je wilt dat de y-positie van de arm begint op 0 en eindigt bij 100 zou dit makkelijk moeten zijn.", "Movie.helpText3": "Het \"tijd\"-blo<PERSON> telt van 0 tot 100. <PERSON><PERSON> nu wil je dat de y-positie van de andere arm begint bij 100 en naar 0 gaat. Kan je een eenvoudige wiskundige formule vinden die de richting omkeert?", "Movie.helpText4": "Gebruik wat je hebt geleerd in het vorige niveau om kruisende benen te vormen.", "Movie.helpText5": "De wiskundige formule voor de arm is ingewikkeld. Hier het antwoord:", "Movie.helpText6": "<PERSON><PERSON> de persoon een paar handen.", "Movie.helpText7": "Gebruik het 'als'-blok om een hoofdje voor de eerste helft van de film te tekenen. Teken daarna een groot hoofd voor de tweede helft.", "Movie.helpText8": "Laat de benen halverwege de film van richting veranderen.", "Movie.helpText9": "Teken een groter wordende cirkel achter de persoon.", "Movie.helpText10": "Maak een film<PERSON><PERSON> over wat je maar wilt. Je hebt een groot aantal nieuwe blokken tot je beschikking. <PERSON>eel plezier!", "Movie.helpText10Reddit": "Gebru<PERSON> de knop \"<PERSON><PERSON><PERSON> bekijk<PERSON>\" om de filmpjes te bekijken die andere mensen hebben gemaakt. Als je zelf een interessant filmpje hebt gemaakt, klik dan op de knop \"In galerij opslaan\" om het te delen.", "Music.playNoteTooltip": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> m<PERSON> met de opgegeven duur en toonhoogte.", "Music.playNote": "speel %1 toon %2", "Music.restTooltip": "Wacht de ingestelde tijd.", "Music.restWholeTooltip": "Wacht een hele noot.", "Music.rest": "rust %1", "Music.setInstrumentTooltip": "<PERSON><PERSON><PERSON>t naar het ingestelde instrument bij het afspelen van de volgende muzieknoten.", "Music.setInstrument": "gebruikt instrument %1", "Music.startTooltip": "<PERSON><PERSON><PERSON> de binnenblokken uit wanneer de knop 'Programma uitvoeren' is ingedrukt.", "Music.start": "wanneer %1 is ingedrukt", "Music.pitchTooltip": "<PERSON><PERSON> (C4 is 7).", "Music.firstPart": "e<PERSON>te deel", "Music.piano": "piano", "Music.trumpet": "trompet", "Music.banjo": "banjo", "Music.violin": "viool", "Music.guitar": "gitaar", "Music.flute": "fluit", "Music.drum": "trommel", "Music.choir": "koor", "Music.submitDisabled": "<PERSON><PERSON>r uw programma uit tot het stopt. <PERSON><PERSON>na kan je je muziek in de galerij zetten.", "Music.galleryTooltip": "Opent de muziekgalerij.", "Music.galleryMsg": "<PERSON><PERSON><PERSON>", "Music.submitTooltip": "Zet je muziek in de galerij.", "Music.submitMsg": "Opslaan in galerij", "Music.helpUseFunctions": "Je op<PERSON> werkt, maar deze kan beter. Gebruik functies om het aantal herhaalde coderegels te beperken.", "Music.helpUseInstruments": "De muziek klint beter als je een ander instrument in elk beginblok gebruikt.", "Music.helpText1": "Componeer de eerste vier tonen van '<PERSON><PERSON> Jacob'.", "Music.helpText2a": "<PERSON><PERSON> 'functie' laat je blokken te groeperen en dan meerma<PERSON> uitvoeren.", "Music.helpText2b": "Maak een functie om de eerste vier noten van <PERSON> af te spelen. Voer deze functie twee keer uit. Voeg geen nieuwe blokken met noten toe.", "Music.helpText3": "Maak een tweede functie voor het volgende deel van '<PERSON><PERSON>'. De laatste noot duurt langer.", "Music.helpText4": "Maak een derde functie voor het volgende deel van '<PERSON><PERSON>'. De eerste vier noten duren korter.", "Music.helpText5": "Maak het hele liedje 'Vader <PERSON>.", "Music.helpText6a": "Dit nieuwe blok laat je een ander instrument kiezen.", "Music.helpText6b": "<PERSON><PERSON><PERSON> je deuntje met een viool.", "Music.helpText7a": "<PERSON>t nieuwe blok voegt stilte toe.", "Music.helpText7b": "Maak een tweede <PERSON>b<PERSON><PERSON> met twee s<PERSON><PERSON><PERSON><PERSON><PERSON>, dat daarna ook '<PERSON><PERSON>' volledig afs<PERSON>t.", "Music.helpText8": "Elk beginblok moet 'V<PERSON>' tweemaal laten afspelen.", "Music.helpText9": "Maak vier beginblokken die elk 'Vader Jacob' twee keer afspelen. Voeg het juiste aantal stilteblokken toe.", "Music.helpText10": "Componeer wat je wilt. Je hebt een enorm aantal nieuwe blokken om te onderzoeken. <PERSON>eel plezier!", "Music.helpText10Reddit": "Gebruik de 'Zie galerij'-knop om te zien wat ondere mensen hebben gecomponeerd. Als u iets interessants hebt gecomponeerd, gebruik de 'Zet in galerij'-knop om het te publiceren.", "Pond.scanTooltip": "<PERSON>k naar vijanden. <PERSON><PERSON> een richting op (0-360). Geeft de afstand tot de dichtstbijzijnde vijand in die richting. Geeft oneindig op als er geen vijand te vinden is.", "Pond.cannonTooltip": "<PERSON>uur het kanon af. <PERSON><PERSON> een rich<PERSON> (0-360) en een bereik op (0-70).", "Pond.swimTooltip": "Zwem vooruit. <PERSON><PERSON> een rich<PERSON> (0-360) op.", "Pond.stopTooltip": "Stop met zwemmen. De speler komt langzaam tot stilstand.", "Pond.healthTooltip": "Geeft de huidige levenspunten van de speler terug (0 is dood, 100 is gezond).", "Pond.speedTooltip": "Geeft de huidige snelheid van de speler terug (0 is staat stil, 100 is volle snelheid).", "Pond.locXTooltip": "Geeft het X-coördinaat van de speler terug (0 is de linkerkant, 100 is de rechterkant).", "Pond.locYTooltip": "Geeft het Y-coördinaat van de speler terug (0 is de onderkant, 100 is de bovenkant).", "Pond.logTooltip": "Drukt een nummer af op de console van je browser.", "Pond.docsTooltip": "Documentatie van de programmeertaal weergeven.", "Pond.documentation": "Documentatie", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON>", "Pond.scaredName": "<PERSON>", "Pond.helpUseScan": "Je op<PERSON> werkt, maar je kunt het beter doen. Gebruik \"scan\" om het kanon te vertellen hoe ver het moet schieten.", "Pond.helpText1": "Gebruik het commando \"cannon\" om op het doel te schieten. De eerste parameter is de hoek, de tweede parameter is het bereik. <PERSON><PERSON> de juiste combinatie.", "Pond.helpText2": "Dit doel moet meerdere keren geraakt worden. Gebruik de lus \"while (true)\" om iets voor onbepaalde tijd uit te voeren.", "Pond.helpText3a": "Deze tegenstander beweegt heen en weer, waardoor die moeilijk te raken is. Het commando \"scan\" geeft de exacte afstand en de richting naar de tegenstander aan.", "Pond.helpText3b": "<PERSON><PERSON> be<PERSON><PERSON> is precies wat het commando \"cannon\" nodig heeft om nauwkeurig te vuren.", "Pond.helpText4": "Deze tegenstander is te ver weg om het kanon gebruiken (dat heeft een limiet van 70 meter). <PERSON><PERSON><PERSON><PERSON> in plaats daarvan het commando \"swim\" opdracht om te beginnen met <PERSON><PERSON><PERSON><PERSON> in de richting van de tegenstander en er tegenaan te botsen.", "Pond.helpText5": "De<PERSON> tegenstander is ook te ver weg om het kanon te gebruiken. <PERSON>ar je bent te zwak om een botsing te overleven. Zwem in de richting van de tegenstander tot de horizontale afstand kleiner is dan 50. \"stop\" dan en gebruik het kanon.", "Pond.helpText6": "Deze tegenstander zwemt weg als die geraakt wordt. Zwemmen in de richting van de tegenstander als deze zich buiten het bereik bevindt (70 meter).", "Gallery": "<PERSON><PERSON><PERSON>"}