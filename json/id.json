{"@metadata": {"authors": ["AJBot2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hidayatsrf", "Kasimtan", "Mir<PERSON>", "Veracious", "WongKentir"]}, "Games.name": "Permainan <PERSON>ly", "Games.puzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "Burung", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Mu<PERSON>", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "Kolam", "Games.linesOfCode1": "Anda menyelesaikan level ini dengan 1 baris JavaScript:", "Games.linesOfCode2": "Anda menyelesaikan level ini dengan %1 baris JavaScript:", "Games.nextLevel": "Apakah Anda siap untuk level %1?", "Games.finalLevel": "<PERSON><PERSON><PERSON><PERSON> Anda siap untuk tantangan berikutnya?", "Games.submitTitle": "Judul:", "Games.linkTooltip": "Simpan dan link ke blok.", "Games.runTooltip": "Jalankan program yang Anda tulis.", "Games.runProgram": "Jalankan Program", "Games.resetTooltip": "Hentikan program dan me-reset level.", "Games.resetProgram": "Reset", "Games.help": "Bantuan", "Games.catLogic": "Logika", "Games.catLoops": "Perulangan", "Games.catMath": "Matematika", "Games.catText": "Teks", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Variabel", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "<PERSON> masalah dengan permintaan.", "Games.linkAlert": "Bagikan blok Anda dengan link ini:\n\n%1", "Games.hashError": "<PERSON><PERSON>, '%1' tidak sesuai dengan program yang tersimpan.", "Games.xmlError": "Tidak dapat memuat file Anda yang tersimpan. Mungkin itu dibuat dengan versi yang berbeda dari <PERSON>?", "Games.submitted": "Te<PERSON> kasih untuk program ini! Jika staf kera ahli kami menyukainya, mereka akan menerbitkannya ke galeri dalam waktu beberapa hari.", "Games.listVariable": "list", "Games.textVariable": "teks", "Games.breakLink": "Sekali Anda mengedit JavaScript, Anda tidak dapat kembali mengedit blok. OK?", "Games.blocks": "Blok", "Games.congratulations": "Selamat!", "Games.helpAbort": "Tingkat ini sangat sulit. Apakah Anda ingin melewatkannya dan melanjutkan ke permainan berikutnya? Anda selalu bisa kembali lagi nanti.", "Index.clear": "Hapus semua solusi <PERSON>?", "Index.subTitle": "Permainan untuk programer masa depan", "Index.moreInfo": "Info untuk pengajar...", "Index.startOver": "Ulangi dari awal?", "Index.clearData": "Hapus data", "Puzzle.animal1": "Be<PERSON>", "Puzzle.animal1Trait1": "Bulu (ungas)", "Puzzle.animal1Trait2": "Paru<PERSON>", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Kucing", "Puzzle.animal2Trait1": "<PERSON><PERSON>", "Puzzle.animal2Trait2": "Bulu (mamalia)", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "Lebah", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "Penyengat", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "Siput", "Puzzle.animal4Trait1": "Tempurung", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "gambar:", "Puzzle.legs": "kaki:", "Puzzle.legsChoose": "pilih...", "Puzzle.traits": "ciri-ciri:", "Puzzle.error0": "Sempurna!\nSemua %1 blok sudah benar.", "Puzzle.error1": "Ham<PERSON>r! Satu blok tidak benar.", "Puzzle.error2": "%1 blok tidak benar.", "Puzzle.tryAgain": "Blok disorot tidak benar.\n<PERSON><PERSON> be<PERSON>.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON>", "Puzzle.helpText": "<PERSON><PERSON><PERSON> masing-ma<PERSON> hewan (hijau), <PERSON><PERSON><PERSON> g<PERSON> , pilih jumlah ka<PERSON>, dan buat tumpukan ciri-ciri nya.", "Maze.moveForward": "bergerak maju", "Maze.turnLeft": "belok kiri", "Maze.turnRight": "belok kanan", "Maze.doCode": "do", "Maze.helpIfElse": "If-else blok akan melakukan satu hal atau yang lain.", "Maze.pathAhead": "if path ahead", "Maze.pathLeft": "if path to the left", "Maze.pathRight": "if path to the right", "Maze.repeatUntil": "repeat until", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> pemain ke depan satu ruang.", "Maze.turnTooltip": "Putar pemain ke kiri atau kanan 90 derajat.", "Maze.ifTooltip": "<PERSON><PERSON> ada jalan ke arah tertentu, kemudian lakukan beberapa tindakan.", "Maze.ifelseTooltip": "<PERSON>ka ada jalan ke arah tertentu, kem<PERSON>an lakukan blok pertama dari tindakan. <PERSON><PERSON> tidak, lakukan blok kedua dari tindakan.", "Maze.whileTooltip": "Ulangi tindakan tertutup sampai titik finish tercapai.", "Maze.capacity0": "Anda memiliki %0 blok tersisa.", "Maze.capacity1": "Anda memiliki %1 blok tersisa.", "Maze.capacity2": "Anda memiliki %2 blok tersisa.", "Maze.runTooltip": "<PERSON><PERSON>t pemain melakukan apa yang blok katakan.", "Maze.resetTooltip": "Tempatkan pemain kembali pada awal labirin.", "Maze.helpStack": "<PERSON><PERSON><PERSON> beberapa 'move forward' blok bersama-sama untuk membantu saya mencapai tujuan.", "Maze.helpOneTopBlock": "<PERSON><PERSON> tingkat ini, <PERSON><PERSON> perlu <PERSON>uk bersama-sama semua blok di ruang kerja putih.", "Maze.helpRun": "Jalankan program Anda untuk melihat apa yang terjadi.", "Maze.helpReset": "Program Anda tidak memecahkan labirin. <PERSON><PERSON> '<PERSON><PERSON>' dan coba lagi.", "Maze.helpRepeat": "Capai ujung jalan ini hanya menggunakan dua blok. <PERSON><PERSON><PERSON> 'repeat' untuk menjalankan blok lebih dari sekali.", "Maze.helpCapacity": "Anda telah menggunakan semua blok untuk tingkat ini. Untuk membuat blok baru, <PERSON><PERSON> harus terlebih dahulu menghapus blok yang ada.", "Maze.helpRepeatMany": "Anda dapat memuat lebih dari satu blok di dalam blok 'repeat'.", "Maze.helpSkins": "<PERSON><PERSON><PERSON> pemain favorit Anda dari menu ini.", "Maze.helpIf": "Se<PERSON>ah blok 'if' akan melakukan sesuatu hanya jika kondisi benar. Coba belok kiri jika ada jalan ke kiri.", "Maze.helpMenu": "Klik pada %1 di blok 'if' untuk mengubah kondisinya.", "Maze.helpWallFollow": "Dapatkah Anda memecahkan labirin yang rumit ini? Cobalah mengikuti dinding tangan-kiri. Para pemrogram tingkat lanjut saja!", "Bird.noWorm": "tidak memiliki cacing", "Bird.heading": "arah", "Bird.noWormTooltip": "Kondisi ketika burung itu belum mendapatkan cacing.", "Bird.headingTooltip": "Bergerak ke arah sudut yang diberikan: 0 adalah ke kanan, 90 adalah lurus ke atas, dll", "Bird.positionTooltip": "x dan y menandai posisi burung. Ketika x = 0 burung dekat tepi kiri, ketika x = 100 burung dekat tepi kanan. Ketika y = 0 burung di bagian bawah, ketika y = 100 burung di atas.", "Bird.helpHeading": "Mengubah sudut tujuan untuk membuat burung mendapatkan cacing dan tanah di sarangnya.", "Bird.helpHasWorm": "Gunakan blok ini untuk pergi dalam satu arah jika Anda memiliki cacing, atau arah yang berbeda jika Anda tidak memiliki cacing.", "Bird.helpX": "'x' posisi horizontal Anda saat ini. Gunakan blok ini untuk pergi dalam satu arah jika 'x' kurang dari satu nomor, atau arah yang berbeda sebaliknya.", "Bird.helpElse": "Klik ikon untuk memodifikasi blok 'if'.", "Bird.helpElseIf": "Tingkat ini membutuhkan baik sebuah 'else if' dan blok 'else'.", "Bird.helpAnd": "The 'and' block benar hanya jika kedua masukan-nya benar.", "Bird.helpMutator": "<PERSON><PERSON> blok 'else' ke dalam blok 'if'.", "Turtle.moveTooltip": "<PERSON><PERSON><PERSON><PERSON> kura-kura maju atau mundur dengan jumlah yang ditentukan.", "Turtle.moveForward": "bergerak maju dengan", "Turtle.moveBackward": "berger<PERSON> mundur dengan", "Turtle.turnTooltip": "Putar kura-kura ke kiri atau kanan dengan jumlah derajat tertentu.", "Turtle.turnRight": "putar ke kanan dengan", "Turtle.turnLeft": "putar ke kiri dengan", "Turtle.widthTooltip": "<PERSON><PERSON> lebar pena.", "Turtle.setWidth": "set lebar ke", "Turtle.colourTooltip": "<PERSON><PERSON> warna pena.", "Turtle.setColour": "set warna ke", "Turtle.penTooltip": "<PERSON><PERSON> atau turunkan pena, men<PERSON><PERSON><PERSON>n atau memulai menggambar.", "Turtle.penUp": "angkat pena", "Turtle.penDown": "turunkan pena", "Turtle.turtleVisibilityTooltip": "Membuat kura-kura (lingkaran dan panah) terlihat atau tidak terlihat.", "Turtle.hideTurtle": "sembunyikan kura-kura", "Turtle.showTurtle": "tampilkan kura-kura", "Turtle.printTooltip": "<PERSON><PERSON><PERSON><PERSON> teks ke arah kura-kura pada lokasinya", "Turtle.print": "cetak", "Turtle.fontTooltip": "Set font yang digunakan oleh blok cetak.", "Turtle.font": "font", "Turtle.fontSize": "ukuran font", "Turtle.fontNormal": "normal", "Turtle.fontBold": "bold", "Turtle.fontItalic": "italic", "Turtle.submitDisabled": "Jalankan program anda sampai berhenti. Kemudian <PERSON><PERSON> dapat mengirimkan gambar Anda ke galeri.", "Turtle.galleryTooltip": "<PERSON><PERSON><PERSON><PERSON> galeri gambar.", "Turtle.galleryMsg": "<PERSON><PERSON>", "Turtle.submitTooltip": "<PERSON><PERSON> gambar <PERSON>a ke galeri.", "Turtle.submitMsg": "<PERSON><PERSON> ke <PERSON>", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON>, tetapi masih ada solusi yang lebih baik.", "Turtle.helpUseLoop3": "Gambar bentuk dengan hanya tiga blok.", "Turtle.helpUseLoop4": "<PERSON><PERSON><PERSON> bintang dengan hanya empat blok.", "Turtle.helpText1": "Buat program yang menggambar persegi.", "Turtle.helpText2": "Ubah program Anda untuk menggambar pentagon bukannya persegi.", "Turtle.helpText3a": "Ada sebuah blok baru yang memungkinkan Anda untuk mengubah warna:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON> bintang berwarna kuni<PERSON>.", "Turtle.helpText4a": "Ada sebuah blok baru yang memungkinkan Anda untuk mengangkat pena dari kertas saat Anda bergerak:", "Turtle.helpText4b": "<PERSON><PERSON><PERSON> bintang kecil ber<PERSON><PERSON> kuni<PERSON>, kem<PERSON>an gambar sebuah garis di atasnya.", "Turtle.helpText5": "Sebagai pengganti satu bintang, bisa<PERSON><PERSON> <PERSON>a menggambar empat bintang diatur dalam persegi?", "Turtle.helpText6": "Gambar tiga bintang kuning, dan satu garis putih.", "Turtle.helpText7": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> bintang, kemudian gambar empat garis putih.", "Turtle.helpText8": "Gambar 360 garis putih yang akan terlihat seperti bulan purnama.", "Turtle.helpText9": "Anda dapat menambahkan lingkaran hitam sehingga bulan menjadi bulan sabit?", "Turtle.helpText10": "<PERSON><PERSON><PERSON> apapun yang <PERSON>a ing<PERSON>. Anda punya sejumlah besar blok baru yang dapat dijelajahi. Selamat bermain!", "Turtle.helpText10Reddit": "<PERSON>aka<PERSON> tombol \"<PERSON><PERSON> Galeri\" untuk melihat apa yang orang lain telah gambar. <PERSON><PERSON> Anda menggambar sesuatu yang menarik, gunakan tombol  \"<PERSON><PERSON> ke <PERSON>\" untuk mempublikasikannya.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON> kategori untuk melihat blok.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "start x", "Movie.y1": "start y", "Movie.x2": "end x", "Movie.y2": "end y", "Movie.radius": "radius", "Movie.width": "lebar", "Movie.height": "tinggi", "Movie.circleTooltip": "Gambar lingkaran pada lokasi tertentu dan dengan radius tertentu.", "Movie.circleDraw": "<PERSON><PERSON><PERSON><PERSON>", "Movie.rectTooltip": "Gambar empat persegi panjang pada lokasi tertentu dan dengan lebar dan tinggi tertentu.", "Movie.rectDraw": "persegi panjang", "Movie.lineTooltip": "G<PERSON>bar garis dari satu titik ke lainnya dengan lebar tertentu.", "Movie.lineDraw": "garis", "Movie.timeTooltip": "Ke<PERSON>lik<PERSON> waktu saat ini dalam animasi (0-100)", "Movie.colourTooltip": "<PERSON><PERSON> warna pena.", "Movie.setColour": "set warna ke", "Movie.submitDisabled": "Film Anda tidak bergerak. Gunakan blok untuk membuat sesuatu yang menarik. Kemudian Anda dapat mengirimkan film Anda ke galeri.", "Movie.galleryTooltip": "<PERSON><PERSON><PERSON><PERSON> galeri film.", "Movie.galleryMsg": "<PERSON><PERSON>", "Movie.submitTooltip": "<PERSON><PERSON> film anda.", "Movie.submitMsg": "<PERSON><PERSON> ke <PERSON>", "Movie.helpLayer": "Gerakkan lingkaran latar belakang ke atas program <PERSON><PERSON>. <PERSON><PERSON><PERSON>, lingkaran akan muncul di belakang orang.", "Movie.helpText1": "<PERSON><PERSON><PERSON> bentuk sederhana untuk menggambar orang ini.", "Movie.helpText2a": "Level ini adalah sebuah film. Anda ingin lengan orang ini untuk bergerak sepanjang layar. <PERSON><PERSON> play untuk lihat tampilan.", "Movie.helpText2b": "Selagi filmnya diputar, nilai blok 'time' be<PERSON><PERSON> dari 0 hingga 100. Ini memudahkan Anda untuk menempatkan posisi \"y\" lengan mulai dari 0 hingga 100.", "Movie.helpText3": "Blok 'time' be<PERSON><PERSON> dari 0 hingga 100. <PERSON><PERSON><PERSON> se<PERSON>ng <PERSON>a ingin menempatkan posisi \"y\" lengan lain mulai dari 100 hingga 0. Da<PERSON>tkah Anda pikirkan rumus matematika sederhana untuk memutarbalikkan arah lengan?", "Movie.helpText4": "Gunakan cara yang telah Anda pelajari di level sebelumnya untuk membuat lengan menyilang.", "Movie.helpText5": "Rumus matematika untuk lengan agak rumit. <PERSON><PERSON><PERSON>:", "Movie.helpText6": "<PERSON>ri orang itu beberapa tangan.", "Movie.helpText7": "<PERSON><PERSON><PERSON> blok 'if' untuk menggambar kepala kecil untuk pertengahan pertama film. <PERSON><PERSON><PERSON> gam<PERSON>an kepala besar untuk pertengahan kedua film.", "Movie.helpText8": "Buatlah lengan berbalik arah di tengah film.", "Movie.helpText9": "Gambarlah lingkaran yang melebar di belakang orang tersebut.", "Movie.helpText10": "Buat film apapun yang <PERSON>a ing<PERSON>. Anda punya sejumlah besar blok baru yang dapat dijelajahi. Selamat bermain!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON> tombol \"<PERSON><PERSON> Galeri\" untuk melihat film-film yang telah dibuat orang lain. <PERSON>ka Anda telah membuat sebuah film menarik, gunakan tombol \"<PERSON><PERSON> ke <PERSON>\" untuk mempublikasikannya.", "Music.playNoteTooltip": "<PERSON><PERSON>inkan satu nada musik dengan waktu dan tinggi nada yang ditentukan.", "Music.playNote": "mainkan %1 nada %2", "Music.restTooltip": "Menunggu untuk durasi yang ditentukan.", "Music.restWholeTooltip": "Menunggu untuk 1 nada.", "Music.rest": "istirahat %1", "Music.setInstrumentTooltip": "<PERSON><PERSON><PERSON> ke instrumen yang dipilih ketika memainkan nada musik berikutnya.", "Music.setInstrument": "pilih instrumen %1", "Music.startTooltip": "Menjalankan blok di dalam ketika tombol \"Jalankan Program\" diklik.", "Music.start": "ketika %1 diklik", "Music.pitchTooltip": "1 not (C4 adalah 7).", "Music.firstPart": "<PERSON>ian pertama", "Music.piano": "piano", "Music.trumpet": "terompet", "Music.banjo": "banjo", "Music.violin": "biola", "Music.guitar": "gitar", "Music.flute": "seruling", "Music.drum": "drum", "Music.choir": "kor", "Music.submitDisabled": "Jalankan program Anda sampai berhen<PERSON>. Kemudian <PERSON><PERSON> dapat mengirimkan musik Anda ke galeri.", "Music.galleryTooltip": "<PERSON><PERSON><PERSON><PERSON> galeri musik.", "Music.galleryMsg": "<PERSON><PERSON>", "Music.submitTooltip": "<PERSON><PERSON> musik <PERSON> ke galeri.", "Music.submitMsg": "<PERSON><PERSON> ke <PERSON>", "Music.helpUseFunctions": "<PERSON><PERSON><PERSON>, namun <PERSON>a bisa lebih baik. <PERSON><PERSON><PERSON> fungsi untuk mengurangi kode yang diulangi.", "Music.helpUseInstruments": "Musik akan lebih baik jika Anda menggunakan instrumen yang berbeda pada setiap blok mulai.", "Music.helpText1": "Buat empat nada pertama \"Frère Jacques\".", "Music.helpText2a": "<PERSON><PERSON><PERSON> \"fungsi\" memu<PERSON><PERSON><PERSON> <PERSON>a men<PERSON>kkan blok-blok, kem<PERSON>an menja<PERSON><PERSON>ya lebih dari sekali.", "Music.helpText2b": "Buat fungsi untuk memainkan empat nada pertama \"Fr<PERSON>\". Jalankan fungsi itu dua kali. <PERSON>an tambahkan blok nada baru.", "Music.helpText3": "<PERSON>uat fungsi kedua untuk bagian \"<PERSON><PERSON>\" se<PERSON>. Nada terakhir lebih panjang.", "Music.helpText4": "Buat fungsi ketiga untuk bagian \"Frère <PERSON>. Empat nada pertama lebih pendek.", "Music.helpText5": "<PERSON><PERSON><PERSON><PERSON> lagu penuh '<PERSON><PERSON>'.", "Music.helpText6a": "Blok baru ini memungkinkan Anda mengganti instrumen.", "Music.helpText6b": "Mainkan lagu Anda dengan biola.", "Music.helpText7a": "Blok baru ini menambahkan jeda tanpa suara.", "Music.helpText7b": "<PERSON><PERSON>t blok mulai kedua yang memiliki dua blok jeda, yang juga memainkan '<PERSON><PERSON>'.", "Music.helpText8": "<PERSON><PERSON>p blok mulai se<PERSON><PERSON><PERSON> me<PERSON> \"<PERSON><PERSON>\" dua kali.", "Music.helpText9": "Buat empat blok mulai yang masing-masing mema<PERSON>an '<PERSON><PERSON>' dua kali. Tambahkan jumlah blok jeda yang benar.", "Music.helpText10": "Buat apapun yang <PERSON>a ing<PERSON>. Anda punya sejumlah besar blok baru yang dapat dijelajahi. Selamat bermain!", "Music.helpText10Reddit": "<PERSON><PERSON><PERSON> tombol \"<PERSON><PERSON>\" untuk melihat musik yang telah dibuat orang lain. <PERSON>ka Anda membuat musik yang menarik, gunakan tombol \"<PERSON><PERSON> ke <PERSON>\" untuk mempublikasikannya.", "Pond.scanTooltip": "Amati musuh. Ten<PERSON>kan arah (0-360). Kembalikan jarak ke musuh terdekat dari arah tersebut. Kembalikan Infinity (tak terhingga) jika musuh tidak ditemukan.", "Pond.cannonTooltip": "Lepas tembakan meriam. Tentu<PERSON> arah (0-360) dan jarak (0-70).", "Pond.swimTooltip": "Berenang kedepan. Tentukan arah (0-360).", "Pond.stopTooltip": "<PERSON><PERSON><PERSON><PERSON> berenang. <PERSON><PERSON><PERSON> akan bergerak perlahan hingga berhenti.", "Pond.healthTooltip": "Kembalikan tingkat kesehatan pemain (0 mati, 100 sehat).", "Pond.speedTooltip": "Kembalikan tingkat kecepatan pemain (0 berhenti, 100 kecepatan penuh).", "Pond.locXTooltip": "Kembalikan koordinat X dari pemain (0 sisi kiri, 100 sisi kanan).", "Pond.locYTooltip": "Ke<PERSON>likan koordinat Y dari pemain (0 sis bawah, 100 sisi atas).", "Pond.logTooltip": "Mencetak angka ke konsol browser Anda.", "Pond.docsTooltip": "<PERSON><PERSON><PERSON><PERSON> dokumentasi bahasa.", "Pond.documentation": "Dokumentasi", "Pond.playerName": "Pemutar media", "Pond.targetName": "Target", "Pond.pendulumName": "Bandul", "Pond.scaredName": "Penakut", "Pond.helpUseScan": "<PERSON><PERSON><PERSON>, tetapi masih ada solusi yang lebih baik. Gunakan 'scan' untuk arahkan tembakan meriam.", "Pond.helpText1": "<PERSON><PERSON><PERSON> perintah 'cannon' untuk tembak target. Parameter pertama adalah sudut, parameter kedua adalah jarak. <PERSON><PERSON><PERSON> kombinasi yang tepat.", "Pond.helpText2": "Target ini harus ditembak berkali-kali. <PERSON><PERSON><PERSON> per<PERSON>ngan 'while (true)' untuk melakukan sesuatu tanpa batas.", "Pond.helpText3a": "<PERSON><PERSON>h ini bergerak mondar-mandir, se<PERSON>ga susah di<PERSON>. Ekspresi 'scan' mengembalikan jarak musuh pada arah tertentu.", "Pond.helpText3b": "Jarak ini adalah nilai yang dibutuhkan perintah 'cannon' untuk menembak secara tepat.", "Pond.helpText4": "<PERSON><PERSON>h ini terlalu jauh dari jang<PERSON>uan meriam (batas 70 meter). <PERSON><PERSON><PERSON>, gunakan perintah 'swim' untuk berenang ke arah musuh dan kemudian menabrak musuh.", "Pond.helpText5": "<PERSON><PERSON><PERSON> ini terlalu jauh dari jang<PERSON>uan meriam. <PERSON>tapi Anda terlalu lemah untuk menabraknya. Berenanglah mendekatinya ketika lokasi horizontal <PERSON><PERSON> 50. <PERSON><PERSON><PERSON> 'stop' dan gunakan meriam.", "Pond.helpText6": "<PERSON><PERSON><PERSON> ini akan bergerak menjauh jika ditembak. Berenanglah mendekatinya jika diluar jang<PERSON>uan meriam (70 meter).", "Gallery": "<PERSON><PERSON>"}