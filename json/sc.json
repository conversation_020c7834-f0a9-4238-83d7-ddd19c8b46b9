{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Via maxima"]}, "Games.name": "<PERSON><PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "Labirint<PERSON>", "Games.bird": "<PERSON><PERSON><PERSON>", "Games.turtle": "Tostoinu", "Games.movie": "Filmi", "Games.pond": "<PERSON><PERSON><PERSON>", "Games.linesOfCode1": "As arresòlviu custu livellu cun 1 arriga de JavaScript:", "Games.linesOfCode2": "As arresòlviu custu livellu cun %1 arrigas de JavaScript:", "Games.nextLevel": "Prontu ses po su livellu %1?", "Games.finalLevel": "Prontu ses po un'àteru disafiu?", "Games.linkTooltip": "Sarva e alliòngia a is brocus.", "Games.runTooltip": "<PERSON><PERSON><PERSON>a su programa chi as scritu.", "Games.runProgram": "<PERSON><PERSON><PERSON><PERSON><PERSON> su <PERSON>a", "Games.resetTooltip": "Firma su programa e anudda su livellu.", "Games.resetProgram": "Reset", "Games.help": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Lògica", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Matemàtica", "Games.catText": "<PERSON><PERSON>", "Games.catLists": "Lista", "Games.catColour": "Colori", "Games.catVariables": "Varia<PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "Ddui fut unu problema cun sa pregunta", "Games.linkAlert": "Poni is brocus tuus in custu acàpiu:\n\n%1", "Games.hashError": "Mi dispraxit, '%1' non torrat a pari cun nimancu unu de is programas sarvaus.", "Games.xmlError": "Non potzu carrigai su file sarvau. Fortzis est stètiu fatu cun d-una versioni diferenti de Blockly?", "Games.listVariable": "lista", "Games.textVariable": "testu", "Games.blocks": "B<PERSON>cus", "Games.congratulations": "Bravu!", "Games.helpAbort": "Custu livellu est difìcili meda. <PERSON><PERSON> andai a fai un'àteru giogu? Podis torrai a custu prus a tardu si bolis.", "Index.clear": "Scancellu totu is solutzionis tuas?", "Index.subTitle": "<PERSON><PERSON><PERSON> po is programadoris de cras.", "Index.moreInfo": "Àteras informatzionis...", "Index.startOver": "<PERSON><PERSON> torrai a cumentzai?", "Index.clearData": "<PERSON><PERSON><PERSON> da<PERSON>", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Bicu", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Gatu", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "Sintzigorru", "Puzzle.animal4Trait1": "Crox<PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "màgini:", "Puzzle.legs": "cambas:", "Puzzle.legsChoose": "scebera...", "Puzzle.traits": "calidadis:", "Puzzle.error0": "Perfetu!\nSu %1 de is brocus funt curretus.", "Puzzle.error1": "Giai-giai! Unu brocu est de curregi.", "Puzzle.error2": "%1 brocus funt de curregi.", "Puzzle.tryAgain": "Su brocu marcau est de curregi.\nProvanci.", "Puzzle.checkAnswers": "Castia Arrespusta", "Puzzle.helpText": "Po dònnia animali (birdi), poni sa màgini sua, scebera su numeru de cambas, e apilla is calidadis.", "Maze.moveForward": "movi ananti", "Maze.turnLeft": "furria a manu manca", "Maze.turnRight": "furria a manu dereta", "Maze.doCode": "fai", "Maze.helpIfElse": "Su brocu si-sinuncas fait una cosa o s'àtera.", "Maze.pathAhead": "si tenis caminu ananti", "Maze.pathLeft": "si tenis caminu a manu manca", "Maze.pathRight": "si tenis caminu a manu dereta", "Maze.repeatUntil": "repiti fintzas", "Maze.moveForwardTooltip": "Movi su giogadori ananti de unu", "Maze.turnTooltip": "Furria su giogadori a manu manca o dereta de 90 gradus.", "Maze.ifTooltip": "Si ddu est unu caminu anca as nau, insaras fai su de fai.", "Maze.ifelseTooltip": "Si tenis caminu in d-una andada, fai su primu brocu de atzionis. Sinuncas, fai su segundu brocu de atzionis.", "Maze.whileTooltip": "Repiti s'atzioni sceberada fintzas a lompi.", "Maze.capacity0": "Abarrant %0 brocus.", "Maze.capacity1": "Abarrant %1 brocus.", "Maze.capacity2": "Abarrant %2 brocus.", "Maze.runTooltip": "Lassa fai a su giogadori su chi nant is brocus.", "Maze.resetTooltip": "Torra a ponni su giogadori a su comintzu de su labirintu.", "Maze.helpStack": "Apilla pariga de brocus 'movi ananti' po m'agiudai a fai su chi depu.", "Maze.helpOneTopBlock": "In custu livellu, depis apillai a pari totu is brocus in s'area bianca.", "Maze.helpRun": "Arròllia su programa po biri ita fait.", "Maze.helpReset": "Su programa no arrèsolvit su labirintu. Craca 'Reset' e torra a provai.", "Maze.helpRepeat": "Arriba a sa fini de custu caminu cun duus brocus sceti. Impera 'repit' po fai arrolliai unu brocu prus de una borta.", "Maze.helpCapacity": "As imperau totu is brocus po custu livellu. Po tenni unu brocu nou, ndi depis bogai unu innantis.", "Maze.helpRepeatMany": "Podis ponni prus de unu brocu aintru de su brocu'repiti'.", "Maze.helpSkins": "Scebera de custu menu su giogadori chi preferis.", "Maze.helpIf": "Su brocu 'si' fait calincuna cosa sceti si sa cunditzioni est bera. Prova a furriai a manu manca si nci est unu caminu a manu manca.", "Maze.helpMenu": "Craca in %1 in su brocu 'si' po mudai sa cunditzioni.", "Maze.helpWallFollow": "Si dda fais a arrèsolvi custu labirintu introbeddau? Prova sighendi su muru a manu manca. Po programadoris espertus sceti!", "Bird.noWorm": "non portat bremi", "Bird.heading": "faci a", "Bird.noWormTooltip": "Sa conditzioni candu su pilloni non portat su bremi", "Bird.headingTooltip": "Movi tirendi faci a s'angulu postu: 0 est a manu manca, 90 est a innantis, etc.", "Bird.positionTooltip": "x e y marcant sa posidura de su pilloni. Candu x = 0 su pilloni est accanta a s'oru de manca, candu x = 100 est acanta a s'oru de deretta. Candu y = 0 est in basciu in basciu, candu y = 100 est in susu totu.", "Bird.helpHeading": "Muda s'angulu a manera chi su pilloni ndi potzat pigai su bremi e lompi a su niu.", "Bird.helpHasWorm": "Impera custu brocu po tirai a una parti si portas su bremi o a un'àtera si no ddu portas.", "Bird.helpX": "'x' est sa posidura orizontali currenti. Impera custu brocu po tirai a una parti si 'x' est prus piticu de unu numeru o sinuncas a un'àtera.", "Bird.helpElse": "Clica s'incona po mudai su brocu 'si'", "Bird.helpElseIf": "A custu livellu serbint unu brocu 'sinuncas si' e unu 'sinuncas' puru.", "Bird.helpAnd": "Su brocu 'e' est beridadi sceti si ambadùs is inputs funt beridadi.", "Bird.helpMutator": "Traga unu brocu 'sinuncas' aintru de su brocu 'si'.", "Turtle.moveTooltip": "Movit su tostoinu ananti o asegus de su tanti inditau.", "Turtle.moveForward": "movi ananti de", "Turtle.moveBackward": "movi asegus de", "Turtle.turnTooltip": "Furria su tostoinu a manu manca o dereta de su numeru de gradus inditaus.", "Turtle.turnRight": "furria a manu dereta de", "Turtle.turnLeft": "furria a manu manca de", "Turtle.widthTooltip": "Muda sa largària de sa pinna.", "Turtle.setWidth": "imposta sa largària a", "Turtle.colourTooltip": "Muda su colori de sa pinna.", "Turtle.setColour": "imposta su colori a", "Turtle.penTooltip": "Àrtzia o cala sa pinna, po acabai o cumentzai a disenniai.", "Turtle.penUp": "pinna artziada", "Turtle.penDown": "pinna calada", "Turtle.turtleVisibilityTooltip": "Fait su tostoinu (circu cun sa frícia), visibili o invisìbili.", "Turtle.hideTurtle": "cua su tostoinu", "Turtle.showTurtle": "amosta su tostoinu", "Turtle.printTooltip": "Poni su testu anca est postu e anca est tirendi su tostoinu.", "Turtle.print": "imprenta", "Turtle.fontTooltip": "Imposta is literas imperadas de is brocus de imprenta.", "Turtle.font": "font (literas)", "Turtle.fontSize": "font (mesura)", "Turtle.fontNormal": "normal", "Turtle.fontBold": "bold", "Turtle.fontItalic": "italic", "Turtle.submitDisabled": "Arròllia su programa tuu fintzas a candu si firmat. <PERSON>pustis as a podi ponni su disinnu tuu in sa galleria.", "Turtle.galleryTooltip": "Aberi sa galleria de is disinnus in Reddit.", "Turtle.galleryMsg": "Castia Galleria", "Turtle.submitTooltip": "Poni su disinnu tuu in Reddit.", "Turtle.submitMsg": "Poni in sa Galleria", "Turtle.helpUseLoop": "Sa faina tua funtzionat, ma podis fai mellus.", "Turtle.helpUseLoop3": "Trassa sa figura un tres brocus sceti.", "Turtle.helpUseLoop4": "Trassa su steddu cun cuatru brocus sceti.", "Turtle.helpText1": "Cuncorda unu programa chi trassit unu cuadru.", "Turtle.helpText2": "Muda su programa po trassai unu pentagunu intamis de unu cuadru.", "Turtle.helpText3a": "Nci est unu brocu nou chi ti permitit de mudai su colori:", "Turtle.helpText3b": "Trassa unu steddu grogu.", "Turtle.helpText4a": "Nci est unu brocu nou chi ti permitit de artziai sa pinna de su paperi candu ti movis:", "Turtle.helpText4b": "Trassa unu steddu grogu piticheddu, a<PERSON><PERSON> trassaddi una linia asusu.", "Turtle.helpText5": "Intamis de unu steddu, ndi podis fai cuatru postus a cuadru?", "Turtle.helpText6": "Trassa tres steddus grogus, e una linia bianca.", "Turtle.helpText7": "Trassa is steddus e apustis cuatru linias biancas.", "Turtle.helpText8": "Trassendi 360 linias biancas at a parri sa luna prena.", "Turtle.helpText9": "Podis a<PERSON>ungi unu circu nieddu a manera chi sa luna si furrit a luna noa?", "Turtle.helpText10": "Disinna su chi bolis. Tenis unu muntoni de brocus nous de provai. Spassiatì.", "Turtle.helpText10Reddit": "<PERSON><PERSON><PERSON> \"Castia Galleria\" po biri ita ant fatu is àterus. Si fais calincuna cosa de giudu, craca \"Poni in sa Galleria\" po ddu publicai.", "Turtle.helpToolbox": "Scebera una categoria po biri is brocus.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "<PERSON><PERSON><PERSON> x", "Movie.y1": "<PERSON><PERSON><PERSON> y", "Movie.x2": "fini x", "Movie.y2": "fini y", "Movie.radius": "arraju", "Movie.width": "<PERSON><PERSON><PERSON>", "Movie.height": "artària", "Movie.circleTooltip": "Trassa unu circu in su puntu specificau e cun s'arraju specificau.", "Movie.circleDraw": "circu", "Movie.rectTooltip": "Trassa unu retàngulu in su puntu specificau e cun sa ladària e artària specificadas.", "Movie.rectDraw": "retàngulu", "Movie.lineTooltip": "Trassa una linia de unu puntu a un'àteru cun sa ladària specificada.", "Movie.lineDraw": "linia", "Movie.timeTooltip": "Torrat su tempus currenti in s'animatzioni(0-100).", "Movie.colourTooltip": "Muda su colori de sa pinna.", "Movie.setColour": "poni su colori a", "Movie.submitDisabled": "Su filmi tuu non si movit. Impera is brocus po fai calincuna cosa de giudu. Apustis podis ponni totu in sa galleria.", "Movie.galleryTooltip": "Aberi sa galleria de is films in Reddit.", "Movie.galleryMsg": "Castia Galleria", "Movie.submitTooltip": "<PERSON>ni su film tuu in Reddit.", "Movie.submitMsg": "Poni in sa Galleria", "Movie.helpText1": "Prova a fai s'omineddu cun simpli fromas.", "Movie.helpText2a": "<PERSON>ustu livellu est unu filmi. Bolis una bòcia arrùbia arrumbuendi. <PERSON><PERSON><PERSON> <PERSON><PERSON> play po dda biri.", "Movie.helpText2b": "In sa proietzioni, su brocu 'time' contat de 0 a 100. Ca bolis chi sa bòcia andit de 0 a 100 in orizòntali.", "Movie.helpText3": "Su brocu 'time' contat de 0 a 100. Ma custa borta tui bolis chi sa bòcia andit de 100 a 0 in orizòntali. Dda agatas una formula matemàtica po inverti diretzioni?", "Movie.helpText4": "Impera su chi as imparau po fai cuatru bòcias birdis chi si movant in totu is cuatru diretzionis.", "Movie.helpText5": "A fai movi sa conca de su topi est simpli. Dda agatas sa manera, matemàtica, de ddi fai movi is origas puru?", "Movie.helpText6": "Duas lìnias sceti. Depis cumprendi ita funt fendi is càbudus de is lìnias.", "Movie.helpText7": "Sa formula matemàtica po custa bòcia arruendi est cumpricada. Innoi sa solutzioni:", "Movie.helpText8": "Impera su brocu 'if' po disinnai bòcias arrùbias e blu po sa primu metadi de su filmi. Apustis disinna una bòcia birdi po s'àtera metadi de su filmi.", "Movie.helpText9": "Arresurtas a fai una bòcia chi sighit su filu? Su filu est stètiu giai fatu po tui. Si fais custu, as a podi fai calisisiat cosa.", "Movie.helpText10": "Fai unu filmi comenti bolis. Tenis unu muntoni de brocus nous. Spassiadì!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON> \"Castia Galleria\" po biri ita ant fatu is àterus. Si fais calincuna cosa de giudu, craca \"Poni in sa Galleria\" po ddu publicai.", "Pond.scanTooltip": "k", "Pond.cannonTooltip": "Spara cun su cannoni. Specifica sa diretzioni(0-360) e sa ghetada(0-70).", "Pond.swimTooltip": "Annada a innantis. Specifica sa diretzioni (0-360).", "Pond.stopTooltip": "Firma de annadai. Su giogadori at a andai a bellu fintzas a si firmai.", "Pond.healthTooltip": "Torrat sa saludi de su giogadori (0 est mortu, 100 est in saludi).", "Pond.speedTooltip": "Torrat sa lestresa de su giogadori (0 est firmu, 100 est a totu fua).", "Pond.locXTooltip": "Torrat sa cordinada X de su giogadori (0 est s'oru a manu manca, 100 est a dereta).", "Pond.locYTooltip": "Torrat sa cordinada Y de su giogadori (0 est s'oru de bàsciu, 100 est su de susu).", "Pond.docsTooltip": "Amosta sa documentatzioni de su linguàgiu.", "Pond.documentation": "Documentatzione", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "<PERSON> chi as fatu podit andai beni, ma podis fai mellus. Impera 'scan' po nai a su cannoni cantu depit scudi atesu.", "Pond.helpText1": "Impera su cumandu 'cannon' po ferri su bressallu. Su primu parametru est s'angulu, su segundu parametru est s'impostúra. Cumbinaddus beni a pari.", "Pond.helpText2": "Custu bressallu depit essi fertu prus e prus bortas. Impera una lòriga 'while (true)' po fai una cosa po unu tempus indefiniu.", "Pond.helpText3a": "Custu bressallu depit essi fertu prus e prus bortas. Impera una lòriga 'while (true)' po fai una cosa po unu tempus indefiniu.", "Pond.helpText3b": "Custa impostúra est propri su chi serbit a su cumandu 'cannon' po ferri in su puntu giustu.", "Pond.helpText4": "Est tropu atesu po su cannoni (chi tirat a massimu 70 metrus). Prova su cumandu 'swim' po annadai, t'acostai e ti nci scudi a s'aversariu.", "Pond.helpText5": "Est tropu atesu po su cannoni e tui ses tropu debili po ddu stumbai. Prova a annadai e candu ses prus acanta de 50 in orizontali firma e tira cun su cannoni.", "Pond.helpText6": "Cutu si ndi stesiat candu ddi ferit sa balla. Prova su cumandu 'swim' po t'acostai si est foras de tiru (70 metrus)."}