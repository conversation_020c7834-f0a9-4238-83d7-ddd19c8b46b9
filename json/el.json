{"@metadata": {"authors": ["Azountas", "Babispan", "Dimitris131", "<PERSON><PERSON><PERSON><PERSON>", "Gchr", "GhotuoIncubator", "Indoril", "KATRINE1992", "McDut<PERSON><PERSON>", "Namatreasure", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tifa93"]}, "Games.name": "Παιχνί<PERSON><PERSON><PERSON>ly", "Games.puzzle": "<PERSON><PERSON><PERSON><PERSON>", "Games.maze": "Λαβύρινθος", "Games.bird": "Πουλί", "Games.turtle": "Χελώνα", "Games.movie": "Ταινία", "Games.music": "Μουσική", "Games.pondTutor": "Εκπαιδευτής Λίμνης", "Games.pond": "Λίμνη", "Games.linesOfCode1": "Λύσατε αυτό το επίπεδο με 1 γραμμή JavaScript:", "Games.linesOfCode2": "Λύσατε αυτό το επίπεδο με %1 γραμμές JavaScript:", "Games.nextLevel": "Είστε έτοιμοι για το επίπεδο %1;", "Games.finalLevel": "Είστε έτοιμοι για την επόμενη πρόκληση;", "Games.submitTitle": "Τίτλος:", "Games.linkTooltip": "Αποθήκευσε και συνέδεσε στα μπλοκ.", "Games.runTooltip": "Εκτέλεσε το πρόγραμμα που έγραψες.", "Games.runProgram": "Εκτέλεση Προγράμματος", "Games.resetTooltip": "Διακοπή του προγράμματος και επαναφορά του επιπέδου.", "Games.resetProgram": "Επαναφορά", "Games.help": "Βοήθεια", "Games.catLogic": "Λογική", "Games.catLoops": "Επαναλ<PERSON>ψ<PERSON>ις", "Games.catMath": "Μαθηματικά", "Games.catText": "Κείμενο", "Games.catLists": "<PERSON><PERSON>στ<PERSON>ς", "Games.catColour": "Χρώμα", "Games.catVariables": "Μεταβλητές", "Games.catProcedures": "Συναρτή<PERSON><PERSON>ις", "Games.httpRequestError": "Υπήρξε πρόβλημα με το αίτημα.", "Games.linkAlert": "Κοινοποίησε τα μπλοκ σου με αυτόν τον σύνδεσμο:\n\n%1", "Games.hashError": "Λυπάμαι, το '%1' δεν αντιστοιχεί σε κανένα αποθηκευμένο πρόγραμμα.", "Games.xmlError": "Δεν μπορώ να φορτώσω το αποθηκευμένο αρχείο σου.  Μήπως δημιουργήθηκε από μία παλιότερη έκδοση του Blockly;", "Games.submitted": "Σε ευχαριστούμε για αυτό το πρόγραμμα! Εάν αρέσει στο προσωπικό των εκπαιδευμένων μαϊμούδων μας, θα το δημοσιεύσουν στην έκθεση εντός δύο ημερών.", "Games.listVariable": "λίστα", "Games.textVariable": "κείμενο", "Games.breakLink": "Μόλις αρχίσετε την επεξεργα<PERSON><PERSON><PERSON>,δεν μπορείτε να επιστρέψετε στην επεξεργασία μπλοκ. Είναι εντάξει αυτό;", "Games.blocks": "Μπλοκ", "Games.congratulations": "Συγχαρητήρια!", "Games.helpAbort": "Αυτό το επίπεδο είναι εξαιρε<PERSON><PERSON><PERSON><PERSON> δύσκολο. Θα ήθελες να το παραλείψεις και να πας στο επόμενο παιχνίδι; Μπ<PERSON><PERSON><PERSON><PERSON>ς πάντα να επανέλθεις αργότερα.", "Index.clear": "Θέλεις να διαγράψεις όλες σου τις λύσεις;", "Index.subTitle": "Παιχνίδια για τους προγραμματιστές του αύριο.", "Index.moreInfo": "Περισσότερες πληροφορίες για εκπαιδευτές…", "Index.startOver": "Θέλεις να ξεκινήσεις πάλι από την αρχή;", "Index.clearData": "Εκκαθάριση δεδομένων", "Puzzle.animal1": "Πάπια", "Puzzle.animal1Trait1": "Φτερά", "Puzzle.animal1Trait2": "Ράμφος", "Puzzle.animal1HelpUrl": "https://el.wikipedia.org/wiki/%CE%A0%CE%AC%CF%80%CE%B9%CE%B1", "Puzzle.animal2": "Γάτα", "Puzzle.animal2Trait1": "Μουστάκια", "Puzzle.animal2Trait2": "Γούνα", "Puzzle.animal2HelpUrl": "https://el.wikipedia.org/wiki/%CE%93%CE%AC%CF%84%CE%B1", "Puzzle.animal3": "Μέλισσα", "Puzzle.animal3Trait1": "Μέλι", "Puzzle.animal3Trait2": "Κεντρί", "Puzzle.animal3HelpUrl": "https://el.wikipedia.org/wiki/%CE%9C%CE%AD%CE%BB%CE%B9%CF%83%CF%83%CE%B1", "Puzzle.animal4": "Σαλιγκάρι", "Puzzle.animal4Trait1": "Κέλυφος", "Puzzle.animal4Trait2": "Γλίτσα", "Puzzle.animal4HelpUrl": "https://el.wikipedia.org/wiki/%CE%A3%CE%B1%CE%BB%CE%B9%CE%B3%CE%BA%CE%AC%CF%81%CE%B9", "Puzzle.picture": "εικόνα:", "Puzzle.legs": "πόδια:", "Puzzle.legsChoose": "διάλεξε...", "Puzzle.traits": "χαρακτηριστικά:", "Puzzle.error0": "Τέλεια!\nΚαι τα %1 μπλοκ είναι σωστά.", "Puzzle.error1": "Σχεδόν! Ένα μπλοκ είναι λάθος.", "Puzzle.error2": "%1 μπλοκ είναι λάθος.", "Puzzle.tryAgain": "Το επισημασμένο μπλοκ δεν είναι σωστό.\nΣυνέχισε την προσπάθεια.", "Puzzle.checkAnswers": "Έλεγξε τις απαντήσεις", "Puzzle.helpText": "Για κάθ<PERSON> ζώ<PERSON> (πρά<PERSON><PERSON><PERSON><PERSON> μπλοκ), σύνδεσε την εικόνα του, διάλεξε τα πόδια του και κάνε μια στοίβα με τα χαρακτηριστικά του.", "Maze.moveForward": "προχώρησε ευθεία", "Maze.turnLeft": "στρίψε αριστερά", "Maze.turnRight": "στρίψε δεξιά", "Maze.doCode": "κάνε", "Maze.helpIfElse": "Τα μπλοκ «εάν...αλλιώς» θα κάνουν είτε το ένα είτε το άλλο.", "Maze.pathAhead": "εάν υπάρχει μονοπάτι μπροστά", "Maze.pathLeft": "εάν υπάρχει μονοπάτι προς τα αριστερά", "Maze.pathRight": "εάν υπάρχει μονοπάτι προς τα δεξιά", "Maze.repeatUntil": "επανάλα<PERSON>ε μέχρι", "Maze.moveForwardTooltip": "Κινεί τον ανθρωπάκι μία θέση μπροστά.", "Maze.turnTooltip": "Στρίβει το ανθρωπάκι αριστερ<PERSON> <PERSON> δεξιά κατά 90 μοίρες.", "Maze.ifTooltip": "Αν υπάρχει μονοπάτι προς τη συγκεκριμένη κατεύθυνση, τότε εκτελεί κάποιες ενέργειες.", "Maze.ifelseTooltip": "Αν υπάρχει μονοπάτι προς τη συγκεκριμένη κατεύθυνση, τότε εκτελεί το πρώτο μπλοκ των δράσεων. Διαφορετικ<PERSON>, εκτελεί το δεύτερο μπλοκ των δράσεων.", "Maze.whileTooltip": "Επαναλαμβάνει τις περικλειόμενες ενέργειες μέχρι να φθάσει στο σημείο τερματισμού.", "Maze.capacity0": "Σου απομένουν %0 μπλοκ.", "Maze.capacity1": "Σου απομένουν %1 μπλοκ.", "Maze.capacity2": "Σου απομένουν %2 μπλοκ.", "Maze.runTooltip": "Κάνει το ανθρωπάκι να κάνει αυτό που λένε τα μπλοκ.", "Maze.resetTooltip": "Βάλε το ανθρω<PERSON><PERSON><PERSON>ι πίσω στην αρχή του λαβυρίνθου.", "Maze.helpStack": "Στοίβαξε δύο μπλοκ 'προχώρησε ευθεία' για να φτάσω στο τέρμα.", "Maze.helpOneTopBlock": "Σε αυτό το επίπεδο, πρέπει να στοιβάξεις όλα τα μπλοκ στον λευκό χώρο εργασίας.", "Maze.helpRun": "Εκτέλεσε το πρόγραμμά σου και δες τι συμβαίνει.", "Maze.helpReset": "Το πρόγραμμα σου δεν έλυσε τον λαβύρινθο. Πάτησε 'Επαναφορά' και δοκίμασε ξανά.", "Maze.helpRepeat": "Προσπάθησε να φτάσεις στο τέλος του μονοπατιού χρησιμοποιώντας μόνο δύο μπλοκ. Χρησιμοποίησε το μπλοκ 'επανάλαβε' για να εκτελέσεις το μπλοκ που θέλεις πολλές φορές.", "Maze.helpCapacity": "Έχεις χρησιμοποιήσει όλα τα μπλοκ για αυτό το επίπεδο. Για να δημιουργήσεις ένα νέο μπλοκ, θα πρέπει πρώτα να διαγράψεις ένα υπάρχον μπλοκ.", "Maze.helpRepeatMany": "Μπορείς να βάλεις περισσότερο από ένα μπλοκ μέσα σε ένα μπλοκ 'επανάληψη'.", "Maze.helpSkins": "Επίλεξε τον αγαπημένο σου χαρακτήρα από αυτό το μενού.", "Maze.helpIf": "Μια συνθήκη «εάν» θα εκτελέσει κάτι μόνο αν είναι αληθής. Προσπάθησε να στρίψεις αριστερά, εάν υπάρχει μονοπάτι προς τα αριστερά.", "Maze.helpMenu": "Κάνε κλικ στο %1 μέσα στο μπλοκ 'εάν' για να αλλάξεις την κατάστασή του.", "Maze.helpWallFollow": "Μπορείς να επιλύσεις αυτόν τον περίπλοκο λαβύρινθο; Προσπάθησε να ακολουθείς τον τοίχο στα αριστερά σου. Μόνο για προχωρημένους προγραμματιστές!", "Bird.noWorm": "δεν έχει σκουλήκι", "Bird.heading": "κατεύθυνση", "Bird.noWormTooltip": "Η κατάσταση, κατά την οποία το πουλί δεν έχει πάρει το σκουλήκι.", "Bird.headingTooltip": "Κινηθείτε προς την κατεύθυνση της δεδομένης γωνίας: 0 προς τα δεξιά, 90 είναι κατ ' ευθείαν επάνω, κλπ.", "Bird.positionTooltip": "x και y ορίζουν την θέση του πουλιού. Όταν το x=0 το πουλί είναι στην άκρη αριστερά, όταν το x=100 είναι στην άκρη δεξιά. Όταν το y=0 το πουλί είναι στο κάτω μέρος, όταν το y=100, είναι στο πάνω μέρος.", "Bird.helpHeading": "Άλλαξε την γωνία κατεύθυνσης για να κάνεις το πουλί να πιάσει το σκουλήκι και να προσγειωθεί στη φωλιά του.", "Bird.helpHasWorm": "Χρησιμοποίησε αυτό το μπλοκ για να πας σε μια κατεύθυνση αν έχεις το σκουλήκι, είτε σε  διαφορετική κατεύθυνση αν δεν έχεις το σκουλήκι.", "Bird.helpX": "'x' είναι η τρέχουσα οριζόντια θέση. Χρησιμοποίησε το μπλοκ για να πας σε μια κατεύθυνση αν το 'x' είναι μικρότερο από έναν αριθμό, διαφορετικά σε μία άλλη κατεύθυνση", "Bird.helpElse": "Κάντε κλικ στο εικονίδιο για να αλλάξετε το μπλοκ 'εάν'.", "Bird.helpElseIf": "Αυτό το επίπεδο χρειάζεται και το μπλοκ 'εναλλακτικά εάν' αλλά και το μπλοκ 'εναλλακτικά'.", "Bird.helpAnd": "Το μπλοκ 'και' ισχύει μόνο αν ισχύουν και οι δύο είσοδοι.", "Bird.helpMutator": "Σύρετε ένα μπλοκ 'εναλλα<PERSON><PERSON>ι<PERSON>ά εάν' στο μπλοκ 'εάν'.", "Turtle.moveTooltip": "Μετακιν<PERSON><PERSON> τη χελώνα προς τα εμπρός ή προς τα πίσω κατά το ορισμένο ποσό.", "Turtle.moveForward": "κινήσου μπροστά κατά", "Turtle.moveBackward": "κινήσου πίσω κατά", "Turtle.turnTooltip": "Γυρίζει τη χελώνα αριστερ<PERSON> ή δεξιά κατά τον καθορισμένο αριθμό των μοιρών.", "Turtle.turnRight": "στρίψε δεξιά κατά", "Turtle.turnLeft": "στρίψε αριστερ<PERSON> κατά", "Turtle.widthTooltip": "Αλλάζει το φάρδος του στιλό.", "Turtle.setWidth": "όρισε το φάρδος ίσο με", "Turtle.colourTooltip": "Αλλάζει το χρώμα του στιλό.", "Turtle.setColour": "όρισε το χρώμα σε", "Turtle.penTooltip": "Ανεβ<PERSON><PERSON><PERSON><PERSON> ή πατάει το στιλό για να σταματήσει ή να ξεκινήσει να σχεδιάζει.", "Turtle.penUp": "στιλό πάνω", "Turtle.penDown": "στιλό κάτω", "Turtle.turtleVisibilityTooltip": "Κάνει τη χελώνα (πρ<PERSON><PERSON><PERSON><PERSON><PERSON> κύκλος και βέλος) ορατή ή αόρατη.", "Turtle.hideTurtle": "κρύψε τη χελώνα", "Turtle.showTurtle": "εμφάνισε τη χελώνα", "Turtle.printHelpUrl": "https://el.wikipedia.org/wiki/%CE%A4%CF%85%CF%80%CE%BF%CE%B3%CF%81%CE%B1%CF%86%CE%AF%CE%B1", "Turtle.printTooltip": "Γράφει το κείμενο στην κατεύθυνση της χελώνας ξεκινώντας από την τωρινή θέση της.", "Turtle.print": "εκτύπωση", "Turtle.fontHelpUrl": "https://el.wikipedia.org/wiki/%CE%93%CF%81%CE%B1%CE%BC%CE%BC%CE%B1%CF%84%CE%BF%CF%83%CE%B5%CE%B9%CF%81%CE%AC", "Turtle.fontTooltip": "Καθορίζει τη γραμματοσειρά που χρησιμοποιείται από το μπλοκ της εκτύπωσης.", "Turtle.font": "γραμματοσειρά", "Turtle.fontSize": "μέγεθος γραμματοσειράς", "Turtle.fontNormal": "κανονική", "Turtle.fontBold": "έντονη", "Turtle.fontItalic": "πλάγια γραφή", "Turtle.submitDisabled": "Εκτέλεσε το πρόγραμμά σου μέχρι να σταματήσει. Στη συνέχεια, μπορείς να υποβάλεις το σχέδιό σου στην έκθεση σχεδίων.", "Turtle.galleryTooltip": "Ανοίξτε την έκθεση σχεδίων.", "Turtle.galleryMsg": "Δες την έκθεση σχεδίων", "Turtle.submitTooltip": "Υπόβαλε το σχέδιο σου στην έκθεση σχεδίων.", "Turtle.submitMsg": "Υποβολή στην Έκθεση Σχεδίων", "Turtle.helpUseLoop": "Η λύση σου λειτουρ<PERSON><PERSON><PERSON>, αλλ<PERSON> μπορείς να το κάνεις καλύτερα.", "Turtle.helpUseLoop3": "Σχεδίασε το σχήμα με μόλις τρία μπλοκ.", "Turtle.helpUseLoop4": "Σχεδίασε το αστέρι με μόνο τέσσερα μπλοκ.", "Turtle.helpText1": "Δημιούργησε ένα πρόγραμμα που σχεδιάζει ένα τετράγωνο.", "Turtle.helpText2": "Άλλαξε το πρόγραμμά σου για να σχεδιάσεις ένα πεντάγωνο αντί για τετράγωνο.", "Turtle.helpText3a": "Υπάρχει ένα νέο μπλοκ που σου επιτρέπει να αλλάξεις το χρώμα:", "Turtle.helpText3b": "Σχεδία<PERSON>ε ένα κίτρινο αστέρι.", "Turtle.helpText4a": "Υπάρχει ένα νέο μπλοκ που σου επιτρέπει να σηκώσεις το στυλό σου από το χαρτί όταν μετακινείσαι:", "Turtle.helpText4b": "Σχεδίασε ένα μικρό κίτρινο αστέρι, στη συνέχεια, σχεδίασε μια γραμμή πάνω από αυτό.", "Turtle.helpText5": "Αντί για ένα αστέρι, μπορε<PERSON>ς να σχεδιάσεις τέσσερα αστέρια τοποθετημένα σε τετράγωνο;", "Turtle.helpText6": "Σχεδίασε τρία κίτρινα αστέρια, και μία λευκή γραμμή.", "Turtle.helpText7": "Σχεδίασε τα αστέρια, και στη συνέχεια, σχεδίασε τέσσερις λευκές γραμμές.", "Turtle.helpText8": "Σχεδι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 360 λευκές γραμμές θα μοιάζει με την πανσέληνο.", "Turtle.helpText9": "Μπορείς να προσθέσεις ένα μαύρο κύκλο, έτσι ώστε το φεγγάρι να γίνει μια ημισέληνος;", "Turtle.helpText10": "Σχεδ<PERSON><PERSON><PERSON><PERSON> <PERSON>,τι θέλεις. Έχεις έναν τεράστιο αριθμό νέων μπλοκ που μπορείς να εξερευνήσεις. Καλ<PERSON> διασκέδαση!", "Turtle.helpText10Reddit": "Χρησιμποίησε το κουμπί \"Δες την Έκθεση Σχεδίων\" για να δεις τι έχουν σχεδιάσει άλλοι άνθρωποι . Εάν σχεδιάσεις κάτι ενδιαφέρον, χρησιμοποιήστε το κουμπί \"Υποβολή στην Έκθεση Σχεδίων\" για να το δημοσιεύσεις.", "Turtle.helpToolbox": "Επέλεξε μια κατηγορία για να δεις τα μπλοκ.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "έναρξη x", "Movie.y1": "έναρξη y", "Movie.x2": "τέλος x", "Movie.y2": "τέλος y", "Movie.radius": "ακτίνα", "Movie.width": "φάρδος", "Movie.height": "ύψος", "Movie.circleTooltip": "Σχεδιά<PERSON><PERSON><PERSON> έναν κύκλο στην καθορισμένη θέση και με την καθορισμένη ακτίνα.", "Movie.circleDraw": "κύκλος", "Movie.rectTooltip": "Σχεδιάζ<PERSON><PERSON> ένα ορθογώνιο στην καθορισμένη θέση και με το καθορισμένο φάρδος και ύψος.", "Movie.rectDraw": "ορθογώνιο", "Movie.lineTooltip": "Σχεδιάζει μια γραμμή από ένα σημείο σε ένα άλλο με το δοσμένο φάρδος.", "Movie.lineDraw": "γραμμή", "Movie.timeTooltip": "Επιστρέφει την τρέχουσα ώρα στην κίνηση (0-100).", "Movie.colourTooltip": "Αλλάζει το χρώμα του στιλό.", "Movie.setColour": "όρισε το χρώμα σε", "Movie.submitDisabled": "Η ταινία σου δεν κινείται. Χρησιμοποίησε κάποια μπλοκ για να κάνετε κάτι ενδιαφέρον. Στη συνέχεια, μπορείς να υποβάλεις την ταινία σου στην έκθεση ταινιών.", "Movie.galleryTooltip": "Ανοίξτε την έκθεση ταινιών.", "Movie.galleryMsg": "Δες την Έκθεση Ταινιών", "Movie.submitTooltip": "Υπόβαλε την ταινία σου στην Έκθεση Ταινιών.", "Movie.submitMsg": "Υπόβαλε στην Έκθεση Ταινιών", "Movie.helpLayer": "Μετακ<PERSON>νησε τον κύκλο παρασκην<PERSON>ου στην κορυφή του προγράμματός σου. Έτσι, θα εμφανιστεί πίσω από τον άνθρωπο.", "Movie.helpText1": "Χρησιμοποίη<PERSON><PERSON> απλά σχήματα για να σχεδιάσεις αυτό το άτομο.", "Movie.helpText2a": "Αυτό το επίπεδο είναι μια ταινία. Θέλεις το χέρι του ανθρώπου να μετακινηθεί κατά μήκος της οθόνης. Πάτα το κουμπί play για να δεις μια προεπισκόπηση.", "Movie.helpText2b": "Καθώς η ταινία παίζει, η τιμή του μπλοκ 'time' μετράει από το 0 έως το 100. Αφού θέλετε τη θέση 'y' του χεριού να ξεκινά από το 0 και να πάει στο 100, αυτό θα πρέπει να είναι εύκολο.", "Movie.helpText3": "Το μπλοκ 'time' μετράει από το 0 έως το 100. Αλλά τώρα θέλετε η θέση 'y' του άλλου μπράτσου να ξεκινήσει από το 100 και να πάει στο 0. Μπορ<PERSON>ί να σας σκεφτείτε ένα απλό μαθηματικό τύπο που αναστρέφει την κατεύθυνση;", "Movie.helpText4": "Χρησιμο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,τι έμαθες στο προηγούμενο επίπεδο για να κάνεις τα πόδια να σταυρώνουν.", "Movie.helpText5": "Ο μαθηματικός τύπος για το χέρι είναι περίπλοκος. Εδ<PERSON> η απάντηση:", "Movie.helpText6": "Δώσε στον άνθρωπο δύο χέρια.", "Movie.helpText7": "Χρησιμοποίησε το μπλοκ 'εάν' για να ζωγραφίσεις ένα μικρό κεφάλι για το πρώτο μισό της ταινίας. Μετά ζωγράφισε ένα μεγάλο κεφάλι για το δεύτερο μισό της ταινίας.", "Movie.helpText8": "Βάλε τα πόδια να αλλάζουν κατεύθυνση στο μέσο της ταινίας.", "Movie.helpText9": "Ζωγράφισε ένα επεκτεινόμενο κύκλο πίσω από τον άνθρωπο.", "Movie.helpText10": "Δημιούργησε μία ταινία με ό,τι θέλεις. Έχεις πάρα πολλά νέα μπλοκ που μπορείς να εξερευνήσεις. Καλή διασκέδαση!", "Movie.helpText10Reddit": "Χρησιμοποίησε το κουμπί \"Δες την Έκθεση Ταινιών\" για να δεις τις ταινίες που έχουν φτιάξει άλλοι. Αν δημιουργήσεις μια ενδιαφέρουσα ταινία, πάτα το \"Υποβολή στην Έκθεση Ταινιών\" για να το δημοσιεύσεις.", "Music.playNoteTooltip": "Παίζει ένα μουσικό τραγούδι ορισμένης διάρκειας και τόνου.", "Music.playNote": "παίξε %1 τη νότα %2", "Music.restTooltip": "Περιμένει για την ορισμένη διάρκεια.", "Music.restWholeTooltip": "Περιμένει για μία ολόκληρη νότα.", "Music.rest": "υπόλοιπα %1", "Music.setInstrumentTooltip": "Αλλά<PERSON><PERSON><PERSON> στο προκαθορισμένο όρ<PERSON>ανο όταν παίζουν τα επόμενα μουσικά τραγούδια.", "Music.setInstrument": "ορισμός οργάνου στο %1", "Music.startTooltip": "Εκτελεί τα μπλοκ στο εσωτερικ<PERSON> όταν το πατηθεί το κουμπί 'Εκτέλεση προγράμματος'.", "Music.start": "όταν το %1 πατηθεί", "Music.pitchTooltip": "Μία νότα (το C4 είναι το 7).", "Music.firstPart": "πρώτο μέρος", "Music.piano": "πιάνο", "Music.trumpet": "τρομπέτα", "Music.banjo": "μπάντζ<PERSON>", "Music.violin": "βιολί", "Music.guitar": "κιθάρα", "Music.flute": "φλάουτο", "Music.drum": "τύμπανο", "Music.choir": "χορωδία", "Music.submitDisabled": "Εκτέλεσε το πρόγραμμά σου μέχρι να σταματήσει. Στη συνέχεια, μπορείς να υποβάλεις τη μουσική σου στην Έκθεση Μουσικής.", "Music.galleryTooltip": "Άνοιξε την έκθεση μουσικής.", "Music.galleryMsg": "Δες την έκθεση μουσικής", "Music.submitTooltip": "Υπόβαλε την μουσική σου στην έκθεση μουσικής.", "Music.submitMsg": "Υπόβαλε στην Έκθεση Μουσικής", "Music.helpUseFunctions": "Η λύση σου δουλεύει, α<PERSON><PERSON><PERSON> μπορείς να το κάνεις και καλύτερα. Χρησιμοποίησε συναρτήσεις για να μειώσεις την ποσότητα επαναλαμβανόμενου κώδικα.", "Music.helpUseInstruments": "Η μουσική θα ακούγεται καλύτερα εάν χρησιμοποιήσεις διαφορετικό μουσικό όργανο σε κάθε μπλοκ αρχής.", "Music.helpText1": "Σύνθεσε τις πρώτες τέσσερις νότες του 'Αδελφέ Ιάκωβε'.", "Music.helpText2a": "Μια 'συνάρτηση' σας επιτρέπει να ομαδοποιείτε μπλοκ μαζί, και να τα τρέξετε πολλές φορές.", "Music.helpText2b": "Δημιούργησε μια συνάρτηση για να παίξεις τις τέσσερις πρώτες νότες του 'Αδελφέ Ιάκωβε'. Εκτελέστε αυτή τη συνάρτηση δύο φορές. Μην προσθέσετε νέα μπλοκ σημειώσεων.", "Music.helpText3": "Δημιούργησε μία δεύτερη συνάρτηση για το επόμενο μέρος του 'Αδελφέ Ιάκωβε'. Η τελευταία νότα είναι μακρύτερη.", "Music.helpText4": "Δημιούργησε μια τρίτη συνάρτηση για το επόμενο μέρος του 'Αδελφέ Ιάκωβε'. Οι τέσσερις πρώτες νότες είναι συντομότερες.", "Music.helpText5": "Ολοκλήρωσε την πλήρη μελωδία του 'Αδελφέ Ιάκωβε'.", "Music.helpText6a": "Αυτό το νέο μπλοκ σου επιτρέπει να αλλάξεις σε άλλο όργανο.", "Music.helpText6b": "Παίξε την μελωδία σου με ένα βιολί.", "Music.helpText7a": "Αυτό το νέο μπλοκ προσθέτει μια αθόρυβη καθυστέρηση.", "Music.helpText7b": "Δημιούργησε ένα δεύτερο μπλοκ αρχής που έχει δύο μπλοκ καθυστέρησης, και μετά παίζει επίσης τον 'Αδελφέ Ιάκωβε'.", "Music.helpText8": "Κάθε μπλοκ αρχής θα πρέπει να παίζει το 'Αδελφέ Ιάκωβε' δύο φορές.", "Music.helpText9": "Δημιούργησε τέσσερα μπλοκ αρχής που το καθένα παίζει δυο φορές το 'Αδελφέ Ιάκωβε'. Πρόσθεσε τον σωστό αριθμό μπλοκ καθυστέρησης.", "Music.helpText10": "Σύνθεσε ό,τι θέλεις. Έχεις έναν τεράστιο αριθμό νέων μπλοκ που μπορείς να εξερευνήσεις. Καλ<PERSON> διασκέδαση!", "Music.helpText10Reddit": "Πάτα το κουμπί \"Δες την Έκθεση Τραγουδιών\" για να δεις τι έχουν σχεδιάσει άλλοι άνθρωποι . Εάν σχεδιάσεις κάτι ενδιαφέρον, πάτα το κουμπί \"Υποβολή στην Έκθεση Τραγουδιών\" για να το δημοσιεύσεις.", "Pond.scanTooltip": "Έλεγξε για εχθρούς. Δώσε μια διεύθυνση (0-360). Επιστρέφει την απόσταση στον πλησιέστερο εχθρό σε αυτήν την διεύθυνση. Επιστρέφει Άπειρο αν δεν βρέθηκε εχθρός.", "Pond.cannonTooltip": "Πυροβόλησε με το κανόνι. Καθόρισε μια κατεύθυνση (0-360) και μια απόσταση (0-70).", "Pond.swimTooltip": "Κολύμπησε προς τα εμπρός. Καθόρισε μια κατεύθυνση (0-360).", "Pond.stopTooltip": "Σταμάτα το κολύμπι. Ο παίκτης θα επιβραδύνει για να σταματήσει.", "Pond.healthTooltip": "Επιστρέφει την τρέχουσα υγεία του παίκτη (0 είναι νεκρός, 100 είναι υγιής).", "Pond.speedTooltip": "Επιστρέφει την τρέχουσα ταχύτητα κίνησης του παίκτη (0 σταματημένος, 100 με πλήρη ταχύτητα).", "Pond.locXTooltip": "Επιστρέφει η θέση X του παίκτη (το 0 είναι το αριστερό άκρο, 100 είναι το δεξί άκρο).", "Pond.locYTooltip": "Επιστρέφει την θέση Υ του παίκτη (0 είναι το κάτω άκρο, 100 είναι το επάνω άκρο).", "Pond.logTooltip": "Εμφανίζει ένα αριθμό στην κονσόλα του περιηγητή σας.", "Pond.docsTooltip": "Εμφάνιση της τεκμηρίωσης της γλώσσας.", "Pond.documentation": "Τεκμηρίωση", "Pond.playerName": "Παίκτης", "Pond.targetName": "Στό<PERSON><PERSON>", "Pond.pendulumName": "Εκκρεμές", "Pond.scaredName": "Φοβισμένος", "Pond.helpUseScan": "Η λύση σου δουλεύει, α<PERSON><PERSON><PERSON> μπορείς να την βελτιώσεις. Χρησιμοποίησε το 'scan' για να ορίσεις την απόσταση βολής του κανονιού.", "Pond.helpText1": "Χρησιμοποίησε την εντολή 'cannon' για να χτυπήσεις τον στόχο. Η πρώτη παράμετρος είναι η γωνία, η δεύτερη είναι η απόσταση. Βρες τον σωστό συνδυασμό.", "Pond.helpText2": "Πρέπει να χτυπήσεις αυτόν τον στόχο πολλές φορές. Χρησιμοποίησε την επανάληψη 'while (true)' για να εκτελέσετε πολλές φορές.", "Pond.helpText3a": "Αυτός ο αντί<PERSON>αλος κινείτε μπροστ<PERSON> και πίσω και έτσι είναι δύσκολο να πετύχετε τον στόχο. Η συνάρτηση 'scan' επιστρέφει την ακριβή απόσταση από τον αντίπαλο στην δοσμένη κατεύθυνση.", "Pond.helpText3b": "Αυτή η απόσταση είναι ακριβώς αυτή που χρειάζεται η εντολή 'cannon' για να πυροβολήσει με ακρίβεια.", "Pond.helpText4": "Αυτ<PERSON>ς ο αντί<PERSON><PERSON><PERSON>ος είναι πολύ μακριά για να χρησιμοποιήσεις το κανόνι (που έχει ένα όριο 70 μέτρων). Αντιθέτως, χρησιμοποίησε την εντολή 'swim' για να αρχίσει να κολυμπά προς τον αντίπαλο και να τον χτυπήσει.", "Pond.helpText5": "Αυτός ο αντί<PERSON>αλος είναι επίσης πολύ μακριά για να χρησιμοποιήσει το κανόνι. Αλλά είσαι πολύ αδύνατος για να επιβιώσεις μία σύγκρουση. Κολύμπησε προς το μέρος του αντιπάλου ενώ η οριζόντια τοποθεσία είναι μικρότερη από 50. Μετά 'stop' και χρησιμοποίησε το κανόνι.", "Pond.helpText6": "Αυτ<PERSON><PERSON> ο αντί<PERSON><PERSON><PERSON>ος απομακρύν<PERSON><PERSON><PERSON><PERSON> όταν τον χτυπάς. Κολύμπα προς το μέρος του αν είναι εκτός βεληνεκούς (70 μέτρα).", "Gallery": "Έκθεση"}