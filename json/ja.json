{"@metadata": {"authors": ["Aefgh39622", "<PERSON><PERSON><PERSON>", "Linuxmetel", "<PERSON><PERSON>", "Omotecho", "RYU N", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TAKAHASHI Shuuji", "Tokoroten", "きこねす"]}, "Games.name": "ブロックリー・ゲーム", "Games.puzzle": "パズル", "Games.maze": "迷路", "Games.bird": "鳥", "Games.turtle": "タートル", "Games.movie": "動画", "Games.music": "音楽", "Games.pondTutor": "ポンド・チューター", "Games.pond": "ポンド", "Games.linesOfCode1": "このレベルでのあなたの回答は１行のJavaScriptになります：", "Games.linesOfCode2": "このレベルでのあなたの回答は%1行のJavaScriptになります：", "Games.nextLevel": "レベル %1 に進みますか？", "Games.finalLevel": "次のチャレンジに進みますか？", "Games.submitTitle": "タイトル:", "Games.linkTooltip": "ブロックの状態を保存してリンクを取得します。", "Games.runTooltip": "あなたの書いたプログラムを実行。", "Games.runProgram": "プログラムを実行", "Games.resetTooltip": "プログラムを止めて、レベルをリセット。", "Games.resetProgram": "リセット", "Games.help": "ヘルプ", "Games.catLogic": "論理", "Games.catLoops": "繰り返し", "Games.catMath": "数学", "Games.catText": "テキスト", "Games.catLists": "リスト", "Games.catColour": "色", "Games.catVariables": "変数", "Games.catProcedures": "関数", "Games.httpRequestError": "ネットワーク接続のエラーです。", "Games.linkAlert": "ブロックの状態をこのリンクで共有できます:\n\n%1", "Games.hashError": "すみません。「%1」という名前のプログラムは保存されていません。", "Games.xmlError": "保存されたファイルを読み込めませんでした。別のバージョンのブロックリーで作成された可能性があります。", "Games.submitted": "プログラムをどうもありがとう! もしめちゃくちゃ忙しいスタッフが気に入ったら2～3日以内にギャラリーに公開されます。", "Games.listVariable": "リスト", "Games.textVariable": "テキスト", "Games.breakLink": "JavaScriptの編集を開始すると、ブロックの編集へは戻れません。それでも宜しいですか？", "Games.blocks": "ブロック", "Games.congratulations": "おめでとう!", "Games.helpAbort": "このレベルは難易度が非常に高いです。スキップして次のゲームを続けますか？いつでも戻ってくることができます。", "Index.clear": "すべての回答を削除しますか？", "Index.subTitle": "明日のプログラマーのためのゲーム", "Index.moreInfo": "教育者の皆様むけのお知らせ…", "Index.startOver": "やり直しますか？", "Index.clearData": "データをクリア", "Puzzle.animal1": "アヒル", "Puzzle.animal1Trait1": "羽毛", "Puzzle.animal1Trait2": "くちばし", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "猫", "Puzzle.animal2Trait1": "ヒゲ", "Puzzle.animal2Trait2": "毛皮", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "ハチ", "Puzzle.animal3Trait1": "ハチミツ", "Puzzle.animal3Trait2": "ハリ", "Puzzle.animal3HelpUrl": "https://ja.wikipedia.org/wiki/ハチ", "Puzzle.animal4": "カタツムリ", "Puzzle.animal4Trait1": "カラ", "Puzzle.animal4Trait2": "スライム", "Puzzle.animal4HelpUrl": "https://ja.wikipedia.org/wiki/カタツムリ", "Puzzle.picture": "絵:", "Puzzle.legs": "足:", "Puzzle.legsChoose": "選んでください...", "Puzzle.traits": "特ちょう:", "Puzzle.error0": "完ぺきです!\n%1 個のブロックが全問正解です。", "Puzzle.error1": "惜しい! 1 個のブロックが間違っています。", "Puzzle.error2": "%1 個のブロックが間違っています。", "Puzzle.tryAgain": "強調されているブロックが正しくありません。\nがんばってください。", "Puzzle.checkAnswers": "答え合わせ", "Puzzle.helpText": "それぞれの動物 (緑) について、絵をつなげて、足の数を選んで、形質を並べてください。", "Maze.moveForward": "まっすぐ進む", "Maze.turnLeft": "左を向く", "Maze.turnRight": "右を向く", "Maze.doCode": "実行", "Maze.helpIfElse": "「if-else」(もし-それ以外) ブロックでは、一方ともう一方の 2 つのことができます。", "Maze.pathAhead": "もしまっすぐ進めるなら", "Maze.pathLeft": "もし左に進めるなら", "Maze.pathRight": "もし右に進めるなら", "Maze.repeatUntil": "「まで繰り返す」", "Maze.moveForwardTooltip": "プレーヤーがまっすぐ 1 マス進みます。", "Maze.turnTooltip": "プレーヤーが左または右に 90 度向きを変えます。", "Maze.ifTooltip": "選んだ方向に進める場合は、何か動作をします。", "Maze.ifelseTooltip": "選んだ方向に進める場合は最初のブロックの動作を、進めない場合は 2 番めのブロックの動作をします。", "Maze.whileTooltip": "内側の動作を、ゴールに着くまで繰り返します。", "Maze.capacity0": "残り %0 ブロックです。", "Maze.capacity1": "残り %1 ブロックです。", "Maze.capacity2": "残り %2 ブロックです。", "Maze.runTooltip": "並べたブロックの動作をプレーヤーに実行させます。", "Maze.resetTooltip": "プレーヤーを迷路の最初の場所に戻します。", "Maze.helpStack": "「まっすぐ進む」のブロックをいくつか縦につないで、ゴールに連れて行ってください。", "Maze.helpOneTopBlock": "この面では、何もないワークスペースに、すべてのブロックを配置する必要があります。", "Maze.helpRun": "動作を見るには、プログラムを実行してください。", "Maze.helpReset": "あなたのプログラムでは迷路を解けませんでした。「リセット」を押してやり直してください。", "Maze.helpRepeat": "2 個のブロックだけでゴールしてください。ブロックを繰り返し実行する「繰り返し」を使ってください。", "Maze.helpCapacity": "このレベルで使えるブロックをすべて使いました。ブロックを新しく作成するには、今あるブロックを消さなければなりません。", "Maze.helpRepeatMany": "「繰り返し」ブロックの中で、1 個以上のブロックを使えます。", "Maze.helpSkins": "このメニューから好きなプレーヤーを選んでください。", "Maze.helpIf": "「if」(もし) ブロックは条件が正しいときだけ何か動作をします。左に進めるときは左を向くようにしてみてください。", "Maze.helpMenu": "「if」(もし)ブロックの条件を変えるには %1 をクリックしてください。", "Maze.helpWallFollow": "この難しい迷路を解けますか? 左側の壁をたどってみてください。上級のプログラマー向けの迷路です!", "Bird.noWorm": "虫を持っていないなら", "Bird.heading": "進む", "Bird.noWormTooltip": "鳥が虫を捕まえていない状態。", "Bird.headingTooltip": "与えられた角度の方向へ動く：0は右、90は上など。", "Bird.positionTooltip": "xとyは鳥の位置を示します。鳥は、x = 0 のときには左辺近く、x = 100 のときには右辺近くにいます。　鳥は、y = 0 のときには下辺にいて、y = 100 のときには上辺にいます。", "Bird.helpHeading": "鳥が虫を捕らえて巣に帰れるように進む角度を変えてください。", "Bird.helpHasWorm": "虫を持っている場合にある方向へ行き、持っていない場合には異なる方向へ行くのにこのブロックを使います。", "Bird.helpX": "'x'は現在の水平位置です。もし'x'がある数より小さい場合にはある方向に、そうでない場合には異なる方向に行くようにこのブロックを使います。", "Bird.helpElse": "'if' ブロックを変更するには、アイコンをクリックしてください。", "Bird.helpElseIf": "このレベルをクリアするには、'else if' と 'else' ブロックの2つが必要です。", "Bird.helpAnd": "'and' ブロックは、2つの入力が両方とも true の時に、true になります。", "Bird.helpMutator": "'else' ブロックを 'if' ブロックの中にドラッグします。", "Turtle.moveTooltip": "指定した長さだけ前または後ろにタートルを動かします。", "Turtle.moveForward": "前に進む", "Turtle.moveBackward": "後ろに進む", "Turtle.turnTooltip": "指定した角度だけタートルを回転させます。", "Turtle.turnRight": "右に回転", "Turtle.turnLeft": "左に回転", "Turtle.widthTooltip": "ペンの太さを変えます。", "Turtle.setWidth": "太さを変える", "Turtle.colourTooltip": "ペンの色を変えます。", "Turtle.setColour": "色を変える", "Turtle.penTooltip": "線を引いたり引くのをやめたりするために、ペンを上げるか下げるかします。", "Turtle.penUp": "ペンを上げる", "Turtle.penDown": "ペンを下ろす", "Turtle.turtleVisibilityTooltip": "タートル (円と矢印) の表示/非表示を切り替えます。", "Turtle.hideTurtle": "タートルを隠す", "Turtle.showTurtle": "タートルを表示する", "Turtle.printHelpUrl": "https://ja.wikipedia.org/wiki/印刷", "Turtle.printTooltip": "タートルの方向と場所を文字として表示します。", "Turtle.print": "表示", "Turtle.fontHelpUrl": "https://ja.wikipedia.org/wiki/フォント", "Turtle.fontTooltip": "表示ブロックで使うフォントを設定します。", "Turtle.font": "フォント", "Turtle.fontSize": "フォント サイズ", "Turtle.fontNormal": "標準", "Turtle.fontBold": "太字", "Turtle.fontItalic": "斜体", "Turtle.submitDisabled": "プログラムが停止するまで実行した後に、図をギャラリーに送信することができます。", "Turtle.galleryTooltip": "図のギャラリーを開きます。", "Turtle.galleryMsg": "ギャラリーを見る", "Turtle.submitTooltip": "ギャラリーに図を送信。", "Turtle.submitMsg": "ギャラリーへ送信", "Turtle.helpUseLoop": "解けましたが、もっと上手に解くこともできるでしょう。", "Turtle.helpUseLoop3": "3つのブロックで形を作ってみましょう。", "Turtle.helpUseLoop4": "4つのブロックで星を描いてみましょう。", "Turtle.helpText1": "四角を書くプログラムを作る", "Turtle.helpText2": "正方形の代わりに五角形を描くようにプログラムを変えてください。", "Turtle.helpText3a": "色を変えられる新しいブロックがあります。", "Turtle.helpText3b": "黄色の星を描いてください。", "Turtle.helpText4a": "動くときにペンを紙から上げられる新しいブロックがあります。", "Turtle.helpText4b": "小さい黄色の星を描いてから、その上に線を描いてみましょう。", "Turtle.helpText5": "正方形の中に、１つの星の代わりに４つの星を並べることができますか？", "Turtle.helpText6": "3つの黄色の星を描き、白いラインを１本描いてみましょう。", "Turtle.helpText7": "星を描いてから、4つの白い線を描いてみましょう。", "Turtle.helpText8": "360本の白い線を描くと満月のように見えるでしょう。", "Turtle.helpText9": "黒い円を加えて月が三日月に見えるようにできますか？", "Turtle.helpText10": "何でも描きたいものを描いてください。たくさんの新しいブロックを試せます。楽しんでください！", "Turtle.helpText10Reddit": "\"ギャラリーを見る\" ボタンを使って、他の人たちが描いたものを見てみましょう。何かおもしろいものを描いたときには、\"ギャラリーへ送信\" ボタンを押して、公開してみましょう。", "Turtle.helpToolbox": "カテゴリーを選んでブロックを見ます。", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "xを開始", "Movie.y1": "yを開始", "Movie.x2": "xを終了", "Movie.y2": "yを終了", "Movie.radius": "半径", "Movie.width": "幅", "Movie.height": "高さ", "Movie.circleTooltip": "指定された位置に指定された半径で円を描きます。", "Movie.circleDraw": "円", "Movie.rectTooltip": "指定された位置に、指定された幅と高さを持つ長方形を描きます。", "Movie.rectDraw": "長方形", "Movie.lineTooltip": "指定された幅である点から別の点へ線を引きます。", "Movie.lineDraw": "線", "Movie.timeTooltip": "アニメーション（1～100）の現在の時間を返します。", "Movie.colourTooltip": "ペンの色を変えます。", "Movie.setColour": "色を設定：", "Movie.submitDisabled": "動画がうまくいっていません。ブロックを使って何かおもしろいものを作ってみましょう。できた動画はギャラリーに送信することができます。", "Movie.galleryTooltip": "動画のギャラリーを開きます。", "Movie.galleryMsg": "ギャラリーを見る", "Movie.submitTooltip": "ギャラリーに動画を送信。", "Movie.submitMsg": "ギャラリーへ送信", "Movie.helpLayer": "円を描くブロックをプログラムのトップに移動すると、円が人の後ろに描画されます。", "Movie.helpText1": "簡単な形を使ってこの人を描いてみましょう。", "Movie.helpText2a": "このレベルは動画です。画面を横切るよう腕を動かしましょう。プレイ・ボタンを押すとプレビューが見られます。", "Movie.helpText2b": "動画が再生されるときには、'time'ブロックの値が0から100まで増えていきます。腕を'y'に0から100まで動かすのは簡単にできそうですね。", "Movie.helpText3": "'time'ブロックは0から100まで増していきます。でも、もう片方のはアームを'y'に100から0まで動かすことにしましょう。方向を変える簡単な数式がどうなるかわかりますか？", "Movie.helpText4": "前のレベルで学んだことを利用して、足を組んでみましょう。", "Movie.helpText5": "腕の数式は複雑なのです。答え合わせをしましょう。", "Movie.helpText6": "この人に腕を2本、付けてみましょう。", "Movie.helpText7": "動画の前半で小さな頭を描くのに、'if' ブロックを使いましょう。後半では大きな頭を描きます。", "Movie.helpText8": "動画の前半では足を反対向きに描いてください。", "Movie.helpText9": "この人の背景にどんどん大きくなる円を描きましょう。", "Movie.helpText10": "何でも作りたい動画を作ってみましょう。新しくたくさんのブロックが用意されています。楽しみましょう！", "Movie.helpText10Reddit": "他の人たちが作った動画を見るには\"ギャラリーを見る\"ボタンを使ってください。おもしろい動画ができたら、\"ギャラリーへ送信\"ボタンを使って公開してみましょう。", "Music.playNoteTooltip": "指定された長さと音程の音を再生します。", "Music.playNote": "%1の%2を再生", "Music.restTooltip": "指定された時間だけ待機します。", "Music.restWholeTooltip": "1音全体を待ちます。", "Music.rest": "%1待つ", "Music.setInstrumentTooltip": "後続の音符を演奏する際に、指定した楽器に切り替えます。", "Music.setInstrument": "楽器を %1 に設定。", "Music.startTooltip": "「プログラムの実行」ボタンをクリックすると、内部のブロックを実行します。", "Music.start": "%1をクリックした時", "Music.pitchTooltip": "1音(ドは7キー)", "Music.firstPart": "前半部分", "Music.piano": "ピアノ", "Music.trumpet": "トランペット", "Music.banjo": "バンジョー", "Music.violin": "バイオリン", "Music.guitar": "ギター", "Music.flute": "フルート", "Music.drum": "ドラム", "Music.choir": "合唱", "Music.submitDisabled": "プログラムが停止するまで実行すると、曲をギャラリーに送ることができます。", "Music.galleryTooltip": "音楽のギャラリーを開きます。", "Music.galleryMsg": "ギャラリーを見る", "Music.submitTooltip": "作った曲をギャラリーへ送ります。", "Music.submitMsg": "ギャラリーへ送信", "Music.helpUseFunctions": "あなたの作ったプログラムは動きますが、改善できます。関数を使って同じコードの繰り返しを減らしてみましょう。", "Music.helpUseInstruments": "各開始ブロックに異なる楽器を使うと、よりよい曲になるでしょう。", "Music.helpText1": "「フレール・ジャック（かねがなる）」の最初の４音符をいれてください。", "Music.helpText2a": "「関数」は、ブロックをグループにまとめることができ、それを何回でも実行できます。", "Music.helpText2b": "「フレール・ジャック（かねがなる）」の最初の4音符を演奏する関数を作ってください。その関数を2回実行します。他の音符のブロックは入れないでください。", "Music.helpText3": "「フレール・ジャック」の次の部分のために2番目の関数を作りましょう。最後の音は長くなっています。", "Music.helpText4": "「フレール・ジャック（かねがなる）」の次の部分のために3番目の関数を作りましょう。最初の4音符は短いです。", "Music.helpText5": "「フレール・ジャック（かねがなる）」全曲を作ってください。", "Music.helpText6a": "この新しいブロックで別の楽器に変えられます。", "Music.helpText6b": "バイオリンで自分の曲を弾いてみましょう。", "Music.helpText7a": "この新しいブロックは、休符（無音）を加えます。", "Music.helpText7b": "2番目の開始ブロックをつくり、2つの休符（無音）ブロックの後に「フルーレ・ジャック（かねがなる）」を演奏するようにしてください。", "Music.helpText8": "各開始ブロックは「フレール・ジャック（かねがなる）」を２回演奏しなければなりません。", "Music.helpText9": "「フレール・ジャック（かねがなる）」を2回」演奏する開始ブロックを4つ作ってください。そのとき、休符（無音）を正しい数だけ入れてください。", "Music.helpText10": "何でも作りたい曲を作ってみましょう。新しくたくさんのブロックを試せます。楽しみましょう！", "Music.helpText10Reddit": "'ギャラリーを見る' ボタンを使って、他の人たちが描いたものを見てみましょう。何かおもしろいものを描いたときには、'ギャラリーへ送信' ボタンを押して、公開してみましょう。", "Pond.scanTooltip": "敵を探します。方向（0～360）を指定してください。その方向で最も近くにいる敵への距離を返します。敵が見つからない場合には無限大を返します。", "Pond.cannonTooltip": "大砲を発射します。方向（0～360）と距離（0～70）を指定してください。", "Pond.swimTooltip": "前へ泳ぐ。方向を指定してください。(0-360)", "Pond.stopTooltip": "水泳を止めます。プレーヤーはゆっくりと止まります。", "Pond.healthTooltip": "プレーヤーの現在の健康状態（0は死、100は健康）を返します。", "Pond.speedTooltip": "プレーヤーの現在の速度（0は停止、100は最速）を返します。", "Pond.locXTooltip": "プレーヤーのX軸上の値（0は左端、100は右端）を返します。", "Pond.locYTooltip": "プレーヤーのY軸上の値（0は下端、100は上端）を返します。", "Pond.logTooltip": "webブラウザーのコンソールに数字を出力します。", "Pond.docsTooltip": "言語の解説文を表示する。", "Pond.documentation": "説明書", "Pond.playerName": "プレイヤー", "Pond.targetName": "ターゲット", "Pond.pendulumName": "ふりこ", "Pond.scaredName": "こわがって", "Pond.helpUseScan": "あなたの回答は有効ですが、もっとよくできます。'scan'を使って大砲にどのくらいの遠くへ射つかを指示してください。", "Pond.helpText1": "'cannon'コマンドを使って標的を射ってください。最初のパラメーターは角度、二番目のパラメーターは範囲です。それらの正しい組み合わせを見つけてください。", "Pond.helpText2": "この標的には何回も当てる必要があります。'while (true)'のループを使うと何かをずっと実行することができます。", "Pond.helpText3a": "この相手は前後に動いて、当てるのが難しいです。 この'scan'の表現は相手の方向への正確な距離を返します。", "Pond.helpText3b": "この射程距離を使って'cannon'の命令が正確に実行されます。", "Pond.helpText4": "この相手は大砲（射程最大70メートル）を使うには離れすぎています。その代わりに、'swim'の命令を使って敵の方へ泳いで突撃してください。", "Pond.helpText5": "この相手も大砲を使うには離れすぎています。しかし、突撃すると弱すぎて生き残れません。敵に向かって泳いで水平位置を50未満にします。そして'stop'で止まり大砲を使ってください。", "Pond.helpText6": "この相手は撃たれると離れていきます。射程範囲（70メートル）外の場合、相手に向かって泳いでください。", "Gallery": "ギャラリー"}