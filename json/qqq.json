{"@metadata": {"authors": ["BaRaN6161 TURK", "Liuxinyu970226", "McDut<PERSON><PERSON>", "MuratTheTurkish", "Nike", "<PERSON><PERSON>", "Urhixidur", "<PERSON><PERSON><PERSON> p", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "The project name.", "Games.puzzle": "title - Specifies that this is <PERSON><PERSON>'s '''Puzzle''' game.  Use the word for a jigsaw puzzle.\n{{Identical|Puzzle}}", "Games.maze": "title - Specifies that this is <PERSON><PERSON>'s '''Maze''' game.\n{{Identical|Maze}}", "Games.bird": "title - Specifies that this is <PERSON><PERSON>'s '''Bird''' game.\n{{Identical|Bird}}", "Games.turtle": "title - Specifies that this is <PERSON><PERSON>'s '''Turtle''' game.\n{{Identical|Turtle}}", "Games.movie": "title - Specifies that this is <PERSON><PERSON>'s '''Movie''' game.\n{{Identical|Movie}}", "Games.music": "title - Specifies that this is <PERSON><PERSON>'s '''Music''' game.\n{{Identical|Music}}", "Games.pondTutor": "title - Specifies that this is <PERSON><PERSON>'s '''Pond Tutor''' game.  It is a series of lessons or practice levels to allow users to play the more advanced '''Pond''' game (in your language: '''{{:Blockly:Games.pond/{{RelevantLanguage}}}}''').", "Games.pond": "title - Specifies that this is <PERSON><PERSON>'s '''Pond''' game.", "Games.linesOfCode1": "alert - Displayed when a level is complete.", "Games.linesOfCode2": "alert - Displayed when a level is complete.  %1 is an integer greater than 1.", "Games.nextLevel": "alert - This is displayed when the user solves the level, inviting them to procede to the next level of difficulty.  %1 is an integer greater than 1.", "Games.finalLevel": "alert - This is displayed when the user solves the most difficult level and is going to go to the next game.", "Games.submitTitle": "Label in front of a field where the user is requested to type a title for their work.\n{{Identical|Title}}", "Games.linkTooltip": "tooltip - Pressing this button will cause the current program to be saved and for a URL to be shown to later retrieve it.", "Games.runTooltip": "tooltip - Pressing this button runs the computer program the user has written.", "Games.runProgram": "button label - Pressing this button runs the computer program the user has written.", "Games.resetTooltip": "tooltip - Pressing this button restores the player to the start position and enables the user's program to be run again.", "Games.resetProgram": "button label - Pressing this button causes the output of the program to be erased but does not delete the user's program).\n{{Identical|Reset}}", "Games.help": "button label - Pressing this button shows help information.\n{{Identical|Help}}", "Games.catLogic": "category - Blocks related to [https://github.com/google/blockly/wiki/Logic logic].\n{{Identical|Logic}}", "Games.catLoops": "category - Blocks related to [https://en.wikipedia.org/wiki/Control_flow#Loops loops].\n{{Identical|Loops}}", "Games.catMath": "category - Blocks related to mathematics.\n{{Identical|Math}}", "Games.catText": "category - Blocks related to [https://github.com/google/blockly/wiki/Text text processing].\n{{Identical|Text}}", "Games.catLists": "category - Blocks related to [https://github.com/google/blockly/wiki/Lists lists].\n{{Identical|List}}", "Games.catColour": "category - Blocks related to [https://github.com/google/blockly/wiki/Colour colour].\n{{Identical|Color}}", "Games.catVariables": "category - Blocks related to [https://github.com/google/blockly/wiki/Variables variables].\n{{Identical|Variables}}", "Games.catProcedures": "category - Blocks related to [https://en.wikipedia.org/wiki/Subroutine defining or using procedures/functions].\n{{Identical|Functions}}", "Games.httpRequestError": "alert - The URL is invalid or a server error occurred.  This message will be followed by technical information useful to engineers trying to understand the problem.", "Games.linkAlert": "alert - After the user has pressed a button to save his/her program, this provides the URL (%1) to retrieve the program.  The characters '\n\n' indicate that a blank line will be displayed before the URL (in English).  Leave those in unless you move %1 to the beginning or middle of the text, in which case you should use your judgment about where blank lines would be most useful.\n\nParameters:\n* %1 - URL of saved program.", "Games.hashError": "alert - A request to retrieve a stored program does not have a valid URL. %1 is the invalid portion of the URL.", "Games.xmlError": "alert - There was a problem loading a file previously saved by the user.  The most likely reason for the problem is that it was created with an earlier, incompatible version of Blockly.  This message will be followed by technical information useful to engineers trying to understand the problem.", "Games.submitted": "alert - After the user has submitted his/her program to the gallery, it must wait for approval.  Please make this message funny!", "Games.listVariable": "variable name - Default [https://github.com/google/blockly/wiki/Variables variable] representing a [https://github.com/google/blockly/wiki/Lists list].  This should be a single word, preferably short.\n{{Identical|List}}", "Games.textVariable": "variable name - Default [https://github.com/google/blockly/wiki/Variables variable] representing a [https://github.com/google/blockly/wiki/Text piece of text].  This should be a single word, preferably short.\n{{Identical|Text}}", "Games.breakLink": "Warning dialog. Options are 'OK' and 'Cancel'.", "Games.blocks": "Label on a tab that contains blocks editor.\n{{Identical|Block}}", "Games.congratulations": "alert - This is displayed when the user solves the level.\n{{Identical|Congratulation}}", "Games.helpAbort": "callout - This dialog gives the user the option to skip this level.", "Index.clear": "Confirmation prompt for deleting all the user's data.", "Index.subTitle": "Brief description of Blockly Games.", "Index.moreInfo": "Link to a description of <PERSON>ly Games, intended for parents and teachers.", "Index.startOver": "Label next to button for deleting all the user's data.", "Index.clearData": "Text on button for deleting all the user's data.", "Puzzle.animal1": "Duck: The bird.\n{{Ident<PERSON>|<PERSON>}}", "Puzzle.animal1Trait1": "Feathers: A trait that ducks have.\n{{Identical|Feather}}", "Puzzle.animal1Trait2": "Beak: A trait that ducks have.\n{{Identical|Beak}}", "Puzzle.animal1HelpUrl": "The URL of a page with basic information about '''ducks''', on the most appropriate linguistic edition of Wikipedia for the target language.\nMake sure you use secure HTTPS links, to avoid generating mixed contents and security alerts in browsers when viewing the current page in a secure HTTPS session. If not using Wikipedia, make sure the target is non intrusive, but if in doubt, link to a page in another suitable language supported by a Wikimedia project.", "Puzzle.animal2": "Cat: The animal.", "Puzzle.animal2Trait1": "Whiskers: A trait that cats have.\n{{Identical|Whisker}}", "Puzzle.animal2Trait2": "Fur: A trait that cats have.\n{{Identical|Fur}}", "Puzzle.animal2HelpUrl": "The URL of a page with basic information about '''cats''', on the most appropriate linguistic edition of Wikipedia for the target language.\nMake sure you use secure HTTPS links, to avoid generating mixed contents and security alerts in browsers when viewing the current page in a secure HTTPS session. If not using Wikipedia, make sure the target is non intrusive, but if in doubt, link to a page in another suitable language supported by a Wikimedia project.", "Puzzle.animal3": "Bee: The insect.", "Puzzle.animal3Trait1": "Honey: A trait that bees have.\n{{Identical|Honey}}", "Puzzle.animal3Trait2": "Stinger: A trait that bees have.\n{{Identical|Stinger}}", "Puzzle.animal3HelpUrl": "The URL of a page with basic information about '''bees''', on the most appropriate linguistic edition of Wikipedia for the target language.\nMake sure you use secure HTTPS links, to avoid generating mixed contents and security alerts in browsers when viewing the current page in a secure HTTPS session. If not using Wikipedia, make sure the target is non intrusive, but if in doubt, link to a page in another suitable language supported by a Wikimedia project.", "Puzzle.animal4": "Snail: The animal.\n{{Identical|Snail}}", "Puzzle.animal4Trait1": "Shell: A trait that snails have.\n{{Identical|Shell}}", "Puzzle.animal4Trait2": "Slime: A trait that snails have.\n{{Identical|Slime}}", "Puzzle.animal4HelpUrl": "The URL of a page with basic information about '''snails''', on the most appropriate linguistic edition of Wikipedia for the target language.\nMake sure you use secure HTTPS links, to avoid generating mixed contents and security alerts in browsers when viewing the current page in a secure HTTPS session. If not using Wikipedia, make sure the target is non intrusive, but if in doubt, link to a page in another suitable language supported by a Wikimedia project.", "Puzzle.picture": "Prompt for a picture of an animal.\n{{Identical|Picture}}", "Puzzle.legs": "Prompt for the number of legs that an animal has.\n{{Identical|Leg}}", "Puzzle.legsChoose": "Initial text displayed in a dropdown menu from which the user should choose an option.\n{{Identical|Choose}}", "Puzzle.traits": "Prompt for a couple of traits of an animal (e.g. Duck: feathers, beak).\n{{Identical|Trait}}", "Puzzle.error0": "A congratulatory message displayed if the user placed all of the blocks correctly.\n\nParameters:\n* %1 - number of blocks correctly placed.  It is always an integer greater than 1.\n\nThe use of a new line character is optional.", "Puzzle.error1": "An encouraging error message displayed if the user placed all blocks except 1 correctly.\n\nIf the number of incorrect blocks is 2 or more, Puzzle.error2 is used instead.", "Puzzle.error2": "An error message displayed if the user misplaced multiple blocks.\n\nParameters:\n* %1 - number of blocks incorrectly placed, which is always greater than 1 (or the message Puzzle.error1 would be used).", "Puzzle.tryAgain": "A message indicating that a visually distinguished block is incorrect and that the user should try to fix it.\n\nThe use of a new line character is optional.", "Puzzle.checkAnswers": "A label on a button the user can press to check his/her answers.", "Puzzle.helpText": "Instructions for the puzzle.  For context, see [https://blockly.games/puzzle Blockly Puzzle].", "Maze.moveForward": "block text - Imperative or infinitive of a verb for a person moving (walking) in the direction he/she is facing.", "Maze.turnLeft": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Imperative or infinitive of a verb for a person turning his head and body one quarter rotation counter-clockwise.  Prefer a translation that has text in common with the translation of 'turn right'.  See [[Translating:Blockly#Drop-Down_Menus]].", "Maze.turnRight": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Imperative or infinitive of a verb for a person turning his head and body one quarter rotation clockwise.  Prefer a translation that has text in common with the translation of 'turn left'.  See [[Translating:Blockly#Drop-Down_Menus]].", "Maze.doCode": "block text - Imperative or infinitive of a verb preceding one or more commands to a person.  This is part of [https://github.com/google/blockly/wiki/Loops#repeat repeat] and [https://github.com/google/blockly/wiki/IfElse#If_blocks if] blocks.\n{{Identical|Do}}", "Maze.helpIfElse": "callout - This is shown when the 'if-else' block is introduced.  For 'if-else' concatenate the words for 'if' and 'else/otherwise'.", "Maze.pathAhead": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Condensed form of 'if there is a path ahead', as in: 'if path ahead, go forward'.  Prefer translation that has text in common with 'if path to the left' and 'if path to the right'.", "Maze.pathLeft": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Condensed form of 'if there is a path to the left', as in: 'if path to the left, turn left'.", "Maze.pathRight": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Condensed form of 'if there is a path to the right', as in: 'if path to the right, turn right'.", "Maze.repeatUntil": "block text - Imperative or infinitive of a verb to repeat the following commands.  The phrase is followed by the symbol denoting the end of the maze.", "Maze.moveForwardTooltip": "tooltip - Moves the icon on the screen representing the player forward one square on the maze board.", "Maze.turnTooltip": "tooltip - Turns the icon on the screen representing the player 90 degrees counter-clockwise (left) or clockwise (right).", "Maze.ifTooltip": "tooltip - 'path' refers to a path through a maze.", "Maze.ifelseTooltip": "tooltip - 'path' refers to a path through a maze.", "Maze.whileTooltip": "tooltip - Repeat the enclosed commands until the maze has been successfully completed (the end point reached).", "Maze.capacity0": "warning - No more blocks may be added until some are removed.  Please include '%0' in the translated string.  It will be replaced with '0' and made bold.", "Maze.capacity1": "warning - Only one more block may be added. Please include '%1' in the translated string. It will be replaced with '1' and made bold.\n\nSee also:\n* {{msg-blockly|Maze.capacity2}}", "Maze.capacity2": "warning - Only %2 more blocks may be used, where %2 is an integer greater than 1.\n\nSee also:\n* {{msg-blockly|Maze.capacity1}}", "Maze.runTooltip": "tooltip - Pressing this button runs the computer program the user has written to move the player through the maze.", "Maze.resetTooltip": "tooltip - Pressing this button restores the player to the start position and enables the user's program to be run again.", "Maze.helpStack": "callout - The word words for 'stack' and 'blocks' should be the same as for stacking children's blocks. Use the imperative verb form appropriate for speaking to a child, gender unspecified.  If no gender-neutral singular/familiar form exists but a gender-neutral plural/formal form exists, use that instead.  Be sure to use the same translation of 'move forward' as above.", "Maze.helpOneTopBlock": "callout - This is displayed if a user attempts to run a program composed of multiple stacks of blocks, letting them know they need to create a single stack.", "Maze.helpRun": "callout - This is shown after the user has created a program on the first level.", "Maze.helpReset": "callout - This is shown after the user has run a program that does not solve the maze.", "Maze.helpRepeat": "callout - This is shown when the 'repeat' block is introduced.  The word 'path' refers to a path through a maze, and 'block' refers to a child's building block.", "Maze.helpCapacity": "callout - This is shown after the user has used all the blocks permitted on this level.", "Maze.helpRepeatMany": "callout - This is a hint that the user should place a second block inside of a a 'repeat' block.", "Maze.helpSkins": "callout - This is a hint that the user can change the appearance of the player that moves within the maze.", "Maze.helpIf": "callout - This is shown when the 'if' block is introduced.  An example of an 'if' block is: 'if there is a path to the left, turn left'.", "Maze.helpMenu": "callout - %1 will be replaced with an image of the dropdown menu that the user needs to click.", "Maze.helpWallFollow": "callout - This advises the user to solve a maze by keeping his/her left hand in contact with the wall while proceeding through it.  The final sentence is a warning that only advanced programmers should attempt this problem, as beginners tend to get frustrated.", "Bird.noWorm": "block text - <PERSON> is not in possession of a worm.  This phrase is prefixed with 'if'.", "Bird.heading": "block text - the compass direction toward which a traveler or vehicle is or should be moving; course.\n{{Identical|Heading}}", "Bird.noWormTooltip": "tooltip - The bird wants to get the worm.", "Bird.headingTooltip": "tooltip - Move in the direction of the given angle, where 0 means going horizontally to the right, and 90 straight up and 270 straight down.", "Bird.positionTooltip": "tooltip - (x, y) marks the coordinate of bird, (0, 0) is the bottom left corner and (100, 100) top right.", "Bird.helpHeading": "callout - This is shown as instruction for the first level.", "Bird.helpHasWorm": "callout - This is shown when the 'does not have worm' block is introduced.", "Bird.helpX": "callout - This is shown when the 'x smaller than 50' block is introduced.", "Bird.helpElse": "callout - This is shown when the user first needs to modify an 'if' block.", "Bird.helpElseIf": "callout - This is shown when the user first needs to modify an 'if' block to have an 'else if' and an 'else'.\n\nMake sure to translate consistently with:\n* {{msg-blockly|CONTROLS IF MSG ELSEIF}}\n* {{msg-blockly|CONTROLS IF MSG ELSE}}", "Bird.helpAnd": "callout - This is shown to introduce the user to the logical 'and' block.\n\nMake sure to translate consistently with:\n* {{msg-blockly|LOGIC OPERATION AND}}", "Bird.helpMutator": "callout - This is shown to demonstrate how to drag a block.\n\nTranslate consistently with:\n* {{msg-blockly|CONTROLS IF MSG ELSE}}\n* {{msg-blockly|CONTROLS IF MSG IF}}", "Turtle.moveTooltip": "tooltip - In this and subsequent messages, 'turtle' refers to a stylized turtle on the screen to represent a position and direction.  This imaginary turtle is carrying a pen in its tail, so moving the turtle draws a line (or curve, etc.).  You are encouraged to play with the [https://blockly.games/turtle Turtle application] before doing this translation.", "Turtle.moveForward": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Infinitive or imperative of a verb telling a turtle to move (walk) in the direction he/she is facing.  This is followed by a number indicating how far (how many pixels) to go.  Prefer a translation that has text in common with the translation of 'move backward'.  See [[Translating:Blockly#Drop-Down_Menus]].", "Turtle.moveBackward": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Infinitive or imperative of a verb telling a turtle to move (walk) in the direction opposite to where he/she is facing.  This is followed by a number indicating how far (how many pixels) to go.  Prefer a translation that has text in common with the translation of 'move forward'.", "Turtle.turnTooltip": "'Left' means counter-clockwise/anti-clockwise, and 'right' means clockwise.", "Turtle.turnRight": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Infinitive or imperative of verb telling a turtle to rotate clockwise.  This is followed by a number indicating how far (how many degrees) to turn.  Prefer a translation that has text in common with the translation of 'turn left by'.", "Turtle.turnLeft": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Infinitive or imperative of verb telling a turtle to rotate counter-clockwise (anti-clockwise).  This is followed by a number indicating how far (how many degrees) to turn.  Prefer a translation that has text in common with the translation of 'turn right by'.", "Turtle.widthTooltip": "tooltip", "Turtle.setWidth": "block text - Infinitive or imperative of a verb to set the width of the lines that should be drawn in the future by an imaginary pen.  This is followed by a number indicating the width in pixels (1 or greater).", "Turtle.colourTooltip": "tooltip - Changes the colour of ink in the pen carried by the turtle.", "Turtle.setColour": "block text - Infinitive or imperative of a verb to specify the colour of the lines that should be drawn in the future by an imaginary pen.  This is followed by a block showing the colour.", "Turtle.penTooltip": "tooltip - Lifting the pen off the writing surface prevents anything from being drawn.  Lowering it (after it has been lifted) enables it to draw again.", "Turtle.penUp": "block text - Infinitive or imperative of a verb to lift up a pen so that moving it leaves no mark on the writing surface.", "Turtle.penDown": "block text - Infinitive or imperative of a verb to lower a raised pen so that moving it leaves a mark on the writing surface.", "Turtle.turtleVisibilityTooltip": "tooltip", "Turtle.hideTurtle": "block text - Infinitive or imperative of a verb telling a turtle to hide itself (become invisible).", "Turtle.showTurtle": "block text - Infinitive or imperative of a verb telling a turtle to show itself (become visible after having been invisible).", "Turtle.printHelpUrl": "{{Optional}} The URL of a page with basic information about '''printing''' or '''typography'''.\nMake sure you use secure HTTPS links, to avoid generating mixed contents and security alerts in browsers when viewing the current page in a secure HTTPS session. If not using Wikipedia, make sure the target is non intrusive, but if in doubt, link to a page in another suitable language supported by a Wikimedia project.", "Turtle.printTooltip": "tooltip - Note that 'print' refers to displaying text on the screen, not on an external printer.", "Turtle.print": "block text - Infinitive or imperative of a verb telling a turtle to display text on the screen.  This is always followed by a block indicating what should be printed.\n{{Identical|Print}}", "Turtle.fontHelpUrl": "{{Optional}} The URL of a page with basic information about '''typographic fonts'''.\nMake sure you use secure HTTPS links, to avoid generating mixed contents and security alerts in browsers when viewing the current page in a secure HTTPS session. If not using Wikipedia, make sure the target is non intrusive, but if in doubt, link to a page in another suitable language supported by a Wikimedia project.", "Turtle.fontTooltip": "tooltip - This is shown on the block that lets the user specify the font [family], size, and style that should be used for subsequent displays of text.", "Turtle.font": "block text - This precedes a dropdown menu specifying the typographic font [family] that should be used when displaying text.\n{{Identical|Font}}", "Turtle.fontSize": "block text - This precedes a number specifying the size of the typographic font that should be used when displaying text.  This appears in the same block as 'font', so that word should not be repeated.\n{{Identical|Font size}}", "Turtle.fontNormal": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Specifies that a typographic font should be normal (neither in italics or bold).", "Turtle.fontBold": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Specifies that a typographic font should be [https://en.wikipedia.org/wiki/Emphasis_(typography) bold].", "Turtle.fontItalic": "[[Translating:Blockly#Drop-Down_Menus dropdown]] - Specifies that a typographic font should be [https://en.wikipedia.org/wiki/Italics italic].", "Turtle.submitDisabled": "Error message.", "Turtle.galleryTooltip": "tooltip - Pressing this button opens a gallery of drawings made by other users.", "Turtle.galleryMsg": "Label on a button that opens a gallery of drawings made by other users.", "Turtle.submitTooltip": "tooltip - Pressing this button causes the drawing created by the user's program to be submitted to a gallery for other people to see.", "Turtle.submitMsg": "Label on a button that submits the user's art to a public gallery.", "Turtle.helpUseLoop": "Dialog telling user to seek a better answer.", "Turtle.helpUseLoop3": "Dialog telling user to seek a simpler answer.", "Turtle.helpUseLoop4": "Dialog telling user to seek a simpler answer.", "Turtle.helpText1": "Instructions.", "Turtle.helpText2": "Instructions.", "Turtle.helpText3a": "Introducing a new block.", "Turtle.helpText3b": "Instructions.", "Turtle.helpText4a": "Introducing a new block.\n\nCross-reference to ensure consistent terminology:\n* {{msg-blockly|Turtle.penTooltip}}", "Turtle.helpText4b": "Instructions.", "Turtle.helpText5": "Instructions.", "Turtle.helpText6": "Instructions.", "Turtle.helpText7": "Instructions.", "Turtle.helpText8": "Instructions.", "Turtle.helpText9": "Instructions.", "Turtle.helpText10": "Instructions.", "Turtle.helpText10Reddit": "Instructions on publishing your drawing.", "Turtle.helpToolbox": "Instructions for accessing blocks that are hidden inside categories.", "Movie.x": "Label for an x-coordinate (horizontal) input.", "Movie.y": "Label for a y-coordinate (vertical) input.", "Movie.x1": "Label for the x-coordinate of the start of a line.", "Movie.y1": "Label for the y-coordinate of the start of a line.", "Movie.x2": "Label for the x-coordinate of the end of a line.", "Movie.y2": "Label for the y-coordinate of the end of a line.", "Movie.radius": "Label for a circle's radius input.\n{{Identical|Radius}}", "Movie.width": "Label for a rectangle or line's width input.\n{{Identical|Width}}", "Movie.height": "Label for a rectangle's height input.\n{{Identical|Height}}", "Movie.circleTooltip": "tooltip", "Movie.circleDraw": "Command to draw a circle.", "Movie.rectTooltip": "tooltip", "Movie.rectDraw": "Command to draw a rectangle.\n{{Identical|Rectangle}}", "Movie.lineTooltip": "tooltip", "Movie.lineDraw": "Command to draw a line.\n{{Identical|Line}}", "Movie.timeTooltip": "tooltip", "Movie.colourTooltip": "tooltip - Changes the colour of ink in the pen carried by the turtle.", "Movie.setColour": "block text - Infinitive or imperative of a verb to specify the colour of the lines that should be drawn in the future by an imaginary pen.  This is followed by a block showing the colour.", "Movie.submitDisabled": "Error message.", "Movie.galleryTooltip": "tooltip - Pressing this button opens a gallery of movies made by other users.", "Movie.galleryMsg": "Label on a button that opens a gallery of movies made by other users.", "Movie.submitTooltip": "tooltip - Pressing this button causes the movie created by the user's program to be submitted to a gallery for other people to see.", "Movie.submitMsg": "Label on a button that submits the user's movie to a public gallery.", "Movie.helpLayer": "Dialog telling user to change the order of their program.", "Movie.helpText1": "Instructions.", "Movie.helpText2a": "Instructions.  The play button looks like the video play button on YouTube.", "Movie.helpText2b": "Instructions.  Do not translate the word 'time' or the name 'y'.", "Movie.helpText3": "Instructions.  Do not translate the word 'time' or the name 'y'.", "Movie.helpText4": "Instructions.", "Movie.helpText5": "Instructions.", "Movie.helpText6": "Instructions.  Drawing hands on a picture of a person.", "Movie.helpText7": "Instructions.  Do translate the word 'if'.", "Movie.helpText8": "Instructions.", "Movie.helpText9": "Instructions.", "Movie.helpText10": "Instructions.", "Movie.helpText10Reddit": "Instructions on publishing your movie.", "Music.playNoteTooltip": "tooltip", "Music.playNote": "Plays one musical note.  %1 is duration of the note (icon for whole, quarter, ...), %2 is the pitch of the note (A3, B3, C4, ...).", "Music.restTooltip": "tooltip", "Music.restWholeTooltip": "tooltip", "Music.rest": "Plays no music for a duration specified.  %1 is the duration (icon for whole, quarter, ...).", "Music.setInstrumentTooltip": "tooltip", "Music.setInstrument": "Sets to an instrument to play music.  %1 is type of the instrument (guitar, piano, ...).", "Music.startTooltip": "tooltip", "Music.start": "The program associated with this label is executed when a button is clicked.  %1 is image of the play icon on the button.", "Music.pitchTooltip": "tooltip - Describes a musical note selector.  'C4' (also known as 'DO') is the note in the center of a piano keyboard and is represented as the number 7.", "Music.firstPart": "Example name of a function that plays the first part of some music.", "Music.piano": "Name of musical instrument.\n{{Identical|piano}}", "Music.trumpet": "Name of musical instrument.\n{{Identical|trumpet}}", "Music.banjo": "Name of musical instrument.\n{{Identical|banjo}}", "Music.violin": "Name of musical instrument.\n{{Identical|violin}}", "Music.guitar": "Name of musical instrument.\n{{Identical|guitar}}", "Music.flute": "Name of musical instrument.\n{{Identical|flute}}", "Music.drum": "Name of musical instrument.\n{{Identical|drum}}", "Music.choir": "Name of musical instrument.\n{{Identical|choir}}", "Music.submitDisabled": "Error message.", "Music.galleryTooltip": "tooltip - Pressing this button opens a gallery of music made by other users.", "Music.galleryMsg": "Label on a button that opens a gallery of music made by other users.", "Music.submitTooltip": "tooltip - Pressing this button causes the music created by the user's program to be submitted to a gallery for other people to see.", "Music.submitMsg": "Label on a button that submits the user's art to a public gallery.", "Music.helpUseFunctions": "Dialog telling user to seek a better answer.", "Music.helpUseInstruments": "Dialog telling user not to use only one musical instrument (piano, flute, etc.).", "Music.helpText1": "Instructions.  If your country has a song to the tune of '<PERSON><PERSON>' use its name.  E.g. <PERSON><PERSON><PERSON> (German) or <PERSON><PERSON><PERSON> <PERSON> (Vietnamese).", "Music.helpText2a": "Introducing a new block: function or subroutines", "Music.helpText2b": "Instructions.", "Music.helpText3": "Instructions.", "Music.helpText4": "Instructions.", "Music.helpText5": "Instructions.", "Music.helpText6a": "Introducing a new block that changes musical instruments.", "Music.helpText6b": "Instructions.", "Music.helpText7a": "Introducing a new block: a musical rest.", "Music.helpText7b": "Instructions.", "Music.helpText8": "Instructions.", "Music.helpText9": "Instructions.", "Music.helpText10": "Instructions.", "Music.helpText10Reddit": "Instructions on publishing your music.", "Pond.scanTooltip": "Tooltip for the block that allows players to scan for enemies.", "Pond.cannonTooltip": "Tooltip for the block that allows players to shoot at other players.", "Pond.swimTooltip": "Tooltip for the block that allows players to move.", "Pond.stopTooltip": "Tooltip for the block that allows players to stop.", "Pond.healthTooltip": "Tooltip for the block that reports the player's health.", "Pond.speedTooltip": "Tooltip for the block that reports the player's speed.", "Pond.locXTooltip": "Tooltip for the block that reports the player's horizontal location.", "Pond.locYTooltip": "Tooltip for the block that reports the player's vertical location.", "Pond.logTooltip": "Tooltip for the block that prints debugging information.", "Pond.docsTooltip": "Tooltip for the button that opens the language reference documentation.", "Pond.documentation": "Text on the button that opens the language reference documentation.\n{{Identical|Documentation}}", "Pond.playerName": "Generic name for the player of this video game.\n{{Identical|Player}}", "Pond.targetName": "Name for a player that does nothing.\n{{Identical|Target}}", "Pond.pendulumName": "Name for a player that moves back and forth.\n{{Identical|Pendulum}}", "Pond.scaredName": "Name for a player that is scared.\n{{Identical|Scared}}", "Pond.helpUseScan": "Dialog telling user to seek a better answer.  Do not translate the word 'scan'.", "Pond.helpText1": "Instructions. Do not translate the word 'cannon'.", "Pond.helpText2": "Instructions.  Do not translate 'while (true)'.", "Pond.helpText3a": "Instructions.  Do not translate 'scan'.", "Pond.helpText3b": "Instructions.  Do not translate 'cannon'.", "Pond.helpText4": "Instructions.  Do not translate the word 'swim'.", "Pond.helpText5": "Instructions.  Do not translate the word 'stop'.", "Pond.helpText6": "Instructions.", "Gallery": "Display of art created by user's programs.\n{{Identical|Gallery}}"}