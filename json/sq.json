{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON><PERSON><PERSON>", "Games.puzzle": "Gjëegjëzë", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "Breshkë", "Games.movie": "Film", "Games.music": "Muzikë", "Games.pondTutor": "Tutori i Pellgut", "Games.pond": "<PERSON><PERSON>g", "Games.linesOfCode1": "Zgjodhët këtë nivel me një 1 rresht JavaScript:", "Games.linesOfCode2": "Zgjodhët këtë nivel me %1 rreshta JavaScript:", "Games.nextLevel": "A je gati për nivelin %1?", "Games.finalLevel": "A je gati për sfidën tjetër?", "Games.submitTitle": "<PERSON><PERSON><PERSON><PERSON>:", "Games.linkTooltip": "Ruaje dhe lidhu te blloqet.", "Games.runTooltip": "Ekzekuto programin që shkrove.", "Games.runProgram": "Ekzekuto Programin", "Games.resetTooltip": "Ndale programin dhe reseto nivelin.", "Games.resetProgram": "<PERSON><PERSON><PERSON><PERSON>", "Games.help": "Ndihmë", "Games.catLogic": "Logjikë", "Games.catLoops": "Qark", "Games.catMath": "Matematikë", "Games.catText": "Tekst", "Games.catLists": "Listat", "Games.catColour": "Ngjyrë", "Games.catVariables": "Variablat", "Games.catProcedures": "Funksionet", "Games.httpRequestError": "Kishte problem me kërkesën.", "Games.linkAlert": "N<PERSON>j bllo<PERSON>et me këtë lidhje:\n\n%1", "Games.hashError": "Na vjen keq, '%1' nuk korrespondon me ndonjë program të ruajtur.", "Games.xmlError": "Nuk mund të ngarkoheshin skedarët tuaj të ruajtur. Ndoshta është krijuar me version tjetër të Blockly?", "Games.submitted": "Faleminderit për këtë program! Nëse stafit tonë të majmunëve të stërvitur i pëlqen, ata do ta botojnë atë në galeri brenda dy ditësh.", "Games.listVariable": "listë", "Games.textVariable": "tekst", "Games.breakLink": "Sapo të filloni të modifikoni JavaScript, nuk mund të ktheheni te blloqet e redaktimit. A është kjo në rregull?", "Games.blocks": "Blloqet", "Games.congratulations": "Urime!", "Games.helpAbort": "Ky nivel është jashtëzakonisht i vështirë. Dëshiron ta kapërcesh dhe të shkosh në lojën tjetër? Mund të ktheheni më vonë.", "Index.clear": "<PERSON><PERSON>j të gjitha zgjedhjet tuaja?", "Index.subTitle": "Lojra për programerët e së nesërmes.", "Index.moreInfo": "Info për eduka<PERSON> ...", "Index.startOver": "Dëshi<PERSON> të rifillosh?", "Index.clearData": "Pastro të dhënat", "Puzzle.animal1": "Shotë", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Sqepi", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Mace", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://sq.wikipedia.org/wiki/<PERSON><PERSON>", "Puzzle.animal3": "Bletë", "Puzzle.animal3Trait1": "Mjaltë", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://sq.wikipedia.org/wiki/Bleta", "Puzzle.animal4": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "Guaskë", "Puzzle.animal4Trait2": "Jargë", "Puzzle.animal4HelpUrl": "https://sq.wikipedia.org/wiki/K%C3%ABrmilli", "Puzzle.picture": "foto:", "Puzzle.legs": "këmbët:", "Puzzle.legsChoose": "zgjedh...", "Puzzle.traits": "tiparet:", "Puzzle.error0": "Perfekt!\nTë gjitha blloqet %1 janë të sakta.", "Puzzle.error1": "Pothuajse. Një bllok nuk është korrekt.", "Puzzle.error2": "%1 blloqet nuk janë korrekt.", "Puzzle.tryAgain": "Blloku i theksuar nuk është korrekt.\nProvo prap.", "Puzzle.checkAnswers": "Kontrollo Përgjigjen", "Puzzle.helpText": "<PERSON><PERSON><PERSON> (të g<PERSON>lbë<PERSON>), bashkëngjitni pamjen e saj, zgjidhni numrin e këmbëve dhe bëni një listë të tipareve të saj.", "Maze.moveForward": "ec përpara", "Maze.turnLeft": "<PERSON><PERSON><PERSON> maj<PERSON>", "Maze.turnRight": "k<PERSON><PERSON> dja<PERSON>", "Maze.doCode": "b<PERSON><PERSON>", "Maze.helpIfElse": "blloqet nëse-përndryshe do ta bëjnë njërën gjë ose tjetrën.", "Maze.pathAhead": "n<PERSON>se rruga p<PERSON>", "Maze.pathLeft": "n<PERSON>se rruga majtas", "Maze.pathRight": "n<PERSON>se r<PERSON>a dja<PERSON>", "Maze.repeatUntil": "<PERSON><PERSON><PERSON><PERSON><PERSON> derisa", "Maze.moveForwardTooltip": "Lëvizë lojtarin përpara një hapësirë", "Maze.turnTooltip": "<PERSON><PERSON><PERSON> loj<PERSON> majtas ose djathta 90 shkallë.", "Maze.ifTooltip": "Nëse ka një rrugë në drejtimin e caktuar, atëherë bëni disa veprime.", "Maze.ifelseTooltip": "Nëse ka një rrugë në drejtimin e caktuar, atëherë bëni bllokun e parë të veprimeve. Përndryshe bëni bllokun e dytë të veprimeve.", "Maze.whileTooltip": "Nëse ka një rrugë në drejtimin e caktuar, atëherë bëni bllokun e parë të veprimeve. Përndryshe bëni bllokun e dytë të veprimeve.", "Maze.capacity0": "Ke %0 blloqe të mbetura.", "Maze.capacity1": "Ke %1 blloqe të mbetura.", "Maze.capacity2": "Ke %2 blloqe të mbetura.", "Maze.runTooltip": "<PERSON> bën loj<PERSON>in të bëj çfarë blloqet thonë.", "Maze.resetTooltip": "<PERSON><PERSON><PERSON> loj<PERSON>in në fillim të labirintit.", "Maze.helpStack": "Rëndit një çift të blloqeve 'lë<PERSON>z përpara' së bashku për të më ndihmuar të arrij q<PERSON>.", "Maze.helpOneTopBlock": "<PERSON><PERSON> k<PERSON>t<PERSON> nivel, duhet të grumbulloni të gjitha blloqet në hapësirën  bardhë të punës.", "Maze.helpRun": "Ekzekuto programin për të parë çfarë ndodhë.", "Maze.helpReset": "Programi yt nuk e zgjidhi këtë labirint. Shtyp 'Reseto' dhe provo përsëri.", "Maze.helpRepeat": "Arrij fundin e kësaj rruge duke p<PERSON><PERSON><PERSON><PERSON> vetëm dy blloqe. Përdor 'p<PERSON>rs<PERSON>ris' për të ekzekutuar një bllok më shumë se një herë.", "Maze.helpCapacity": "Ke përdorur të gjitha blloqet për këtë nivel. Për të krijuar një bllok të ri, së pari duhet të fshish një bllok ekzistues.", "Maze.helpRepeatMany": "Mund të vendosni më shumë se një bllok brenda një blloku 'përsëritje'.", "Maze.helpSkins": "Zgjedhni lojtarin tuaj të preferuar nga kjo meny.", "Maze.helpIf": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> \"në<PERSON>\" do të bëjë diçka vetëm nëse kushti është i vërtetë. Provoni të ktheheni majtas nëse ka një rrugë në të majtë.", "Maze.helpMenu": "Kliko në %1 në bllokun 'nëse' për të ndryshuar gjendjen e tij.", "Maze.helpWallFollow": "Mund ta zg<PERSON>hësh këtë labirint të komplikuar? Provo të ndiqni murin në të majtë. Vetëm për programuesit e avancuar!", "Bird.noWorm": "nuk ka krimb", "Bird.heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "Gjendja kur zogu nuk e ka fituar krimbin.", "Bird.headingTooltip": "Lëviz në drejtim të këndit të dhënë: 0 është djathtas, 90 është përpjetë, etj.", "Bird.positionTooltip": "x dhe y shënojnë pozicionin e shpendëve. Kur x = 0, zogu është afër skajit të majtë, kur x = 100 është pranë skajit të djathtë. Kur y = 0 zogu është në fund, kur y = 100 është në krye.", "Bird.helpHeading": "Ndryshoni këndin e drejtimit për ta bërë zogun të marrë krimbën dhe të fluturoj në fole.", "Bird.helpHasWorm": "Përdore këtë bllok për të shkuar në një drejtim nëse keni krimb, ose një drejtim tjetër nëse nuk keni krimb.", "Bird.helpX": "'x' është pozicioni juaj aktual horizontal. Përdoreni këtë bllok për të shkuar në një drejtim nëse 'x' është më pak se një numër, ose në një drejtim tjetër nëse është ndryshe.", "Bird.helpElse": "Klikoni ikonën për të modifikuar bllokun \"nëse\".", "Bird.helpElseIf": "Ky nivel ka nevojë edhe për një bllok \"përndryshe nëse\" dhe një \"tjet<PERSON>r\" bllok.", "Bird.helpAnd": "Blloku 'dhe' është i vërtetë vetëm nëse të dyja inputet e tij janë të vërteta.", "Bird.helpMutator": "<PERSON><PERSON><PERSON> bllokun '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' në bllokun 'në<PERSON>'.", "Turtle.moveTooltip": "Lëvizë breshkën përpara ose prapa sipas vlerës së specifikuar.", "Turtle.moveForward": "ec përpara", "Turtle.moveBackward": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Turtle.turnTooltip": "<PERSON><PERSON><PERSON> breshkën majtas ose djathtas me numër të caktuar të shkallëve.", "Turtle.turnRight": "k<PERSON><PERSON> dja<PERSON>", "Turtle.turnLeft": "<PERSON><PERSON><PERSON> maj<PERSON>", "Turtle.widthTooltip": "Ndryshon gjerësinë e lapsit.", "Turtle.setWidth": "vendosë gjerësinë në", "Turtle.colourTooltip": "Ndryshon ng<PERSON>yrën e lapsit.", "Turtle.setColour": "b<PERSON><PERSON>", "Turtle.penTooltip": "<PERSON><PERSON><PERSON> ose ulë s<PERSON>, për të ndaluar ose për të filluar vizatimin.", "Turtle.penUp": "pena lart<PERSON>", "Turtle.penDown": "pena posht<PERSON>", "Turtle.turtleVisibilityTooltip": "E bën breshkën (rrethi dhe shigjeta) të dukshme ose të padukshme.", "Turtle.hideTurtle": "f<PERSON>h bresh<PERSON>n", "Turtle.showTurtle": "sh<PERSON>q bresh<PERSON>", "Turtle.printHelpUrl": "https://sq.wikipedia.org/wiki/Shtypi", "Turtle.printTooltip": "Vizaton tekst në drejtimin e breshkës në vendndodhjen e saj.", "Turtle.print": "shtyp", "Turtle.fontTooltip": "Vendosë fontin e përdorur nga blloku i shtypur.", "Turtle.font": "fonti", "Turtle.fontSize": "madhësia e shkronjave", "Turtle.fontNormal": "normale", "Turtle.fontBold": "trashë", "Turtle.fontItalic": "italike", "Turtle.submitDisabled": "Ekzekuto programin deri sa të ndalet. Pastaj mund ta dorëzosh vizatimin në galeri.", "Turtle.galleryTooltip": "Hapni galerinë e vizatimeve.", "Turtle.galleryMsg": "<PERSON><PERSON>", "Turtle.submitTooltip": "<PERSON><PERSON> vizatimin tuaj në galeri.", "Turtle.submitMsg": "Dorëzo në Galeri", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON><PERSON><PERSON> yte punon, por mund të bësh më mire.", "Turtle.helpUseLoop3": "Vizato formën me vetëm tri blloqe.", "Turtle.helpUseLoop4": "<PERSON><PERSON>to yllin me vetëm katër bllo<PERSON>e.", "Turtle.helpText1": "Krijo një program që vizaton një katror.", "Turtle.helpText2": "Ndrysho një program për të vizatuar një pentagon në vend të katrorit.", "Turtle.helpText3a": "Ka një bllok të ri që të lejon të ndryshoni ngjyrën:", "Turtle.helpText3b": "Vizato një yll të verdhë.", "Turtle.helpText4a": "Ka një bllok të ri që ju lejon të hiqni penën nga letra kur lëvizni:", "Turtle.helpText4b": "Vizato një yll të verdhë të vogël, pastaj vizato një vijë mbi ti.", "Turtle.helpText5": "Në vend të një ylli, a mund të vizatoni katër yje të aranzhuara në një katror?", "Turtle.helpText6": "Vizato tri yje të verdha, dhe një vijë të bardhë.", "Turtle.helpText7": "<PERSON><PERSON><PERSON> y<PERSON>, pastaj vizato katër vija të bardha.", "Turtle.helpText8": "Vizatimi i 360 vijave të bardha do të duket si hëna e plotë.", "Turtle.helpText9": "A mund të shtoni një rreth të zi në mënyrë që hëna të bëhet gjysmëhënë?", "Turtle.helpText10": "Vizatoni çdo gjë që dëshironi. Keni një numër të madh të blloqeve të reja që mund të eksploroni. <PERSON>ë<PERSON>hi qejf!", "Turtle.helpText10Reddit": "<PERSON><PERSON>rdor butonin \"<PERSON><PERSON> galerin<PERSON>\" për të parë se çfarë kanë vizatuar të tjerët. Nëse vizatoni diçka interesante, përdor butonin \"Paraqit në Galeri\" për ta publikuar atë.", "Turtle.helpToolbox": "Zgjedh një kategori për të parë blloqet.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "fillo x", "Movie.y1": "fillo y", "Movie.x2": "përfundo x", "Movie.y2": "përfundo y", "Movie.radius": "<PERSON><PERSON><PERSON>", "Movie.width": "gjerësia", "Movie.height": "lartësia", "Movie.circleTooltip": "<PERSON><PERSON>ton një rreth në vendin e caktuar dhe me rreze të caktuar.", "Movie.circleDraw": "rreth", "Movie.rectTooltip": "Tërheq një drejtkëndësh në vendin e caktuar dhe me gjerësinë dhe lartësinë e specifikuar.", "Movie.rectDraw": "kënddrejtë", "Movie.lineTooltip": "Vizaton një vijë nga një pikë në tjetrën me gjerësinë e specifikuar.", "Movie.lineDraw": "vijë", "Movie.timeTooltip": "<PERSON><PERSON> koh<PERSON> e tanishme në animacion (0-100).", "Movie.colourTooltip": "Ndryshon ng<PERSON>yrën e lapsit.", "Movie.setColour": "b<PERSON><PERSON>", "Movie.submitDisabled": "Filmi yt nuk lëviz. Përdor blloqe për të bërë diçka interesante. Pastaj mund ta paraqesësh filmin tënd në galeri.", "Movie.galleryTooltip": "Hapni galerinë e filmave.", "Movie.galleryMsg": "<PERSON><PERSON>", "Movie.submitTooltip": "Postoni filmin tuaj në galeri.", "Movie.submitMsg": "Dorëzo në Galeri", "Movie.helpLayer": "Zhvendosni rrethin e sfondit në krye të programit tuaj.Atëherë ajo do të shfaqet pas personit.", "Movie.helpText1": "Përdor forma të thjeshta për ta vizatuar këtë person.", "Movie.helpText2a": "<PERSON><PERSON> nivel është një film. Ju dëshironi që krahu i personit të lëvizë nëpër ekran. Shtypni butonin e luajtjes për të parë një pamje paraprake.", "Movie.helpText2b": "Ndërsa filmi luan, vlera e bllokut 'koha' llogaritet nga 0 në 100. Meqenëse dëshironi që pozicioni 'y' i krahut të fillojë në 0 dhe të shkojë në 100 kjo duhet të jetë e lehtë.", "Movie.helpText3": "Blloku 'koha' llogaritet nga 0 në 100. Por tani ju doni që pozicioni 'y' i krahut tjetër të fillojë në 100 dhe të shkojë në 0. A mund të gjeni një formulë të thjeshtë matematikore që ndryshon drejtimin?", "Movie.helpText4": "Përdorni atë që keni mësuar në nivelin e mëparshëm për të bërë këmbët që kryqë<PERSON>hen.", "Movie.helpText5": "Formula matematikore për krahun është e komplikuar. Ja përgjigja:", "Movie.helpText6": "<PERSON><PERSON><PERSON> personit dy duar.", "Movie.helpText7": "Përdorni bllokun 'në<PERSON>' për të nxjerrë një kokë të vogël për gjysmën e parë të filmit. Pastaj vizatoni një kokë të madhe për gjysmën e dytë të filmit.", "Movie.helpText8": "Bëni që këmbët të ndërrojnë drejtimin në të kundërt në gjysmë të rrugës përmes filmit.", "Movie.helpText9": "Vizatoni një rreth që zgjerohet pas personit.", "Movie.helpText10": "Bëj një film të çkado që dëshirosh. Ke një numër të madh të blloqeve të reja me të cilët mund të eksplorosh. Bë<PERSON>h qejf!", "Movie.helpText10Reddit": "Përdorni butonin '<PERSON><PERSON>' për të parë filmat që kanë bërë të tjerët. Nëse bëni një film interesant, përdorni butonin 'Posto në Galeri' për ta botuar.", "Music.playNoteTooltip": "Luaj një notë muzikore të kohëzgjatjes dhe lartësisë së caktuar.", "Music.playNote": "luaj %1 nota %2", "Music.restTooltip": "Prit për kohëzgjatjen e specifikuar.", "Music.restWholeTooltip": "<PERSON>rit për një notë të plotë.", "Music.rest": "pusho %1", "Music.setInstrumentTooltip": "Kalon në instrumentin e caktuar kur luan notat muzikore në vazhdim.", "Music.setInstrument": "vendos instrumentin %1", "Music.startTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> blloqet brenda kur klikohet butoni 'Ekzekuto Programin'.", "Music.start": "kur klikohet %1", "Music.pitchTooltip": "Një shënim (C4 është 7).", "Music.firstPart": "pjesa e parë", "Music.piano": "piano", "Music.trumpet": "trumpetë", "Music.banjo": "<PERSON><PERSON><PERSON>", "Music.violin": "violinë", "Music.guitar": "gitarë", "Music.flute": "flautë", "Music.drum": "bateri", "Music.choir": "kor", "Music.submitDisabled": "Ekzekuto programin deri sa të ndalet. Pastaj mund ta dorëzo<PERSON> muzikën në galeri.", "Music.galleryTooltip": "Hapni galerinë e muzikës.", "Music.galleryMsg": "<PERSON><PERSON>", "Music.submitTooltip": "Postoni muzikën tuaj në galeri.", "Music.submitMsg": "Dorëzo në Galeri", "Music.helpUseFunctions": "Zgjid<PERSON>ja yte funksionon, por mund të bësh më mirë. Përdor funksione për të zvogëluar sasinë e kodit të përsëritur.", "Music.helpUseInstruments": "Muzika do të tingëllojë më mirë nëse përdorni një instrument tjetër në secilin bllok fillestar.", "Music.helpText1": "Kompono katër notat e para të 'Frère Jacques'.", "Music.helpText2a": "Një 'funksion' të lejon t'i gruposh blloqet së bashku, pastaj t'i ekzeku<PERSON>h ato më shumë se një herë.", "Music.helpText2b": "Krijo një funksion për të luajtur katër notat e para të 'Frère Jacques'. <PERSON><PERSON><PERSON><PERSON><PERSON> atë funksion dy herë. <PERSON><PERSON> shto blloqe të reja të notave.", "Music.helpText3": "Krijo një funksion të dytë për pjesën tjetër të '<PERSON><PERSON> Jacques'. Nota e fundit është më i gjatë.", "Music.helpText4": "Krijo një funksion të tretë për pjesën tjetër të '<PERSON><PERSON> Jacques'. Katër notat e para janë më të shkurtra.", "Music.helpText5": "Plotësoni melodinë e plotë të '<PERSON>ère Jacques'.", "Music.helpText6a": "Ky bllok i ri të lejon të ndryshosh në një instrument tjetër.", "Music.helpText6b": "<PERSON><PERSON> melodinë me violinë.", "Music.helpText7a": "K<PERSON> bllok i ri shton një vonesë pa zë.", "Music.helpText7b": "<PERSON><PERSON><PERSON> një bllok të dytë që ka dy bllo<PERSON><PERSON>, pastaj luan edhe '<PERSON><PERSON>'.", "Music.helpText8": "Çdo bllok fillimi duhet të luajë '<PERSON><PERSON>' dy herë.", "Music.helpText9": "<PERSON><PERSON><PERSON> blloqe fillestare që secili luan \"<PERSON><PERSON>\" dy herë. Shtoni numrin e saktë të blloqeve të vonesës.", "Music.helpText10": "Përgatit çfarëdo që dëshiron. Ke një numër të madh të blloqeve të reja që mund të eksplorosh. Bëfsh qejf!", "Music.helpText10Reddit": "P<PERSON>rdor butonin \"<PERSON><PERSON> galerin<PERSON>\" për të parë se çfarë kanë komponuar të tjerët. Nëse komponon diçka interesante, përdor butonin \"Dorëzo në Galeri\" për ta publikuar atë.", "Pond.scanTooltip": "Skano për armiq. Specifiko një drejtim (0-360). Kthen distancën e armikut më të afërt në atë drejtim. Kthen Pafundësisht nëse nuk gjendet armik.", "Pond.cannonTooltip": "<PERSON><PERSON><PERSON><PERSON> topin. <PERSON><PERSON><PERSON><PERSON> (0-360) d<PERSON> <PERSON><PERSON><PERSON><PERSON> (0-70).", "Pond.swimTooltip": "Noto përpara. Specifikon drejtimin (0-360).", "Pond.stopTooltip": "Ndalo notimin. Lojtari do të ngadalësohet deri sa të ndalet.", "Pond.healthTooltip": "Kthen shëndetin aktual të lojtarit (0 është i vdekur, 100 është i shëndetshëm).", "Pond.speedTooltip": "<PERSON><PERSON> shpejtësinë aktuale të lojtarit (0 është ndalur, 100 është shpejtësia e plotë).", "Pond.locXTooltip": "Jep koordinatën X të lojtarit (0 është buza e majtë, 100 është buza e djathtë).", "Pond.locYTooltip": "<PERSON>p koordinatën Y të lojtarit (0 është buza e poshtme, 100 është buza e sipërme).", "Pond.logTooltip": "Printon një numër në shfletuesin tuaj console.", "Pond.docsTooltip": "Shfaq dokumentimin e gjuhës.", "Pond.documentation": "Dokumentacion", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "Caku", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "I friksuar", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yte funk<PERSON>on, por mund të bësh më mirë. Përdorni 'scan' për t'i treguar topit sa larg të gjuaj.", "Pond.helpText1": "Përdorni komandën 'cannon' për të goditur objektivin. Parametri i parë është këndi, parametri i dytë është largësia. Gjeni kombinimin e duhur.", "Pond.helpText2": "Ky objektiv duhet të goditet shumë herë. Përdorni një 'qark' 'while (true)' për të bërë diçka pa fund.", "Pond.helpText3a": "<PERSON><PERSON> kund<PERSON><PERSON><PERSON><PERSON> lë<PERSON>z prapa dhe m<PERSON>pa, duke e bërë të vështirë për t'u goditur. Shprehja 'scan' kthen distancën e saktë të kundërshtarit në drejtimin e caktuar.", "Pond.helpText3b": "Ky  është pikërisht ajo që komanda e 'cannon' duhet të zjarri me saktësi.", "Pond.helpText4": "Ky kundërshtar është shumë larg për ta përdorur topin (i cili ka një limit prej 70 metrash). Në vend të kësaj, p<PERSON>rdorni komandën 'swim' për të filluar notimin ndaj kundërshtarit dhe përplasuni në të.", "Pond.helpText5": "Ky kundërshtar është gjithashtu shumë larg për të përdorur topin. Por ti je shumë i dobët për të mbijetuar një përplasje. Noto drejt kundërshtarit ndërsa vendndodhja yte horizontale është më pak se 50. <PERSON><PERSON> 'ndal<PERSON>' dhe përdor topin.", "Pond.helpText6": "Ky kundërshtar do të largohet kur të goditet. Noto drejt tij nëse është jashtë rrezes së veprimit (70 metra).", "Gallery": "Galeria"}