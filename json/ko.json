{"@metadata": {"authors": ["Codenstory", "Delanoor", "Hym411", "JeonHK", "Jerrykim306", "<PERSON><PERSON><PERSON>", "Neatnet", "Priviet", "<PERSON><PERSON>", "SeoJeongHo", "<PERSON><PERSON><PERSON><PERSON>", "렌즈", "아라"]}, "Games.name": "블록리 게임", "Games.puzzle": "퍼즐", "Games.maze": "미로", "Games.bird": "새", "Games.turtle": "거북이", "Games.movie": "동영상", "Games.music": "음악", "Games.pondTutor": "폰드 튜터", "Games.pond": "Pond", "Games.linesOfCode1": "이 레벨을 자바스크립트 1 줄로 해결했습니다.", "Games.linesOfCode2": "이 레벨을 자바스크립트 %1 줄로 해결했습니다.", "Games.nextLevel": "레벨 %1 에 대한 준비가 됬나요?.", "Games.finalLevel": "다음 도전과제에 대한 준비가 됬나요?", "Games.submitTitle": "제목:", "Games.linkTooltip": "블록을 저장하고 링크를 가져옵니다.", "Games.runTooltip": "당신이 작성한 프로그램을 실행합니다.", "Games.runProgram": "프로그램 실행", "Games.resetTooltip": "프로그램을 멈추고 레벨을 초기화합니다.", "Games.resetProgram": "다시하기", "Games.help": "도움말", "Games.catLogic": "논리", "Games.catLoops": "반복", "Games.catMath": "수학", "Games.catText": "문자열", "Games.catLists": "목록", "Games.catColour": "색", "Games.catVariables": "변수", "Games.catProcedures": "함수", "Games.httpRequestError": "요청에 문제가 있습니다.", "Games.linkAlert": "다음 링크로 블록을 공유하세요:\n\n%1", "Games.hashError": "죄송하지만 '%1'은 저장된 프로그램 중 일치하는 것이 없습니다.", "Games.xmlError": "저장된 파일을 불러올 수 없습니다. 혹시 블록리의 다른 버전으로 만들었습니까?", "Games.submitted": "이 프로그램에 감사드립니다! 훈련된 원숭이 직원이 좋아하면 며칠 내에 갤러리에 게시할 것입니다.", "Games.listVariable": "목록", "Games.textVariable": "텍스트", "Games.breakLink": "자바스크립트 편집을 시작하면 편집 블록으로 되돌아갈 수 없습니다. 그렇게 하시겠습니까?", "Games.blocks": "블록", "Games.congratulations": "축하합니다!", "Games.helpAbort": "이 레벨은 굉장히 어렵습니다. 건너뛰고 다음으로 넘어가시겠습니까? 나중에 다시 돌아올 수 있습니다.", "Index.clear": "당신의 모든 답을 지웁니까?", "Index.subTitle": "미래의 프로그래머들을 위한 게임.", "Index.moreInfo": "교사 정보...", "Index.startOver": "다시 시작하시겠습니까?", "Index.clearData": "데이터 지우기", "Puzzle.animal1": "오리", "Puzzle.animal1Trait1": "깃털", "Puzzle.animal1Trait2": "부리", "Puzzle.animal1HelpUrl": "https://ko.wikipedia.org/wiki/%EC%98%A4%EB%A6%AC", "Puzzle.animal2": "고양이", "Puzzle.animal2Trait1": "수염", "Puzzle.animal2Trait2": "모피", "Puzzle.animal2HelpUrl": "https://ko.wikipedia.org/wiki/%EA%B3%A0%EC%96%91%EC%9D%B4", "Puzzle.animal3": "벌", "Puzzle.animal3Trait1": "꿀", "Puzzle.animal3Trait2": "독침", "Puzzle.animal3HelpUrl": "https://ko.wikipedia.org/wiki/%EB%B2%8C_(%EA%B3%A4%EC%B6%A9)", "Puzzle.animal4": "달팽이", "Puzzle.animal4Trait1": "껍질", "Puzzle.animal4Trait2": "점액", "Puzzle.animal4HelpUrl": "https://ko.wikipedia.org/wiki/%EB%8B%AC%ED%8C%BD%EC%9D%B4", "Puzzle.picture": "사진:", "Puzzle.legs": "다리:", "Puzzle.legsChoose": "선택...", "Puzzle.traits": "성질:", "Puzzle.error0": "완벽합니다!\n모든 블록 %1개가 정확합니다.", "Puzzle.error1": "거의 됐습니다! 블록 한 개가 잘못되었습니다.", "Puzzle.error2": "블록 %1개가 잘못되었습니다.", "Puzzle.tryAgain": "강조한 블록은 올바르지 않습니다.\n계속 해보세요.", "Puzzle.checkAnswers": "정답 확인", "Puzzle.helpText": "각 동물(초록)에 그림을 연결하고 다리의 개수를 선택하여 성질의 스택을 만드세요.", "Maze.moveForward": "앞으로 가기", "Maze.turnLeft": "왼쪽으로 돌기", "Maze.turnRight": "오른쪽으로 돌기", "Maze.doCode": "하기", "Maze.helpIfElse": "만약-그렇지 않으면 블록은 한 가지 또는 다른 행동을 합니다.", "Maze.pathAhead": "만약 길이 앞으로 갈 수 있다면", "Maze.pathLeft": "만약 길이 왼쪽으로 갈 수 있다면", "Maze.pathRight": "만약 길이 오른쪽으로 갈 수 있다면", "Maze.repeatUntil": "다음까지 반복", "Maze.moveForwardTooltip": "플레이어가 한 칸 앞으로 이동합니다.", "Maze.turnTooltip": "플레이어가 90도로 왼쪽이나 오른쪽으로 돕니다.", "Maze.ifTooltip": "만약 지정된 방향으로 하고 있으면, 몇 가지 행동을 합니다.", "Maze.ifelseTooltip": "만약 지정된 방향으로 하고 있으면, 첫 번째 블록을 행동을 합니다. 그렇지 않으면, 두 번째 블록을 행동을 합니다.", "Maze.whileTooltip": "도착점에 도달할 때까지 내부 행동을 반복합니다.", "Maze.capacity0": "나머지 블록 %0개가 있습니다.", "Maze.capacity1": "나머지 블록 %1개가 있습니다.", "Maze.capacity2": "나머지 블록 %2개가 있습니다.", "Maze.runTooltip": "블록이 말하는 행동을 플레이어가 합니다.", "Maze.resetTooltip": "미로의 시작에서 플레이어를 다시 넣습니다.", "Maze.helpStack": "목표에 도달할 수 있도록 함께 '앞으로 가기' 블록을 세로로 쌓으세요.", "Maze.helpOneTopBlock": "이 단계에서, 하얀 작업 공간에서 모든 블록을 함께 쌓을 필요가 있습니다.", "Maze.helpRun": "어떻게 되는지 보려면 프로그램을 실행하세요.", "Maze.helpReset": "당신의 프로그램은 미로를 풀 수 없습니다. '재설정'을 누르고 다시 시도하세요.", "Maze.helpRepeat": "컴퓨터는 제한된 메모리가 있습니다. 블록 두 개만 사용해 이 길의 끝에 도달하세요. 한 번 이상 블록을 실행하려면 '반복'을 사용하세요.", "Maze.helpCapacity": "이 단계에 대한 모든 블록을 사용했습니다. 새 블록은 만드려면, 먼저 기존 블록을 삭제해야 합니다.", "Maze.helpRepeatMany": "'반복' 블록 안에 하나 이상의 블록을 넣을 수 있습니다.", "Maze.helpSkins": "메뉴에서 당신이 좋아하는 선수를 고르세요.", "Maze.helpIf": "'만약' 블록은 조건이 참이면 무언가를 합니다. 왼쪽에 길이 있으면 왼쪽으로 돌도록 하세요.", "Maze.helpMenu": "'만약' 블록의 조건을 바꾸려면 %1 부분을 클릭하세요.", "Maze.helpWallFollow": "복잡한 미로를 풀 수 있습니까? 다음 왼쪽 벽을 시도하세요. 고급 프로그래머만!", "Bird.noWorm": "지렁이가 없다", "Bird.heading": "방향", "Bird.noWormTooltip": "새가 지렁이를 잡지 않은 경우.", "Bird.headingTooltip": "각도에 따라 움직입니다: 0은 오른쪽으로, 90은 위로, 등등.", "Bird.positionTooltip": "x와 y가 새의 위치를 표시합니다. x = 0일때는 새가 왼쪽 끝에 있고, x = 100일때는 오른쪽 끝에 있습니다. y = 0일때는 새가 아랫쪽 끝에 있고, y = 100일때는 새가 윗쪽 끝에 있습니다.", "Bird.helpHeading": "방향 각도를 바꿔서 새가 지렁이를 잡고 둥지로 돌아가게 하세요.", "Bird.helpHasWorm": "이 블럭을 사용해서 지렁이가 있으면 한 방향으로, 없으면 다른 방향으로 움직이세요.", "Bird.helpX": "'x'가 당신이 현재 가로 위치입니다. 이 블럭을 사용해서 'x'가 숫자보다 작으면 한 방향으로, 아니면 다른 방향으로 움직이세요.", "Bird.helpElse": "아이콘을 클릭하여 '만약' 블럭을 수정하세요.", "Bird.helpElseIf": "이 레벨은 '다른 경우'와 '그렇지 않으면' 블럭이 필요합니다.", "Bird.helpAnd": "'그리고' 블럭은 입력된 조건이 둘 다 사실일때만 사실입니다.", "Bird.helpMutator": "'그렇지 않으면' 블럭을 '만약' 블럭 안으로 드래그하세요.", "Turtle.moveTooltip": "지정된 양으로 터틀을 앞이나 뒤로 이동합니다.", "Turtle.moveForward": "앞으로 가기", "Turtle.moveBackward": "뒤로 가기", "Turtle.turnTooltip": "지정된 각도로 터틀을 왼쪽이나 오른쪽으로 돕니다.", "Turtle.turnRight": "오른쪽으로 돌기", "Turtle.turnLeft": "왼쪽으로 돌기", "Turtle.widthTooltip": "펜의 너비를 바꿉니다.", "Turtle.setWidth": "너비 설정", "Turtle.colourTooltip": "펜의 색을 바꿉니다.", "Turtle.setColour": "색 설정", "Turtle.penTooltip": "그리기를 멈추거나 시작하기 위해 펜을 올리거나 내립니다.", "Turtle.penUp": "펜 올리기", "Turtle.penDown": "펜 내리기", "Turtle.turtleVisibilityTooltip": "터틀(동그라미와 화살표)를 보이거나 숨길 수 있습니다.", "Turtle.hideTurtle": "터틀 숨기기", "Turtle.showTurtle": "터틀 보이기", "Turtle.printHelpUrl": "https://ko.wikipedia.org/wiki/인쇄", "Turtle.printTooltip": "터틀의 방향과 위치를 텍스트로 그립니다.", "Turtle.print": "인쇄", "Turtle.fontHelpUrl": "https://ko.wikipedia.org/wiki/글꼴", "Turtle.fontTooltip": "인쇄 블록에서 사용되는 글꼴을 설정합니다.", "Turtle.font": "글꼴", "Turtle.fontSize": "글자 크기", "Turtle.fontNormal": "보통", "Turtle.fontBold": "굵게", "Turtle.fontItalic": "기울임꼴", "Turtle.submitDisabled": "프로그램이 멈출 때까지 실행시키세요. 그러면 당신의 그림을 갤러리에 제출할 수 있습니다.", "Turtle.galleryTooltip": "드로잉 갤러리를 엽니다.", "Turtle.galleryMsg": "갤러리 보기", "Turtle.submitTooltip": "갤러리에 그림을 제출합니다.", "Turtle.submitMsg": "갤러리에 내기", "Turtle.helpUseLoop": "당신의 솔루션도 맞지만 더 나은 방법이 있습니다.", "Turtle.helpUseLoop3": "3개의 블럭으로만 모양을 만들어 보세요.", "Turtle.helpUseLoop4": "4개의 블럭으로만 별을 만들어 보세요.", "Turtle.helpText1": "사각형을 그리는 프로그램을 만드세요.", "Turtle.helpText2": "사각형 대신에 오각형을 그리게 프로그램을 수정하세요.", "Turtle.helpText3a": "색깔을 수정할 수 있는 블럭이 새로 생겼습니다:", "Turtle.helpText3b": "노란 별을 그리세요.", "Turtle.helpText4a": "움직일때 펜을 종이에서 땔 수 있게 하는 블럭이 새로 생겼습니다:", "Turtle.helpText4b": "작은 노란 별을 그린 후에 그 윗쪽에 선을 그리세요.", "Turtle.helpText5": "별 하나뿐만이 아니라 사각형으로 배치된 4개의 별을 그릴 수 있으세요?", "Turtle.helpText6": "3개의 노란 별과 흰 선을 그리세요.", "Turtle.helpText7": "별들을 그린 후에 4개의 흰 선을 그리세요.", "Turtle.helpText8": "360개의 힌 선을 그리면 보름달 같이 보이겠죠.", "Turtle.helpText9": "검은색 원을 추가해서 보름달이 초승달이 되게 할 수 있나요?", "Turtle.helpText10": "이 블럭을 모두 사용해 보시면서 재밌게 아무거나 그리세요.", "Turtle.helpText10Reddit": "다른 사람의 그림을 보려면 '갤러리 보기' 버튼을 사용하십시오. 흥미로운 그림을 그리시면 \"갤러리에 제출하기\" 버튼을 사용하여 게시하십시오.", "Turtle.helpToolbox": "블럭의 종류를 골라서 보세요.", "Movie.x": "x좌표", "Movie.y": "y좌표", "Movie.x1": "x좌표 시작점", "Movie.y1": "y좌표 시작점", "Movie.x2": "끝 x", "Movie.y2": "끝 y", "Movie.radius": "범위", "Movie.width": "폭", "Movie.height": "높이", "Movie.circleTooltip": "입력받은 위치에 입력받은 반지름을 가진 원을 그립니다.", "Movie.circleDraw": "원", "Movie.rectTooltip": "입력받은 위치에 입력받은 가로와 세로길이를 가진 직사각형을 그립니다.", "Movie.rectDraw": "직사각형", "Movie.lineTooltip": "한 점에서 다른 점까지 선을 그립니다.", "Movie.lineDraw": "선", "Movie.timeTooltip": "애니메이션에서의 현재 시간을 출력합니다 (0-100).", "Movie.colourTooltip": "펜의 색을 바꿉니다.", "Movie.setColour": "색 설정", "Movie.submitDisabled": "애니메이션이 움직이지 않습니다. 블럭으로 재밌는 애니메이션을 만드시면 갤러리에 낼 수 있습니다.", "Movie.galleryTooltip": "동영상 갤러리를 엽니다.", "Movie.galleryMsg": "갤러리 보기", "Movie.submitTooltip": "갤러리에 동영상을 제출합니다.", "Movie.submitMsg": "갤러리에 내기", "Movie.helpLayer": "프로그램의 맨 위로 배경 원을 이동하세요. 그러면 사람 뒤에 나타납니다.", "Movie.helpText1": "기본 도형으로 이 사람을 그리세요.", "Movie.helpText2a": "이 레벨은 영화가 주제입니다. 당신은 사람의 팔을 화면에서 움직이고자 합니다. 재생 버튼을 눌러서 미리보기를 하세요.", "Movie.helpText2b": "애니메이션이 재생되는 동안에 'time' 블럭의 값이 0에서 100까지 갑니다. 팔의 'y'위치를 0에서 100까지 움직이고자 하니 쉽게 할 수 있습니다.", "Movie.helpText3": "`time` 블록은 0에서 100까지 셉니다. 하지만 이번엔 다른 쪽 팔의 `y` 위치를 100에서 시작하여 0으로 움직이려 합니다. 방향을 뒤집는 간단한 수학 공식을 알아낼 수 있나요?", "Movie.helpText4": "이전 레벨에서 배운 것을 이용하여 다리가 교차하게 만드세요.", "Movie.helpText5": "팔의 수학 공식은 복잡합니다. 답은 다음과 같습니다.", "Movie.helpText6": "이 사람에게 두 손을 주세요.", "Movie.helpText7": "애니메이션 전반부엔 `만약` 블록을 사용하여 작은 머리를 그리세요. 그다음 애니메이션의 후반부에서는 큰 머리를 그리세요.", "Movie.helpText8": "애니메이션 중간에 다리가 반대 방향으로 가게 만드세요.", "Movie.helpText9": "사람 뒤에서 원이 확장되게 만드세요.", "Movie.helpText10": "이 블럭을 모두 사용해 보시면서 재밌게 애니메이션을 만들어보세요.", "Movie.helpText10Reddit": "다른 사람이 만든 동영상을 보려면 '갤러리 보기' 버튼을 사용하십시오. 흥미로운 동영상을 만드시면 '갤러리에 제출하기' 버튼을 사용하여 게시하십시오.", "Music.playNoteTooltip": "특정한 음높이와 길이로 1개 음을 연주합니다.", "Music.playNote": "%2 음을 %1 동안 연주하기", "Music.restTooltip": "특정한 시간 동안 기다립니다.", "Music.restWholeTooltip": "온음표(4박자)만큼 쉽니다.", "Music.rest": "쉬기 %1", "Music.setInstrumentTooltip": "다음 음표를 연주할 때 지정된 악기로 전환합니다.", "Music.setInstrument": "%1로 악기 설정", "Music.startTooltip": "'프로그램 실행' 버튼을 클릭하면 안에 있는 블록을 실행합니다.", "Music.start": "%1 클릭 시", "Music.pitchTooltip": "한 음표 (C4는 7).", "Music.firstPart": "첫째 부분", "Music.piano": "피아노", "Music.trumpet": "트럼펫", "Music.banjo": "밴조", "Music.violin": "바이올린", "Music.guitar": "기타", "Music.flute": "플룻", "Music.drum": "드럼", "Music.choir": "합창단", "Music.submitDisabled": "프로그램이 멈출 때까지 실행시키세요. 그러면 당신의 음악을 갤러리에 제출할 수 있습니다.", "Music.galleryTooltip": "음악 갤러리를 엽니다.", "Music.galleryMsg": "갤러리 보기", "Music.submitTooltip": "갤러리에 음악을 제출합니다.", "Music.submitMsg": "갤러리에 제출하기", "Music.helpUseFunctions": "솔루션은 효과가 있지만 더 나은 방법이 있습니다. 함수를 사용하여 반복되는 코드의 양을 줄여보세요.", "Music.helpUseInstruments": "각 시작 블록에 다른 악기를 사용하면 음악이 더 좋게 들릴 것입니다.", "Music.helpText1": "'안녕'의 처음 네 음을 만들어보세요.", "Music.helpText2a": "'함수'를 사용하면 블록을 모아서 여러 번 실행할 수 있습니다.", "Music.helpText2b": "'안녕'의 처음 네 음을 연주하는 함수를 만드세요. 해당 함수를 두 번 실행하세요. 새로운 음 블록은 추가하지 마세요.", "Music.helpText3": "'안녕'의 다음 부분의 두 번째 함수를 만드세요. 마지막 음은 더 깁니다.", "Music.helpText4": "'안녕'의 다음 부분의 세 번째 함수를 만드세요. 처음 네 음은 더 짧습니다.", "Music.helpText5": "곡 '안녕'의 전체를 완성하세요.", "Music.helpText6a": "이 새로운 블록을 사용하면 다른 악기로 변경할 수 있습니다.", "Music.helpText6b": "바이올린으로 곡을 연주하세요.", "Music.helpText7a": "이 새로운 블록은 무음 구간을 추가합니다.", "Music.helpText7b": "두 개의 무음 구간 후에 '안녕'을 연주하는 또 다른 블록을 만드세요.", "Music.helpText8": "각 시작 블록은 '안녕'을 두 번씩 연주해야 합니다.", "Music.helpText9": "각각 '안녕'을 두 번씩 연주하는 시작 블록을 4개 만드세요. 정확한 수의 무음 구간 블록을 추가하세요.", "Music.helpText10": "원하는 대로 작곡해보세요. 탐구할 수 있는 수많은 새 블록이 있습니다. 즐겨 보세요!", "Music.helpText10Reddit": "다른 사람들이 작곡한 것을 보려면 '갤러리 보기' 버튼을 사용하세요. 흥미로운 곡을 작곡했다면 \"갤러리에 제출하기\" 버튼을 사용하여 게시해보세요.", "Pond.scanTooltip": "적을 찾습니다. 방향 (0-360)을 입력하세요. 그 방향에 있는 제일 가까운 적의 거리를 출력하고 적이 없으면 무한을 출력합니다.", "Pond.cannonTooltip": "대포를 쏩니다. 방향 (0-360)과 거리 (0-70)을 입력하세요.", "Pond.swimTooltip": "앞으로 수영합니다. 방향 (0-360)을 입력하세요.", "Pond.stopTooltip": "헤엄치는 것을 멈춥니다. 플레이어의 속도가 느려지며 멈춥니다.", "Pond.healthTooltip": "플레이어의 현재 체력을 출력합니다 (0은 죽음, 100은 건강).", "Pond.speedTooltip": "플레이어의 현재 속도를 출력합니다 (0은 멈춤, 100은 최대 속도).", "Pond.locXTooltip": "플레이어의 x 좌표를 출력합니다 (0은 왼쪽 끝, 100은 오른쪽 끝).", "Pond.locYTooltip": "플레이어의 y 좌표를 출력합니다 (0은 아랫쪽 끝, 100은 윗쪽 끝).", "Pond.logTooltip": "브라우저의 콘솔에 숫자를 출력합니다.", "Pond.docsTooltip": "언어에 대한 설명서를 표시합니다.", "Pond.documentation": "증거 서류", "Pond.playerName": "플레이어", "Pond.targetName": "목표물", "Pond.pendulumName": "추", "Pond.scaredName": "두려움", "Pond.helpUseScan": "당신의 답은 맞지만, 더 나은 답도 있습니다. 'scan'을 사용해서 대포를 얼마나 멀리 쏘는지 조종하세요.", "Pond.helpText1": "'cannon'으로 목표를 맞추세요. 첫째 값은 각도, 두 번째 값은 거리입니다. 맞는 조합을 찾으세요.", "Pond.helpText2": "이 목표는 여러 번 맞춰야 됩니다. 'while (true)' 루프를 사용해서 동작을 무한대로 반복하세요.", "Pond.helpText3a": "이 상대는 움직여서 맞추기 힘듭니다. 'scan'은 입력된 방향에 대한 상대의 정확한 거리를 출력합니다.", "Pond.helpText3b": "이 거리가 'cannon'이 정확히 발사하기 위해서 딱 알맞습니다.", "Pond.helpText4": "이 상대는 대포를 쓰기에는 너무 멉니다 (대포의 거리 제한은 70 미터입니다). 그러니, 이번에는 'swim' 명령을 사용하여 상대에게 돌진한 다음 부딪치세요.", "Pond.helpText5": "이 적도 대포로 쏘기엔 너무 멉니다. 하지만 부딪치기에는 너무 체력이 낮습니다. 가로 좌표가 50보다 적을 때 헤엄친 후 'stop'을 사용한 뒤에 대포를 쏘세요.", "Pond.helpText6": "이 적은 맞으면 도망칩니다. 사정거리 (70 미터) 밖에 있으면 그 쪽으로 헤엄치세요.", "Gallery": "갤러리"}