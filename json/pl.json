{"@metadata": {"authors": ["Anwar2", "Chrumps", "CiaPan", "DeRudySoulStorm", "<PERSON><PERSON>", "Fringoo", "Jchwastowska", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Krzyz23", "Mateon1", "Pan Cube", "Pbz", "Pio387", "Rezonansowy", "<PERSON><PERSON>", "<PERSON><PERSON>je<PERSON>", "Ty221", "WaldiSt", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Łukasz3212"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "<PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Ptak", "Games.turtle": "Żółw", "Games.movie": "Animacja", "Games.music": "Muzyka", "Games.pondTutor": "Staw: <PERSON><PERSON>", "Games.pond": "Staw", "Games.linesOfCode1": "Rozwiązałeś ten poziom za pomocą 1 linii JavaSkryptu:", "Games.linesOfCode2": "Rozwiązałeś ten poziom za pomocą %1 linii JavaSkryptu:", "Games.nextLevel": "<PERSON><PERSON><PERSON> gotów na %1 poziom?", "Games.finalLevel": "<PERSON><PERSON><PERSON> gotów na następne wyzwanie?", "Games.submitTitle": "Tytuł:", "Games.linkTooltip": "Zapisz i podlinkuj do klocków", "Games.runTooltip": "Uruchom napisany przez Ciebie program.", "Games.runProgram": "Uruchom Program", "Games.resetTooltip": "Zatrzymaj program i zresetuj poziom.", "Games.resetProgram": "Zresetuj", "Games.help": "Pomoc", "Games.catLogic": "Logika", "Games.catLoops": "Pętle", "Games.catMath": "Matematyka", "Games.catText": "Tekst", "Games.catLists": "Listy", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Zmienne", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "Wystąpił problem z żądaniem.", "Games.linkAlert": "Udostępnij swoje klocki korzystając z poniższego linku: \n\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, \"%1\" nie odpowiada żadnemu zapisanemu programowi.", "Games.xmlError": "Nie można zała<PERSON>wać zapisanego pliku. <PERSON><PERSON> może został utworzony za pomocą innej wersji <PERSON>ly?", "Games.submitted": "Dziękujemy za ten program! Jeśli nasi pracownicy wyszkolonych małp to lubią, opublikują go w galerii w ciągu kilku dni.", "Games.listVariable": "lista", "Games.textVariable": "tekst", "Games.breakLink": "Jak zaczniesz edytować JavaScript, nie możesz powrócić do edycji klocków. Kontynuować?", "Games.blocks": "<PERSON><PERSON><PERSON>", "Games.congratulations": "G<PERSON>ulacje!", "Games.helpAbort": "Ten poziom jest bardzo trudny. <PERSON><PERSON>/ch<PERSON><PERSON><PERSON><PERSON> pominąć go i iść do następnej gry? Możesz zawsze powrócić później.", "Index.clear": "Usunąć wszystkie rozwiązania?", "Index.subTitle": "Gry dla przyszłych programistów.", "Index.moreInfo": "Informację dla nauczycieli", "Index.startOver": "<PERSON><PERSON><PERSON> zacząć od nowa?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "Pióra", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://pl.wikipedia.org/wiki/<PERSON><PERSON>ka", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://pl.wikipedia.org/wiki/Ko<PERSON>_domowy", "Puzzle.animal3": "Pszczoła", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "Żądł<PERSON>", "Puzzle.animal3HelpUrl": "https://pl.wikipedia.org/wiki/Pszczoła", "Puzzle.animal4": "Ślimak", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Śluz", "Puzzle.animal4HelpUrl": "https://pl.wikipedia.org/wiki/%C5%9Alimaki", "Puzzle.picture": "zdjęcie:", "Puzzle.legs": "nogi:", "Puzzle.legsChoose": "wybierz...", "Puzzle.traits": "cechy:", "Puzzle.error0": "Doskonale!\nWszystkie z %1 kloclów są poprawne.", "Puzzle.error1": "<PERSON><PERSON>ie! <PERSON><PERSON> klocek jest niepoprawny.", "Puzzle.error2": "%1 klocków jest nieprawidłowych.", "Puzzle.tryAgain": "Podświetlony klocek nie jest poprawny.\nSpróbuj jeszcze raz.", "Puzzle.checkAnswers": "Sprawdź Odpowiedzi", "Puzzle.helpText": "<PERSON>la każdego zwierzęcia (zielonego), do<PERSON><PERSON><PERSON> jego zdj<PERSON>, wy<PERSON><PERSON> liczbę nóg, i określ jego cechy.", "Maze.moveForward": "<PERSON><PERSON>", "Maze.turnLeft": "s<PERSON><PERSON><PERSON><PERSON> w lewo", "Maze.turnRight": "s<PERSON><PERSON><PERSON>ć w prawo", "Maze.doCode": "wykonaj", "Maze.helpIfElse": "<PERSON><PERSON><PERSON> \"if-else (jeśli-w przeciwnym razie)\" wykonują jedn<PERSON> c<PERSON>, albo drugą.", "Maze.pathAhead": "je<PERSON><PERSON> ścieżka prowadzi prosto", "Maze.pathLeft": "jeśli ścieżka prowadzi w lewo", "Maze.pathRight": "je<PERSON>li ścieżka prowadzi w prawo", "Maze.repeatUntil": "powtarzaj aż", "Maze.moveForwardTooltip": "Porusza gracza do przodu o jedno pole.", "Maze.turnTooltip": "Obraca gracza w lewo lub prawo o 90 stopni.", "Maze.ifTooltip": "<PERSON><PERSON><PERSON> istnieje ścieżka w określonym kierunku, wykonaj jakieś akcje.", "Maze.ifelseTooltip": "<PERSON><PERSON><PERSON> istnieje ścieżka w określonym kierunku, wykonaj pierwszy klocek akcji. W innym przypadku wykonaj drugi klocek akcji", "Maze.whileTooltip": "Powtarzaj załączone kroki, dopóki nie osiągniesz punktu końcowego.", "Maze.capacity0": "Zostało Ci %0 klocków.", "Maze.capacity1": "Został Ci %1 klocek.", "Maze.capacity2": "Zostały(o) Ci %2 klocki(ów).", "Maze.runTooltip": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>e gracz robi to, co mówią klocki.", "Maze.resetTooltip": "Umieszcza gracza na początku labiryntu.", "Maze.helpStack": "Ustaw kilka poleceń \"id<PERSON> naprzód\" jedno za drugim, abym doszedł do celu.", "Maze.helpOneTopBlock": "Na tym poziomie, musisz ułożyć razem wszystkie klocki w białym obszarze roboczym.", "Maze.helpRun": "Uruchom program, a<PERSON> <PERSON><PERSON> co się stanie.", "Maze.helpReset": "Program nie rozwiązał labiryntu. Naciśnij przycisk \"Reset\" i spróbuj ponownie.", "Maze.helpRepeat": "Dotrzyj do końca ścieżki za pomocą tylko dwóch klocków. Użyj \"powtórz\" aby uruchomić klocek więcej niż raz.", "Maze.helpCapacity": "Wykorzystałeś już wszystkie klocki dostępne na tym poziomie. Aby utowrzyć nowy klocek musisz najpierw skasować jeden z już istniejących klocków.", "Maze.helpRepeatMany": "<PERSON><PERSON><PERSON><PERSON> więcej niż jeden klocek wewnątrz klocka \"Powtórz\".", "Maze.helpSkins": "Wybierz swojego ulubionego gracza z tego menu.", "Maze.helpIf": "Klocek \"if (jeśli)\" wykona określoną c<PERSON>, tylko kiedy warunek jest spełniony. Spróbuj pójść w lewo, jeśli ścieżka prowadzi w lewo.", "Maze.helpMenu": "Kliknij %1 w klocku \"if (jeżeli)\", <PERSON><PERSON><PERSON> zmienić warunek.", "Maze.helpWallFollow": "<PERSON><PERSON> dasz radę przejść ten trudny labirynt? Wypróbnuj metodę trzymania się lewą ręką ściany. Tylko dla zaawansowanych programistów!", "Bird.noWorm": "nie ma robaka", "Bird.heading": "kierunek", "Bird.noWormTooltip": "<PERSON>, w którym ptak nie złapał robaka.", "Bird.headingTooltip": "Poruszaj się w kierunku danego kąta: 0 - w prawo, 90 - do g<PERSON><PERSON>, itp.", "Bird.positionTooltip": "x i y oznaczają pozycję ptaka. Gdy x = 0, ptak jest przy lewej krawędzi, gdy x = 100, jest przy prawej krawędzi. Gdy y = 0, ptak jest na dole, a gdy y = 100, jest na górze.", "Bird.helpHeading": "Zmień kąt kierunku, aby ptak złapał robaka i wylądował w swoim gnieździe.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, a<PERSON> w jednym kierunku, je<PERSON><PERSON> masz robaka, albo w innym kierunku jeśli go nie masz.", "Bird.helpX": "'x' jest twoją pozycją w poziomie. Uż<PERSON>j tego k<PERSON>a, aby i<PERSON> w jednym kierunku, je<PERSON><PERSON> 'x' jest mniej<PERSON>e od <PERSON>, b<PERSON><PERSON><PERSON> w innym kierunku, w przeciwnym przypadku.", "Bird.helpElse": "<PERSON><PERSON><PERSON><PERSON>, aby zmodyfikować klocek 'if'.", "Bird.helpElseIf": "Na tym poziomie potrzebne są klocki \"else if\" oraz \"else\".", "Bird.helpAnd": "<PERSON><PERSON><PERSON> 'and' ma war<PERSON> prawdy tylko jeśli oba jego parametry są prawdziwe.", "Bird.helpMutator": "Prz<PERSON><PERSON>ś klocek 'else' w klocek 'if'.", "Turtle.moveTooltip": "Przesuwa żółwia na określoną odległość do przodu lub do tyłu.", "Turtle.moveForward": "idź do przodu o", "Turtle.moveBackward": "idź do tyłu o", "Turtle.turnTooltip": "Obraca żółwia w lewo lub w prawo o określoną liczbę stopni.", "Turtle.turnRight": "s<PERSON><PERSON><PERSON><PERSON>ć w prawo o", "Turtle.turnLeft": "s<PERSON><PERSON><PERSON><PERSON>ć w lewo o", "Turtle.widthTooltip": "Zmienia szerokość pióra.", "Turtle.setWidth": "ustaw szerokość na", "Turtle.colourTooltip": "Zmienia kolor pióra.", "Turtle.setColour": "ustaw kolor na", "Turtle.penTooltip": "Opuszcza lub podnosi pióro, a<PERSON> z<PERSON><PERSON><PERSON> albo skoń<PERSON>ć rys<PERSON>.", "Turtle.penUp": "podnieś pióro", "Turtle.penDown": "opuść pióro", "Turtle.turtleVisibilityTooltip": "Sprawia że żółw (kółko i strzałka) staje się widoczny lub niewidoczny.", "Turtle.hideTurtle": "ukryj <PERSON>", "Turtle.showTurtle": "pokaż żółwia", "Turtle.printHelpUrl": "https://pl.wikipedia.org/wiki/Druk", "Turtle.printTooltip": "Rysuje tekst w miejscu położenia żółwia we wskazanym przez niego kierunku.", "Turtle.print": "d<PERSON><PERSON><PERSON>", "Turtle.fontHelpUrl": "https://pl.wikipedia.org/wiki/Font", "Turtle.fontTooltip": "Określa czcionkę używaną przez blok drukuj.", "Turtle.font": "czcionka", "Turtle.fontSize": "roz<PERSON><PERSON>", "Turtle.fontNormal": "normalny", "Turtle.fontBold": "pog<PERSON><PERSON><PERSON>", "Turtle.fontItalic": "k<PERSON>ywa", "Turtle.submitDisabled": "Uruchom program. Po jego zakończeniu będzie można umieścić rysunek w galerii.", "Turtle.galleryTooltip": "Otwórz galerię rysunków.", "Turtle.galleryMsg": "Wyświ<PERSON><PERSON> galerię", "Turtle.submitTooltip": "Udostępnij swój rysunek w galeri.", "Turtle.submitMsg": "Udostępnij do galerii", "Turtle.helpUseLoop": "<PERSON><PERSON> rozwiązanie d<PERSON>ła, ale moż<PERSON>z zrobić to lepiej.", "Turtle.helpUseLoop3": "Narysuj kształt tylko trzema klockami.", "Turtle.helpUseLoop4": "Narysuj gwiazdę tylko czterema klockami.", "Turtle.helpText1": "Utwórz program, który rysuje kwadrat.", "Turtle.helpText2": "Zmień swój program, aby rysował pięciokąt zamiast kwadratu.", "Turtle.helpText3a": "Pojawił się nowy klocek, który pozwala zmienić kolor:", "Turtle.helpText3b": "Nary<PERSON>j <PERSON>ą gwiazdę.", "Turtle.helpText4a": "Jest nowy blok, k<PERSON><PERSON><PERSON> pozwala podnieść pisak z papieru, kiedy się ruszasz:", "Turtle.helpText4b": "Narysuj małą żółtą gwiazdę, a potem narysuj linię nad nią.", "Turtle.helpText5": "<PERSON><PERSON> moż<PERSON>z narysować cztery gwiazdy w rogach kwadratu zamiast jednej?", "Turtle.helpText6": "Narysuj trzy żółte gwiazdy i jedną białą linię.", "Turtle.helpText7": "<PERSON><PERSON><PERSON><PERSON>, a potem cztery białe linie.", "Turtle.helpText8": "Narysowanie 360-ciu białych linii będzie wyglądać jak księżyc w pełni.", "Turtle.helpText9": "<PERSON><PERSON> moż<PERSON> dodać czarny krąg, aby księżyc zmienił się w półksiężyc?", "Turtle.helpText10": "Narysuj cokolwiek. Masz sporo nowych klocków, które możesz poznać. Baw się dobrze!", "Turtle.helpText10Reddit": "Użyj przycisku \"Wyświetl galerię\", aby <PERSON><PERSON><PERSON><PERSON> co inne osoby narysowały. <PERSON><PERSON><PERSON> narysu<PERSON> coś ciekawego, użyj przycisku \"Udostępnij do galerii\", aby to opublikować.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON> z<PERSON><PERSON><PERSON> klocki.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "początkowe x", "Movie.y1": "początkowe y", "Movie.x2": "końcowe x", "Movie.y2": "końcowe y", "Movie.radius": "promień", "Movie.width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.circleTooltip": "Rysuje okrąg w określonym miejscu o określonym promieniu.", "Movie.circleDraw": "okrąg", "Movie.rectTooltip": "Rysuje prostokąt w danym miejscu o danej długości i szerokości.", "Movie.rectDraw": "prostokąt", "Movie.lineTooltip": "Rysuje odcinek pomiędzy dwoma punktami, z określoną szerokością.", "Movie.lineDraw": "linia", "Movie.timeTooltip": "Zwraca aktualny czas w filmie (0-100).", "Movie.colourTooltip": "Zmienia kolor pióra.", "Movie.setColour": "ustaw kolor na", "Movie.submitDisabled": "Twoja animacja się nie porusza. Użyj bloków aby zrobić coś interesującego. Potem możesz umieścić swoją animację w galerii.", "Movie.galleryTooltip": "Otwórz galerię filmów.", "Movie.galleryMsg": "Wyświ<PERSON><PERSON> galerię", "Movie.submitTooltip": "Udostępnij swój film w galeri.", "Movie.submitMsg": "Udostępnij do galerii", "Movie.helpLayer": "Przenieś kółko tła na górę programu. Wtedy pojawi się za osobą.", "Movie.helpText1": "Użyj prostych kształtów, aby nary<PERSON><PERSON>ć tę postać.", "Movie.helpText2a": "Ten poziom jest filmem. <PERSON><PERSON>z, aby ramię postaci wykonało ruch po ekranie. Klik<PERSON>j przycisk play, aby z<PERSON><PERSON><PERSON><PERSON> zapowiedź.", "Movie.helpText2b": "Podczas pokazu filmu wartość klocka 'c<PERSON>' zwiększa się od 0 do 100. <PERSON><PERSON><PERSON><PERSON>z, aby współrzędna 'y' ramienia zmieniała się od 0 do 100, to pow<PERSON><PERSON> być łatwe.", "Movie.helpText3": "Klocek 'c<PERSON>' liczy od 0 do 100. Ale tym razem chcesz, aby pozycja pozioma zaczynała się od 100 i kończyła na 0. Dasz radę wymyślić prostą formułę matematyczną, która odwraca kierunek ruchu?", "Movie.helpText4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, czego się nauczyłeś w poprzednim poziomie, aby uzyskać krzyżujące się nogi.", "Movie.helpText5": "Matematyczne równanie dla ramienia jest skomplikowane. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>:", "Movie.helpText6": "Dodaj postaci par<PERSON> rąk.", "Movie.helpText7": "Użyj blo<PERSON> \"jeś<PERSON>\" aby narysować małą głowę w pierwszej połowie animacji. Następnie narysuj dużą głowę w drugiej połowie.", "Movie.helpText8": "Zmień kierunek nóg w drugiej połowie filmu.", "Movie.helpText9": "Narysuj rosnące koło w tle, za postacią.", "Movie.helpText10": "Utwórz dowolną anikację. Masz dostęp do wielu nowych bloków, którw możesz poznawać. <PERSON><PERSON><PERSON>!", "Movie.helpText10Reddit": "Użyj przycisku \"Wyświetl galerię\"aby ogl<PERSON>dnąć animacje utworzone przez innych. Jeśli udało Ci się utworzyć intetesującą animację, użyj przycisku \"Udostępnij do galerii\" aby ją opublikować.", "Music.playNoteTooltip": "Odtwórz jeden ton o zadanym czasie trwania i wysokości.", "Music.playNote": "graj %1 nota %2", "Music.restTooltip": "Czeka przez określony czas.", "Music.restWholeTooltip": "Czeka na jedną całą nutę.", "Music.rest": "reszta %1", "Music.setInstrumentTooltip": "Przełącza się na określony instrument podczas odtwarzania kolejnych nut.", "Music.setInstrument": "ustaw instrument na %1", "Music.startTooltip": "Wykonuje bloki wewnątrz po kliknięciu przycisku „Uruchom program”.", "Music.start": "po kliknięciu %1", "Music.pitchTooltip": "<PERSON><PERSON> (C4 to 7).", "Music.firstPart": "<PERSON><PERSON><PERSON>", "Music.piano": "Piano", "Music.trumpet": "trąbka", "Music.banjo": "banjo", "Music.violin": "skrzypce", "Music.guitar": "Gitara", "Music.flute": "flet", "Music.drum": "b<PERSON>ben", "Music.choir": "chór", "Music.submitDisabled": "Uruchom program. Po jego zakończeniu będzie można umieścić rysunek w galerii.", "Music.galleryTooltip": "Otwórz <PERSON>ę muzyki.", "Music.galleryMsg": "<PERSON><PERSON><PERSON><PERSON>", "Music.submitTooltip": "Wyślij swoją muzykę do galerii", "Music.submitMsg": "Wyślij do Galerii", "Music.helpUseFunctions": "<PERSON><PERSON> roz<PERSON> d<PERSON>, ale mo<PERSON><PERSON><PERSON> to lepiej. Użyj odpowied<PERSON>j <PERSON>, aby z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ilość powtarzanego kodu.", "Music.helpUseInstruments": "Muzyka będzie brz<PERSON> le<PERSON>j, jeś<PERSON> użyjesz innego instrumentu w każdym bloku początkowym.", "Music.helpText1": "Skomponuj pierwsze cztery nuty „Frère Jacques”.", "Music.helpText2a": "„Funkcja” umożliwia grupowanie bloków, a następnie uruchamianie ich więcej niż jeden raz.", "Music.helpText2b": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby z<PERSON><PERSON><PERSON> pierwsze cztery nuty „<PERSON><PERSON>. Uruchom tę funkcję dwukrotnie. Nie dodawaj żadnych nowych bloków notatek.", "Music.helpText3": "Utwórz <PERSON>ą funkcję dla następnej czę<PERSON> „Fr<PERSON>”. Ostatnia nuta jest dłuższa.", "Music.helpText4": "Utwórz trzecią funkcję dla następnej części „Frère Jacques”. Pierwsze cztery nuty są krótsze.", "Music.helpText5": "Ukończ całą melodię „Fr<PERSON>.", "Music.helpText6a": "Ten nowy blok umożliwia przejście na inny instrument.", "Music.helpText6b": "Zagraj swoją melodię na skrzypcach.", "Music.helpText7a": "Ten nowy blok dodaje ciche opóźnienie.", "Music.helpText7b": "Utwórz drugi blok początkowy, który ma dwa bloki opóźniające, a następnie zagraj także „Frère Jacques”.", "Music.helpText8": "<PERSON><PERSON><PERSON> blok startowy powinien zagrać „Frère Jacques” dwa razy.", "Music.helpText9": "Stwórz cztery bloczki startowe, z których każdy zagra dwa razy „Frère Jacques”. Dodaj odpowiednią liczbę bloków opóźniających.", "Music.helpText10": "<PERSON><PERSON><PERSON><PERSON><PERSON>, co chcesz. Ma<PERSON> ogromną liczbę nowych bloków, które możesz eksplorować. Baw się dobrze!", "Music.helpText10Reddit": "Użyj przycisku '<PERSON><PERSON><PERSON><PERSON> galeri<PERSON>', aby <PERSON><PERSON><PERSON>, co skomponowali inni użytkownicy. Je<PERSON>li skomponujesz coś interesującego, użyj przycisku 'Prześlij do galerii', aby go opublik<PERSON>.", "Pond.scanTooltip": "Szuka wrogów. <PERSON><PERSON><PERSON><PERSON> (0-360). Zwraca odległość do najbliższego wroga w tym kierunku. Zwraca nieskończoność jeśli nie znajdzie wroga.", "Pond.cannonTooltip": "Wystrzel z działa. <PERSON><PERSON><PERSON><PERSON> (0-360) i odległ<PERSON>ść (0-70).", "Pond.swimTooltip": "Płyń do przodu. Wybierz kierunek (0-360).", "Pond.stopTooltip": "Przestań płynąć. Gracz zwolni i zatrzyma się.", "Pond.healthTooltip": "Zwraca bieżące zdrowie gracza (0 - nie <PERSON><PERSON>je, 100 - zdrowy)", "Pond.speedTooltip": "Zwraca bieżącą pręd<PERSON><PERSON><PERSON> gracza (0 - stoi, 100 - pełna prędkość).", "Pond.locXTooltip": "Zwraca współrzędną X gracza (0 - lewa krawędź, 100 - prawa krawędź).", "Pond.locYTooltip": "Zwraca współrzędną Y gracza (0 - dolna krawędź, 100 - g<PERSON><PERSON> krawędź).", "Pond.logTooltip": "Drukuje numer na konsoli przeglądarki.", "Pond.docsTooltip": "Pokaż dokumentację języka.", "Pond.documentation": "Dokumentacja", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON>l", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Przestraszony", "Pond.helpUseScan": "<PERSON><PERSON> rozwiązanie działa, ale można to z<PERSON>bi<PERSON> lepiej. Użyj 'scan', aby <PERSON><PERSON><PERSON><PERSON><PERSON> odle<PERSON><PERSON><PERSON><PERSON><PERSON> strzału z działa.", "Pond.helpText1": "Użyj polecenia 'cannon', aby trafić w cel. <PERSON><PERSON><PERSON> parametr to k<PERSON>t, drugi to odległość. Znajdź odpowiednią kombinację.", "Pond.helpText2": "Ten cel musi być trafiony wiele razy. Uż<PERSON>j pętli 'while (true)' aby rob<PERSON> coś w nies<PERSON>.", "Pond.helpText3a": "Ten przeciwnik łazi tam i z powrotem, co czyni go trudnym celem. Parametr 'skanuj' dokładnie określa odległość od przeciwnika w określonym kierunku.", "Pond.helpText3b": "<PERSON> odle<PERSON><PERSON><PERSON><PERSON><PERSON> to dokładnie to co jest potrzebne w poleceniu 'dział<PERSON>' aby oddać trafny strzał.", "Pond.helpText4": "Ten przeciwnik jest za daleko, aby u<PERSON><PERSON><PERSON> d<PERSON>ła (które ma limit 70 metrów). <PERSON><PERSON><PERSON> tego, u<PERSON><PERSON><PERSON> komendy 'swim', aby podp<PERSON><PERSON>ąć do przeciwnika i zderzyć się z nim.", "Pond.helpText5": "Ten przeciwnii jest również za daleko by <PERSON><PERSON><PERSON><PERSON> d<PERSON>. Jesteś jednak za słaby aby przeżyć zderzenie. Płyń w jego stronę dopóki pozycja w poziomie nie osiągnie 50. Nast<PERSON><PERSON><PERSON> 'stop' i użyj działa.", "Pond.helpText6": "Ten przeciwnik odpłynie po trafieniu. Płyń za nim jeśli jest poza zasięgiem (70 metrów).", "Gallery": "Galeria"}