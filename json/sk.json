{"@metadata": {"authors": ["HuntaH", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>s<PERSON>", "Nykta 1917", "<PERSON><PERSON><PERSON>", "Yardom78"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Skladačka", "Games.maze": "Bludis<PERSON>", "Games.bird": "Vták", "Games.turtle": "Korytnačka", "Games.movie": "Film", "Games.music": "Hudba", "Games.pondTutor": "Nacvičiť si Rybník", "Games.pond": "Rybník", "Games.linesOfCode1": "Vyriešil si úroveň jedným riadkom JavaScriptu:", "Games.linesOfCode2": "Vyriešil si túto úroveň s %1 riadkami JavaScriptu:", "Games.nextLevel": "Pripravený na úroveň %1?", "Games.finalLevel": "Pripravený na ďalšiu výzvu?", "Games.submitTitle": "Názov:", "Games.linkTooltip": "Uložiť a zdieľať odkaz na tento program.", "Games.runTooltip": "Spustiť napísaný program.", "Games.runProgram": "Spustiť", "Games.resetTooltip": "Zastaviť program a vrátiť úroveň do pôvodného stavu.", "Games.resetProgram": "<PERSON><PERSON><PERSON><PERSON>", "Games.help": "Pomoc", "Games.catLogic": "Logické", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Matematika", "Games.catText": "Textové", "Games.catLists": "Zoznamy", "Games.catColour": "Farba", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "Funkcie", "Games.httpRequestError": "Nastal problém pri spracovaní požiadavky.", "Games.linkAlert": "Zdieľať odkaz na tento program: %1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON>, '%1' nie je meno žiadneho uloženého programu.", "Games.xmlError": "Nebolo možné načítať uložený súbor. Možno bol vytvorený v inej verzii Blockly.", "Games.submitted": "Ďakujeme Vám za tento program! Ak sa našim cvičeným opičkám (zamestnancom) bude páčiť, uverejnia ho v galérii v priebehu niekoľkých dní.", "Games.listVariable": "zoznam", "Games.textVariable": "text", "Games.breakLink": "Ak už raz začnete písať JavaScript, nemôžete sa vrátiť k úprave blokov. Je to v poriadku?", "Games.blocks": "Bloky", "Games.congratulations": "Blahoželáme!", "Games.helpAbort": "<PERSON><PERSON><PERSON> je veľmi ťažká. <PERSON><PERSON><PERSON> ju preskočiť a pozrieť sa na ďalš<PERSON> hru? Neskôr sa sem môžeš vrátiť.", "Index.clear": "Zmazať všetky tvoje riešienia?", "Index.subTitle": "<PERSON><PERSON> <PERSON> bud<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "Index.moreInfo": "Informácie pre vzdeľávateľov...", "Index.startOver": "<PERSON><PERSON>š začať odznova?", "Index.clearData": "Zmazať údaje", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Mač<PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Ko<PERSON><PERSON>š<PERSON>", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "Včela", "Puzzle.animal3Trait1": "Med", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "<PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Sliz", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "obrázok:", "Puzzle.legs": "nohy:", "Puzzle.legsChoose": "zvoliť...", "Puzzle.traits": "vlastnosti:", "Puzzle.error0": "Výborne!\nVšetkých %1 blokov je na správnom mieste.", "Puzzle.error1": "Takmer! Jeden blok je nesprávne.", "Puzzle.error2": "%1 blokov je nesprávne.", "Puzzle.tryAgain": "Zvýraznený blok nie je správne.\nSkúšaj ďalej.", "Puzzle.checkAnswers": "Skontrolovať odpovede", "Puzzle.helpText": "Ku každému <PERSON> (zelené) priraď počet nôh a usporiadaj vlastnosti.", "Maze.moveForward": "choď dopredu", "Maze.turnLeft": "otoč sa vľavo", "Maze.turnRight": "otoč sa vpravo", "Maze.doCode": "urob", "Maze.helpIfElse": "Príkaz ak-inak urobí buď jedno alebo druhé.", "Maze.pathAhead": "ak je cesta pred", "Maze.pathLeft": "ak je cesta vľavo", "Maze.pathRight": "ak je cesta vpravo", "Maze.repeatUntil": "op<PERSON><PERSON><PERSON> kým nebude", "Maze.moveForwardTooltip": "Posun hráča o jednu dĺžku dopredu.", "Maze.turnTooltip": "Otočenie hráča o 90° vľavo či vpravo.", "Maze.ifTooltip": "<PERSON>k je tým smerom cesta, vykonaj príkazy.", "Maze.ifelseTooltip": "<PERSON>k je tým smerom cesta, vykonaj prvý blok príkazov. Inak vykonaj druhý blok príkazov.", "Maze.whileTooltip": "Opakuj príkazy vo vnútri bloku, až kým neprídeš do cieľa.", "Maze.capacity0": "Zostalo ti %0 blokov.", "Maze.capacity1": "Máš už iba %1 blok.", "Maze.capacity2": "Zostalo ti ešte %2 blokov.", "Maze.runTooltip": "Postavička vykoná to, čo je napís<PERSON>é na bloku.", "Maze.resetTooltip": "Presunúť hráča späť na začiatok bludiska.", "Maze.helpStack": "Program je postupnosť blokov. Pospájaj niekoľko blokov 'vpred' a pomôž mi dôj<PERSON>ť do cieľa.", "Maze.helpOneTopBlock": "V tejto úrovni máš na bielej ploche poskladať všetky diely skladačky.", "Maze.helpRun": "Spusti svoj program a uvidíš, čo sa stane.", "Maze.helpReset": "Tvoj program neprešiel cez bludisko. <PERSON><PERSON><PERSON> \"Obnoviť\" a skús to znova.", "Maze.helpRepeat": "Dosiahni cieľ použitím len dvoch blokov. Na zopakovanie bloku použi blok 'opakuj'.", "Maze.helpCapacity": "Využil si všetky bloky dostupné v tejto úrovni. Ak chceš nový blok, odstráň najprv nejaký existujúci.", "Maze.helpRepeatMany": "Do opakovacieho bloku môžeš umiestniť aj viac ako jeden blok.", "Maze.helpSkins": "Zvoľ si svojho obľúbeného hráča z ponuky.", "Maze.helpIf": "Rozhodovací blok 'ak' urobí niečo len v prípade, že je splnená podmienka. Skús otočenie vľavo, ak je cesta naľavo.", "Maze.helpMenu": "Klikni na %1 v rozhodovacom bloku a nastav podmienku.", "Maze.helpWallFollow": "Zvládneš aj toto komplikované bludisko?\nSkús ísť popri ľavej stene. Len pre pokročilých programátorov!", "Bird.noWorm": "<PERSON><PERSON><PERSON>", "Bird.heading": "smer", "Bird.noWormTooltip": "Podmienka ak ešte vták nechytil červa.", "Bird.headingTooltip": "Pohybuj sa v smere uhla. 0 je vpravo, 90 je nahor atď.", "Bird.positionTooltip": "x a y označuj<PERSON> pozíciu vtáka. Keď x = 0, vták je blízko ľavého okraja, keď x = 100, je blízko pravého okraja. Keď y = 100, je navrchu.", "Bird.helpHeading": "Zmeň smer vtáka tak, aby chytil červa a pristál v hniezde.", "Bird.helpHasWorm": "Použi tento blok na pohyb jedným smerom, ak má<PERSON>, alebo iným smerom, ak ho nem<PERSON>š.", "Bird.helpX": "'x' je tvoja momentálna vodorovná pozícia. Použi blok na pohyb jedným smerom, ak 'x' je menej a<PERSON>, alebo iným smerom, ak je to inak.", "Bird.helpElse": "Klikni na inkonku a zmeň blok 'ak'.", "Bird.helpElseIf": "V tejto úrovni sú potrebné bloky 'inak ak' aj 'inak'.", "Bird.helpAnd": "Blok 'a' je pravda len ak oba jeho vstupy sú pravdivé.", "Bird.helpMutator": "Potiahni blok 'inak' dovn<PERSON><PERSON> bloku 'ak'.", "Turtle.moveTooltip": "Presunie korytnačku dopredu alebo dozadu o určitý počet krokov.", "Turtle.moveForward": "posunúť dopredu o", "Turtle.moveBackward": "posunúť vzad o", "Turtle.turnTooltip": "Korytnačka sa otočí vľavo alebo vpravo o zadaný počet stupňov.", "Turtle.turnRight": "otočiť vpravo o", "Turtle.turnLeft": "otočiť vľavo o", "Turtle.widthTooltip": "Zmeniť hrúbku pera.", "Turtle.setWidth": "nastaviť šírku", "Turtle.colourTooltip": "Zmeniť farbu pera.", "Turtle.setColour": "nastaviť farbu", "Turtle.penTooltip": "<PERSON>o hore alebo dole, skonč<PERSON> alebo začne kresliť.", "Turtle.penUp": "pero hore", "Turtle.penDown": "pero dole", "Turtle.turtleVisibilityTooltip": "Ukáže alebo skryje korytnačku (krúžok so šípkou).", "Turtle.hideTurtle": "skryť korytnačku", "Turtle.showTurtle": "ukázať korytnačku", "Turtle.printHelpUrl": "https://sk.wikipedia.org/wiki/Kn%C3%ADhtla%C4%8D", "Turtle.printTooltip": "Vypíše text na pozícii korytnačky v smere jej natočenia.", "Turtle.print": "tlačiť", "Turtle.fontHelpUrl": "https://sk.wikipedia.org/wiki/Font_%28po%C4%8D%C3%ADta%C4%8De%29", "Turtle.fontTooltip": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>žíva tlačový dielec.", "Turtle.font": "písmo", "Turtle.fontSize": "veľkosť písma", "Turtle.fontNormal": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontBold": "tučné", "Turtle.fontItalic": "kurzíva", "Turtle.submitDisabled": "Spustiť program, až kým nezastaví. <PERSON><PERSON> môž<PERSON>š svoj výtvor poslať do galérie.", "Turtle.galleryTooltip": "Otvoriť galériu o<PERSON>.", "Turtle.galleryMsg": "Prezrieť galériu", "Turtle.submitTooltip": "Pošlite Váš obrázok do galérie.", "Turtle.submitMsg": "Poslať do Galérie", "Turtle.helpUseLoop": "T<PERSON>je riešenie pracuje, ale môže by<PERSON> ešte le<PERSON>.", "Turtle.helpUseLoop3": "Nakresli tento útvar iba s tromi blokmi.", "Turtle.helpUseLoop4": "Nakresli hviezdičku iba so štyrmi blokmi.", "Turtle.helpText1": "Vytvor program, ktorý kreslí štvorec.", "Turtle.helpText2": "Zmeň svoj program tak, aby namiesto štvorca nakreslil päťuholník.", "Turtle.helpText3a": "Pribudol nový blok, ktorý mení farbu čiary:", "Turtle.helpText3b": "Nakresli žltú hviezdičku.", "Turtle.helpText4a": "Pribudol nový blok, ktorým sa zdvíha pero z papiera:", "Turtle.helpText4b": "Nakresli žltú hviezdičku a potom čiaru nad ňou.", "Turtle.helpText5": "Namiesto jednej hviezdičky nakresli štyri, usporiadné do štvorca.", "Turtle.helpText6": "Nakresli tri žlté hviezdy a jednu bielu čiaru.", "Turtle.helpText7": "Nakresli hviezdy, a potom štyri biele čiary.", "Turtle.helpText8": "360 bielych čiar bude pripomínať mesiac v splne.", "Turtle.helpText9": "Ak pridáš ešte čierny kruh, z mesiaca sa stane polmesiac.", "Turtle.helpText10": "Nakresli si čokoľvek. Pribudlo mnoho nových blokov, ktor<PERSON> mô<PERSON>skú<PERSON>. Bav sa!", "Turtle.helpText10Reddit": "Použi tlačidlo \"Prezrieť Galériu\" a prezri si výtvory iných ľudí. <PERSON>k nak<PERSON> niečo zaujímavé, s<PERSON><PERSON><PERSON> \"Poslať do Galérie\" a zverejni svoj výtvor.", "Turtle.helpToolbox": "Zvoľ si kategóriu a prezri si bloky v nej.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "štart x", "Movie.y1": "štart y", "Movie.x2": "cieľ x", "Movie.y2": "cieľ y", "Movie.radius": "polomer", "Movie.width": "hr<PERSON><PERSON><PERSON>", "Movie.height": "výška", "Movie.circleTooltip": "Nakreslí kružnicu na zadaných súradniciach s daným polomerom.", "Movie.circleDraw": "kružnica", "Movie.rectTooltip": "Nakreslí obdĺžnik na zadaných súradniciach s danou výškou a šírkou.", "Movie.rectDraw": "obdĺžnik", "Movie.lineTooltip": "Nakreslí čiaru danej hrúbky z jedného bodu do druhého.", "Movie.lineDraw": "čiara", "Movie.timeTooltip": "Vráti aktuálny čas v animácii (0-100)", "Movie.colourTooltip": "Zmení farbu pera.", "Movie.setColour": "nastav farbu na", "Movie.submitDisabled": "Tvoj film sa zatiaľ nehýbe. <PERSON><PERSON>b si z dielikov niečo zaujímavé. Potom nám svoj film môžeš vyzdieľať do galérie.", "Movie.galleryTooltip": "Otvoriť galériu filmov.", "Movie.galleryMsg": "Zobraziť galériu", "Movie.submitTooltip": "Vložte váš film do galérie.", "Movie.submitMsg": "Poslať do galérie", "Movie.helpLayer": "Premiestnite kruh z pozadia na vrch Vášho programu. Následne sa objaví za osobou.", "Movie.helpText1": "<PERSON><PERSON><PERSON><PERSON> postavu j<PERSON><PERSON> t<PERSON>mi", "Movie.helpText2a": "V tejto úrovni tvoríš film. Potrebuješ, aby sa ruka človeka pohybovala po obrazovke. Stlač tlačidlo na prehratie, aby si zhľiadol náhľad.", "Movie.helpText2b": "Počas prehrávania filmu sa hodnota bloku 'čas' mení od 0 do 100. <PERSON><PERSON><PERSON><PERSON> ch<PERSON>š meniť vodorovnú polohu od 0 do 100, malo to by by<PERSON> jed<PERSON><PERSON>.", "Movie.helpText3": "Blok 'čas' počíta od 0 do 100. <PERSON><PERSON><PERSON>, aby vodorovná poloha druhej ruky začala na 100 a znížila sa na 0. Do<PERSON><PERSON><PERSON><PERSON>š prísť na jednoduchý matematický vzorec, ktorý dokáže zmeniť smer?", "Movie.helpText4": "Použi vedomosti z predošlej úrovne a vytvor prekrížené nohy.", "Movie.helpText5": "Matematický vzorec pre ruku je zložitý. Tu je odpoveď:", "Movie.helpText6": "<PERSON><PERSON> o<PERSON> d<PERSON>.", "Movie.helpText7": "Použi blok Ak na nakreslenie malej hlavy pre prvú polovicu filmu. <PERSON><PERSON> nakresly veľkú hlavu pre druhú časť filmu.", "Movie.helpText8": "<PERSON><PERSON><PERSON>i toho, aby nohy zmenili svoj smer v polovici filmu.", "Movie.helpText9": "Nakresli zvečšujúci sa kruh za osobou.", "Movie.helpText10": "Urob si film podľa vlastnej fantázie. Na preskúmanie tu máš veľký počet nových die<PERSON>. Príjemnú zábavu!", "Movie.helpText10Reddit": "Tlač<PERSON>lom \"Galéria\" si prezri filmy iných ľudí. Ak sa ti podarí zaujímavý film, zverejníš ho tlačidlo \"Zdieľať do galérie\".", "Music.playNoteTooltip": "<PERSON><PERSON><PERSON><PERSON> jednu notu so špecifikovanou dĺžkou a výškou.", "Music.playNote": "zahraj %1 notu %2", "Music.restTooltip": "Čaká na špecifikované trvanie.", "Music.restWholeTooltip": "Čaká na jednu celú notu.", "Music.rest": "prestávka %1", "Music.setInstrumentTooltip": "Zmení na zadaný nástroj po prehraní nasledujúcich hudobných nôt.", "Music.setInstrument": "nastaviť nástroj na %1", "Music.startTooltip": "Po kliknutí na tlačítko \"Spustiť program\" vykoná akciu zadanú v blokoch vo vnútri.", "Music.start": "keď je stlačený %1", "Music.pitchTooltip": "<PERSON><PERSON> nota (C4 je 7).", "Music.firstPart": "prvá časť", "Music.piano": "piano", "Music.trumpet": "tr<PERSON><PERSON><PERSON>", "Music.banjo": "banjo", "Music.violin": "husle", "Music.guitar": "gitara", "Music.flute": "flauta", "Music.drum": "bubny", "Music.choir": "chór", "Music.submitDisabled": "Spustiť program do konca. Potom môžete odoslať svoju hudbu do galérie.", "Music.galleryTooltip": "Otvoriť galériu hudby.", "Music.galleryMsg": "Zobraziť galériu", "Music.submitTooltip": "Odoslať svoju hudbu do galérie.", "Music.submitMsg": "Odoslať do galérie", "Music.helpUseFunctions": "Vaše riešenie funguje, ale dá sa vylepšiť. Použite funkcie na zníženie množstva opakujúceho sa kódu.", "Music.helpUseInstruments": "Hudba bude znieť lepšie ak použijete rôzne nástroje na každý začiatočný blok.", "Music.helpText1": "Skomponujte prvé štyri noty piesne 'Frère Jacques'.", "Music.helpText2a": "'Funkcia' Vám dovolí usporiadať bloky spolu a potom ich spustiť viac než raz.", "Music.helpText2b": "Vytvorte funkciu prehrania prvých štyroch nôt piesne '<PERSON><PERSON> Jacques'. Spustite túto funkciu dvakrát. Nepridávajte žiadne nové bloky.", "Music.helpText3": "Vytvorte druhú funkciu pre ďalšiu časť piesne 'Frère Jacques'. Posledná nota je dlhšia.", "Music.helpText4": "Vytvorte tretiu funkciu pre ďalšiu časť piesne 'Frère Jacques'. Prvé štyri noty sú kratšie.", "Music.helpText5": "Dokončite plné znenie melódie 'Frère Jacques'.", "Music.helpText6a": "Tento nový blok Vám umožní vymeniť nástroj.", "Music.helpText6b": "Prehrajte Vašu melódiu husľami.", "Music.helpText7a": "Tento nový blok pridá tiché ones<PERSON>e.", "Music.helpText7b": "Vytvorte druhý š<PERSON>tovný blok, ktorý obsahuje dva bloky oneskorenia a potom prehrá 'Frère Jacques'.", "Music.helpText8": "<PERSON><PERSON><PERSON><PERSON> štartovný blok by mal hra<PERSON> '<PERSON><PERSON>' <PERSON>.", "Music.helpText9": "Vytvorte štyri <PERSON>, k<PERSON><PERSON> '<PERSON><PERSON>' dvakrát. Pridajte správny počet oneskorení.", "Music.helpText10": "Skomponujte čokoľvek chcete. Máte obrovské množstvo nových blokov na objavenie. Dobre sa zabavte!", "Music.helpText10Reddit": "Použite tlačítko \"Prezrieť Galériu\" a prezrite si čo iní ľudia skomponovali. Ak sa Vám niečo zapáči použite tlačítko \"Poslať do Galérie\" a zverejnite to.", "Pond.scanTooltip": "Hľadá nepriateľa. <PERSON><PERSON>j smer (0-360). Vráti vzdialenosť od najbližšieho nepriateľa v danom smere. Vráti Nekonečno ak v danom smere nie je žiadny nepriateľ.", "Pond.cannonTooltip": "Vypáli z dela. <PERSON><PERSON><PERSON> s<PERSON> (0-360) a silu (0-70).", "Pond.swimTooltip": "Pláva vp<PERSON>. <PERSON><PERSON><PERSON> (0-360).", "Pond.stopTooltip": "Skončí plávanie. Hráč sa pomaly zastaví.", "Pond.healthTooltip": "<PERSON><PERSON><PERSON><PERSON> zdravie hráča (0 je mŕtvy, 100 zdravý).", "Pond.speedTooltip": "<PERSON><PERSON><PERSON><PERSON> rýchlosť hráča (0 stojaci, 100 plná rýchlosť).", "Pond.locXTooltip": "<PERSON><PERSON><PERSON><PERSON> (x) súradnicu hráča (0 ľavý okraj, 100 pravý okraj).", "Pond.locYTooltip": "<PERSON><PERSON><PERSON><PERSON> (y) súradnicu hráča (0 dolný okraj, 100 horný okraj).", "Pond.logTooltip": "Vytlačí číslo do konzoly Vášho prehliadača.", "Pond.docsTooltip": "Zobrazí dokumentáciu programovacieho jazyka.", "Pond.documentation": "Dokumentácia", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Kyvadlo", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "Tvoje riešenie pracuje, ale mô<PERSON> to urobiť lepšie. Pomocou príkazu \"scan\" poved<PERSON> kan<PERSON>, ako <PERSON>ko má strieľať.", "Pond.helpText1": "Na zasiahnutie cieľa p<PERSON>ži prík<PERSON> \"cannon\". Prvý parameter je uhol, druh<PERSON> parameter je sila. Nájdi správnu kombináciu oboch čisel.", "Pond.helpText2": "Tento cieľ treba zasiahnuť veľakrát. Slučka \"while (true)\" opakuje niečo done<PERSON>.", "Pond.helpText3a": "Tento protivník sa pohybuje tam a späť, takže je ťažké ho zasiahnuť. Výraz \"scan\" vráti presnú silu na zasiahnutie súpera v určenom smere.", "Pond.helpText3b": "Presne takúto silu potrebuje príkaz \"cannon\" na presný zásah.", "Pond.helpText4": "Tento protivník je mimo dosahu kanóna (ktorý má limit 70 metrov). Namiesto streľby použi príkaz \"swim\", plávaj smerom k protivníkovi a nabúraj doň.", "Pond.helpText5": "Tento protivník je tiež mimo dosahu kanóna. Si príliš slab<PERSON> a náraz neprežiješ. Plávaj teda smerom k súperovi, kým je tvoja vodorovná súradnica menšia ako 50. <PERSON><PERSON> použ<PERSON> \"stop\" a \"cannon\".", "Pond.helpText6": "Tento protivník po zásahu začne utekať. Ak je mimo dosahu, plávaj mu oproti.", "Gallery": "Galéria"}