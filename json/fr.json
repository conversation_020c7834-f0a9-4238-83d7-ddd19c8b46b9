{"@metadata": {"authors": ["Aprungfoster", "Florian <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mulcyber", "NemesisIII", "Od1n", "Thibaut120094", "Urhixidur", "<PERSON><PERSON><PERSON> p", "Wladek92", "<PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "Labyrinthe", "Games.bird": "<PERSON><PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Musique", "Games.pondTutor": "<PERSON><PERSON><PERSON>", "Games.pond": "Pond", "Games.linesOfCode1": "Vous avez résolu ce niveau avec 1 ligne de JavaScript :", "Games.linesOfCode2": "Vous avez résolu ce niveau avec %1 lignes de JavaScript :", "Games.nextLevel": "Êtes-vous prêt(e) pour le niveau %1 ?", "Games.finalLevel": "Êtes-vous prêt(e) pour le prochain défi ?", "Games.submitTitle": "Titre :", "Games.linkTooltip": "Enregistrer et lier aux blocs.", "Games.runTooltip": "Lancer le programme que vous avez écrit.", "Games.runProgram": "Exécuter le programme", "Games.resetTooltip": "Arrêter le programme et réinitialiser le niveau.", "Games.resetProgram": "Réinitialiser", "Games.help": "Aide", "Games.catLogic": "Logique", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Mathématiques", "Games.catText": "Texte", "Games.catLists": "Listes", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variables", "Games.catProcedures": "Fonctions", "Games.httpRequestError": "Un problème est survenu avec la requête.", "Games.linkAlert": "Partagez vos blocs grâce à ce lien :\n\n%1", "Games.hashError": "Désolé, « %1 » ne correspond à aucun programme enregistré.", "Games.xmlError": "Impossible de charger votre fichier enregistré. Peut être a-t-il été créé avec une autre version de Blockly ?", "Games.submitted": "Merci pour ce programme ! Si notre équipe de singes savants l’aime, elle le publiera sur la galerie dans les deux jours.", "Games.listVariable": "liste", "Games.textVariable": "texte", "Games.breakLink": "Une fois que vous aurez commencé à modifier le JavaScript, vous ne pourrez pas revenir à la modification des blocs. Est-ce bon pour vous ?", "Games.blocks": "Blocs", "Games.congratulations": "Félicitations !", "Games.helpAbort": "Ce niveau est très difficile. Voulez-vous le sauter et passer au jeu suivant ? Vous pourrez toujours y revenir plus tard.", "Index.clear": "Supprimer toutes vos solutions ?", "Index.subTitle": "Jeux pour les programmeurs de demain.", "Index.moreInfo": "Informations pédagogiques...", "Index.startOver": "Voulez-vous tout recommencer ?", "Index.clearData": "Efface<PERSON> les données", "Puzzle.animal1": "Canard", "Puzzle.animal1Trait1": "Plumes", "Puzzle.animal1Trait2": "Bec", "Puzzle.animal1HelpUrl": "https://fr.wikipedia.org/wiki/Canard", "Puzzle.animal2": "Cha<PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Fourrure", "Puzzle.animal2HelpUrl": "https://fr.wikipedia.org/wiki/Chat", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://fr.wikipedia.org/wiki/<PERSON>ille", "Puzzle.animal4": "Escargot", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Bave", "Puzzle.animal4HelpUrl": "https://fr.wikipedia.org/wiki/Escargot", "Puzzle.picture": "image :", "Puzzle.legs": "pattes :", "Puzzle.legsChoose": "choisir...", "Puzzle.traits": "traits :", "Puzzle.error0": "Parfait !\nLes %1 blocs sont tous corrects.", "Puzzle.error1": "Presque ! Un bloc est incorrect.", "Puzzle.error2": "%1 blocs sont incorrects.", "Puzzle.tryAgain": "Le bloc en surbrillance est incorrect.\nEssayez à nouveau.", "Puzzle.checkAnswers": "Vérifier les réponses", "Puzzle.helpText": "Pour chaque animal (en vert), attacher son image, choisissez son nombre de pattes et faites une pile de ses traits caractéristiques.", "Maze.moveForward": "avancer", "Maze.turnLeft": "tourner à gauche", "Maze.turnRight": "tourner à droite", "Maze.doCode": "faire", "Maze.helpIfElse": "Un bloc « si–sinon » permet d’exécuter une chose sinon autre chose.", "Maze.pathAhead": "si chemin devant", "Maze.pathLeft": "si chemin vers la gauche", "Maze.pathRight": "si chemin vers la droite", "Maze.repeatUntil": "<PERSON><PERSON><PERSON><PERSON><PERSON> jusqu’à", "Maze.moveForwardTooltip": "Avance le joueur d’une case.", "Maze.turnTooltip": "<PERSON>ne le joueur à gauche ou à droite de 90 degrés.", "Maze.ifTooltip": "S’il y a un chemin dans la direction spécifiée, alors effectue ces actions.", "Maze.ifelseTooltip": "S’il y a un chemin dans la direction spécifiée, alors fais le premier bloc d’actions. Sinon fais le second bloc d’actions.", "Maze.whileTooltip": "Rép<PERSON><PERSON> les actions à l’intérieur du bloc jusqu’à atteindre le but final.", "Maze.capacity0": "Il vous reste %0 bloc.", "Maze.capacity1": "Il vous reste %1 bloc.", "Maze.capacity2": "Il vous reste %2 blocs.", "Maze.runTooltip": "Fait faire au joueur ce que disent les blocs.", "Maze.resetTooltip": "Replace le joueur au début du labyrinthe.", "Maze.helpStack": "Empiler ensemble deux blocs d’instructions « avancer » pour m’aider à atteindre le but.", "Maze.helpOneTopBlock": "Dans ce niveau, vous aurez besoin d’empiler les blocs les uns au dessus des autres dans la zone blanche de travail.", "Maze.helpRun": "Exécute votre programme pour voir ce qui arrive.", "Maze.helpReset": "Votre programme n’a pas résolu le labyrinthe. Appuyez sur « Réinitialiser » et essayez à nouveau.", "Maze.helpRepeat": "Utilise seulement deux blocs pour atteindre le but. Utilisez le bloc « répéter » pour exécuter un bloc plus d’une fois.", "Maze.helpCapacity": "Vous avez utilisé tous les blocs pour ce niveau. Pour créer un nouveau bloc, vous devez d’abord supprimer un bloc existant.", "Maze.helpRepeatMany": "Vous pouvez mettre plus d’un bloc dans un bloc « répéter ».", "Maze.helpSkins": "Choisissez votre joueur favori dans ce menu.", "Maze.helpIf": "Un bloc « si » va exécuter ce qu’il contient seulement si la condition est vraie. Essayez de tourner à gauche s’il y a un chemin vers la gauche.", "Maze.helpMenu": "Cliquez sur %1 dans le bloc « si » pour modifier sa condition.", "Maze.helpWallFollow": "Pouvez-vous résoudre ce labyrinthe plus difficile ? Essayez de suivre le mur du côté de votre main gauche. Pour les programmeurs avancés seulement !", "Bird.noWorm": "n’a pas de ver", "Bird.heading": "cap", "Bird.noWormTooltip": "L’état quand l’oiseau n’a pas atteint le ver.", "Bird.headingTooltip": "Se déplacer dans la direction d’un angle donné : 0 est vers la droite, 90 est en face, 180 est vers la gauche, etc.", "Bird.positionTooltip": "« x » et « y » marquent la position de l’oiseau. Quand « x = 0 » l’oiseau est près du bord gauche, quand « x = 100 » il est près du bord droit. Quand « y = 0 » l’oiseau est en bas, quand « y = 100 » il est en haut.", "Bird.helpHeading": "Modifier l’angle du cap pour que l’oiseau attrape le ver et atterrisse dans son nid.", "Bird.helpHasWorm": "Utiliser ce bloc pour aller dans une direction si vous avez le ver, ou bien dans une autre si vous ne l’avez pas.", "Bird.helpX": "« x » est votre position horizontale. Utiliser ce bloc pour aller dans une direction si « x » est inférieur à un nombre, dans une autre direction sinon.", "Bird.helpElse": "Cliquer sur l’icône pour modifier le bloc « si ».", "Bird.helpElseIf": "Ce niveau nécessite à la fois un bloc « sinon si » et un bloc « sinon ».", "Bird.helpAnd": "Le bloc « et » est vrai seulement si ses deux entrées sont vraies.", "Bird.helpMutator": "Glissez un bloc « sinon » dans le bloc « si ».", "Turtle.moveTooltip": "Déplace la tortue en avant ou en arrière de la quantité indiquée.", "Turtle.moveForward": "avancer de", "Turtle.moveBackward": "reculer de", "Turtle.turnTooltip": "Faire tourner la tortue à gauche ou à droite du nombre de degrés indiqué.", "Turtle.turnRight": "tourner à droite de", "Turtle.turnLeft": "tourner à gauche de", "Turtle.widthTooltip": "Modifie l’épaisseur de tracé du crayon.", "Turtle.setWidth": "mettre l’épaisseur à", "Turtle.colourTooltip": "Modi<PERSON> la couleur du crayon.", "Turtle.setColour": "mettre la couleur à", "Turtle.penTooltip": "<PERSON><PERSON>ve ou pose le crayon, pour arrêter ou commencer à dessiner.", "Turtle.penUp": "lever le crayon", "Turtle.penDown": "poser le crayon", "Turtle.turtleVisibilityTooltip": "Rend visible ou invisible la tortue (cercle et flèche).", "Turtle.hideTurtle": "cacher la tortue", "Turtle.showTurtle": "afficher la tortue", "Turtle.printHelpUrl": "https://fr.wikipedia.org/wiki/Imprimerie", "Turtle.printTooltip": "Dessine le texte dans la direction de la tortue à son emplacement.", "Turtle.print": "écrire", "Turtle.fontHelpUrl": "https://fr.wikipedia.org/wiki/Police_d'%C3%A9criture", "Turtle.fontTooltip": "Définit la police utilisée par le bloc d’écriture.", "Turtle.font": "police", "Turtle.fontSize": "taille de la police", "Turtle.fontNormal": "normal", "Turtle.fontBold": "gras", "Turtle.fontItalic": "italique", "Turtle.submitDisabled": "Lancez votre programme jusqu’à ce qu’il s’arrête. Vous pourrez ensuite publier votre dessin dans la galerie.", "Turtle.galleryTooltip": "Ouvrir la galerie des dessins.", "Turtle.galleryMsg": "Voir la galerie", "Turtle.submitTooltip": "Publier votre dessin sur la galerie.", "Turtle.submitMsg": "Publier dans la galerie", "Turtle.helpUseLoop": "Votre solution fonctionne, mais vous pouvez faire mieux.", "Turtle.helpUseLoop3": "Dessinez la forme avec seulement trois blocs.", "Turtle.helpUseLoop4": "Dessinez l’étoile avec seulement quatre blocs.", "Turtle.helpText1": "<PERSON><PERSON>ez un programme qui dessine un carré.", "Turtle.helpText2": "Modifiez votre programme pour dessiner un pentagone plutôt qu’un carré.", "Turtle.helpText3a": "Voici un nouveau bloc qui vous permet de modifier la couleur :", "Turtle.helpText3b": "Dessinez une étoile jaune.", "Turtle.helpText4a": "Voici un nouveau bloc qui vous permet de lever votre crayon de la feuille quand vous vous déplacez :", "Turtle.helpText4b": "Dessinez une petite étoile jaune, puis une ligne par dessus.", "Turtle.helpText5": "Au lieu d’une seule étoile, pouvez-vous dessiner quatre étoiles arrangées en carré ?", "Turtle.helpText6": "Dessinez trois étoiles jaunes et une ligne blanche.", "Turtle.helpText7": "Dessinez les étoiles puis quatre lignes blanches.", "Turtle.helpText8": "Dessinez 360 lignes blanches qui ressemblent à une pleine lune.", "Turtle.helpText9": "Pouvez-vous ajouter un cercle noir afin que la lune devienne un croissant ?", "Turtle.helpText10": "Dessinez ce que vous voulez. Vous avez un grand nombre de blocs à explorer. Amusez-vous !", "Turtle.helpText10Reddit": "Utilisez le bouton « Voir la galerie » pour voir ce que les autres personnes ont dessiné. Si vous dessinez quelque chose d’intéressant, utilisez le bouton « Publier dans la galerie » pour le publier.", "Turtle.helpToolbox": "Choisir une catégorie pour voir les blocs.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x de départ", "Movie.y1": "<PERSON> de d<PERSON><PERSON>", "Movie.x2": "x de fin", "Movie.y2": "y de fin", "Movie.radius": "rayon", "Movie.width": "largeur", "Movie.height": "hauteur", "Movie.circleTooltip": "Dessine un cercle de rayon donné à l’endroit spécifié.", "Movie.circleDraw": "cercle", "Movie.rectTooltip": "Dessine un rectangle de largeur et de hauteur données à l’endroit spécifié.", "Movie.rectDraw": "rectangle", "Movie.lineTooltip": "Dessine une ligne d’épaisseur donnée d’un point à un autre.", "Movie.lineDraw": "ligne", "Movie.timeTooltip": "Renvoie la durée actuelle de l’animation (0–100).", "Movie.colourTooltip": "Change la couleur du crayon.", "Movie.setColour": "fixe la couleur à", "Movie.submitDisabled": "Votre animation ne bouge pas. Utilisez les blocs pour faire quelque chose d’intéressant. Vous pourrez ensuite publier votre animation dans la galerie.", "Movie.galleryTooltip": "Ouvrir la galerie des animations.", "Movie.galleryMsg": "Voir la galerie", "Movie.submitTooltip": "Publier votre animation sur la galerie.", "Movie.submitMsg": "Publier sur la galerie", "Movie.helpLayer": "<PERSON><PERSON><PERSON><PERSON> le cercle de fond tout en haut de votre programme. Il apparaîtra alors derrière la personne.", "Movie.helpText1": "Utilisez des formes simples pour dessiner cette personne.", "Movie.helpText2a": "Ce niveau est une animation. Vous voulez déplacer le bras de la personne à travers l’écran. Appuyez sur le bouton de lecture pour voir un aperçu.", "Movie.helpText2b": "Pendant que l’animation se déroule, la valeur du bloc « temps » avance de 0 à 100. Comme vous voulez que la position « y » du bras commence à 0 et aille jusqu’à 100, cela devrait être facile.", "Movie.helpText3": "Le bloc « temps » va de 0 à 100. Mais maintenant vous voulez que la position « y » de l'autre bras démarre à 100 et aille jusqu’à 0. <PERSON><PERSON><PERSON>-vous trouver une formule mathématique qui inverse la direction ?", "Movie.helpText4": "Utilisez ce que vous avez appris au niveau précédent pour faire se croiser les jambes.", "Movie.helpText5": "La formule mathématique pour le bras est compliquée. Voici la réponse :", "Movie.helpText6": "Donner deux mains à la personne.", "Movie.helpText7": "Utilisez le bloc « si » pour dessiner une petite tête durant la première moitié du film. P<PERSON><PERSON> dessinez une grosse tête pour la seconde moitié du film.", "Movie.helpText8": "Faites aller les jambes dans l’autre direction à la moitié du film.", "Movie.helpText9": "Dessinez un cercle qui s’agrandit derrière la personne.", "Movie.helpText10": "Faites une animation de ce que vous voulez. Vous avez un grand nombre de blocs à explorer. Amusez-vous !", "Movie.helpText10Reddit": "Utilisez le bouton « Voir la galerie » pour voir les animations faites par les autres personnes. Si vous avez créé une animation intéressante, utilisez le bouton « Publier sur la galerie » pour la publier.", "Music.playNoteTooltip": "Joue une note de musique de la durée et de la hauteur données.", "Music.playNote": "jouer une %1 note %2", "Music.restTooltip": "Attend la durée spécifiée.", "Music.restWholeTooltip": "Attend la durée d’une note entière.", "Music.rest": "attendre %1", "Music.setInstrumentTooltip": "Bascule sur l’instrument spécifié en jouant les notes musicales qui suivent.", "Music.setInstrument": "règle l’instrument à : %1", "Music.startTooltip": "Exécute les blocs à l’intérieur quand le bouton « Lancer le programme » est cliqué.", "Music.start": "quand %1 est cliqué", "Music.pitchTooltip": "Une note (do4 vaut 7).", "Music.firstPart": "première partie", "Music.piano": "piano", "Music.trumpet": "trompette", "Music.banjo": "banjo", "Music.violin": "violon", "Music.guitar": "guitare", "Music.flute": "flûte", "Music.drum": "tambour", "Music.choir": "chœur", "Music.submitDisabled": "Lancer votre programme jusqu’à ce qu’il s’arrête. Vous pourrez alors publier votre musique dans la galerie.", "Music.galleryTooltip": "Ouvrir la galerie de musique.", "Music.galleryMsg": "Voir la galerie", "Music.submitTooltip": "Publier votre musique sur la galerie.", "Music.submitMsg": "Publier sur la galerie", "Music.helpUseFunctions": "Votre solution fonctionne, mais vous pouvez faire mieux. Utilisez des fonctions pour réduire la quantité de code répétée.", "Music.helpUseInstruments": "La musique rendra mieux si vous utilisez un instrument différent dans chaque bloc de démarrage.", "Music.helpText1": "Composez les quatre premières notes de « Frère Jacques ».", "Music.helpText2a": "Une « fonction » vous permet de regrouper ensemble des blocs, puis de les exécuter plus d’une fois.", "Music.helpText2b": "Créez une fonction pour jouer les quatre premières notes de « Frère Jacques ». Lancez cette fonction deux fois. N’ajoutez pas d’autres blocs de notes.", "Music.helpText3": "Créez une deuxième fonction pour la partie suivante de « Frère Jacques ». La dernière note est plus longue.", "Music.helpText4": "Créez une troisième fonction pour la partie qui suit de « Frère Jacques ». Les quatre premières notes sont plus courtes.", "Music.helpText5": "Terminez l’air complet de « Frère Jacques ».", "Music.helpText6a": "Ce nouveau bloc vous permet de passer à un autre instrument.", "Music.helpText6b": "Jouer votre air sur un violon.", "Music.helpText7a": "Ce nouveau bloc ajoute un temps de silence.", "Music.helpText7b": "<PERSON><PERSON>ez un second bloc qui a deux blocs de silence, puis joue aussi « <PERSON><PERSON> ».", "Music.helpText8": "Chaque bloc de départ doit jouer « Frère Jacques » deux fois.", "Music.helpText9": "<PERSON><PERSON><PERSON> quatre blocs de démarrage qui jouent chacun « <PERSON><PERSON> » deux fois. Ajou<PERSON>z le bon nombre de blocs de silence.", "Music.helpText10": "Composez ce que vous voulez. Vous avez un grand nombre de nouveaux blocs à explorer. Amusez-vous !", "Music.helpText10Reddit": "Utilisez le bouton « Voir la galerie » pour voir ce que les autres ont composé. Si vous composez quelque chose d’intéressant, utilisez le bouton « Publier sur la galerie » pour le publier.", "Pond.scanTooltip": "Rechercher des ennemis. Spécifier une direction (0–360). Renvoie la distance à l’ennemi le plus proche dans cette direction. Renvoie Infini si aucun ennemi n’a été trouvé.", "Pond.cannonTooltip": "Tirer au canon. Spécifier une direction (0–360) et une portée (0–70).", "Pond.swimTooltip": "<PERSON><PERSON>. Spécifier une direction (0–360).", "Pond.stopTooltip": "<PERSON><PERSON><PERSON><PERSON> de nager. Le joueur ralentira pour arrêter.", "Pond.healthTooltip": "Renvoie la santé actuelle du joueur (0 est mort, 100 est en pleine forme).", "Pond.speedTooltip": "Renvoie la vitesse actuelle du joueur (0 est à l’arrêt, 100 est à pleine vitesse).", "Pond.locXTooltip": "Renvoie la coordonnée X du joueur (0 est le bord gauche, 100 le bord droit).", "Pond.locYTooltip": "Renvoie la coordonnée Y du joueur (0 est le bord du bas, 100 est le bord du haut).", "Pond.logTooltip": "Imprime un nombre sur la console de votre navigateur.", "Pond.docsTooltip": "Affiche la documentation sur la langue.", "Pond.documentation": "Documentation", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "Cible", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "Votre solution fonctionne, mais vous pouvez faire mieux. Utilisez «analyser » pour indiquer au canon à quelle distance tirer.", "Pond.helpText1": "Utilisez la commande « canon » pour toucher la cible. Le premier paramètre est l’angle, le second la distance. Trouvez la bonne combinaison.", "Pond.helpText2": "Cette cible doit être touchée plusieurs fois. Utilisez une boucle « tant que (vrai) » pour faire quelque chose indéfiniment.", "Pond.helpText3a": "Cet adversaire avance et recule, le rendant difficile à toucher. L’expression « analyser » retourne la distance exacte à l’opposant dans la direction spécifiée.", "Pond.helpText3b": "Cette portée est exactement ce dont la commande « canon » a besoin pour tirer avec précision.", "Pond.helpText4": "Cet adversaire est trop loin pour utiliser le canon (qui a une portée limite de 70 mètres). À la place, utilisez la commande « nager » pour commencer à nager vers l’adversaire et le percuter.", "Pond.helpText5": "Cet adversaire est également trop loin pour utiliser le canon. Mais vous êtes trop faible pour survivre à une collision. Nagez vers l’adversaire tant que votre position horizontale est inférieure à 50. <PERSON><PERSON><PERSON> « arrêter » avant d’utiliser le canon.", "Pond.helpText6": "Cet adversaire s’éloignera quand il sera touché. Nagez vers lui s’il est hors de portée (70 mètres).", "Gallery": "Galerie"}