{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, "Games.name": "የብሎክሊ ጨዋታዎች", "Games.puzzle": "እንቆቅልሽ", "Games.maze": "መንገድ", "Games.bird": "ወፍ", "Games.turtle": "ኤሊ", "Games.movie": "ፊልም", "Games.music": "ሙዚቃ", "Games.pondTutor": "ኩሬ መምህር", "Games.pond": "ኩሬ", "Games.linesOfCode1": "ይህን ደረጃ በ1 የጃቫ ስክሪፕት መስመር ፈትተዋል", "Games.linesOfCode2": "ይህን ደረጃ በ%1 የጃቫ ስክሪፕት መስመሮች ፈትተዋል", "Games.nextLevel": "ለደረጃ %1 ዝግጁ ነዎት?", "Games.finalLevel": "ለሚቀጥለው ፈተና ዝግጁ ነዎት?", "Games.submitTitle": "አርዕስት", "Games.linkTooltip": "ብሎኮች ያስቀምጡ እና መስፈንጠሪያ ያግኙ", "Games.runTooltip": "የጻፉትን ፕሮግራም ያሂዱ", "Games.runProgram": "ፕሮግራም ያሂዱ", "Games.resetTooltip": "ፕሮግራሙን ያቁሙና ደረጃውን እንደገና ያስጀምሩ", "Games.resetProgram": "ዳግም ይጀመር", "Games.help": "እርዳታ", "Games.catLogic": "የሁኔታዊ መገንቢያ ብሎኮች", "Games.catLoops": "የዙሮች መገንቢያ ብሎኮች", "Games.catMath": "የሂሳብ መገንቢያ ብሎኮች", "Games.catText": "የጽሕፈት መገንቢያ ብሎኮች", "Games.catLists": "የዝርዝር መገንቢያ ብሎኮች", "Games.catColour": "የቀለም መገንቢያ ብሎኮች", "Games.catVariables": "የተላውጠ ቃላት መገንቢያ ብሎኮች", "Games.catProcedures": "የመላዎች መገንቢያ ብሎኮች", "Games.httpRequestError": "ማግኘት አልተቻለም", "Games.linkAlert": "ብሎኮችዎን በዚህ መስፈንጠርያ አጋሩ:\n\n%1", "Games.hashError": "ይቅርታ ፣ '%1' ከማንኛውም የተቀመጠ ፕሮግራም ጋር ስያሜው አይጣጣምም", "Games.xmlError": "የተቀመጠ ፋይልዎን መጫን አልተቻለም። ምናልባትም በልዩ የብሎኪ ስሪት ተፈጠሮ ይሆን?", "Games.submitted": "ለዚህ ፕሮግራም እናመሰግናለን! የሰለጠኑ ጦጣዎች ሰራተኞቻችን ከወደዱት በጥቂት ቀናት ጊዜ ውስጥ ያትሙታል", "Games.listVariable": "ዝርዝር", "Games.textVariable": "ጽሕፈት", "Games.breakLink": "አንድ ጊዜ ጃቫስክሪፕትን ማስተካከል ከጀመሩ ፣ ወደ ብሎኮች መመለስ አይችሉም ፡፡ ይሁን?", "Games.blocks": "ብሎኮች", "Games.congratulations": "እንኳን ደስ አለዎት!", "Games.helpAbort": "ይህ ደረጃ እጅግ በጣም ከባድ ነው፡፡ ዘለው ወደሚቀጥለው ጨዋታ መሄድ ይፈልጋሉ? በኋላ በማንኛውም ጊዜ ተመልሰው መምጣት ይችላሉ።", "Index.clear": "ሁሉንም መፍትሔዎችዎን ይሰረዙ?", "Index.subTitle": "ለነገዎቹ ቀማሪዎች የተቀመሙ ጨዋታዎች", "Index.moreInfo": "ለአስተማሪዎች መረጃ ...", "Index.startOver": "እንደገና መጀመር ይፈልጋሉ?", "Index.clearData": "መረጃ ያፅዱ", "Puzzle.animal1": "ዳክዬ", "Puzzle.animal1Trait1": "ላባዎች ያሉት", "Puzzle.animal1Trait2": "ማንቁርት ያለው", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "ድመት", "Puzzle.animal2Trait1": "ጢም ያለው", "Puzzle.animal2Trait2": "ፀጉር ያላት", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "ንብ", "Puzzle.animal3Trait1": "ማር የምትሰጥ", "Puzzle.animal3Trait2": "መናደፊያ ያላት", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "ቀንድ አውጣ", "Puzzle.animal4Trait1": "ሼል ያለው", "Puzzle.animal4Trait2": "የሰውነት ፈሳሽ ያለው", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "ስዕል:", "Puzzle.legs": "እግሮች:", "Puzzle.legsChoose": "ይምረጡ...", "Puzzle.traits": "ባህሪያት:", "Puzzle.error0": "ድንቅ ነው!\nሁሉም %1 ብሎኮች ትክክል ናቸው።", "Puzzle.error1": "ደርሰዋል! አንድ ብሎክ ብቻ የተሳሳተ ነው ፡፡", "Puzzle.error2": "%1 ብሎኮች ትክክል አይደሉም ፡፡", "Puzzle.tryAgain": "የደመቀው ብሎክ ትክክል አይደለም ፡፡\nመሞከርዎን አያቋርጡ！", "Puzzle.checkAnswers": "መልሶችን ይመልከቱ", "Puzzle.helpText": "ለእያንዳንዱ እንስሳ (አረንጓዴ) ስዕሉን ያያይዙ ፣ የእግሮቹን ብዛት ይምረጡ እና የባህሪያቱን አይነት መርጠው በመደርደር ያብጁት", "Maze.moveForward": "ወደፊት ሂድ", "Maze.turnLeft": "ታጠፍ ወደግራ", "Maze.turnRight": "ታጠፍ ወደቀኝ", "Maze.doCode": "አድርግ", "Maze.helpIfElse": "የ ከሆነ-ካልሆነ ብሎኮች ተፈታኝ ብሎኩ ከሆነ የመጀመሪያውን ካልሆነ ደግሞ ሌላውን ለሚያሰናደር ይረዳሉ", "Maze.pathAhead": "መንገድ ካለ ወደፊት", "Maze.pathLeft": "መንገድ ካለ በስተግራ", "Maze.pathRight": "መንገድ ካለ በስተቀኝ", "Maze.repeatUntil": "እስከዚህ ድረስ ደጋግም", "Maze.moveForwardTooltip": "ይሄ ተጫዋቹን አንድ እርምጃ ወደ ፊት ይወስደዋል", "Maze.turnTooltip": "ይሄ ተጫዋቹን ወደ ግራ ወይንም ወደቀኝ በ 90 ዲግሪዋች ያሳጥፈዋል", "Maze.ifTooltip": "በተጠቀሰው አቅጣጫ መንገድ ካለ የሆነ እርምጃ ይውሰዱ", "Maze.ifelseTooltip": "በተጠቀሰው አቅጣጫ አንድ መንገድ ካለ ፣ የመጀመሪያዎቹን ብሎክ ድርጊቶች ያከናውን። ያለበለዚያ ፣ የሁለተኛውን ብሎክ ድርጊቶች ያከናውን።", "Maze.whileTooltip": "የመጨረሻው ነጥብ ላይ እስከሚደርስ ድረስ ድርጊቶችን ይደጋግሙ ፡፡", "Maze.capacity0": "የቀሩትዎት ብሎኮች %0 ናቸው፡፡", "Maze.capacity1": "የቀሩትዎት ብሎኮች %1 ናቸው፡፡", "Maze.capacity2": "የቀሩትዎት ብሎኮች %2 ናቸው፡፡", "Maze.runTooltip": "ይህን ቁልፍ መጫን የተፃፈውን የኮምፒተር ፕሮግራም ያሂዳል።", "Maze.resetTooltip": "ይህን ቁልፍ በመጫን ተጫዋቹን ወደ ጀመረበት ቦታ ይመልሱ።", "Maze.helpStack": "\"ወደፊት\" ብሎኮችን በአንድ ላይ በመቀጣጠል ወደ መጨረሻው ግብ አድርሰኝ", "Maze.helpOneTopBlock": "በዚህ ደረጃ ፣ ሁሉንም ብሎኮች በአንድ ላይ በነጭ የሥራ ቦታ ላይ ቀጣጥሎ መደርደር ያስፈልግዎታል ፡፡", "Maze.helpRun": "ምን እንደሚከሰት ለማየት ፕሮግራምዎን ያሂዱ።", "Maze.helpReset": "የገነቡት ፕሮግራም ከግብ የሚደርስ አልሆነም፡፡ ‹ዳግም አስጀምር› ን ተጭነው እንደገና ይሞክሩ፡፡", "Maze.helpRepeat": "ሁለት ብሎኮችን ብቻ በመጠቀም የዚህን መንገድ መጨረሻ ይድረሱ ፡፡ ከአንድ ጊዜ በላይ ብሎክን ለማሄድ 'ድገም' ን ይጠቀሙ ፡፡", "Maze.helpCapacity": "ለዚህ ደረጃ ሁሉንም ብሎኮች ተጠቅመዋል ፡፡ አዲስ ብሎክ ለመፍጠር በመጀመሪያ አንድ ብሎክ መሰረዝ ያስፈልግዎታል ፡፡", "Maze.helpRepeatMany": "ከአንድ በላይ ብሎኮችን በ'ድገም' ብሎክ ውስት ማገጣጠም ይችላሉ ፡፡", "Maze.helpSkins": "ከዚህ ምናሌ ውስጥ ተወዳጅ ተጫዋችዎን ይምረጡ።", "Maze.helpIf": "የ'ቢሆን' ብሎክ አንድ ነገር የሚያከናው ሁኔታው እውነት ከሆነ ብቻ ነው ፡፡ ወደ ግራ የሚወስደ መንገድ ካለ ወደ ግራ ለመሄድ ይሞክሩ ፡፡", "Maze.helpMenu": "ሁኔታውን ለመቀየር በ 'ቢሆን' ብሎኩ ላይ %1 ን ጠቅ ያድርጉ ፡፡", "Maze.helpWallFollow": "ይህንን የተወሳሰበ መንገድ አልፈው ግብ ላይ መድረስ ይችላሉ? የግራ ግድግዳውን ለመከተል ይሞክሩ። የላቀ ችሎታ ላላቸው ቀማሪዎች ብቻ!", "Bird.noWorm": "ወፏ ትል ከሌላት", "Bird.heading": "አቅጣጫ", "Bird.noWormTooltip": "ወፏ ትል ሳታገኝ ስትቀር ያለው ሁኔታ", "Bird.headingTooltip": "የተሰጠው አንግል አቅጣጫ ይጠቀሙ: 0 ወደ ቀኝ ነው፣ 90 ቀጥታ ነው ወዘተ", "Bird.positionTooltip": "x እና y የወፏን አቀማመጥ ያመለክታሉ፡፡ X = 0 ወፏ ከግራ ጠርዝ አጠገብ ስትሆን ሲሆን በቀኝ ጠርዝ አጠገብ ስትገኝ x = 100 ነው ፡፡ Y = 0 ወፏ ከስር ስትሆን ሲሆን ፣ y = 100 ወፏ ከአናት ላይ ስትደርስ ነው ፡፡", "Bird.helpHeading": "ወፏ ምግቧ የሆነውን ትል ይዛ ወደ ጎጆዋ ውስጥ እንድትገባ ለማድረግ አቅጣጫ ይለውጡ ፡፡", "Bird.helpHasWorm": "ትል ካለዎት በአንኛው አቅጣጫ ለመሄድ ይህንን ብሎክ ይጠቀሙ ፣ ትል ከሌልዎት ደግሞ የተለየ አቅጣጫ ይጠቀሙ፡፡", "Bird.helpX": "'x' እርስዎ ያሉበት የአግድም አቀማመጥ ነው። 'x' ቀጥሎ ካለው ቁጥር በታች ከሆነ ፣ በአንዱ ከበለጠ ደግሞ በሌላ አቅጣጫ ለመሄድ ይህንን ብሎክ ይጠቀሙ።", "Bird.helpElse": "የ 'If' ብሎኩን ለመቀየር አዶውን ጠቅ ያድርጉ።", "Bird.helpElseIf": "ይህ ደረጃ የ'else if' እና 'else' ብሎኮችን ይፈልጋል ፡፡", "Bird.helpAnd": "የ 'and' ብሎክ እውነት የሚሆነው ሁለቱም ግብአቶች እውነት ከሆኑ ብቻ።", "Bird.helpMutator": "አንድን የ'else' ብሎክ ወደ 'if' ብሎክ ይጎትቱ።", "Turtle.moveTooltip": "ኤሊውን ወደ ፊት ወይም ወደ ኋላ በተጠቀሰው መጠን ያንቀሳቅሰዋል።", "Turtle.moveForward": "በዚህ ያህል መጠን ወደፊት ሂድ", "Turtle.moveBackward": "በዚህ ያህል መጠን ወደ ኋላ ሂድ", "Turtle.turnTooltip": "በተጠቀሰው ዲግሪዎች ቁጥር ኤሊውን ወደ ግራ ወይም ወደ ቀኝ ያዞረዋል።", "Turtle.turnRight": "በዚህ ያህል መጠን ወደ ቀኝ ሂድ", "Turtle.turnLeft": "በዚህ ያህል መጠን ወደ ግራ ሂድ", "Turtle.widthTooltip": "የብዕሩን ቀለም ይለውጣል።", "Turtle.setWidth": "ስፋቱን ይለውጡ ወደ", "Turtle.colourTooltip": "የብዕሩን ቀለም ይለውጣል።", "Turtle.setColour": "ቀለም ይለውጡ ወደ", "Turtle.penTooltip": "ስዕሉ ለመስጀመር ብዕሩን ከፍ ለማድረግ ፣ ወይም ስለው ሲጨርሱ ብዕሩን ለማስቀመጥ ይረዳል", "Turtle.penUp": "ብዕር ያንሱ", "Turtle.penDown": "ብዕር ያስቀምጡ", "Turtle.turtleVisibilityTooltip": "ኤሊው (ክብ እና ቀስት) እንዲታይ ወይም የማይታይ ያደርገዋል።", "Turtle.hideTurtle": "ኤሊውን ይደብቁ", "Turtle.showTurtle": "ኤሊውን ያሳዩ", "Turtle.printTooltip": "ኤሊው ወዳለበት አቅጣጫ ጽሑፍን ይስላል፡፡", "Turtle.print": "ያትሙ", "Turtle.fontTooltip": "በ print ብሎክ ውስጥ ጥቅም ላይ የሚውለውን ቅርጸ-ቁምፊ ያዘጋጃል።", "Turtle.font": "ቅርጸ-ቁምፊ", "Turtle.fontSize": "የቅርጸ-ቁምፊ መጠን", "Turtle.fontNormal": "መደበኛ", "Turtle.fontBold": "ደማቅ", "Turtle.fontItalic": "ሰያፍ", "Turtle.submitDisabled": "ፕሮግራምዎ እስኪያቆም ድረስ ያሂዱ። ከዚያ ስዕልዎን ወደ ቤተ-ስዕሉ ማስገባት ይችላሉ።", "Turtle.galleryTooltip": "የስዕሎችን ማእከል ይክፈቱ።", "Turtle.galleryMsg": "ማዕከለ-ስዕላትን ይመልከቱ", "Turtle.submitTooltip": "ስዕልዎን ወደ ማእከለ ስዕላት ያስገቡ።", "Turtle.submitMsg": "ወደ ማዕከለ ስዕላት ያስገቡ", "Turtle.helpUseLoop": "ያበጁት መፍትሔ መልካም ነው፣ ነገር ግን ከዚህ የተሻለ መስራት ይችላሉ።", "Turtle.helpUseLoop3": "ቅርጹን በሶስት ብሎኮች ብቻ ይሳሉ ፡፡", "Turtle.helpUseLoop4": "ኮከቡን በአራት ብሎኮች ብቻ ይሳሉ ፡፡", "Turtle.helpText1": "ካሬ የሚስል ፕሮግራም ይፍጠሩ።", "Turtle.helpText2": "በካሬ ፋንታ ፔንታጎን ለመሳል ፕሮግራምዎን ይለውጡ።", "Turtle.helpText3a": "ቀለሙን ለመለወጥ የሚያስችልዎ አዲስ ብሎክ አለ", "Turtle.helpText3b": "ቢጫ ኮከብ ይሳሉ።", "Turtle.helpText4a": "በሚንቀሳቀሱበት ጊዜ ብዕርዎን ከወረቀት ላይ ለማንሳት የሚያስችል አዲስ ብሎክ አለ፤", "Turtle.helpText4b": "አንድ ትንሽ ቢጫ ኮከብ ይሳሉ እና ከዚያ በላይ መስመር ይሳሉ።", "Turtle.helpText5": "በአንድ ኮከብ ምትክ አራት ኮከቦችን በካሬ አይነት መሳል ይችላሉ?", "Turtle.helpText6": "ሶስት ቢጫ ኮከቦችን ይሳሉ እና አንድ ነጭ መስመር ይሳሉ።", "Turtle.helpText7": "ኮከቦችን ይሳሉ ከዚያም አራት ነጭ መስመሮችን ይሳሉ።", "Turtle.helpText8": "360 ነጫጭ መስመሮችን መሳል ሙሉ ጨረቃን ይመስላል።", "Turtle.helpText9": "ግማሽ ጨረቃ እንድትሆን ጥቁር ክበብ ማከል ይችላሉ?", "Turtle.helpText10": "ማንኛውንም የሚፈልጉትን ነገር ይሳሉ። እርስዎ ሊጠቀሙ የሚችሉባቸው በጣም ብዙ ቁጥር ያላቸው አዳዲስ ብሎኮች አሉዎት ፡፡ ይዝናኑ!", "Turtle.helpText10Reddit": "ሌሎች ሰዎች ምን እንደሳቡ ለመመልከት የ “ማዕከለ-ስዕላትን ይመልከቱ” የሚለውን ቁልፍ ይጠቀሙ ፡፡ አንድ አስደሳች ነገር ከሳሉ እሱን ለማተም 'ወደ ማእከለ-ስዕላት ያስገቡ' የሚለውን ቁልፍ ይጠቀሙ ፡፡", "Turtle.helpToolbox": "ብሎኮችን ለማየት አንድ ምድብ ይምረጡ፡፡", "Movie.x": "X", "Movie.y": "Y", "Movie.x1": "መነሻ X", "Movie.y1": "መነሻ Y", "Movie.x2": "መጨረሻ X", "Movie.y2": "መጨረሻ Y", "Movie.radius": "ራዲየስ", "Movie.width": "ስፋት", "Movie.height": "ቁመት", "Movie.circleTooltip": "በተጠቀሰው ቦታ እና ከተጠቀሰው ራዲየስ ክብ ይስላል ፡፡", "Movie.circleDraw": "ክብ", "Movie.rectTooltip": "በተጠቀሰው ቦታ ላይ እና ከተጠቀሰው ስፋት እና ቁመት አራት ማእዘን ይስላል።", "Movie.rectDraw": "አራት ማእዘን", "Movie.lineTooltip": "በተጠቀሰው ስፋት ከአንድ ነጥብ ወደ ሌላው መስመር ይሳላል።", "Movie.lineDraw": "መስመር", "Movie.timeTooltip": "በአኒሜሽኑ ውስጥ የአሁኑን ጊዜ ይመልሳል (0-100) ።", "Movie.colourTooltip": "የብዕሩን ቀለም ይለውጣል።", "Movie.setColour": "ቀለም ይለውጡ ወደ", "Movie.submitDisabled": "የእርስዎ ፊልም አይንቀሳቀስም። አንድ አስደሳች ነገር ለማድረግ ብሎኮችን ይጠቀሙ ፡፡ ከዚያ ፊልምዎን ወደ ቤተ-ስዕሉ ማስገባት ይችላሉ።", "Movie.galleryTooltip": "የፊልሞች ማዕከል ይክፈቱ።", "Movie.galleryMsg": "ማዕከለ-ፊልምን ይመልከቱ", "Movie.submitTooltip": "ፊልምዎን ወደ ማእከለ ፊልም ያስገቡ።", "Movie.submitMsg": "ወደ ማዕከለ ስዕላት ያስገቡ", "Movie.helpLayer": "የበስተጀርባውን ክብ ወደ ፕሮግራምዎ አናት ያዛውሩ ፡፡ ከዚያ ከሰውየው ጀርባ ይታያል።", "Movie.helpText1": "ይህንን ሰው ለመሳል ቀላል ቅርጾችን ይጠቀሙ።", "Movie.helpText2a": "ይህ ደረጃ ፊልም ነው ፡፡ የግለሰቡ ክንድ በማያ ገጹ ላይ እንዲንቀሳቀስ ያድርጉ። ቅድመ-እይታን ለማየት የጨዋታ ቁልፉን ይጫኑ ፡፡", "Movie.helpText2b": "ፊልሙ ሲጫወት ፣ የ 'time' ብሎኩ ዋጋ ከ 0 እስከ 100 ይቆጥራል። የክንዱ የ y አቀማመጥ በ 0 ጀምሮ ወደ 100 ስለሚቆትጥር ይሄ ቀለል ይላል ፡፡", "Movie.helpText3": "የ 'time' ብሎክ ቁጥር ከ 0 እስከ 100 ይቆጥራል። አሁን ግን የሌላኛው ክንድ የ “y” አቀማመጥ በ 100 እንዲጀምር እና ወደ 0 ይሄዳል ፡፡ አቅጣጫውን የሚያስቀይር ቀላል የሂሳብ ቀመር ማግኘት ይችላሉ?", "Movie.helpText4": "ቀደም ሲል የተማሩትን ተጠቅመው እግሮቹን የተጣመሩ ያድርጉ፡፡", "Movie.helpText5": "ለክንድ  የሚሆነው የሂሳብ ቀመር ትንሽ የተወሳሰበ ነው። መልሱ እዚህ አለ", "Movie.helpText6": "ለሰውየው ሁለት እጆች ይስሩለት፡፡", "Movie.helpText7": "ለፊልሙ የመጀመሪያ አጋማሽ ትንሽ ጭንቅላትን ለመሳል የ “if” ብሎክን ይጠቀሙ ፡፡ ከዚያ ለፊልሙ ሁለተኛ አጋማሽ አንድ ትልቅ ጭንቅላት ይሳሉ።", "Movie.helpText8": "በፊልሙ አጋማሽ ላይ እግሮቹ አቅጣጫ እንዲቀይሩ ያድርጉ።", "Movie.helpText9": "ከሰውየው በስተጀርባ የሚሰፋ ክብ ይሳሉ።", "Movie.helpText10": "ማንኛውንም የሚፈልጉትን ነገር ይሳሉ። እርስዎ ሊጠቀሙ የሚችሉባቸው በጣም ብዙ ቁጥር ያላቸው አዳዲስ ብሎኮች አሉዎት ፡፡ ይዝናኑ!", "Movie.helpText10Reddit": "ሌሎች ሰዎች ምን እንደሳቡ ለመመልከት የ “ማዕከለ-ስዕላትን ይመልከቱ” የሚለውን ቁልፍ ይጠቀሙ ፡፡ አንድ አስደሳች ነገር ከሳሉ እሱን ለማተም 'ወደ ማእከለ-ስዕላት ያስገቡ' የሚለውን ቁልፍ ይጠቀሙ ፡፡", "Music.playNoteTooltip": "በመረጡት የጊዜ ቆይታ እና ውፍረት አንድ የሙዚቃ ኖታ ይጫወታል።", "Music.playNote": "ጨዋታ %1  ኖታ %2", "Music.restTooltip": "ለተጠቀሰው የጊዜ ቆይታ ይጠብቃል።", "Music.restWholeTooltip": "አንድ ሙሉ ኖታ ይጠብቃል።", "Music.rest": "ያሳርፉ %1", "Music.setInstrumentTooltip": "ተከታታይ የሙዚቃ ኖታዎችን ሲያጫውቱ ድምጹ ወደተመረጠው የሙዚቃ መሣሪያ ይቀየራል።", "Music.setInstrument": "የሙዚቃ መሣሪያ ይቀይሩ %1", "Music.startTooltip": "የ 'ፕሮግራም ያሂዱ' አዝራሩ ጠቅ ሲደረግ በውስጡ ያሉትን ብሎኮች ያሄዳሉ።", "Music.start": "%1 ጠቅ ሲደረግ", "Music.pitchTooltip": "አንድ ኖታ(C4 7 ነው)።", "Music.firstPart": "የመጀመሪያ ክፍል", "Music.piano": "ፒያኖ", "Music.trumpet": "ትራምፔት", "Music.banjo": "ባንጆ", "Music.violin": "ቫዮሊን", "Music.guitar": "ጊታር", "Music.flute": "ዋሽንት", "Music.drum": "ከበሮ", "Music.choir": "መዘምራን", "Music.submitDisabled": "ፕሮግራምዎ እስኪያቆም ድረስ ያሂዱ። ከዚያ ስዕልዎን ወደ ቤተ-ስዕሉ ማስገባት ይችላሉ።", "Music.galleryTooltip": "የሙዚቃ ማእከልን ይክፈቱ።", "Music.galleryMsg": "የሙዚቃ ማእከሉን ይመልከቱ", "Music.submitTooltip": "በማእከል የእርስዎን ሙዚቃ ያስገቡ", "Music.submitMsg": "ሙዚቃ ወደ ማእከሉ ያስገቡ", "Music.helpUseFunctions": "የሰሩት መፍትሔ ይሠራል ፣ ግን ከዚህም በተሻለ መስራት ይችላሉ። የተደጋገሙ ኮዶችን መጠን ለመቀነስ የመላ መገንቢያዎችን (functions) ይጠቀሙ።", "Music.helpUseInstruments": "በእያንዳንዱ የመነሻ ብሎክ ውስጥ የተለየ የሙዚቃ መሳሪያ የሚጠቀሙ ከሆነ ሙዚቃው በተሻለ ሁኔታ ይሰማል ፡፡", "Music.helpText1": "የ‹ፍሬ ዣክ›ን የመጀመሪያዎቹን አራት ኖታዎች ያቀናብሩ፡፡", "Music.helpText2a": "'መላ' (functions) ብሎኮችን በአንድ ላይ ለመሰብሰብ ያስችልዎታል ፣ ከዚያም ይሄን በተፈለገው ድግግሞሽ በቀላሉ ማስኬድ ይችላሉ፡፡", "Music.helpText2b": "የመጀመሪያዎቹን አራት የ 'ፍሬ ዣክ' ኖታዎች ለመጫወት መላ ይፍጠሩ ፡፡ ያንን የመላ መገንቢያ ሁለት ጊዜ ያሂዱት። ምንም አዳዲስ የnote ብሎኮች አይጨምሩ።", "Music.helpText3": "ለሚቀጥለው የ 'ፍሬ ዣክ' ሁለተኛ መላ ይፍጠሩ ፡፡ የመጨረሻው ኖታ ረዘም ይላል ፡፡", "Music.helpText4": "ለሚቀጥለው የ 'ፍሬ ዣክ' አንድ ሦስተኛ መላ ይፍጠሩ ፡፡ የመጀመሪያዎቹ አራት ኖታዎች አጭጫር ናቸው ፡፡", "Music.helpText5": "ሙሉውን የ 'ፍሬ ዣክ'  ዜማ ይሙሉ ፡፡", "Music.helpText6a": "ይህ አዲስ ብሎክ ወደ ሌላ መሣሪያ እንዲቀይሩ ያስችልዎታል ፡፡", "Music.helpText6b": "ዜማዎን በቫዮሊን ይጫወቱ።", "Music.helpText7a": "ይህ አዲስ ብሎክ ጸጥታን ይጨምራል።", "Music.helpText7b": "ሁለት የሚያዘገዩ ብሎኮች ያሉት የመጀመሪያ ብሎኮች ይፍጠሩ ፣ ከዚያ 'ፍሬ ዣክ' ይጫወት ፡፡", "Music.helpText8": "እያንዳንዱ ጅምር 'ፍሬ ዣክ'ን ሁለት ጊዜ መጫወት አለበት ፡፡", "Music.helpText9": "እያንዳንዳቸው ሁለት ጊዜ ‹ፍሬ ዣክ› የሚጫወቷቸውን አራት የመጀመሪያ ብሎኮች ይፍጠሩ ፡፡ ተገቢ የማዘግያ ብሎኮችን ያክሉ።", "Music.helpText10": "ማንኛውንም የሚፈልጉትን ነገር ይሳሉ። እርስዎ ሊጠቀሙ የሚችሉባቸው በጣም ብዙ ቁጥር ያላቸው አዳዲስ ብሎኮች አሉዎት ፡፡ ይዝናኑ!", "Music.helpText10Reddit": "ሌሎች ሰዎች ምን እንደሳቡ ለመመልከት የ “ማዕከለ-ስዕላትን ይመልከቱ” የሚለውን ቁልፍ ይጠቀሙ ፡፡ አንድ አስደሳች ነገር ከሳሉ እሱን ለማተም 'ወደ ማእከለ-ስዕላት ያስገቡ' የሚለውን ቁልፍ ይጠቀሙ ፡፡", "Pond.scanTooltip": "ጠላቶችን ይቃኙ፤ አቅጣጫን ይግለጹ (0-360)፣  በሰጡት አቅጣጫ በኩል በቅርበት የሚገኘውን ጠላት ርቀት ያያሉ ፡፡ ምንም ጠላት ካልተገኘ Infinity ይመልሳል።", "Pond.cannonTooltip": "መድፉን ይተኩሱ፡፡ አቅጣጫ (0-360) እና ርቀት(0-70) ይግለጹ።", "Pond.swimTooltip": "ወደ ፊት ይዋኙ፤ አቅጣጫ ይግለጹ (0-360)", "Pond.stopTooltip": "መዋኘት ያቁሙ ፤ ተጫዋቹ ቀስ እያለ ሄዶ ይቆማል።", "Pond.healthTooltip": "የተጫዋቹን ወቅታዊ ጤና ይመልሳል (0 ሞቷል ፣ 100 ጤናማ ነው)።", "Pond.speedTooltip": "የተጫዋቹን የአሁኑ ፍጥነት ይመልሳል (0 ቆሟል ፣ 100 ሙሉ ፍጥነት ነው)።", "Pond.locXTooltip": "ተጫዋቹ ያለበትን የ X ቦታ ይመልሳል (0 የግራ ጠርዝ ነው ፣ 100 የቀኝ ጠርዝ ነው)።", "Pond.locYTooltip": "ተጫዋቹ ያለበትን የ Y ቦታ ይመልሳል (0 የግራ ጠርዝ ነው ፣ 100 የቀኝ ጠርዝ ነው)።", "Pond.docsTooltip": "የቋንቋ ሰነዶችን ያሳዩ።", "Pond.documentation": "ሰነዶች", "Pond.playerName": "ተጫዋች", "Pond.targetName": "ዒላማ", "Pond.pendulumName": "ፔንዱለም", "Pond.scaredName": "ፈሪ", "Pond.helpUseScan": "የእርስዎ መፍትሔ ይሠራል ፣ ግን በተሻለ መስራት ይችላሉ። ምን ያህል ርቀት መተኮስ እንዳለበት ለመንገር 'scan' ይጠቀሙ።", "Pond.helpText1": "ዒላማውን ለመምታት የ ‹canon› ትዕዛዙን ይጠቀሙ ፡፡ የመጀመሪያው ግባአት አንግል ነው ፣ ሁለተኛው ርቀት ነው። ትክክለኛውን ጥምር ይፈልጉ።", "Pond.helpText2": "ይህ ዒላማ ብዙ ጊዜ መምታት አለበት። ያለገደብ የሆነ ነገር ለመስራት 'while (true)' ይጠቀሙ።", "Pond.helpText3a": "ይህ ጠላት ወደፊትና ወደኋላ ስለሚንቀሳቀስ ለመመታት ከባድ ያደርገዋል ፡፡ ‹scan› በተጠቀሰው አቅጣጫ ከጠላት ጋር ያለዎትን ትክክለኛውን ርቀት ወደ ያሳያልና ይጠቀሙበት ፡፡", "Pond.helpText3b": "ይህ ርቀት በትክክል ‹canon› ለመተኮስ የሚያስፈልገው ነው ፡፡", "Pond.helpText4": "ይህ ጠላት መድፉን ለመጠቀም በጣም ሩቅ ነው (የ 70 ሜትር ወሰን አለው) ፡፡ በምትኩ ፣ ወደ ተቃዋሚው መዋኘት ለመጀመር 'swim' ትዕዛዙን ይጠቀሙ።", "Pond.helpText5": "ይህ ጠላት መድፉን ለመጠቀም በጣም ሩቅ ነው ፡፡ እርስዎ ደግሞ ከግጭት ለመትረፍ በጣም ደካማ ነዎት ፡፡ አዋጭ የሚሆነው አግዳሚ ስፍራዎ ከ 50 በታች እስኪሆን ድረስ ወደ ተጋጣሚው ላይ ይዋኙና  ከዚያ 'stop'ን ተጠቅመው መድፉን ይጠቀሙ ፡፡", "Pond.helpText6": "ይህ ጠላት ሲመቱት ይሸሻል። ከርቀት (70 ሜትር) ውጭ ከሆነ ይዋኙ ፡፡", "Gallery": "ማጠራቀሚያ ማእከል"}