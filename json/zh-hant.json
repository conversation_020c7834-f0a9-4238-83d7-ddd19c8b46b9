{"@metadata": {"authors": ["Dnow", "Gasolin", "HellojoeAoPS", "Jessica1213", "Justincheng12345", "Kasimtan", "<PERSON><PERSON>", "LNDDYL", "Liuxinyu970226", "Maskers", "Weather Top Wizard", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xiplus", "捍粵者"]}, "Games.name": "Blockly 遊戲", "Games.puzzle": "拼圖", "Games.maze": "迷宮", "Games.bird": "小鳥", "Games.turtle": "烏龜", "Games.movie": "影片", "Games.music": "音樂", "Games.pondTutor": "池塘遊戲教程", "Games.pond": "池塘", "Games.linesOfCode1": "你用了一行JavaScript過關：", "Games.linesOfCode2": "你用了%1行JavaScript過關：", "Games.nextLevel": "準備好要闖第 %1 關了嗎？", "Games.finalLevel": "準備好接受下個挑戰嗎？", "Games.submitTitle": "標題：", "Games.linkTooltip": "儲存並連結到區塊。", "Games.runTooltip": "運行你寫的程式。", "Games.runProgram": "運行程式", "Games.resetTooltip": "停止程式並重置關卡。", "Games.resetProgram": "重置", "Games.help": "說明", "Games.catLogic": "邏輯", "Games.catLoops": "迴圈", "Games.catMath": "運算", "Games.catText": "文字", "Games.catLists": "清單", "Games.catColour": "顏色", "Games.catVariables": "變數", "Games.catProcedures": "函式", "Games.httpRequestError": "這個請求有些問題。", "Games.linkAlert": "透過此連結分享您的區塊：\n\n%1", "Games.hashError": "對不起，「%1」並未對應任何已保存的程式。", "Games.xmlError": "未能載入您保存的檔案。或許它是由其他版本的 Blockly 創建？", "Games.submitted": "感謝您所提交的程式！若我們的那一群訓練有素猴子們喜歡它的話，將會公開此內容至展廊裡一陣子。", "Games.listVariable": "清單", "Games.textVariable": "文字", "Games.breakLink": "一旦你開始編輯 JavaScript，就不能返回編輯區塊，確定嗎？", "Games.blocks": "區塊", "Games.congratulations": "恭喜！", "Games.helpAbort": "此關卡非常困難。您是否要跳過並進入下一關？您隨時可以返回這關。", "Index.clear": "刪除你所有的解法？", "Index.subTitle": "為了明日的程式設計師所設計的遊戲。", "Index.moreInfo": "給教育者的資訊...", "Index.startOver": "要重新開始嗎？", "Index.clearData": "清除資料", "Puzzle.animal1": "鴨子", "Puzzle.animal1Trait1": "羽毛", "Puzzle.animal1Trait2": "鳥嘴", "Puzzle.animal1HelpUrl": "https://zh.wikipedia.org/wiki/鴨", "Puzzle.animal2": "貓", "Puzzle.animal2Trait1": "鬍鬚", "Puzzle.animal2Trait2": "毛皮", "Puzzle.animal2HelpUrl": "https://zh.wikipedia.org/wiki/貓", "Puzzle.animal3": "蜜蜂", "Puzzle.animal3Trait1": "蜂蜜", "Puzzle.animal3Trait2": "蜂刺", "Puzzle.animal3HelpUrl": "https://zh.wikipedia.org/wiki/蜂", "Puzzle.animal4": "蝸牛", "Puzzle.animal4Trait1": "貝殼", "Puzzle.animal4Trait2": "黏液", "Puzzle.animal4HelpUrl": "https://zh.wikipedia.org/wiki/蝸牛", "Puzzle.picture": "圖片：", "Puzzle.legs": "腿：", "Puzzle.legsChoose": "選擇…", "Puzzle.traits": "特徵", "Puzzle.error0": "太完美了！\n所有的 %1 個區塊都正確。", "Puzzle.error1": "差不多了！只剩 1 個區塊不正確。", "Puzzle.error2": "%1 個區塊不正確。", "Puzzle.tryAgain": "高亮顯示的區塊不正確，再試試看。", "Puzzle.checkAnswers": "檢查答案", "Puzzle.helpText": "為每個動物（綠色）加上它的圖片、選擇腿的數量並加入其擁有的特徵。", "Maze.moveForward": "向前移動", "Maze.turnLeft": "向 左 轉", "Maze.turnRight": "向 右 轉", "Maze.doCode": "執行", "Maze.helpIfElse": "如果…否則區塊會依條件執行某事，若不符合條件則執行其他事。", "Maze.pathAhead": "如果 前 方有路", "Maze.pathLeft": "如果 左 方有路", "Maze.pathRight": "如果 右 方有路", "Maze.repeatUntil": "重複 直到", "Maze.moveForwardTooltip": "讓角色向前移動一格。", "Maze.turnTooltip": "向左或向右轉 90 度。", "Maze.ifTooltip": "如果某方向有路可以移動，就去執行某些動作。", "Maze.ifelseTooltip": "如果某方向有路可以移動，就去執行第一個區塊指定的動作，如果沒有，執行第二個區塊指定的動作。", "Maze.whileTooltip": "重複區塊內的操作，直至到達終點。", "Maze.capacity0": "你還可以使用 %0 個區塊。", "Maze.capacity1": "你還可以使用 %1 個區塊。", "Maze.capacity2": "你還可以使用 %2 個區塊。", "Maze.runTooltip": "讓角色按區塊指令動作。", "Maze.resetTooltip": "將角色放回迷宮入口。", "Maze.helpStack": "區塊組是由一系列區塊組成。將數個「移動-向前」區塊連結起來，好讓角色走到出口。", "Maze.helpOneTopBlock": "在這關中，您需要在白色的工作區中將區塊堆在一起。", "Maze.helpRun": "運行您的程式，看看會發生什麼。", "Maze.helpReset": "你寫的程式無法完成這個迷宮，點擊「重置」再試一次。", "Maze.helpRepeat": "使用 2 個區塊來到達迷宮出口。使用「重複直到」區塊以多次執行該區塊中包含的程式。", "Maze.helpCapacity": "您使用的區塊已達本關上限。若要再使用一個新的區塊，請先移除工作區中的一個區塊。", "Maze.helpRepeatMany": "你可以在「重複直到」區塊中塞入多個區塊。", "Maze.helpSkins": "從選單中選擇你最喜歡的角色。", "Maze.helpIf": "「如果」區塊只有當條件成立時才會執行。試著向左轉，如果左方有路的話。", "Maze.helpMenu": "點擊「如果」區塊中的 %1 以變更其狀態。", "Maze.helpWallFollow": "你能解決這個複雜的迷宮嗎？請嘗試沿著左手邊的牆走。只有高級的程式設計師解得出來喔！", "Bird.noWorm": "還沒吃到蟲", "Bird.heading": "面向", "Bird.noWormTooltip": "鳥沒有捉到蟲子的條件。", "Bird.headingTooltip": "轉到指定角度的方向：0 是向右，90 是向上…", "Bird.positionTooltip": "橫坐標x和縱坐標y記錄鳥的當前位置。當 x = 0 時鳥貼著左側邊緣，當 x = 100 時貼著右側邊緣。當 y = 0 時在最底端，當 y = 100 時在最上端。", "Bird.helpHeading": "變更方向，讓鳥可以吃到蟲子，最後降落在牠的巢上。", "Bird.helpHasWorm": "如果你已經吃到蟲子，可以用這個區塊來往單一方向前進；如果你還沒有吃到蟲子，就往另一個方向。", "Bird.helpX": "「x」是你現在的水平位置，當「x」小於某個數字的時候，用這個區塊來往單一方向前進；否則，則往另一個方向前進。", "Bird.helpElse": "點擊圖標以修改「如果」區塊。", "Bird.helpElseIf": "此關卡需要一個「否則如果」和一個「否則」區塊。", "Bird.helpAnd": "「且」區塊只在兩個輸入都為 true 時，才會返回 true。", "Bird.helpMutator": "拖曳「否則」區塊到「如果」區塊那裡。", "Turtle.moveTooltip": "指定烏龜向前或向後移動的量。", "Turtle.moveForward": "向前移動指定距離", "Turtle.moveBackward": "向後移動指定距離", "Turtle.turnTooltip": "指定烏龜向左或向右轉向的角度。", "Turtle.turnRight": "向右轉向指定角度", "Turtle.turnLeft": "向左轉向指定角度", "Turtle.widthTooltip": "變更筆跡的寬度。", "Turtle.setWidth": "寬度設為", "Turtle.colourTooltip": "變更筆跡的顏色。", "Turtle.setColour": "顏色設為", "Turtle.penTooltip": "下筆或是抬筆以開始或停止畫圖。", "Turtle.penUp": "抬筆", "Turtle.penDown": "下筆", "Turtle.turtleVisibilityTooltip": "讓烏龜（圓圈和箭頭）顯示或隱藏。", "Turtle.hideTurtle": "隱藏烏龜", "Turtle.showTurtle": "顯示烏龜", "Turtle.printHelpUrl": "https://zh.wikipedia.org/wiki/印刷", "Turtle.printTooltip": "在烏龜的方向及位置繪製文字。", "Turtle.print": "列印", "Turtle.fontHelpUrl": "https://zh.wikipedia.org/wiki/字體", "Turtle.fontTooltip": "設置列印區塊要使用的字體。", "Turtle.font": "字體", "Turtle.fontSize": "字體大小", "Turtle.fontNormal": "一般", "Turtle.fontBold": "粗體", "Turtle.fontItalic": "斜體", "Turtle.submitDisabled": "執行您的程式直到它停止。然後您可以提交您的繪畫至圖庫。", "Turtle.galleryTooltip": "開啟繪畫庫。", "Turtle.galleryMsg": "檢視繪畫庫", "Turtle.submitTooltip": "提交你的繪圖作品到繪畫庫。", "Turtle.submitMsg": "提交至繪畫庫", "Turtle.helpUseLoop": "你的解法有用，但是你還可以做得更好。", "Turtle.helpUseLoop3": "請僅使用到 3 塊區塊來畫出這個圖形。", "Turtle.helpUseLoop4": "僅用四個區塊來繪製星形。", "Turtle.helpText1": "建立畫出一個正方形的程式。", "Turtle.helpText2": "更改你的程式，讓它改為繪製一個五邊形而不是正方形。", "Turtle.helpText3a": "有一個新的區塊了，它可以讓你修改顏色：", "Turtle.helpText3b": "畫一個黃色的星星。", "Turtle.helpText4a": "這個新區塊能讓你在移動時停筆：", "Turtle.helpText4b": "畫 1 個黃色小星星，然後在它上面畫 1 條直線。", "Turtle.helpText5": "你可不可以在正方形上畫出 4 個星星（而不只是一個）？", "Turtle.helpText6": "畫出 3 個黃色星星，和 1 條白色直線。", "Turtle.helpText7": "畫出星星，然後再畫 4 條白色直線。", "Turtle.helpText8": "畫出 360 條白色直線，讓它看起來像滿月一樣。", "Turtle.helpText9": "你能加入一個黑色的圓圈，讓月亮變成弦月嗎？", "Turtle.helpText10": "畫出任何您想畫的東西。有眾多的新區塊讓您探索。祝您玩得愉快！", "Turtle.helpText10Reddit": "使用 \"檢視繪畫庫\" 按鈕看看其他人所製作的作品。若您繪製了有趣的東西，使用 \"提交至繪畫庫\" 按鈕來發佈你的作品。", "Turtle.helpToolbox": "選擇一個類別，看看裡頭有哪些區塊。", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "起點 x", "Movie.y1": "起點 y", "Movie.x2": "終點 x", "Movie.y2": "終點 y", "Movie.radius": "半徑", "Movie.width": "寬度", "Movie.height": "高度", "Movie.circleTooltip": "在指定位置畫一個指定半徑的圓形。", "Movie.circleDraw": "圓形", "Movie.rectTooltip": "在指定位置畫一個指定長和寬的方形。", "Movie.rectDraw": "方形", "Movie.lineTooltip": "從一點到另一點畫一條指定粗細的線。", "Movie.lineDraw": "直線", "Movie.timeTooltip": "返回動畫中的當前時間(0-100)。", "Movie.colourTooltip": "變更筆跡顏色。", "Movie.setColour": "顏色設為", "Movie.submitDisabled": "您的影片沒有動。使用區塊來製作一些有趣的東西。之後您可以提交您的影片至圖庫。", "Movie.galleryTooltip": "開啟影片庫。", "Movie.galleryMsg": "檢視影片庫", "Movie.submitTooltip": "提交你的影片到影片庫。", "Movie.submitMsg": "提交到影片庫", "Movie.helpLayer": "移動背景圓圈到您的程式頂端。這將會出現在人物後方。", "Movie.helpText1": "使用簡單的圖形來畫出這個人物。", "Movie.helpText2a": "此關卡是一個影片，你需要讓人物的手臂在螢幕上移動，點擊播放按鈕可以預覽。", "Movie.helpText2b": "隨著電影在播放，time（時間）區塊會從 0 算到 100。因為你需要將手臂的「y」座標從 0 移動到 100，而這個區塊可以幫得上忙。", "Movie.helpText3": "time（時間）區塊計算是從 0 到 100。但現在您需要將其它手臂的「y」座標從 100 移動到 0。你可以想想看一個簡單的數學公式來翻轉方向嗎？", "Movie.helpText4": "利用你在之前關卡所學到來把腿給交叉。", "Movie.helpText5": "手臂的數學公式相當複雜。答案是：", "Movie.helpText6": "給人加上一雙手。", "Movie.helpText7": "使用「if」區塊在影片的前半段繪製顆小頭。然後在影片的後半段畫出一顆大頭。", "Movie.helpText8": "在影片中途讓腿轉向反方向。", "Movie.helpText9": "在人物身後畫一個漸漸變大的圓形。", "Movie.helpText10": "做出任何您想做的影片。有眾多的新區塊讓您探索。祝您玩得愉快！", "Movie.helpText10Reddit": "使用 \"檢視影片庫\" 按鈕看看其他人所製作的影片。若您製作了有趣的影片，使用 \"提交至影片庫\" 按鈕來發布您的影片。", "Music.playNoteTooltip": "演奏一個指定長度與音高的音符。", "Music.playNote": "演奏%1音符 %2", "Music.restTooltip": "等待指定音符長度", "Music.restWholeTooltip": "等待完整音符", "Music.rest": "%1休止", "Music.setInstrumentTooltip": "在演奏後續音符時切換成指定樂器。", "Music.setInstrument": "設定樂器為%1", "Music.startTooltip": "當「運行程式」按鍵點擊時，會執行區塊裡頭項目。", "Music.start": "當 %1 點擊時", "Music.pitchTooltip": "一個音符（C4用7表示）。", "Music.firstPart": "第一部份", "Music.piano": "鋼琴", "Music.trumpet": "小喇叭", "Music.banjo": "斑鳩琴", "Music.violin": "小提琴", "Music.guitar": "吉他", "Music.flute": "長笛", "Music.drum": "鼓", "Music.choir": "合唱", "Music.submitDisabled": "執行你的程式直到它停止。接著，可以把自己的音樂提交到樂廊。", "Music.galleryTooltip": "開啟樂廊。", "Music.galleryMsg": "檢視樂廊", "Music.submitTooltip": "把音樂提交到樂廊。", "Music.submitMsg": "提交至樂廊", "Music.helpUseFunctions": "您的成果可用，但是可以試著弄得更好。請使用功能來刪減重複的代碼內容。", "Music.helpUseInstruments": "若您在各起始區塊使用不同的樂器，會讓音樂聽起來更好。", "Music.helpText1": "作出《兩隻老虎》的前四粒音符。", "Music.helpText2a": "「功能」可讓您把區塊聚集起來，並且可多次執行。", "Music.helpText2b": "建立一個功能來演奏《兩隻老虎》的前四個音符。請執行此功能兩次，並且不要添加任何新的區塊。", "Music.helpText3": "建立內容是《兩隻老虎》下一段的第二個功能。最後的音符為長音。", "Music.helpText4": "建立內容是《兩隻老虎》下一段的第三個功能。前四個音符為短音。", "Music.helpText5": "作出《兩隻老虎》的完整旋律。", "Music.helpText6a": "此新的區塊讓您可更改成其它樂器。", "Music.helpText6b": "以小提琴演奏您的旋律。", "Music.helpText7a": "此新區塊添加了無聲的延遲。", "Music.helpText7b": "建立包含著兩個延遲區塊的區塊，然後會演奏《兩隻老虎》的第二個起始區塊。", "Music.helpText8": "任一起始區塊皆會演奏《兩隻老虎》兩次。", "Music.helpText9": "建立四個皆會演奏《兩隻老虎》兩次的起始區塊，並且請添加正確數量的延遲區塊。", "Music.helpText10": "作出任何您想聽的曲子。有眾多的新區塊讓您探索。祝您玩得愉快！", "Music.helpText10Reddit": "使用 \"檢視樂廊\" 按鈕看看其他人所製作的作品。若您編出了有趣的曲子，使用 \"提交至樂廊\" 按鈕來發佈你的樂曲。", "Pond.scanTooltip": "指定一個方向(0-360)掃描敵人位置，並返回該方向最近的敵人距離。當無法找到敵人時則返回無限大。", "Pond.cannonTooltip": "指定一個方向(0-360)和一個範圍(0-70)來發射大炮。", "Pond.swimTooltip": "指定一個方向(0-360)以向前游動。", "Pond.stopTooltip": "停止游動。角色將緩慢停止。", "Pond.healthTooltip": "返回角色的健康狀況（0 代表死亡，100 代表健康）。", "Pond.speedTooltip": "返回角色目前的速度（0 代表停止，100 代表全速）。", "Pond.locXTooltip": "返回角色的 x 座標位置（0 代表最左邊，100 代表最右邊）。", "Pond.locYTooltip": "返回角色的 y 座標位置（0 代表最下面，100 代表最上面）。", "Pond.logTooltip": "印出一個數字到您的瀏覽器控制台。", "Pond.docsTooltip": "顯示語言說明文件。", "Pond.documentation": "說明文件", "Pond.playerName": "角色", "Pond.targetName": "目標", "Pond.pendulumName": "鐘擺", "Pond.scaredName": "害怕", "Pond.helpUseScan": "你的解法有用，但你還可以做得更好。使用 scan（掃描）以告訴大炮要發射的距離。", "Pond.helpText1": "使用 cannon（大炮）指令來攻擊目標。第一個參數是角度，第二個參數是範圍。找出正確的組合值。", "Pond.helpText2": "此目標需要不斷的被攻擊。使用「while (true)」迴圈來無限地執行指令。", "Pond.helpText3a": "對手會動來動去的，很難攻擊到。scan（掃描）表達式返回對手在特定方向的具體範圍。", "Pond.helpText3b": "這個範圍就是 cannon 指令需要準確地發射精確火力的範圍。", "Pond.helpText4": "這個對手離得太遠了，距離超出大炮所能射到的範圍（限 70 公尺）。不過使用 swim（游動）指令可游向對手並且撞擊它。", "Pond.helpText5": "此對手距離超出大炮所能射到的範圍。而且以你的健康狀況無法撞擊對手。當您的水平位置小於 50 您可游往對手的方向。然後 stop（停止）以使用大炮。", "Pond.helpText6": "這個對手在被攻擊的時候會逃跑。若距離超過範圍 (70公尺)請游往它的方向。", "Gallery": "圖庫"}