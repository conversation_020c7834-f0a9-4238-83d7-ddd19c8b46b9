{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Espeox", "KK", "McSalama", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nike", "PStudios", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Samoasambia", "<PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON><PERSON>p<PERSON><PERSON>", "Games.puzzle": "<PERSON><PERSON><PERSON>", "Games.maze": "Sokkelo", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Elokuva", "Games.music": "<PERSON><PERSON><PERSON><PERSON>", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "<PERSON><PERSON>", "Games.linesOfCode1": "Ratkaisit tämän tason yhdellä rivillä JavaScriptiä:", "Games.linesOfCode2": "Ratkaisit tämän tason %1 rivillä JavaScriptiä:", "Games.nextLevel": "<PERSON><PERSON><PERSON> valmis tasoon %1?", "Games.finalLevel": "<PERSON><PERSON><PERSON> val<PERSON> se<PERSON> ha<PERSON>?", "Games.submitTitle": "<PERSON><PERSON><PERSON><PERSON>:", "Games.linkTooltip": "Tallenna ja linkitä lo<PERSON>.", "Games.runTooltip": "<PERSON><PERSON><PERSON>.", "Games.runProgram": "<PERSON><PERSON><PERSON>", "Games.resetTooltip": "<PERSON><PERSON><PERSON> oh<PERSON>lma ja nollaa taso.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON>", "Games.catLogic": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catText": "<PERSON><PERSON><PERSON>", "Games.catLists": "Listat", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "Pyyntö epäonnistui.", "Games.linkAlert": "Jaa lohkosi tällä linkillä:", "Games.hashError": "Valitettavasti '%1' ei vastaa mitään tallennettua ohje<PERSON>.", "Games.xmlError": "Tallennuksen lataaminen epäonnistui. Ehkä se on tehty Blocklyn eri versiolla?", "Games.submitted": "<PERSON><PERSON>s ohje<PERSON>! Jos meidän koulutetuista apinoista koostuva henkilöstömme pitää ohjelma<PERSON>, niin he julkaisevat sen galleriassa muutaman päivän sisällä.", "Games.listVariable": "lista", "Games.textVariable": "<PERSON><PERSON><PERSON>", "Games.breakLink": "Kun alat muokkaamaan JavaScriptiä, et voi enää palata muokkaamaan lohkoja. Haluatko jatkaa?", "Games.blocks": "Lohkot", "Games.congratulations": "Onnittelut!", "Games.helpAbort": "<PERSON><PERSON><PERSON><PERSON> taso on eritt<PERSON><PERSON> haastava. <PERSON><PERSON>t<PERSON> ohittaa sen ja siirtyä seuraavaan peliin? Voit palata takaisin my<PERSON>.", "Index.clear": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>ti poistaa kaikki rat<PERSON>t?", "Index.subTitle": "Pelejä tuleville ohjelmoijille.", "Index.moreInfo": "Lisätietoja opettajille...", "Index.startOver": "<PERSON><PERSON>tko aloittaa alusta?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON><PERSON>ot", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "Höyhenet", "Puzzle.animal1Trait2": "Nokka", "Puzzle.animal1HelpUrl": "https://fi.wikipedia.org/wiki/<PERSON><PERSON>", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "Viikset", "Puzzle.animal2Trait2": "Turk<PERSON>", "Puzzle.animal2HelpUrl": "https://fi.wikipedia.org/wiki/Kissa", "Puzzle.animal3": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://fi.wikipedia.org/wiki/Me<PERSON><PERSON><PERSON>t", "Puzzle.animal4": "Etana", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "Lima", "Puzzle.animal4HelpUrl": "https://fi.wikipedia.org/wiki/Kä<PERSON>rme", "Puzzle.picture": "kuva:", "Puzzle.legs": "jalkoja:", "Puzzle.legsChoose": "valitse...", "Puzzle.traits": "piirteet:", "Puzzle.error0": "Hienoa!\nKaikki %1 lohkoa ovat oikein.", "Puzzle.error1": "Lähes o<PERSON>in! Vain yksi lohko on väärin.", "Puzzle.error2": "%1 lohkoa ovat virheellisiä.", "Puzzle.tryAgain": "Korostettu lohko ei ole o<PERSON>in.\nYritä uudelleen.", "Puzzle.checkAnswers": "Tarkista vastaukset", "Puzzle.helpText": "<PERSON><PERSON><PERSON> (vihreä), liit<PERSON> o<PERSON> kuva, vali<PERSON>e jalkojen lukumäärä ja tee pino piirteistä.", "Maze.moveForward": "liiku eteenpä<PERSON>", "Maze.turnLeft": "k<PERSON><PERSON><PERSON>", "Maze.turnRight": "k<PERSON><PERSON><PERSON> o<PERSON>", "Maze.doCode": "tee", "Maze.helpIfElse": "If-else (joko-tai) -lohko tekee joko yhden tai toisen asian.", "Maze.pathAhead": "jos ed<PERSON><PERSON> on polku", "Maze.pathLeft": "jos polku kääntyy vasemmalle", "Maze.pathRight": "jos polku kääntyy o<PERSON>", "Maze.repeatUntil": "to<PERSON> kunnes", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> pelaajaa eteenpäin yksi askel.", "Maze.turnTooltip": "Kääntää pelaajaa vasemmalle tai oikealle 90 astetta.", "Maze.ifTooltip": "<PERSON><PERSON> on olemassa polku mä<PERSON><PERSON><PERSON><PERSON>, tee joitain to<PERSON>.", "Maze.ifelseTooltip": "Jos polku vie an<PERSON><PERSON> su<PERSON>, ensimmäisen osan palikat suoritetaan. Muuten suoritetaan toisen osan palikat.", "Maze.whileTooltip": "Toistaa sulkujen sisällä olevaa koodia siihen saakka, että sulkuja edeltävä ehto tä<PERSON>.", "Maze.capacity0": "Sinulla on %0 lohkoa jäljellä.", "Maze.capacity1": "Sinulla on %1 lohko jäljellä.", "Maze.capacity2": "Sinulla on %2 lohkoa jäljellä.", "Maze.runTooltip": "<PERSON>a pelaajan teke<PERSON><PERSON><PERSON><PERSON>, joita kood<PERSON> ku<PERSON>.", "Maze.resetTooltip": "Aseta pelaaja sokkelon alkuun.", "Maze.helpStack": "<PERSON><PERSON><PERSON> muutama \"liiku eteenpäin\" -<PERSON><PERSON><PERSON>, jotta pä<PERSON><PERSON>in ma<PERSON>in asti.", "Maze.helpOneTopBlock": "Tässä tehtävässä sinun pitää pinota kaikki lohkot päällekkäin valkoisella työalueella.", "Maze.helpRun": "<PERSON><PERSON><PERSON> oh<PERSON> n<PERSON>, mitä <PERSON>.", "Maze.helpReset": "Oh<PERSON>lma<PERSON> ei ratkaise sok<PERSON>. <PERSON><PERSON> \"Nolla<PERSON>\" ja yritä u<PERSON>.", "Maze.helpRepeat": "<PERSON>ita saavuttaa polun pää käyttäen vain kahta lohkoa. Käytä 'toista' lohkoa suorittaaksesi lohkoja useamman kerran.", "Maze.helpCapacity": "<PERSON><PERSON> k<PERSON>äny tämän tason kaikki käytettävissä olevat lohkot. Sinun pitää ensin poistaa olemassa oleva loh<PERSON>, ennen kuin voit luoda uuden lohkon.", "Maze.helpRepeatMany": "Voit laittaa useita lohkoja 'toista' lohkon si<PERSON>.", "Maze.helpSkins": "Valitse suosikkihahmosi tästä valikosta.", "Maze.helpIf": "'jos' lo<PERSON>ko toimii vain jos jokin ehto on tosi. Kokeile kääntyä vasemmalle kun polku kääntyy vasemmalle.", "Maze.helpMenu": "Napsauta %1 'jos' lohkossa vaihta<PERSON><PERSON> eh<PERSON>a.", "Maze.helpWallFollow": "Pystytkö ratkaisemaan tämän monimutkaisen sokkelon? Kokeile seurata vasenta seinää. Vain kokeneille ohjelmoijille.", "Bird.noWorm": "linnulla ei ole matoa", "Bird.heading": "<PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "<PERSON>ila jossa lintu ei ole saanut matoa.", "Bird.headingTooltip": "<PERSON><PERSON><PERSON> an<PERSON> kul<PERSON> su<PERSON>an: 0 tark<PERSON><PERSON><PERSON>alle, 90 suoraan yl<PERSON>s jne.", "Bird.positionTooltip": "x ja y ilmaisevat linnun sijainnin. Kun x = 0, lintu on vasemma<PERSON> laidassa; kun taas x = 100, lintu on oikeassa laidassa. Kun y = 0, lintu on alareunassa; kun taas y = 100, lintu on ylälaidassa.", "Bird.helpHeading": "<PERSON><PERSON><PERSON><PERSON> menokulmaa ni<PERSON>, että lintu saa madon ja laskeutuu pesäänsä.", "Bird.helpHasWorm": "Käytä tätä lohkoa siirtyäks<PERSON> tie<PERSON>yn kohtaan jos sinulla on mato, tai toiseen kohtaan jos sinulla ei ole matoa.", "Bird.helpX": "'x' on vaakasuora sijaintisi. Käytä tätä lohkoa siirtyäksesi sivuttain jos 'x' on pienempi kuin jokin numero tai käytä jotain toista lohkoa.", "Bird.helpElse": "Na<PERSON><PERSON><PERSON>alla kuvaketta voit muokata 'jos'-lo<PERSON><PERSON><PERSON>.", "Bird.helpElseIf": "Tässä tehtävässä tarvitaan sekä 'muuten tai' sekä 'muuten' lohko<PERSON>.", "Bird.helpAnd": "'ja' lohko on totta vain jos molemmat sen ehdot ovat totta.", "Bird.helpMutator": "<PERSON><PERSON><PERSON> 'muuten' loh<PERSON> 'jos' lohkon si<PERSON>n.", "Turtle.moveTooltip": "Liikuttaa kilpikonnaa eteenpäin tai taaksepäin antamasi luvun verran.", "Turtle.moveForward": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.moveBackward": "<PERSON><PERSON><PERSON>", "Turtle.turnTooltip": "Kääntää kilpikonnaa vasta- tai myötäpäivään tietyn verran astetta.", "Turtle.turnRight": "k<PERSON><PERSON><PERSON> o<PERSON>", "Turtle.turnLeft": "k<PERSON><PERSON><PERSON>", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON><PERSON> kyn<PERSON>n le<PERSON>.", "Turtle.setWidth": "as<PERSON><PERSON> leveyden arvoon", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON>a kynän väriä.", "Turtle.setColour": "aseta väriksi", "Turtle.penTooltip": "Nostaa tai laskee k<PERSON>n, jotta piirt<PERSON><PERSON>n loppuu tai alkaa.", "Turtle.penUp": "kyn<PERSON>", "Turtle.penDown": "kyn<PERSON> alas", "Turtle.turtleVisibilityTooltip": "Te<PERSON>e kilpikonnasta (ympyrä ja nuoli) näkyvän tai näkymättömän.", "Turtle.hideTurtle": "p<PERSON><PERSON>a kilpi<PERSON>na", "Turtle.showTurtle": "näytä kilpikonna", "Turtle.printHelpUrl": "https://fi.wikipedia.org/wiki/Painaminen", "Turtle.printTooltip": "Tuottaa tekstiä kilpikonnan osoittama<PERSON> suuntaan.", "Turtle.print": "tulosta", "Turtle.fontHelpUrl": "https://fi.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "Turtle.fontTooltip": "<PERSON><PERSON>a k<PERSON>ytettävän fontin tekstin n<PERSON>ä<PERSON>.", "Turtle.font": "k<PERSON><PERSON><PERSON>", "Turtle.fontSize": "Fonttikoko", "Turtle.fontNormal": "<PERSON><PERSON><PERSON>", "Turtle.fontBold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.fontItalic": "kursivoitu", "Turtle.submitDisabled": "<PERSON><PERSON> kunnes se päättyy. Sitten voit lähettää piirroksesi galleriaan.", "Turtle.galleryTooltip": "Avaa piirrosgalleria.", "Turtle.galleryMsg": "Näytä galleria", "Turtle.submitTooltip": "Lähetä p<PERSON>rroksesi galleriaan.", "Turtle.submitMsg": "Lähetä galleriaan", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>ii, mutta pys<PERSON><PERSON> par<PERSON>.", "Turtle.helpUseLoop3": "<PERSON><PERSON><PERSON><PERSON> kuvio vain kolmella lo<PERSON>.", "Turtle.helpUseLoop4": "Pi<PERSON><PERSON><PERSON> tähti vain neljällä lo<PERSON>.", "Turtle.helpText1": "<PERSON><PERSON>, joka pii<PERSON><PERSON><PERSON>.", "Turtle.helpText2": "<PERSON><PERSON> oh<PERSON>, että se piirtääkin viisikulmion nelikulmion asemesta.", "Turtle.helpText3a": "<PERSON><PERSON> k<PERSON><PERSON><PERSON> on u<PERSON>i <PERSON>, jonka avulla voit muuttaa väriä.", "Turtle.helpText3b": "<PERSON><PERSON><PERSON><PERSON> keltainen tähti.", "Turtle.helpText4a": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> on u<PERSON><PERSON>, jonka avulla voit nostaa kynän ylö<PERSON> paperista, kun liikut.", "Turtle.helpText4b": "Piirr<PERSON> pieni keltainen tähti ja sitten piirrä viiva sen yläpuolelle.", "Turtle.helpText5": "<PERSON>hden tähden sijaan voitko tällä kertaa piirtää neljä tähteä neliön muotoon?", "Turtle.helpText6": "Piirrä kolme keltaista tähteä ja yksi valkoinen viiva.", "Turtle.helpText7": "<PERSON><PERSON><PERSON><PERSON> tähdet ja sitten piirrä neljä valkoista viivaa.", "Turtle.helpText8": "360 valkoisen viivan piirtäminen saa aikaan täyden kuun.", "Turtle.helpText9": "<PERSON><PERSON><PERSON> kuusta sirpin muotoisen lisäämällä mustan ympyrän?", "Turtle.helpText10": "<PERSON><PERSON><PERSON><PERSON> mitä tahansa haluat. <PERSON><PERSON><PERSON> on suuri määrä uusia lohkoja tutustuttavana. <PERSON>dä hauskaa!", "Turtle.helpText10Reddit": "K<PERSON>yt<PERSON> \"Katso Galleria\" - painiketta nähdäksesi mitä muut ovat piirtäneet. <PERSON><PERSON> p<PERSON>ät jotain mielenkiintoista, k<PERSON>ytä \"Lähetä Galleriaan\" - painiketta julkaistaksesi sen.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON> luo<PERSON>, niin n<PERSON>et lo<PERSON>.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "alku-x", "Movie.y1": "alku-y", "Movie.x2": "loppu-x", "Movie.y2": "loppu-y", "Movie.radius": "säde", "Movie.width": "leveys", "Movie.height": "kor<PERSON><PERSON>", "Movie.circleTooltip": "Piirtää ympyrän määrite<PERSON>yn sijantiin annetulla s<PERSON>.", "Movie.circleDraw": "ympyrä", "Movie.rectTooltip": "Piirtää suorakulmion määritettyyn sijantiin annetulla korke<PERSON>lla ja levey<PERSON>.", "Movie.rectDraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.lineTooltip": "Piirtää viivan yhdestä pisteestä toiseen annetulla le<PERSON>.", "Movie.lineDraw": "viiva", "Movie.timeTooltip": "<PERSON>lauttaa anima<PERSON> nykyisen ajan (0–100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON><PERSON> kyn<PERSON>n v<PERSON>.", "Movie.setColour": "aseta väriksi", "Movie.submitDisabled": "Elokuva ei liiku. Käytä lohkoja tehdäksesi jotain mielenkiintoista. Sitten voit lähettää elokuvasi galleriaan.", "Movie.galleryTooltip": "Avaa elokuvagalleria.", "Movie.galleryMsg": "Näytä galleria", "Movie.submitTooltip": "Lähetä elokuvasi galleriaan.", "Movie.submitMsg": "Lähetä galleriaan", "Movie.helpLayer": "<PERSON><PERSON><PERSON><PERSON> taustalla oleva ympyrä oh<PERSON> yl<PERSON><PERSON>, niin se ilmestyy henkilön takana.", "Movie.helpText1": "Käytä yksinkertaisia muotoja tämän henkilön piirtämiseen.", "Movie.helpText2a": "Tämä tehtävä on elokuva. Siirrä henkilön käsi näytön poikki. Paina toista-painiketta esikatsellaksesi.", "Movie.helpText2b": "<PERSON><PERSON> elokuva on k<PERSON><PERSON><PERSON><PERSON>, aika etenee 'aika'-<PERSON><PERSON><PERSON><PERSON> nollasta sataan. <PERSON><PERSON> tark<PERSON> on saada käden 'y'-sija<PERSON>i alkamaan nollasta ja loppumaan sataan, pit<PERSON><PERSON> sen onnistua helposti.", "Movie.helpText3": "<PERSON><PERSON> juoksee 'time' <PERSON><PERSON><PERSON><PERSON> nollasta sataan mutta tällä kertaa punaisen pallon vaakasuora sijainti alkaa sadasta ja siirtyy nollaan. Osaatko ratkaista tämän yksinkertaisen matemaattisen yhtälön jossa käytät hyväksesi ajan arvoa.", "Movie.helpText4": "Käytä edellisessä tasossa oppimaasi luodakses<PERSON> jalat, jotka menevät ristiin.", "Movie.helpText5": "<PERSON><PERSON>den matemaattinen laskentakaava on monimutkainen. Tässä vastaus:", "Movie.helpText6": "<PERSON> kädet.", "Movie.helpText7": "<PERSON><PERSON><PERSON><PERSON> 'jos'-pali<PERSON>a piirtääkseski pienen pään elokuvan ensimmäiseen puoliskoon. Sitten piirrä iso pää elokuvan toista puoliskoa varten.", "Movie.helpText8": "<PERSON><PERSON> jalat vai<PERSON>maan suuntaa el<PERSON> puolivä<PERSON>ä.", "Movie.helpText9": "Piirrä laajentuva ympyrä henkilön taakse.", "Movie.helpText10": "Nyt voit tehdä minkälaisen elokuvan vain haluat. Pidä hauskaa!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON> Galle<PERSON>\" - painiketta nähdäksesi muiden tekemiä elokuvia. <PERSON><PERSON> teet mielenkiinto<PERSON>n elokuvan, <PERSON><PERSON><PERSON><PERSON> \"Lähetä Galleriaan\" - painiketta julkaistaksesi sen.", "Music.playNoteTooltip": "Soittaa yhden nuotin tietyllä kestolla ja sävel<PERSON>lla.", "Music.playNote": "Toista %1 nuotti %2", "Music.restTooltip": "Odottaa tietyn ajan.", "Music.restWholeTooltip": "Odottaa yhtä kokonaista nuottia.", "Music.rest": "lepää %1", "Music.setInstrumentTooltip": "<PERSON><PERSON><PERSON><PERSON> mä<PERSON><PERSON> soittimeen kun soitetaan useampia säveli<PERSON>.", "Music.setInstrument": "aseta instrumentiksi %1", "Music.startTooltip": "Su<PERSON>ttaa lohkot si<PERSON>, k<PERSON> '<PERSON><PERSON> oh<PERSON>' <PERSON><PERSON><PERSON>.", "Music.start": "kun %1 napsautetaan", "Music.pitchTooltip": "<PERSON><PERSON><PERSON> (C4 on 7).", "Music.firstPart": "ensimmäinen osa", "Music.piano": "piano", "Music.trumpet": "trumpet<PERSON>", "Music.banjo": "banjo", "Music.violin": "viulu", "Music.guitar": "kitara", "Music.flute": "huilu", "Music.drum": "rumpu", "Music.choir": "kuoro", "Music.submitDisabled": "<PERSON><PERSON> kunnes se päättyy. Sitten voit lähettää musiikkisi galleriaan.", "Music.galleryTooltip": "Avaa musiikkigalleria.", "Music.galleryMsg": "Näytä galleria", "Music.submitTooltip": "Lähetä musiikkisi galleriaan.", "Music.submitMsg": "Lähetä galleriaan", "Music.helpUseFunctions": "<PERSON><PERSON><PERSON><PERSON> toimii, mutta voit tehdä paremmin. Käytä toimintoja vähentääks<PERSON> toistetun koodin määrää.", "Music.helpUseInstruments": "Musiik<PERSON> ku<PERSON> par<PERSON>, jos käyt<PERSON> eri instrumenttiä jokaisessa aloituspalika<PERSON>.", "Music.helpText1": "Sävellä '<PERSON><PERSON><PERSON><PERSON> kulta'n ensimmäiset neljä nuottia", "Music.helpText2a": "\"Funktio\" mahdo<PERSON><PERSON>a sinun yhdistävän palikoita yhteen. Sitten voit suorittaa niitä useammin kuin kerran.", "Music.helpText2b": "<PERSON><PERSON>, joka soittaa '<PERSON><PERSON>':n neljä ensimmäistä säveltä. <PERSON><PERSON>ta funktio kaksi kertaa. Älä lisää uusia sävelpalikoita.", "Music.helpText3": "<PERSON><PERSON> toinen funktio <PERSON> k<PERSON> toista osiota varten. Viimeinen nuotti on pidempi.", "Music.helpText4": "<PERSON><PERSON> kolmas funktio '<PERSON><PERSON>':n se<PERSON><PERSON>an osaan. Ensimmäiset neljä säveltä ovat lyhyempiä.", "Music.helpText5": "Suorita täydellinen sävelmä 'Jaa<PERSON><PERSON> kult<PERSON>'lle", "Music.helpText6a": "Tämä uusi lohko antaa sinun vaihtaa toiseen soittimeen.", "Music.helpText6b": "<PERSON><PERSON> melodiasi viululla.", "Music.helpText7a": "Tämä uusi lohko lisää hiljaisen viiveen.", "Music.helpText7b": "<PERSON><PERSON> to<PERSON> al<PERSON>, jossa on kaksi vii<PERSON><PERSON><PERSON>, ja sitten soita '<PERSON><PERSON>'.", "Music.helpText8": "<PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON>kon pitäisi soittaa 'Jaa<PERSON><PERSON> kult<PERSON>' ka<PERSON>.", "Music.helpText9": "<PERSON><PERSON> ne<PERSON><PERSON><PERSON> al<PERSON>, jotka kukin soittavat '<PERSON>aa<PERSON><PERSON> kulta' kahdesti. Lisää oikeat numerot viivekoodeihin", "Music.helpText10": "Sävellä mitä tahansa haluat. <PERSON><PERSON><PERSON> on valtava määrä uusia paliko<PERSON>, joita voit tutkia. Pidä hauskaa!", "Music.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> \"Näytä galleria\" painiketta nähdäks<PERSON>, mitä muut ihmiset ovat sävelt<PERSON>t. Jos sävellät jotain mielenkiinto<PERSON>, k<PERSON>ytä \"Lähetä galleriaan\" painiketta julkaistaksesi sen.", "Pond.scanTooltip": "Etsi vihollisia. Määritä suunta (0-360). Palauttaa kyseisessä suunnassa lähimpänä olevan vihollisen etäisyyden. Palauttaa äärettömän a<PERSON>, jos vihollista ei löydy.", "Pond.cannonTooltip": "Ammu tykillä. Mä<PERSON><PERSON><PERSON> (0-360) ja kanta<PERSON> (0-70).", "Pond.swimTooltip": "Ui eteenpäin. Määrittele suunta (0-360).", "Pond.stopTooltip": "<PERSON><PERSON><PERSON>. Pelaaja hidastaa vauhtia ja pysähtyy.", "Pond.healthTooltip": "Palauttaa pelaajan terveyden (0 on kuollut, 100 terve)", "Pond.speedTooltip": "Palauttaa pelajaan nopeuden (0 tarkoittaa pysähtynyttä, 100 täyttä nopeutta).", "Pond.locXTooltip": "Palauttaa pelaajan X-koordinaatin (0 on vasen reuna, 100 on oikea reuna)", "Pond.locYTooltip": "Palauttaa pelaajan Y-koordinaatin (0 on alareuna, 100 on yläreuna)", "Pond.logTooltip": "<PERSON>los<PERSON>a luvun selaimesi kons<PERSON>in.", "Pond.docsTooltip": "Näytä kieliohje", "Pond.documentation": "<PERSON><PERSON><PERSON>", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "Ratkaisusi toimii mutta osaatko tehdä sen paremmin. Käytä 'scan' toimintoa jotta voit määrittää kuinka kauas tykin pitää ampua.", "Pond.helpText1": "<PERSON><PERSON><PERSON><PERSON> 'cannon' kome<PERSON>a o<PERSON> kohteeseen. Ensimmäinen parametri on kulma, toinen parametri on etäisyys. Koita löytää oikea yhdistelmä.", "Pond.helpText2": "<PERSON><PERSON><PERSON><PERSON><PERSON> maaliin pitä<PERSON> osua useita kertoja. Käytä 'while (true)' lohkoa  to<PERSON><PERSON><PERSON> jotain i<PERSON>.", "Pond.helpText3a": "Tämä vastustaja liikkuu edestakaisin ja on si<PERSON>en on siksi vaikea osua. 'scan' toiminto palauttaa tarkan etäisyyden vastustajaan määriteltyyn su<PERSON>an.", "Pond.helpText3b": "Tämä etäisyys on juuri mitä 'cannon' komento tarvitsee ampuakseen tarkasti.", "Pond.helpText4": "<PERSON><PERSON><PERSON><PERSON> vastustaja on liian kaukana jotta voisit käyttää kanuunaa (kanta<PERSON> on 70 metriä). <PERSON><PERSON><PERSON> siksi käyttämään 'swim' toimintoa uidaksesi vastustajaa kohti ja törmätäksesi siihen.", "Pond.helpText5": "<PERSON>ämä viholl<PERSON> on my<PERSON>s liian kaukana jotta kanuunaa voisi käyttää. <PERSON>t myös liian heikko selvi<PERSON>äksesi yhteentörmäyksestä. Ui vastustajaa kohti kunnes etäisyys on vähemmän kuin 50. <PERSON><PERSON><PERSON><PERSON><PERSON> sitter ja käytä kanuunaa.", "Pond.helpText6": "Tämä vihollinen siirtyy kauemmas kun saa osuman. Ui sitä kohti jos se on ampumasäteen ulko<PERSON>olella (70 metriä).", "Gallery": "Galleria"}