{"@metadata": {"authors": ["AliceJ", "BaRaN6161 TURK", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HakanIST", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ko<PERSON>lu", "Kumkumuk", "<PERSON><PERSON><PERSON>", "MuratTheTurkish", "<PERSON><PERSON><PERSON>", "Sucsuzz", "Trockya", "Velg"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Bulmaca", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "Kuş", "Games.turtle": "Ka<PERSON>lumbağa", "Games.movie": "Film", "Games.music": "Müzik", "Games.pondTutor": "Gölet Eğitimi", "Games.pond": "<PERSON><PERSON><PERSON>", "Games.linesOfCode1": "Bu seviyeyi 1 satır JavaScript ile çözdünüz:", "Games.linesOfCode2": "Bu düzeyi %1 JavaScript satırı ile çözdünüz:", "Games.nextLevel": "%1 seviyesine hazır mısınız?", "Games.finalLevel": "Bir sonraki mücadeleye hazır mısın?", "Games.submitTitle": "Başlık:", "Games.linkTooltip": "<PERSON><PERSON><PERSON> ve bloklara bağlayın.", "Games.runTooltip": "Yazdığınız programı çalıştırın.", "Games.runProgram": "Programı Çalıştır", "Games.resetTooltip": "Programı durdurun ve seviyeyi sıfırlayın.", "Games.resetProgram": "Sıfırla", "Games.help": "Yardım", "Games.catLogic": "Mantık", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Matematik", "Games.catText": "<PERSON><PERSON>", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "Renk", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "İşlevler", "Games.httpRequestError": "İstekle ilgili bir sorun <PERSON>.", "Games.linkAlert": "Bloklarınızı bu bağlantıyla paylaşın:\n\n%1", "Games.hashError": "Üzgünüz, '%1' kaydedilmiş bir programla uyuşmuyor.", "Games.xmlError": "Kay<PERSON>ilen dosya yüklenemedi. <PERSON><PERSON> de <PERSON>ly'nin farklı bir versiyonuyla oluşturuldu?", "Games.submitted": "Bu program için teşekkürler! Eğer eğitimli maymun personelimiz bunu beğendiyse, birka<PERSON> gün içinde galeriye yayınlayacaklar.", "Games.listVariable": "liste", "Games.textVariable": "metin", "Games.breakLink": "JavaScript'i düzenlemeye başladığınızda, düzen<PERSON>e bloklarına geri dönemezsiniz. Bu tamam mı?", "Games.blocks": "Bloklar", "Games.congratulations": "Tebrikler!", "Games.helpAbort": "<PERSON>u seviye son derece z<PERSON>. Atlamak ve bir sonraki oyuna geçmek ister misiniz? <PERSON>ha sonra her zaman geri gelebilirsin.", "Index.clear": "Tüm çözümleriniz silinsin mi?", "Index.subTitle": "Yarının programcıları için oyun<PERSON>.", "Index.moreInfo": "Eğitimciler için bilgi...", "Index.startOver": "Baş<PERSON> ba<PERSON><PERSON><PERSON> ister misiniz?", "Index.clearData": "V<PERSON><PERSON>i temizle", "Puzzle.animal1": "Ördek", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON>", "Puzzle.animal1HelpUrl": "https://tr.wikipedia.org/wiki/<PERSON>rdek", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "Bıyıklar", "Puzzle.animal2Trait2": "Kürk", "Puzzle.animal2HelpUrl": "https://tr.wikipedia.org/wiki/Kedi", "Puzzle.animal3": "Arı", "Puzzle.animal3Trait1": "<PERSON>l", "Puzzle.animal3Trait2": "İğne", "Puzzle.animal3HelpUrl": "https://tr.wikipedia.org/wiki/Arı", "Puzzle.animal4": "Salyangoz", "Puzzle.animal4Trait1": "Kabuk", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>de", "Puzzle.animal4HelpUrl": "https://tr.wikipedia.org/wiki/Salyangoz", "Puzzle.picture": "resim:", "Puzzle.legs": "bacaklar:", "Puzzle.legsChoose": "se<PERSON><PERSON> ya<PERSON>ın...", "Puzzle.traits": "özellikleri:", "Puzzle.error0": "Muhteşem!\n%1 bloğun hepsi de doğru.", "Puzzle.error1": "Neredeyse oluyordu! Bir blok yanlış.", "Puzzle.error2": "%1 blok yanlış.", "Puzzle.tryAgain": "Vurgulanan blok doğru değil.\nDenemeye devam et.", "Puzzle.checkAnswers": "Yanıtları Kontrol Et", "Puzzle.helpText": "Her hayvan için (<PERSON><PERSON><PERSON>) resm<PERSON>, baca<PERSON> sayı<PERSON>ını seçin ve özelliklerinin bir yığınını yapın.", "Maze.moveForward": "il<PERSON><PERSON>", "Maze.turnLeft": "sola dön", "Maze.turnRight": "<PERSON><PERSON><PERSON> d<PERSON>n", "Maze.doCode": "yap", "Maze.helpIfElse": "İf-else blokları bir ş<PERSON> ya<PERSON>.", "Maze.pathAhead": "e<PERSON>er yol ileride ise", "Maze.pathLeft": "sola giden yol", "Maze.pathRight": "sa<PERSON>a giden yol", "Maze.repeatUntil": "kadar tekrar edin", "Maze.moveForwardTooltip": "Oynatıcıyı bir boşluk ileri götürür.", "Maze.turnTooltip": "Oyuncuya 90 derece sola veya sağa çevirir.", "Maze.ifTooltip": "Belirtilen yönde bir yol varsa, bazı eylemler yapın.", "Maze.ifelseTooltip": "Belirtilen yönde bir yol var<PERSON>, ilk eylem bloğunu yapın. <PERSON><PERSON><PERSON>, i<PERSON><PERSON> eylem bloğunu yapın.", "Maze.whileTooltip": "Bitiş noktasına ulaşılana kadar ekteki işlemleri tekrarlayın.", "Maze.capacity0": "%0 bloğunuz kaldı.", "Maze.capacity1": "%1 bloğunuz kaldı.", "Maze.capacity2": "%2 bloğunuz kaldı.", "Maze.runTooltip": "Oyuncunun blokların söylediklerini yapmasını sağlar.", "Maze.resetTooltip": "Oynatıcıyı labirentin b<PERSON><PERSON>langı<PERSON>ına geri koyun.", "Maze.helpStack": "<PERSON><PERSON><PERSON>şmama yardımcı olmak için birkaç 'ileri' bloğunu bir araya getir.", "Maze.helpOneTopBlock": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, be<PERSON><PERSON>ma alanındaki tüm blokları bir araya getirmeniz gerekir.", "Maze.helpRun": "Ne olduğunu görmek için programınızı çalıştırın.", "Maze.helpReset": "<PERSON><PERSON><PERSON><PERSON>z lab<PERSON>. 'Sıfırla'ya basın ve tekrar deneyin.", "Maze.helpRepeat": "Yalnızca iki blok kullanarak bu yolun sonuna ulaşın. Bir bloğu bir kereden fazla çalıştırmak için 'tekrarla'yı kullanın.", "Maze.helpCapacity": "Bu seviye için tüm blokları kullandınız. Yeni bir blok oluşturmak için önce mevcut bir bloğu silmeniz gerekir.", "Maze.helpRepeatMany": "'tekrar' bloğuna birden fazla blok takabilirsiniz.", "Maze.helpSkins": "Bu menüden favori oynatıcınızı seçin.", "Maze.helpIf": "'if' b<PERSON><PERSON><PERSON> yalnızca koşul doğruysa bir şeyler yapar. Sola giden bir yol varsa sola dönmeyi deneyin.", "Maze.helpMenu": "Durumunu değiştirmek için 'if' bloğunda %1 tıklayın.", "Maze.helpWallFollow": "Bu karmaşık labirenti çözebilir misin? Sol duvarı takip etmeyi deneyin. Yalnızca gelişmiş programcılar!", "Bird.noWorm": "solucanı yok", "Bird.heading": "başlığı", "Bird.noWormTooltip": "Kuş solucanı almadığı durum.", "Bird.headingTooltip": "Verilen açının yönünde hareket edin: 0 sağa, 90 düz yukarı vb.", "Bird.positionTooltip": "x ve y, kuşun konumunu işaretler. X = 0 olduğunda kuş sol kenara yakındır, x = 100 olduğunda sağ kenara yakındır. Y = 0 olduğunda kuş en altta, y = 100 olduğunda en üsttedir.", "Bird.helpHeading": "Kuşun solucanını ve yuvasına inmesini sağlamak için yön açısını değiştirin.", "Bird.helpHasWorm": "Solucanınız varsa bir başlığa veya solucanınız yoksa farklı bir başlığa gitmek için bu bloğu kullanın.", "Bird.helpX": "'x' ge<PERSON><PERSON><PERSON> yatay k<PERSON>umunuzdur. 'x' say<PERSON><PERSON> kü<PERSON><PERSON> bir başl<PERSON>kta, aksi takdirde farklı bir başlıkta gitmek için bu bloğu kullanın.", "Bird.helpElse": "'if' b<PERSON><PERSON><PERSON><PERSON>tirmek için simgeyi tıklayın.", "Bird.helpElseIf": "Bu seviye hem 'else if' hem de 'else' b<PERSON><PERSON><PERSON> ihtiyaç duyar.", "Bird.helpAnd": "'and' b<PERSON><PERSON><PERSON> ya<PERSON>ca her iki girişi de doğru<PERSON>ğrud<PERSON>.", "Bird.helpMutator": "'else' bloğunu 'if' b<PERSON><PERSON><PERSON> sürükley<PERSON>.", "Turtle.moveTooltip": "Kaplumbağayı belirtilen miktarda ileri veya geri hareket ettirir.", "Turtle.moveForward": "<PERSON>u kadar ileri git", "Turtle.moveBackward": "<PERSON>u kadar geri git", "Turtle.turnTooltip": "Kaplumbağayı belirtilen derece kadar sola veya sağa çevirir.", "Turtle.turnRight": "<PERSON><PERSON> kadar sa<PERSON>a dön", "Turtle.turnLeft": "<PERSON>u kadar sola dön", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON>.", "Turtle.setWidth": "genişliği a<PERSON>", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON>.", "Turtle.setColour": "re<PERSON>i a<PERSON>", "Turtle.penTooltip": "Çizimi durdurmak veya başlatmak için kalemi kaldırır veya indirir.", "Turtle.penUp": "ka<PERSON>i kaldır", "Turtle.penDown": "kalemi indir", "Turtle.turtleVisibilityTooltip": "Kaplumbağayı (daire ve ok) görünür veya görünmez yapar.", "Turtle.hideTurtle": "kaplumbağayı gizle", "Turtle.showTurtle": "kaplumbağayı göster", "Turtle.printHelpUrl": "https://tr.wikipedia.org/wiki/Matbaacılık", "Turtle.printTooltip": "<PERSON><PERSON>, ka<PERSON>lumbağa yönünde bulunduğu yere çizer.", "Turtle.print": "yazdır", "Turtle.fontHelpUrl": "https://tr.wikipedia.org/wiki/Yaz<PERSON>_tipi", "Turtle.fontTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloğu tarafından kullanılan yazı tipini ayarlar.", "Turtle.font": "yazı tipi", "Turtle.fontSize": "yazı tipi boyutu", "Turtle.fontNormal": "normal", "Turtle.fontBold": "kalın", "Turtle.fontItalic": "<PERSON><PERSON><PERSON>", "Turtle.submitDisabled": "Programınızı durana kadar çalıştırın. Ardından çiziminizi galeriye gönderebilirsiniz.", "Turtle.galleryTooltip": "Çizimler galerisini a<PERSON>ı<PERSON>.", "Turtle.galleryMsg": "Galeriye Bakın", "Turtle.submitTooltip": "Çiziminizi galeriye gönderin.", "Turtle.submitMsg": "<PERSON><PERSON><PERSON>", "Turtle.helpUseLoop": "Çözümünüz işe yarıyor, ancak daha iyisini yapabilirsiniz.", "Turtle.helpUseLoop3": "Şekli yalnızca üç blokla çizin.", "Turtle.helpUseLoop4": "Yıldızı yalnızca dört blokla çizin.", "Turtle.helpText1": "<PERSON>re çizen bir program oluşturun.", "Turtle.helpText2": "<PERSON><PERSON> yerine beşgen çizmek için programınızı değiştirin.", "Turtle.helpText3a": "<PERSON><PERSON>ştirmenize izin veren yeni bir blok var:", "Turtle.helpText3b": "Sarı bir yıldız ç<PERSON>in.", "Turtle.helpText4a": "Hareket ettiğinizde kaleminizi kağıttan kaldırmanıza izin veren yeni bir blok var:", "Turtle.helpText4b": "Küçük bir sarı yıldız çizin, ardından üzerine bir çizgi çizin.", "Turtle.helpText5": "<PERSON>ir yıldız yerine, bir karede düzenlenmiş dört yıldız çizebilir misiniz?", "Turtle.helpText6": "Üç sarı yıldız ve bir beyaz çizgi çizin.", "Turtle.helpText7": "Yıld<PERSON><PERSON><PERSON><PERSON> çizin, ardından dört beyaz çizgi çizin.", "Turtle.helpText8": "360 beyaz çizgi çizmek dolunay gibi görünecektir.", "Turtle.helpText9": "<PERSON><PERSON><PERSON><PERSON> hilal haline gelmesi için siyah bir daire ekleyeb<PERSON>r misiniz?", "Turtle.helpText10": "İstediğiniz her şeyi çizin. Keşfedebileceğiniz çok sayıda yeni blok var. İyi eğlenceler!", "Turtle.helpText10Reddit": "Başkalarının çizdiklerini görmek için 'Galeriye Bakın' düğmesini kullanın. İlginç bir şey çizerseniz, yayınlamak için 'Galeriye Gönder' düğmesini kullanın.", "Turtle.helpToolbox": "Blokları görmek için bir kategori seç.Blokları görmek için bir kategori seçin.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "ba<PERSON><PERSON><PERSON><PERSON> x", "Movie.y1": "b<PERSON><PERSON><PERSON><PERSON><PERSON> y", "Movie.x2": "bitiş x", "Movie.y2": "bitiş y", "Movie.radius": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.width": "genişlik", "Movie.height": "yükseklik", "Movie.circleTooltip": "Belirtilen yere ve belirtilen yarıçapa sahip bir daire çizer.", "Movie.circleDraw": "daire", "Movie.rectTooltip": "Belirtilen konuma ve belirtilen genişlik ve yüksekliğe sahip bir dikdörtgen çizer.", "Movie.rectDraw": "dikdörtgen", "Movie.lineTooltip": "Belirtilen genişlikte bir noktadan diğerine bir çizgi ç<PERSON>.", "Movie.lineDraw": "<PERSON>ır", "Movie.timeTooltip": "Animasyondaki geçerli saati dö<PERSON>ü<PERSON>ü<PERSON> (0-100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON>.", "Movie.setColour": "rengi o<PERSON>ak a<PERSON>", "Movie.submitDisabled": "Filminiz hareket etmiyor. İlginç bir şey yapmak için blokları kullanın. Ardından filminizi galeriye gönderebilirsiniz.", "Movie.galleryTooltip": "Film galerisini açın.", "Movie.galleryMsg": "Galeriye Bakın", "Movie.submitTooltip": "Filminizi galeriye gönderin.", "Movie.submitMsg": "<PERSON><PERSON><PERSON>", "Movie.helpLayer": "Arka plan dairesini programınızın üstüne taşıyın. Sonra kişinin arkasında görünecektir.", "Movie.helpText1": "Bu kişiyi çizmek için basit şekiller kullanın.", "Movie.helpText2a": "Bu seviye bir film. Kişinin kolunun ekran boyunca hareket etmesini istiyorsunuz. Bir önizleme görmek için oynat düğ<PERSON> basın.", "Movie.helpText2b": "Film oynatılırk<PERSON>, 'zaman' bloğunun değeri 0'dan 100'e kadar sayar. Kolun 'y' konumunun 0'dan başlamasını ve 100'e gitmesini istediğiniz için bu kolay olmalıdır.", "Movie.helpText3": "'Zaman' bloğu 0'dan 100'e kadar say<PERSON>. <PERSON>a şimdi diğer kolun 'y' pozisyonunun 100'de başlamasını ve 0'a gitmesini istiyorsunuz. Yönü çeviren basit bir matematiksel formül bulabilir misiniz?", "Movie.helpText4": "Bacakları çapraz yapmak için önceki seviyede öğrendiklerini kullanın.", "Movie.helpText5": "<PERSON><PERSON> için matematiksel formül karmaşıktır. İşte cevap:", "Movie.helpText6": "Kişiye birkaç el verin.", "Movie.helpText7": "Filmin ilk yarısına küçük bir kafa çizmek için 'if' bloğunu kullanın. Ardından filmin ikinci yarısı için büyük bir kafa çizin.", "Movie.helpText8": "Bacakları filmin ortasında ters yönde hareket ettirin.", "Movie.helpText9": "Kişinin arkasına genişleyen bir daire çizin.", "Movie.helpText10": "İstediğiniz her şeyin bir filmini yapın. Keşfedebileceğiniz çok sayıda yeni blok var. İyi eğlenceler!", "Movie.helpText10Reddit": "<PERSON>ğer kişilerin çektiği filmleri görmek için 'Galeriye Bakın' düğmesini kullanın. İlginç bir film yaparsanız, yayınlamak için 'Galeriye Gönder' düğmesini kullanın.", "Music.playNoteTooltip": "Belirtilen süre ve adımda bir nota çalar.", "Music.playNote": "%1 sürede %2 oynat", "Music.restTooltip": "<PERSON><PERSON><PERSON><PERSON> süre kadar bekler.", "Music.restWholeTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> bir notu bekler.", "Music.rest": "kalan süre: %1", "Music.setInstrumentTooltip": "Sonraki müzik notalarını çalarken belirtilen enstrümana geçer.", "Music.setInstrument": "enstrümanı %1 olarak ayarla", "Music.startTooltip": "'Programı Çalıştır' d<PERSON><PERSON><PERSON><PERSON> tıklandığında içerideki blokları yürütür.", "Music.start": "%1 simgesine tıkladığınızda", "Music.pitchTooltip": "Bir not (C4 7'dir).", "Music.firstPart": "ilk kısım", "Music.piano": "piyano", "Music.trumpet": "trompet", "Music.banjo": "banço", "Music.violin": "keman", "Music.guitar": "gitar", "Music.flute": "<PERSON><PERSON><PERSON>", "Music.drum": "davul", "Music.choir": "koro", "Music.submitDisabled": "Programınızı durana kadar çalıştırın. Ardından müziğinizi galeriye gönderebilirsiniz.", "Music.galleryTooltip": "Müzik galerisini açın.", "Music.galleryMsg": "Galeriyi Bakın", "Music.submitTooltip": "Müziğinizi galeriye gönderin.", "Music.submitMsg": "<PERSON><PERSON><PERSON>", "Music.helpUseFunctions": "Çözümünüz işe yarıyor, ancak daha iyisini yapabilirsiniz. Tekrarlanan kod miktarını azaltmak için fonksiyonları kullanın.", "Music.helpUseInstruments": "Her baş<PERSON><PERSON>ç bloğunda farklı bir enstrüman kullanırsanız müzik daha iyi ses çıkarır.", "Music.helpText1": "'<PERSON><PERSON>'in ilk dört notunu yazın.", "Music.helpText2a": "Bir 'function' blokları bir arada gruplandırmanıza ve sonra birden fazla çalıştırmanıza izin verir.", "Music.helpText2b": "'<PERSON><PERSON> Jacques'in ilk dört notasını çalmak için bir işlev oluşturun. Bu işlevi iki kez çalıştırın. Yeni not blokları eklemeyin.", "Music.helpText3": "'<PERSON><PERSON>'in son<PERSON>i kısmı için ikinci bir fonksiyon oluşturun. Son not daha uzundur.", "Music.helpText4": "'<PERSON><PERSON> Jacques'in sonraki kısmı için üçüncü bir fonksiyon oluşturun. İlk dört not daha kısadır.", "Music.helpText5": "'<PERSON><PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tamamını tamamlayın.", "Music.helpText6a": "<PERSON>u yeni blok, başka bir enstrümana geçmenizi sağlar.", "Music.helpText6b": "Melodinizi bir keman ile çalın.", "Music.helpText7a": "Bu yeni blok sessiz bir gecikme ekler.", "Music.helpText7b": "<PERSON><PERSON> gecikme bloğuna sahip ikinci bir ba<PERSON><PERSON><PERSON><PERSON> blo<PERSON><PERSON>, ard<PERSON><PERSON><PERSON> '<PERSON><PERSON>.", "Music.helpText8": "Her ba<PERSON><PERSON><PERSON>ç bloğunda iki kez 'Frère Jacques' <PERSON>ıdır.", "Music.helpText9": "Her biri iki kez '<PERSON><PERSON>' <PERSON>yan dört ba<PERSON><PERSON><PERSON><PERSON> bloğu oluşturun. <PERSON><PERSON><PERSON> sayı<PERSON> gecikme bloğ<PERSON>.", "Music.helpText10": "İstediğiniz her şeyi oluşturun. Keşfedebileceğiniz çok sayıda yeni blok var. İyi eğlenceler!", "Music.helpText10Reddit": "Başkalarının yazdıklarını görmek için 'Galeriye Bakın' düğmesini kullanın. İlginç bir şey oluşturursanız, yayınlamak için 'Galeriye Gönder' düğmesini kullanın.", "Pond.scanTooltip": "Düşmanları tarayın. <PERSON><PERSON>ö<PERSON> be<PERSON> (0-360). Bu yöndeki en yakın düşmana olan mesafeyi verir. <PERSON>ç düşman bulunmazsa Sonsuzluğu döndürür.", "Pond.cannonTooltip": "Topu ateşle. <PERSON><PERSON> (0-360) ve bir a<PERSON><PERSON> (0-70) belirleyin.", "Pond.swimTooltip": "İleriye doğru yüzün. <PERSON><PERSON><PERSON><PERSON> be<PERSON> (0-360).", "Pond.stopTooltip": "Yüzmeyi bırak. Oyuncu durur.", "Pond.healthTooltip": "Oyuncunun mevcut sağlığını döndürür (0 ölü, 100 sağlıklı).", "Pond.speedTooltip": "Oynatıcının geçerli hızını döndürür (0 durdurulur, 100 tam hızdır).", "Pond.locXTooltip": "Oynatıcının X koordinatını döndürür (0 sol kenar, 100 sağ kenardır).", "Pond.locYTooltip": "Oynatıcının Y koordinatını döndürür (0 alt kenar, 100 üst kenardır).", "Pond.logTooltip": "Tarayıcınızın konsoluna bir numara yazdırır.", "Pond.docsTooltip": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> gö<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Pond.documentation": "Belgelendirme", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "PendulumSarkaç", "Pond.scaredName": "Korkmuş", "Pond.helpUseScan": "Çözümünüz işe yarıyor, ancak daha iyisini yapabilirsiniz. Topa ne kadar uzak ateş edileceğini söylemek için 'scan'ı kullanın.", "Pond.helpText1": "<PERSON><PERSON><PERSON> v<PERSON> için 'cannon' komutunu kullanın. İlk parametre açı, ikinci parametre aralıktır. Doğru kombinasyonu bul.", "Pond.helpText2": "Bu hedefe birçok kez vurulması gerekiyor. Süresiz bir şey yapmak için 'while (true)' d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kullanın.", "Pond.helpText3a": "<PERSON>u rakip ileri geri hareket ederek vurmayı zorlaştırıyor. 'scan' <PERSON><PERSON><PERSON>, rakibe tam olarak belirtilen yönde döndürür.", "Pond.helpText3b": "<PERSON>u menzil 'cannon' komutunun tam olarak ateş etmesi için gereken şeydir.", "Pond.helpText4": "Bu rakip topu kullanmak için çok uzak (70 metre sınırı var). <PERSON><PERSON><PERSON> yer<PERSON>, raki<PERSON> doğru yüzmeye başlamak ve ona çarpmak için 'swim' komutunu kullanın.", "Pond.helpText5": "Bu rakip aynı zamanda topu kullanamayacak kadar uzakta. Ancak bir çarpışmadan kurtulmak için çok zayıfsın. Yatay konumunuz 50'den azken rakibe doğru yüzün. Sonra 'durun' ve topu kullanın.", "Pond.helpText6": "<PERSON><PERSON> raki<PERSON> v<PERSON>u<PERSON>unda uzaklaşacak. <PERSON><PERSON>l dışındaysa (70 metre) yüzün.", "Gallery": "<PERSON><PERSON>"}