{"@metadata": {"authors": ["Athena in Wonderland", "Diniscoelho", "<PERSON><PERSON><PERSON>", "<PERSON>", "Imperadeiro98", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "MokaAkashiyaPT", "<PERSON>", "Vitorvicentevalente", "Waldir", "<PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Jo<PERSON>", "Games.puzzle": "Quebra-cabeça", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON><PERSON>", "Games.turtle": "Tartaruga", "Games.movie": "Filme", "Games.music": "Música", "Games.pondTutor": "<PERSON><PERSON> de lib<PERSON>", "Games.pond": "Lagoa", "Games.linesOfCode1": "Resolveste este nivel com 1 linha de JavaScript:", "Games.linesOfCode2": "Resolveste este nivel com %1 linhas de JavaScript:", "Games.nextLevel": "Estás pronto para o nível %1?", "Games.finalLevel": "Estás pronto para o próximo desafio?", "Games.submitTitle": "Título:", "Games.linkTooltip": "Salva conexão com o blockly.", "Games.runTooltip": "Executar o programa que escreveste.", "Games.runProgram": "Executar o programa", "Games.resetTooltip": "Parar o programa e reiniciar o nível.", "Games.resetProgram": "Reiniciar", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Lógica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matemática", "Games.catText": "Texto", "Games.catLists": "Listas", "Games.catColour": "Cor", "Games.catVariables": "Variáveis", "Games.catProcedures": "Funções", "Games.httpRequestError": "Houve um problema com a solicitação.", "Games.linkAlert": "Compartilhe os seus blocos com este link:\n\n%1", "Games.hashError": "<PERSON><PERSON>l<PERSON>, '%1' não corresponde a um blockly salvo.", "Games.xmlError": "Não foi possível carregar o seu ficheiro gravado. Talvez ele tenha sido criado por uma versão diferente do Blockly?", "Games.submitted": "Agradecemos pelo programa! Se a nossa equipa gostar dele, este será publicado na galeria por nós dentro de alguns dias.", "Games.listVariable": "lista", "Games.textVariable": "texto", "Games.breakLink": "Assim que começar a editar JavaScript, não pode voltar a editar blocos. Clique em OK para continuar.", "Games.blocks": "Blocos", "Games.congratulations": "Parabéns!", "Games.helpAbort": "Este nível é extremamente difícil. Deseja ignorá-lo e ir para o próximo jogo? Sempre pode voltar mais tarde.", "Index.clear": "Eliminar todas as suas soluç<PERSON>?", "Index.subTitle": "Jogos para os programadores de amanhã.", "Index.moreInfo": "Informações para educadores...", "Index.startOver": "<PERSON>eja começar de novo?", "Index.clearData": "Apagar dados", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "Penas", "Puzzle.animal1Trait2": "Bico", "Puzzle.animal1HelpUrl": "https://pt.wikipedia.org/wiki/Pato", "Puzzle.animal2": "Gato", "Puzzle.animal2Trait1": "Bigodes", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://pt.wikipedia.org/wiki/Gato", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://pt.wikipedia.org/wiki/Anthophila", "Puzzle.animal4": "Caracol", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "Baba", "Puzzle.animal4HelpUrl": "https://pt.wikipedia.org/wiki/Caracol", "Puzzle.picture": "imagem:", "Puzzle.legs": "patas:", "Puzzle.legsChoose": "escolha...", "Puzzle.traits": "características:", "Puzzle.error0": "Perfeito!\nTodos os %1 blocos estão corretos.", "Puzzle.error1": "Quase! Um bloco está incorreto.", "Puzzle.error2": "%1 blocos estão incorretos.", "Puzzle.tryAgain": "O bloco realçado não está correto.\nContinue tentando.", "Puzzle.checkAnswers": "Verificar respostas", "Puzzle.helpText": "Para cada animal (verde), anexar a sua imagem, escolher o seu número de pernas, e fazer uma lista das suas características.", "Maze.moveForward": "Mover para a frente", "Maze.turnLeft": "gire para a esquerda", "Maze.turnRight": "gire para a direita", "Maze.doCode": "faça", "Maze.helpIfElse": "O comando 'Se-senão' faz uma coisa ou outra.", "Maze.pathAhead": "se existe um caminho em frente", "Maze.pathLeft": "se existe um caminho para a esquerda", "Maze.pathRight": "se existe um caminho para a direita", "Maze.repeatUntil": "repetir até", "Maze.moveForwardTooltip": "Move o Pegman um quadro para frente.", "Maze.turnTooltip": "G<PERSON> o Pegman 90 graus para esquerda ou para a direita.", "Maze.ifTooltip": "Se existe um caminho na direção especificada, entao executa algumas ações.", "Maze.ifelseTooltip": "Se não existe um caminho na direção especificada, então execute o primeiro bloco de ações. <PERSON><PERSON>, execute o segundo bloco de ações.", "Maze.whileTooltip": "<PERSON><PERSON> as a<PERSON><PERSON><PERSON> seguintes até que o ponto final seja alcançado.", "Maze.capacity0": "Você tem mais %0 blocos.", "Maze.capacity1": "Resta-lhe %1 bloco.", "Maze.capacity2": "Você tem mais %2 blocos.", "Maze.runTooltip": "Faça o jogador executar o que os blocos dizem.", "Maze.resetTooltip": "Colocar o jogador novamente no início do labirinto.", "Maze.helpStack": "<PERSON><PERSON><PERSON><PERSON> alguns blocos 'Mover para a frente' para ajudar-me a chegar ao destino.", "Maze.helpOneTopBlock": "<PERSON><PERSON> nível, você precisa empilhar todos os blocos nos espaços em branco.", "Maze.helpRun": "Execute o seu programa para ver o que acontece.", "Maze.helpReset": "O programa não resolve o labirinto. Pressione 'Reiniciar' e tente novamente.", "Maze.helpRepeat": "Os computadores têm uma memoria limitada. Chegue ao destino usando apenas dois blocos. Utilize o comando 'repita' para executar um bloco várias vezes.", "Maze.helpCapacity": "<PERSON>ou todos os blocos para este nível. Para criar um novo bloco, deve primeiro eliminar um bloco existente.", "Maze.helpRepeatMany": "Pode pôr mais de um bloco dentro de um bloco 'repetir'.", "Maze.helpSkins": "Escolha o seu jogador favorito a partir deste menu.", "Maze.helpIf": "A condição 'se' executa algo apenas se a condição for verdadeira. Tente girar à esquerda se existe um caminho à esquerda", "Maze.helpMenu": "Clique em %1 no bloco 'se' para mudar a sua condição.", "Maze.helpWallFollow": "Você consegue resolver este quebra cabeças complexo. Tente seguir o muro do lado es<PERSON>do. Para programadores avançados apenas!", "Bird.noWorm": "não tem minhoca", "Bird.heading": "direção", "Bird.noWormTooltip": "Esse bloco indica quando o pássaro não possui a minhoca.", "Bird.headingTooltip": "Mover na direção do ângulo indicado: 0 é para a direita, 90 é para frente, etc.", "Bird.positionTooltip": "x e y marcam a posição do pássaro. Quando x = 0 o pássaro esta próximo da margem esquerda, quando x = 100 esta perto da margem direita. Quando y = 0 o pássaro esta em baixo, quando y = 100 ele está no topo.", "Bird.helpHeading": "Trocar o ângulo da direção para que o pássaro apanhe a minhoca e chegue ao seu ninho.", "Bird.helpHasWorm": "Use este bloco para ir numa direção se tiver a minhoca, ou noutra se não tiver a minhoca.", "Bird.helpX": "'x' e sua posição horizontal atual. Use este bloco para ir numa direção se 'x' for menor que um numero, ou noutra se não for.", "Bird.helpElse": "Clique no ícone para modificar o bloco 'se'.", "Bird.helpElseIf": "Este nível precisa de um bloco 'senão se' e de um bloco 'senão'.", "Bird.helpAnd": "O bloco \"e\" somente é verdadeiro se as duas entradas forem verdadeiras.", "Bird.helpMutator": "Arraste um bloco \"senão\" em cima do bloco \"se\".", "Turtle.moveTooltip": "Move a tartaruga para frente ou para trás de acordo com a quantidade especificada.", "Turtle.moveForward": "Mover para a frente", "Turtle.moveBackward": "mover para trás", "Turtle.turnTooltip": "Gira a tartaruga para esquerda ou direita de acordo com o número de graus especificado.", "Turtle.turnRight": "girar para a direita", "Turtle.turnLeft": "girar para a esquerda", "Turtle.widthTooltip": "Altera a largura da caneta.", "Turtle.setWidth": "definir largura para", "Turtle.colourTooltip": "<PERSON>da a cor da caneta.", "Turtle.setColour": "definir cor para", "Turtle.penTooltip": "Levanta ou baixa a caneta, para parar ou voltar a desenhar.", "Turtle.penUp": "le<PERSON><PERSON> caneta", "Turtle.penDown": "baixar cane<PERSON>", "Turtle.turtleVisibilityTooltip": "Faz a tartaruga (círculo e a seta) visível ou invisível.", "Turtle.hideTurtle": "ocultar tartaruga", "Turtle.showTurtle": "mostrar tartaruga", "Turtle.printHelpUrl": "https://pt.wikipedia.org/wiki/Impress%C3%A3o", "Turtle.printTooltip": "Desenha texto na direção da tartaruga na sua posição atual.", "Turtle.print": "imprimir", "Turtle.fontHelpUrl": "https://pt.wikipedia.org/wiki/Fonte_tipogr%C3%A1fica", "Turtle.fontTooltip": "Define o tipo de letra utilizado pela impressão de bloco.", "Turtle.font": "fonte", "Turtle.fontSize": "ta<PERSON><PERSON> da fonte", "Turtle.fontNormal": "normal", "Turtle.fontBold": "negrito", "Turtle.fontItalic": "itálico", "Turtle.submitDisabled": "Executa o programa até parar. De seguida, pode enviar o seu desenho para a galeria.", "Turtle.galleryTooltip": "Abra a galeria de desenhos.", "Turtle.galleryMsg": "Ver a Galeria", "Turtle.submitTooltip": "Envie o seu desenho para a galeria.", "Turtle.submitMsg": "Enviar para a Galeria", "Turtle.helpUseLoop": "A sua solução funciona, mas pode fazer melhor.", "Turtle.helpUseLoop3": "Desenhe a forma com apenas três blocos.", "Turtle.helpUseLoop4": "Desenhe a estrela com apenas quatro blocos.", "Turtle.helpText1": "Crie um programa que desenha um quadrado.", "Turtle.helpText2": "Modifique o seu programa para que desenhe um pentágono em vez de um quadrado.", "Turtle.helpText3a": "Existe um bloco novo que permite alterar a cor:", "Turtle.helpText3b": "Desenhe uma estrela amarela.", "Turtle.helpText4a": "Há um bloco novo que lhe permite levantar a caneta quando você move o cursor:", "Turtle.helpText4b": "Desenhe uma pequena estrela amarela, depois desenhe uma linha em cima dela.", "Turtle.helpText5": "Em vez de uma estrela, consegue desenhar quatro estrelas dispostas em quadrado?", "Turtle.helpText6": "Desenhe três estrelas amarelas e uma linha em branco.", "Turtle.helpText7": "<PERSON><PERSON>he as estrelas, depois desenhe quatro linhas brancas.", "Turtle.helpText8": "Desenhar 360 linhas brancas vai parecerer uma lua cheia.", "Turtle.helpText9": "Consegue adicionar um circulo preto para que a lua se torne num crescente?", "Turtle.helpText10": "Desenhe qualquer coisa que quiser. Você tem um grande número de blocos novos que pode explorar. Divirta-se!", "Turtle.helpText10Reddit": "Use o botão \"Ver Galeria\" para ver o que as outras pessoas desenharam. Se você desenhar algo interessante, use o botão \"Enviar para a Galeria\" para publicar.", "Turtle.helpToolbox": "Escolhe uma categoria para ver os blocos.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "inicio x", "Movie.y1": "inicio y", "Movie.x2": "fim x", "Movie.y2": "fim y", "Movie.radius": "raio", "Movie.width": "largura", "Movie.height": "altura", "Movie.circleTooltip": "Desenha um circulo num local especificado e com raio especificado.", "Movie.circleDraw": "<PERSON><PERSON><PERSON><PERSON>", "Movie.rectTooltip": "Desenha um circulo num local especificado e com raio especificado.", "Movie.rectDraw": "retân<PERSON><PERSON>", "Movie.lineTooltip": "Desenha uma linha de um ponto a outro, com a largura especificada.", "Movie.lineDraw": "linha", "Movie.timeTooltip": "Retornar o tempo corrente na animação  (0-100).", "Movie.colourTooltip": "<PERSON>da a cor da caneta.", "Movie.setColour": "mudar a cor para", "Movie.submitDisabled": "O seu filme não se move. Use blocos para fazer alguma coisa interessante. Depois pode enviar o seu filme para a galeria.", "Movie.galleryTooltip": "Abra a galeria de filmes.", "Movie.galleryMsg": "Ver a Galeria", "Movie.submitTooltip": "Envie o seu filme para a galeria.", "Movie.submitMsg": "Enviar para a Galeria", "Movie.helpLayer": "Mova o círculo de fundo na parte superior do seu programa. Então aparecerá atrás da pessoa.", "Movie.helpText1": "Use formas simples para desenhar essa pessoa.", "Movie.helpText2a": "Este nível é um filme. Se deseja que o braço da pessoa se mova pelo ecrã. Prima o botão Reproduzir para ver uma antevisão.", "Movie.helpText2b": "Enquanto o filme passa, o valor do bloco 'time' conta de 0 a 100. Como queira a posição 'y' do braço iniciar no 0 e que vá até 100 isto deve ser fácil.", "Movie.helpText3": "O bloco 'time' conta de 0 a 100. Mas desta vez quer a posição 'y' do outro braço comece no 100 e vá ate 0. Consegue criar uma simples fórmula matemática para trocar a direção?", "Movie.helpText4": "Use o que aprendeu no nível anterior para cruzar as pernas.", "Movie.helpText5": "A fórmula matemática para o braço é complicada. Aqui está a resposta:", "Movie.helpText6": "<PERSON><PERSON> as duas mãos a pessoa.", "Movie.helpText7": "Use o bloco 'if' para desenhar uma pequena cabeça para a primeira metade do filme. Em seguida, desenhe uma grande cabeça para a segunda metade do filme.", "Movie.helpText8": "<PERSON><PERSON> as pernas na direção reversa na metade do filme.", "Movie.helpText9": "Desenhe um círculo crescente atrás da pessoa.", "Movie.helpText10": "Faça um filme à sua vontade! Tem uma quantidade enorme de blocos novos que pode explorar. Divirta-se!", "Movie.helpText10Reddit": "Use o botão \"Ver Galeria\" para ver o que outras pessoas desenharam. Se desenhar algo interessante,use o botão \"Enviar para a Galeria\" para publicar.", "Music.playNoteTooltip": "Toque uma nota musical com a duração e altura específicas.", "Music.playNote": "tocar %1 nota %2", "Music.restTooltip": "Aguarde pela duração especificada.", "Music.restWholeTooltip": "Aguarde por uma semibreve.", "Music.rest": "resta %1", "Music.setInstrumentTooltip": "Alterne para o instrumento específico ao tocar as notas musicais posteriores.", "Music.setInstrument": "ajustar o instrumento para %1", "Music.startTooltip": "Execute os blocos dentro quando o botão de 'Executar programa' for clicado.", "Music.start": "quando %1 é clicado", "Music.pitchTooltip": "<PERSON>a nota (C4 é 7).", "Music.firstPart": "primeira parte", "Music.piano": "piano", "Music.trumpet": "trompete", "Music.banjo": "banjo", "Music.violin": "violino", "Music.guitar": "guitarra", "Music.flute": "flauta", "Music.drum": "tambor", "Music.choir": "coro", "Music.submitDisabled": "Execute o seu programa até ele parar. Então poderá enviar a sua música para a galeria.", "Music.galleryTooltip": "Abra a galeria de música.", "Music.galleryMsg": "Ver a galeria", "Music.submitTooltip": "Envie a sua música para a galeria.", "Music.submitMsg": "Enviar para a galeria", "Music.helpUseFunctions": "A sua solução funcionou, mas pode fazer melhor. Utilize as funções para reduzir a quantidade de código repetido.", "Music.helpUseInstruments": "A música soará melhor se usar um instrumento diferente em cada bloco inicial.", "Music.helpText1": "<PERSON><PERSON><PERSON><PERSON> as primeiras quatro notas de 'Frère Jacques'.", "Music.helpText2a": "Uma 'função' permite agrupar os blocos, e depois executá-los mais de uma vez.", "Music.helpText2b": "Crie uma função para tocar as quatro primeiras notas de 'Frère Jacques'. Execute essa função duas vezes. Não acrescente novos blocos de notas.", "Music.helpText3": "Crie uma segunda função para a próxima parte de 'Frère Jacques'. A última nota é mais longa.", "Music.helpText4": "Crie uma terceira função para a próxima parte de 'Frère Jacques'. As primeiras quatro notas são mais curtas.", "Music.helpText5": "Complete toda a cantiga de '<PERSON><PERSON> Jacques'.", "Music.helpText6a": "Este bloco novo permite que mude para outro instrumento.", "Music.helpText6b": "Tocar a sua ária com um violino.", "Music.helpText7a": "Este novo bloco acrescenta um temporizador silencioso.", "Music.helpText7b": "Crie um segundo bloco inicial que tenha dois blocos silenciosos, e então toque 'Frère Jacques'.", "Music.helpText8": "Cada bloco inicial deve tocar '<PERSON><PERSON>' duas vezes.", "Music.helpText9": "Crie quatro blocos iniciais que toquem 'Frère Jacques' duas vezes. Acrescente o número correto dos blocos silenciosos.", "Music.helpText10": "Componha o que o que quiseres. Tens uma grande quantidade de blocos novos que podes explorar. Divirta-se!", "Music.helpText10Reddit": "Utilize o botão \"Ver Galeria\" para ver o que as outras pessoas compuseram. Se compuser algo interessante, use o botão \"Enviar para a Galeria\" para publicar.", "Pond.scanTooltip": "Buscar inimigos. Especifique uma direção(0-360). Retorna a distancia do inimigo mais próximo naquela direção. Retorna infinito se nenhum inimigo for encontrado.", "Pond.cannonTooltip": "Disparar o canhão. Especificar uma direção (0-360) e um intervalo (0-70).", "Pond.swimTooltip": "<PERSON><PERSON> para a frente. Especificar uma direção (0-360).", "Pond.stopTooltip": "Parar de nadar. O jogador vai abrandar até parar.", "Pond.healthTooltip": "Retorna a condição atual do jogador (0 esta morto, 100 esta saudável).", "Pond.speedTooltip": "Retorna a velocidade atual do jogador (0 esta parado, 100 é a velocidade máxima).", "Pond.locXTooltip": "Retorna a coordenada X do jogador (0 é a margem esquerda, 100 é a margem direita).", "Pond.locYTooltip": "Retorna a  coordenada Y do jogador (0 é a margem inferior, 100 é a margem superior).", "Pond.logTooltip": "Imprime um número na consola do seu browser.", "Pond.docsTooltip": "Mostrar a documentação de idioma.", "Pond.documentation": "Documentação", "Pond.playerName": "Jogador", "Pond.targetName": "Alvo", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "A sua solução funciona, mas pode fazer melhor. Use 'scan' para mostrar ao canhão a que distância atirar.", "Pond.helpText1": "Use o comando 'cannon' para atingir o alvo. O primeiro parâmetro é o ângulo, o segundo é o alcance. Encontre a combinação certa.", "Pond.helpText2": "Este destino tem de ser atingido muitas vezes. Utilizar um ciclo 'while (true)' para fazer algo indefinidamente.", "Pond.helpText3a": "Esse oponente move-se para frente e para trás, fazendo difícil acertá-lo. A Expressão 'scan' retorna a distância até ao oponente naquela direção.", "Pond.helpText3b": "Este alcance é exatamente o que o comando 'cannon' precisa para atirar precisamente.", "Pond.helpText4": "Esse oponente está muito longe para usar o canhão (que tem um alcance de 70 metros). Em vez disso, use o comando 'swim' para começar a nadar até o oponente e abalroá-lo.", "Pond.helpText5": "Este oponente também esta muito longe para usar o canhão. Mas você está muito fraco para sobreviver uma colisão. Nade até o oponente enquanto a sua posição horizontal seja menor que 50. Então pare e use o canhão.", "Pond.helpText6": "Este oponente irá afastar-se quando atingido. Nade até ele se estiver fora do alcance (70 metros).", "Gallery": "Galeria"}