{"@metadata": {"authors": ["Bugoslav", "<PERSON><PERSON><PERSON>", "Lkralj15", "<PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "Ptica", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Glazba", "Games.pondTutor": "Vodič za Ribnjak", "Games.pond": "Ribnjak", "Games.linesOfCode1": "Ovu razinu ste riješili s 1 retkom JavaScripta:", "Games.linesOfCode2": "Ovu razinu ste riješili s %1 retkom JavaScripta:", "Games.nextLevel": "Jeste li spremni za %1 razinu?", "Games.finalLevel": "Jeste li spremni za sljedeći izazov?", "Games.submitTitle": "Naslov:", "Games.linkTooltip": "Spremi i poveži s blokovima.", "Games.runTooltip": "Pokrenite program koji ste napisali.", "Games.runProgram": "Pokreni program", "Games.resetTooltip": "Zaustavite program i ponovno postavite razinu.", "Games.resetProgram": "Vraćanje", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logika", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematika", "Games.catText": "Tekst", "Games.catLists": "Liste", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Varijable", "Games.catProcedures": "Funkcije", "Games.httpRequestError": "Do<PERSON><PERSON> je do problema sa zahtjevom.", "Games.linkAlert": "Podijelite svoje blokove s ovom vezom:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON>, '%1' ne odgovara nijednom spremljenom programu.", "Games.xmlError": "<PERSON><PERSON> moguće učitati spremljenu datoteku. <PERSON>ž<PERSON> je stvorena s drugačijom verzijom Blocklyja?", "Games.submitted": "Hvala vam na ovom programu!  Ako se svidi našem timu, objavit će ga u galeriji za nekoliko dana.", "Games.listVariable": "lista", "Games.textVariable": "tekst", "Games.breakLink": "<PERSON>da počnete uređivati JavaScript, ne možete se vratiti na uređivanje blokova. Je li ovo u redu?", "Games.blocks": "B<PERSON><PERSON><PERSON>", "Games.congratulations": "Čestitam!", "Games.helpAbort": "Ova razina je izuzetno teška. Želite li je preskočiti i otići na sljedeću igru? Uvijek se možeš vratiti kasnije.", "Index.clear": "Želite li izbrisati sva vaša rješenja?", "Index.subTitle": "Igre za buduće programere.", "Index.moreInfo": "Informacije za učitelje...", "Index.startOver": "Želite li početi ispočetka?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON> pod<PERSON>", "Puzzle.animal1": "Pat<PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://hr.wikipedia.org/wiki/Patka", "Puzzle.animal2": "Mač<PERSON>", "Puzzle.animal2Trait1": "Brkovi", "Puzzle.animal2Trait2": "Krzno", "Puzzle.animal2HelpUrl": "https://hr.wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ma<PERSON>ka", "Puzzle.animal3": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "Med", "Puzzle.animal3Trait2": "Žalac", "Puzzle.animal3HelpUrl": "https://hr.wikipedia.org/wiki/Anthophila", "Puzzle.animal4": "P<PERSON>ž", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "Sluz", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "slika:", "Puzzle.legs": "noge:", "Puzzle.legsChoose": "i<PERSON><PERSON>i...", "Puzzle.traits": "osobine:", "Puzzle.error0": "Izvrsno!\nSvi %1 blokovi su ispravni.", "Puzzle.error1": "Skoro točno! Jedan blok je pogrešan.", "Puzzle.error2": "%1 blokova je netočno.", "Puzzle.tryAgain": "Označeni blok nije točan.\nPokušaj ponovo.", "Puzzle.checkAnswers": "Provjeri odgovore", "Puzzle.helpText": "Za svaku životinju (zeleno), prik<PERSON><PERSON><PERSON> njezinu sliku, odaberi broj nogu i izradi popis njezinih osobina.", "Maze.moveForward": "idi <PERSON>d", "Maze.turnLeft": "<PERSON><PERSON> ul<PERSON>", "Maze.turnRight": "okret udesno", "Maze.doCode": "radi", "Maze.helpIfElse": "Ako-onda blokovi će učiniti jedno ili drugo.", "Maze.pathAhead": "ako je staza ispred", "Maze.pathLeft": "ako je staza lijevo", "Maze.pathRight": "ako je staza desno", "Maze.repeatUntil": "ponavljaj do", "Maze.moveForwardTooltip": "Pomakni igrača naprijed za jedno mjesto.", "Maze.turnTooltip": "Okreni igrača ulijevo ili udesno za 90 stupnjeva.", "Maze.ifTooltip": "<PERSON><PERSON> put u odabranom smjeru, onda napravi neke radnje.", "Maze.ifelseTooltip": "<PERSON><PERSON> post<PERSON> put u odabranom smjeru, onda napravi radnje iz prvog bloka. <PERSON><PERSON><PERSON><PERSON>, napravi radnje iz drugog bloka.", "Maze.whileTooltip": "Ponavljaj priložene radnje sve dok ne dođeš do završne točke.", "Maze.capacity0": "Imate jo<PERSON> %0 b<PERSON><PERSON>.", "Maze.capacity1": "Imate još %1 blok.", "Maze.capacity2": "Imate još %2 bloka.", "Maze.runTooltip": "Natjera igrača da učini ono što blokovi govore.", "Maze.resetTooltip": "Vraća igraća natrag na početak labirinta.", "Maze.helpStack": "Složite ne<PERSON><PERSON><PERSON> blo<PERSON> \"kreni napri<PERSON>\" zajedno kako biste mi pomogli da dosegnem cilj.", "Maze.helpOneTopBlock": "Na ovoj razini morate složiti sve blokove u bijeli radni prostor.", "Maze.helpRun": "Pokrenite program kako biste vidjeli što se događa.", "Maze.helpReset": "Vaš program nije riješio labirint. Pritisnite \"Ponovo pokreni\" i pokušajte ponovo.", "Maze.helpRepeat": "Dođite do kraja ove staze koristeći samo dva bloka. Koristite \"ponovo pokreni\" kako biste prošli kroz blokove više od jednom.", "Maze.helpCapacity": "Iskoristili ste sve blokove na ovoj razini. Kako biste stvorili nove blokove, prvo morate izbrisati postojeći blok.", "Maze.helpRepeatMany": "Možete smjestiti više od jednog bloka u \"ponovo pokreni\" blok.", "Maze.helpSkins": "Odaberite najdražeg igrača iz ovog izbornika.", "Maze.helpIf": "Blok \"ako\" učinit će nešto samo ako je uvjet istinit. Pokušajte skretati lijevo ako postoji put ulijevo.", "Maze.helpMenu": "Kliknite %1 u bloku 'ako' kako biste promijenili njegov uvjet.", "Maze.helpWallFollow": "Možete li riješiti ovaj složeni labirint? Pokušajte slijediti lijevi zid. Samo za napredne programere!", "Bird.noWorm": "nema crva", "Bird.heading": "usmjerenje", "Bird.noWormTooltip": "Uvjet kada ptica nije dobila crva.", "Bird.headingTooltip": "Idi u smjeru zadanog kuta: 0 je desno, 90 je ravno gore itd.", "Bird.positionTooltip": "x i y označavaju položaj ptice. Kada je x = 0 ptica blizu lijevog ruba, kada je x = 100 blizu desnog ruba. Kada je y = 0 ptica na dnu, kada je y = 100 na vrhu.", "Bird.helpHeading": "Promijenite kut smjera kako bi ptica dobila crva i sletjela u svoje gnijezdo.", "Bird.helpHasWorm": "Koristite ovaj blok za pomicanje u jednom smjeru ako imate crva ili drugom smjeru ako nemate crva.", "Bird.helpX": "'x' je vaš trenutni vodoravni položaj. Ovaj blok koristite za pomicanje u jednom smjeru ako je 'x' manji od broja ili drugom smjeru inače.", "Bird.helpElse": "Kliknite ikonu da biste izmijenili blok \"ako\".", "Bird.helpElseIf": "Ova razina treba i blok \"inače ako\" i \"inače\".", "Bird.helpAnd": "Blo<PERSON> \"i\" je istinit samo ako su oba njegova ulazne vrijednosti istinite.", "Bird.helpMutator": "Povucite blok 'inače' u blok 'ako'.", "Turtle.moveTooltip": "Pomiče kornjaču naprijed ili natrag za zadanu vrijednost.", "Turtle.moveForward": "idi naprijed za", "Turtle.moveBackward": "idi natrag za", "Turtle.turnTooltip": "Okreće kornjaču lijevo ili desno za određeni broj stupnjeva.", "Turtle.turnRight": "okret udes<PERSON> za", "Turtle.turnLeft": "okret ulijevo za", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON>.", "Turtle.setWidth": "<PERSON>avi <PERSON> na", "Turtle.colourTooltip": "Mijenja boju olov<PERSON>.", "Turtle.setColour": "<PERSON><PERSON> boju na", "Turtle.penTooltip": "Podiže ili spušta olovku kako bi se zaustavila ili počela crtati.", "Turtle.penUp": "pero gore", "Turtle.penDown": "pero dolje", "Turtle.turtleVisibilityTooltip": "Čini kornjaču (krug i strelicu) vidljivom ili nevidljivom.", "Turtle.hideTurtle": "sa<PERSON><PERSON><PERSON>", "Turtle.showTurtle": "pokaži kornjaču", "Turtle.printHelpUrl": "https://hr.wikipedia.org/wiki/Tiskarstvo", "Turtle.printTooltip": "Crta tekst u smjeru kornjače na njezinoj lokaciji.", "Turtle.print": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontTooltip": "Postavlja font koji koristi blok za ispis.", "Turtle.font": "font", "Turtle.fontSize": "ve<PERSON><PERSON><PERSON> fonta", "Turtle.fontNormal": "normal", "Turtle.fontBold": "podebljano", "Turtle.fontItalic": "Kurziv", "Turtle.submitDisabled": "Pokrenite program dok se ne zaustavi. Zatim možete poslati svoj crtež u galeriju.", "Turtle.galleryTooltip": "Otvori galeriju crteža.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON>", "Turtle.submitTooltip": "Pošaljite crtež u galeriju.", "Turtle.submitMsg": "Pošalji u galeriju", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON><PERSON> rješenje radi, ali mož<PERSON> pronaći bolje rješenje.", "Turtle.helpUseLoop3": "Nacrtajte oblik sa samo tri upotrijebljena bloka.", "Turtle.helpUseLoop4": "Nacrtajte zvijezdu sa samo četiri upotrijebljena bloka.", "Turtle.helpText1": "Izradite program koji crta kvadrat.", "Turtle.helpText2": "Promijenite program da crta peterokut umjesto kvadrata.", "Turtle.helpText3a": "Postoji novi blok koji vam omogućuje da promijenite boju:", "Turtle.helpText3b": "Nacrtaj žutu zvijezdu.", "Turtle.helpText4a": "Postoji novi blok koji vam omogućuje da podignete olovku s papira kada se krećete:", "Turtle.helpText4b": "Nacrtajte malu žutu zvijezdu, a zatim nacrtajte crtu iznad nje.", "Turtle.helpText5": "Umjesto jedne zvijezde, možete li nacrtati četiri zvjezdice raspoređene u kvadrat?", "Turtle.helpText6": "Nacrtajte tri žute zvijezde i jednu bijelu crtu.", "Turtle.helpText7": "Nacrtajte zvijezde, a zatim nacrtajte četiri bijele crte.", "Turtle.helpText8": "Crtanje 360 bijelih crta izgledat će kao pun mjesec.", "Turtle.helpText9": "Možete li dodati crni krug tako da Mjesec postane polumjesec?", "Turtle.helpText10": "Nacrtajte što god želite. Imate veliki broj novih blokova koje možete istražiti. Uživaj!", "Turtle.helpText10Reddit": "Upotrijebite gumb \"Pogledaj galeriju\" kako biste vidjeli što su drugi nacrtali. Ako nacrtate neš<PERSON>, upotrijebite gumb \"Dodaj u galeriju\" kako biste crte<PERSON> o<PERSON>.", "Turtle.helpToolbox": "Odaberite kategoriju da biste vidjeli blokove.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "početna x koordinata", "Movie.y1": "početna y koordinata", "Movie.x2": "završna x koordinata", "Movie.y2": "završna y koordinata", "Movie.radius": "polumjer", "Movie.width": "<PERSON><PERSON><PERSON>", "Movie.height": "visina", "Movie.circleTooltip": "Crta krug na navedenom mjestu i s navedenim radijusom.", "Movie.circleDraw": "krug", "Movie.rectTooltip": "Crta pravokutnik na navedenom mjestu i s navedenom širinom i visinom.", "Movie.rectDraw": "pravokutnik", "Movie.lineTooltip": "Crta crtu iz jedne točke u drugu s navedenom širinom.", "Movie.lineDraw": "crta", "Movie.timeTooltip": "Vraća trenutno vrijeme u animaciji (0-100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON>na bo<PERSON>.", "Movie.setColour": "<PERSON><PERSON> boju na", "Movie.submitDisabled": "Vaš film se ne miče. Koristite blokove kako biste napravili nešto zanimljivo. Zatim možete poslati svoj film u galeriju.", "Movie.galleryTooltip": "Otvorite galeriju filmova.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON>", "Movie.submitTooltip": "Pošaljite film u galeriju.", "Movie.submitMsg": "Pošalji u galeriju", "Movie.helpLayer": "Pomaknite pozadinski krug na vrh programa. Tada će se pojaviti iza osobe.", "Movie.helpText1": "Upotrijebite jednostavne oblike kako biste nacrtali ovu osobu.", "Movie.helpText2a": "<PERSON>va razina je film. <PERSON><PERSON><PERSON> da se ruke osobe pomiču preko zas<PERSON>a. Kliknite na gumb pokreni kako biste vidjeli pretpregled.", "Movie.helpText2b": "Za vrijeme izvođenja filma, vremenski blok broji od 0 do 100. <PERSON><PERSON><PERSON><PERSON> da želite da 'y' položaj ruku počne u 0 i ide do 100 to bi trebao biti jednostavno.", "Movie.helpText3": "Za vrijeme izvođenja filma, vremenski blok broji od 0 do 100. Vi želite da 'y' položaj ruku počne u 100 i ide do 0. Možete li smisliti jednostavnu matematičku formulu kojom biste obrnuli smjer?", "Movie.helpText4": "Iskoristite što ste naučili na prethodnoj razini kako biste napravili prekrižene noge.", "Movie.helpText5": "Matematička formula za ruke je složena. Ovo je rješenje:", "Movie.helpText6": "Nacrtajte osobi par ruku.", "Movie.helpText7": "Upotrijebite blok ako (if) kako biste nacrtali malu glavi za prvu polovicu filma. Zatim nacrtajte veliku glavu za drugu polovicu filma.", "Movie.helpText8": "Napravite da se noge pomiču u obratnom smjeru na polovici filma.", "Movie.helpText9": "Nacrtajte širi krug iza osobe.", "Movie.helpText10": "Napravite film po želji. Na raspolaganju imate velik broj novih blokova za istraživanje. Zabavite se.", "Movie.helpText10Reddit": "Upotrijebite gumb \"Pogledaj galeriju\" kako biste vidjeli filmove koje su drugi napravili. Ako napravite zanimljiv film, upotrijebite gumb \"Dodaj u galeriju\" kako biste film objavili.", "Music.playNoteTooltip": "Svira jednu muzičku notu zadanog trajanja i visine", "Music.playNote": "svir<PERSON> %1 notu %2", "Music.restTooltip": "Čeka zadani period", "Music.restWholeTooltip": "Čeka za cijelu notu", "Music.rest": "miruj %1", "Music.setInstrumentTooltip": "Promijeni instrument za vrijeme sviranja niza nota.", "Music.setInstrument": "postavi instrument na %1", "Music.startTooltip": "Izvrši blokove naredbi kad se klikne na Pokreni program.", "Music.start": "kad %1 je kliknuto", "Music.pitchTooltip": "<PERSON><PERSON> nota (C4 je 7).", "Music.firstPart": "prvi dio", "Music.piano": "klavir", "Music.trumpet": "truba", "Music.banjo": "<PERSON>ž<PERSON>", "Music.violin": "violina", "Music.guitar": "gitara", "Music.flute": "flauta", "Music.drum": "bubanj", "Music.choir": "zbor", "Music.submitDisabled": "Pokrenite program dok se ne zaustavi. Zatim svoju glazbu možete poslati u galeriju.", "Music.galleryTooltip": "Otvorite galeriju glazbe.", "Music.galleryMsg": "Pogledajte galeriju", "Music.submitTooltip": "Pošaljite glazbu u galeriju.", "Music.submitMsg": "Objavite u galeriji", "Music.helpUseFunctions": "Vaše rješenje radi, ali može biti i bolje. Upotrijebite funkcije kako biste smanjili broj naredbi koje se ponavljaju.", "Music.helpUseInstruments": "Glazba će zvučati bolje ako koristite različite instrumente u svakom početnom bloku.", "Music.helpText1": "Skladajte prve četiri note \"Brateca Martina\".", "Music.helpText2a": "'Funkcija' vam o<PERSON> da grupirate blokove, a zatim ih pokrenete više puta.", "Music.helpText2b": "Stvorite funkciju koja će svirati prve četiri note \"Brateca Martina\". Pokrenite funkciju dva puta. Nemojte dodavati nove blokove s notama.", "Music.helpText3": "Stvorite drugu funkciju za idući dio \"Brateca Martina\". Zadnja nota traje duže.", "Music.helpText4": "Stvorite treću funkciju za idući dio \"Brateca Martina\". Prve četiri note su kraće.", "Music.helpText5": "Dovršite cijelu pjesmu \"Bratec Martin\".", "Music.helpText6a": "Novi blok vam omogućava promjenu instrumenta.", "Music.helpText6b": "Svirajte svoju pjesmu na violini.", "Music.helpText7a": "Ovaj novi blok dodaje stanku.", "Music.helpText7b": "Stvorite drugi početni blok koji ima dva bloka sa stankom, tada ponovo zasvirajte \"Brateca Martina\".", "Music.helpText8": "Svaki početni blok bi trebao svirati \"Brateca Martina\" dvaput.", "Music.helpText9": "Stvorite četiri početna bloka od kojih svaki svira \"Brateca Martina\" dvaput. Dodajte ispravan broj blokova sa stankama.", "Music.helpText10": "Nacrtaj<PERSON> što god želite. Imate veliki broj novih blokova koje možete istražiti. Zabavite se!", "Music.helpText10Reddit": "Upotrijebite gumb \"Pogledaj galeriju\" kako biste vidjeli što su drugi skladali. Ako skladate nešto z<PERSON>lji<PERSON>, upotrijebite gumb \"Dodaj u galeriju\" kako biste skladbu objavili.", "Pond.scanTooltip": "Traži neprijatelje. <PERSON><PERSON><PERSON> s<PERSON> (0-360). Vraća udaljenost do najbližeg neprijatelja u zadanom smjeru. Vraća vrijednost beskonačno ako neprijatelj nije pronađen.", "Pond.cannonTooltip": "Pucaj topom. <PERSON><PERSON><PERSON> (0-360) i raspon (0-70)", "Pond.swimTooltip": "<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> (0-360).", "Pond.stopTooltip": "Pre<PERSON><PERSON> pliva<PERSON>. Igrač će se zaustaviti.", "Pond.healthTooltip": "Vraća igračevo trenutačno zdravlje (0 znači mrtav, 100 zdrav)", "Pond.speedTooltip": "Daje trenutačnu brzinu igrača (0 je mirovanje, 100 puna brzina)", "Pond.locXTooltip": "Vraća X koordinatu igrača (0 je lijevi rub, 100 je desni rub).", "Pond.locYTooltip": "<PERSON>je Y koordinatu igrača (0 je donji rub, 100 je gornji rub).", "Pond.logTooltip": "Ispisuje broj u mrežnom pregledniku.", "Pond.docsTooltip": "Prikaži jezik dokumentacije.", "Pond.documentation": "Dokumentacija", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Klatno", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "<PERSON><PERSON>š<PERSON> rje<PERSON> radi, ali može biti bolje. Upotrijebite tražilo (scan) kako biste zadali udaljenost za pucanj topa.", "Pond.helpText1": "Upotrijebite naredbu top 'cannon' kako biste pogodili metu. Prvi parametar je kut, a drugi raspon. Pronađite odgovarajuću kombinaciju.", "Pond.helpText2": "Ovu metu morate pogoditi nekoliko puta. Upotrijebite petlju 'dok je (istinito)' ('while (true)') kako biste nešto ponovili neodređeni broj puta.", "Pond.helpText3a": "Ovaj protivnik se pomiče naprijed-natrag što otežava gađanje. Naredba 'scan' vraća točnu udaljenost do neprijatelja u zadanom smjeru.", "Pond.helpText3b": "<PERSON><PERSON> u<PERSON> je ono što naredba 'cannon' top treba kako bi to<PERSON> pogodili.", "Pond.helpText4": "Neprijatelj je predaleko za upotrebu topa (koje<PERSON> je doseg 70 metara=. <PERSON>jesto toga upotrijebite naredbu 'swim' plivaj, kako biste doplivali bliže neprijatelju i pogodili ga.", "Pond.helpText5": "Ovaj neprijatelj je također predaleko za gađanje topom, ali vi ste preslabi da biste preživjeli sudar. Plivajte prema neprijatelju zadržavajući horizontalnu lokaciju ispod 50, a zatim se zaustavite i upotrijebite top.", "Pond.helpText6": "O<PERSON>j neprijatelj će se odmaknuti nakon pogotka. Plivajte prema njemu ako je izvan dose<PERSON> (70 metara).", "Gallery": "<PERSON><PERSON><PERSON>"}