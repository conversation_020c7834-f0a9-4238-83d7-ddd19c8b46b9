{"@metadata": {"authors": ["Aefgh39622", "Azpirin", "Nantapat", "Octahedron80", "Patsagorn Y.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Trisorn Triboon"]}, "Games.name": "บล็อกลีเกมส์", "Games.puzzle": "ปริศนา", "Games.maze": "เขาวงกต", "Games.bird": "นก", "Games.turtle": "เต่า", "Games.movie": "ภาพยนตร์", "Games.music": "เพลง", "Games.pondTutor": "Pond Tutor", "Games.pond": "สระน้ำ", "Games.linesOfCode1": "คุณแก้ปัญหาด่านนี้ได้ด้วยจาวาสคริปต์ 1 บรรทัด", "Games.linesOfCode2": "คุณแก้ปัญหาด่านนี้ได้ด้วยจาวาสคริปต์ %1 บรรทัด", "Games.nextLevel": "คุณพร้อมหรือยังกับด่าน %1?", "Games.finalLevel": "คุณพร้อมหรือยังกับความท้าทายถัดไป?", "Games.submitTitle": "ชื่อเรื่อง:", "Games.linkTooltip": "บันทึกและสร้างลิงก์ไปยังบล็อก", "Games.runTooltip": "เรียกใช้โปรแกรมที่คุณเขียน", "Games.runProgram": "เรียกใช้โปรแกรม", "Games.resetTooltip": "หยุดโปรแกรมและเริ่มใหม่", "Games.resetProgram": "เริ่มใหม่", "Games.help": "ช่วยเหลือ", "Games.catLogic": "ตรรกะ", "Games.catLoops": "การวนซ้ำ", "Games.catMath": "คณิตศาสตร์", "Games.catText": "ข้อความ", "Games.catLists": "รายการ", "Games.catColour": "สี", "Games.catVariables": "ตัวแปร", "Games.catProcedures": "ฟังก์ชัน", "Games.httpRequestError": "มีปัญหาเกี่ยวกับการร้องขอ", "Games.linkAlert": "แบ่งปันบล็อกของคุณด้วยลิงก์นี้:\n\n%1", "Games.hashError": "ขออภัย '%1' ไม่ตรงกับโปรแกรมใด ๆ ที่บันทึกไว้", "Games.xmlError": "ไม่สามารถโหลดไฟล์ที่บันทึกไว้ของคุณได้ บางทีมันอาจถูกสร้างขึ้นด้วยบล็อกลีรุ่นอื่นที่ต่างออกไป?", "Games.submitted": "ขอบคุณสำหรับโปรแกรมนี้! หากพนักงานของเราที่ได้รับการฝึกฝนลิงชอบมันพวกเขาจะเผยแพร่ไปยังแกลเลอรี่ภายในสองสามวัน", "Games.listVariable": "รายการ", "Games.textVariable": "ข้อความ", "Games.breakLink": "ทันทีที่คุณเริ่มการแก้ไข Javascipt คุณจะไม่สามารถกลับไปยังการแก้ไข block เหล่านั้นได้นะ คุณยังจะ OK อยู่ไหม?", "Games.blocks": "บล็อก", "Games.congratulations": "ยินดีด้วย!", "Games.helpAbort": "ด่านนี้ยากมาก คุณต้องการข้ามด่านนี้และไปยังเกมถัดไปหรือไม่? คุณสามารถกลับมายังด่านนี้ได้ในภายหลัง", "Index.clear": "ลบการแก้ปัญหาทั้งหมดของคุณหรือไม่?", "Index.subTitle": "เกมสำหรับนักเขียนโปรแกรมในวันข้างหน้า", "Index.moreInfo": "ข้อมูลสำหรับผู้สอน...", "Index.startOver": "ต้องการเริ่มใหม่หรือไม่?", "Index.clearData": "ล้างข้อมูล", "Puzzle.animal1": "เป็ด", "Puzzle.animal1Trait1": "ขนนก", "Puzzle.animal1Trait2": "จะงอยปาก", "Puzzle.animal1HelpUrl": "https://th.wikipedia.org/wiki/เป็ด", "Puzzle.animal2": "แมว", "Puzzle.animal2Trait1": "หนวด", "Puzzle.animal2Trait2": "ขน", "Puzzle.animal2HelpUrl": "https://th.wikipedia.org/wiki/แมว", "Puzzle.animal3": "ผึ้ง", "Puzzle.animal3Trait1": "น้ำผึ้ง", "Puzzle.animal3Trait2": "เหล็กใน", "Puzzle.animal3HelpUrl": "https://th.wikipedia.org/wiki/ผึ้ง", "Puzzle.animal4": "หอยทาก", "Puzzle.animal4Trait1": "หอย", "Puzzle.animal4Trait2": "สไลม์", "Puzzle.animal4HelpUrl": "https://th.wikipedia.org/wiki/หอยทาก", "Puzzle.picture": "ภาพ:", "Puzzle.legs": "ขา:", "Puzzle.legsChoose": "เลือก...", "Puzzle.traits": "ลักษณะ:", "Puzzle.error0": "สมบูรณ์แบบ!\n%1 บล็อกถูกต้องทั้งหมด", "Puzzle.error1": "เกือบแล้ว! เหลือแค่บล็อกเดียวที่ยังผิดอยู่", "Puzzle.error2": "%1 บล็อกยังผิดอยู่", "Puzzle.tryAgain": "บล็อกที่เน้นสีไว้ยังผิดอยู่\nลองอีกครั้ง", "Puzzle.checkAnswers": "ตรวจคำตอบ", "Puzzle.helpText": "ในสัตว์แต่ละตัว (บล็อกสีเขียว) ให้นำบล็อกภาพมาต่อ เลือกจำนวนขาของสัตว์แต่ละตัว และให้ใส่บล็อกลักษณะเรียงไว้อยู่ข้างใน", "Maze.moveForward": "เดินหน้า", "Maze.turnLeft": "หันซ้าย", "Maze.turnRight": "หันขวา", "Maze.doCode": "ทำ", "Maze.helpIfElse": "บล็อก \"ถ้า-มิฉะนั้น\" จะทำอย่างใดอย่างหนึ่งเท่านั้น", "Maze.pathAhead": "ถ้ามีทางเดินข้างหน้า", "Maze.pathLeft": "ถ้ามีทางเดินด้านซ้าย", "Maze.pathRight": "ถ้ามีทางเดินด้านขวา", "Maze.repeatUntil": "ทำซ้ำจนกระทั่ง", "Maze.moveForwardTooltip": "เคลื่อนตัวผู้เล่นไปข้างหน้าหนึ่งช่อง", "Maze.turnTooltip": "หมุนตัวผู้เล่นไปทางซ้ายหรือขวา 90 องศา", "Maze.ifTooltip": "ถ้ามีทางเดินตามทิศที่กำหนดไว้ ก็ให้ทำตามคำสั่ง", "Maze.ifelseTooltip": "ถ้ามีทางเดินตามทิศที่กำหนดไว้ ก็ให้ทำตามคำสั่งในบล็อกแรก มิฉะนั้นก็ให้ทำตามคำสั่งในบล็อกที่สอง", "Maze.whileTooltip": "ทำซ้ำคำสั่งที่ถูกปิดคลุมไปเรื่อย ๆ จนกว่าจะถึงจุดสิ้นสุด", "Maze.capacity0": "คุณเหลืออีก %0 บล็อก", "Maze.capacity1": "คุณเหลืออีก %1 บล็อก", "Maze.capacity2": "คุณเหลืออีก %2 บล็อก", "Maze.runTooltip": "ทำให้ตัวผู้เล่นทำตามที่บล็อกสั่ง", "Maze.resetTooltip": "วางตัวผู้เล่นไว้ที่จุดเริ่มต้นเขาวงกต", "Maze.helpStack": "ต่อบล็อก 'เดินหน้า' สองอันเข้าด้วยกัน เพื่อช่วยให้ไปถึงจุดหมาย", "Maze.helpOneTopBlock": "ในด่านนี้ คุณต้องต่อทุกบล็อกเข้าด้วยกันในพื้นที่ทำงานสีขาว", "Maze.helpRun": "เรียกใช้โปรแกรมของคุณเพื่อดูว่าจะเกิดอะไรขึ้น", "Maze.helpReset": "โปรแกรมของคุณยังไม่สามารถแก้ปัญหาเขาวงกตนี้ได้ กดปุ่ม 'เริ่มใหม่' แล้วลองอีกครั้ง", "Maze.helpRepeat": "ไปให้ถึงสุดทางเดินนี้โดยใช้เพียง 2 บล็อกเท่านั้น ใช้บล็อก 'ทำซ้ำ' เพื่อเรียกใช้บล็อกเดิมซ้ำมากกว่าหนึ่งครั้ง", "Maze.helpCapacity": "คุณได้ใช้บล็อกทั้งหมดสำหรับด่านนี้แล้ว เพื่อที่จะสร้างบล็อกใหม่ คุณต้องลบบล็อกใดบล็อกหนึ่งที่มีอยู่เสียก่อน", "Maze.helpRepeatMany": "คุณสามารถใส่บล็อกเข้าไปข้างในบล็อก 'ทำซ้ำ' ได้มากกว่าหนึ่งบล็อก", "Maze.helpSkins": "เลือกตัวผู้เล่นที่คุณชอบได้จากเมนูนี้", "Maze.helpIf": "บล็อก 'ถ้า' จะทำบางอย่างก็ต่อเมื่อเงื่อนไขเป็นจริง ลองกำหนดให้หันซ้ายเมื่อมีทางเดินด้านซ้ายดูสิ", "Maze.helpMenu": "คลิกที่ %1 ในบล็อก 'ถ้า' เพื่อเปลี่ยนเงื่อนไข", "Maze.helpWallFollow": "คุณสามารถแก้ปัญหาเขาวงกตที่ซับซ้อนนี้ได้หรือเปล่า? ลองเดินเลียบกำแพงทางซ้ายดูสิ สำหรับโปรแกรมเมอร์ระดับสูงเท่านั้นนะ!", "Bird.noWorm": "ไม่มีหนอน", "Bird.heading": "มุ่งหน้า", "Bird.noWormTooltip": "เงื่อนไขที่จะเกิดขึ้นเมื่อนกจับหนอนไม่ได้", "Bird.headingTooltip": "ย้ายไปในทิศที่กำหนดไว้ให้: 0 คือไปทางขวา , 90 คือตรงไป , และอื่นๆ", "Bird.positionTooltip": "x และ y จะทำเครื่องหมายตำแหน่งของนกเอาไว้ เมื่อ x = 0 นกจะขยับไปใกล้บริเวณขอบด้านซ้าย เมื่อ x= 100 นกจะขยับไปใกล้บริเวณขอบด้านขวา เมื่อ y = 0 นกจะไปอยู่บริเวณด้านล่าง และเมื่อ y = 100 นกจะไปอยู่บริเวณด้านบน", "Bird.helpHeading": "เปลี่ยนทิศทางมุมที่ส่วนหัวด้านบนเพื่อที่จะทำให้นกไปรับหนอนมาและร่อนลงบนรังนกของมัน", "Bird.helpHasWorm": "ใช้บล็อกนี้ไปใส่ในหัวเรื่อง if ถ้ามีหนอน หรือไม่ก็เอาบล็อกไปใส่ในหัวเรื่องอื่นที่ไม่ใช่ if ถ้าสมมติว่าไม่มีหนอนอยู่", "Bird.helpX": "x เป็นตำแหน่งปัจจุบันของคุณในทิศทางแกนนอน ให้ใช้บล็อกที่ได้มานี้นำไปใส่ในหัวเรื่องแรกถ้า x น้อยกว่าตัวเลขใด ๆ หรือไม่ก็นำไปใส่ในหัวเรื่องอื่น ๆ แทนตามแต่เงื่อนไขที่สร้าง", "Bird.helpElse": "คลิกสัญลักษณ์รูปฟันเฟืองเพื่อทำการเปลี่ยนแปลงบล็อกที่ชื่อ if", "Bird.helpElseIf": "ในด่านนี้ต้องการให้ใช้ทั้งบล็อกของ else if และ else", "Bird.helpAnd": "บล็อกของ and จะเป็นจริงก็ต่อเมื่อข้อมูลนำเข้าทั้งสองเป็นจริง", "Bird.helpMutator": "ลากบล็อกของ else ให้เข้าไปในบล็อกของ if", "Turtle.moveTooltip": "เคลื่อนที่เต่าไปข้างหน้าหรือข้างหลังตามระยะทางที่กำหนด", "Turtle.moveForward": "เคลื่อนที่ไปข้างหน้าด้วยระยะทาง", "Turtle.moveBackward": "เคลื่อนที่ไปข้างหลังด้วยระยะทาง", "Turtle.turnTooltip": "หันเต่าไปทางซ้ายหรือขวาด้วยขนาดองศาที่กำหนด", "Turtle.turnRight": "หันไปทางขวาด้วยองศา", "Turtle.turnLeft": "หันไปทางซ้ายด้วยองศา", "Turtle.widthTooltip": "เปลี่ยนความกว้างของปากกา", "Turtle.setWidth": "ตั้งความกว้างเป็น", "Turtle.colourTooltip": "เปลี่ยนสีของปากกา", "Turtle.setColour": "ตั้งสีเป็น", "Turtle.penTooltip": "ยกหรือกดปากกา เพื่อหยุดหรือเริ่มวาดรูป", "Turtle.penUp": "ปากกายกขึ้น", "Turtle.penDown": "ปากกากดลง", "Turtle.turtleVisibilityTooltip": "ทำให้เต่า (วงกลมและลูกศร) มองเห็นหรือมองไม่เห็น", "Turtle.hideTurtle": "ซ่อนเต่า", "Turtle.showTurtle": "แสดงเต่า", "Turtle.printHelpUrl": "https://th.wikipedia.org/wiki/การพิมพ์", "Turtle.printTooltip": "แสดงข้อความในทิศทางที่เต่าหันหน้าตรงตำแหน่งนั้น", "Turtle.print": "พิมพ์", "Turtle.fontTooltip": "ตั้งค่าแบบอักษรที่ใช้ในบล็อกพิมพ์", "Turtle.font": "แบบอักษร", "Turtle.fontSize": "ขนาดอักษร", "Turtle.fontNormal": "ปกติ", "Turtle.fontBold": "ตัวหนา", "Turtle.fontItalic": "ตัวเอน", "Turtle.submitDisabled": "เรียกใช้โปรแกรมของคุณจนกว่าโปรแกรมของคุณจะหยุดทำงาน จากนั้นคุณสามารถส่งภาพวาดของคุณไปที่คลังภาพได้", "Turtle.galleryTooltip": "เปิดคลังภาพวาด", "Turtle.galleryMsg": "ดูคลังภาพ", "Turtle.submitTooltip": "ส่งภาพวาดของคุณไปที่แกลเลอรี", "Turtle.submitMsg": "ส่งไปที่คลังภาพ", "Turtle.helpUseLoop": "การแก้ไขของคุณใช้ได้ แต่คุณสามารถทำได้ดีกว่านี้", "Turtle.helpUseLoop3": "วาดรูปด้วยบล็อกเพียงสามบล็อก", "Turtle.helpUseLoop4": "วาดรูปดาวด้วยบล็อกเพียงสี่บล็อก", "Turtle.helpText1": "สร้างโปรแกรมที่สามารถวาดรูปสี่เหลี่ยมได้", "Turtle.helpText2": "เปลี่ยนคำสั่งของโปรแกรมของคุณให้วาดรูปห้าเหลี่ยมแทนรูปสี่เหลี่ยม", "Turtle.helpText3a": "มีบล็อกใหม่ที่ให้คุณสามารถเปลี่ยนสีได้:", "Turtle.helpText3b": "วาดรูปดาวสีเหลือง", "Turtle.helpText4a": "มีบล็อกใหม่ที่ให้คุณสามารถยกปากกาของคุณขึ้นจากกระดาษได้เมื่อคุณเคลื่อนไหว:", "Turtle.helpText4b": "วาดรูปดาวสีเหลืองเล็ก ๆ แล้ววาดเส้นทับข้างบน", "Turtle.helpText5": "แทนที่จะวาดรูปดาวดวงเดียว คุณวาดรูปดาวสี่ดวงข้างในรูปสี่เหลี่ยมหนึ่งรูปได้หรือไม่?", "Turtle.helpText6": "วาดรูปดาวสีเหลืองสามดวง และวาดเส้นสีขาวอีกหนึ่งเส้น", "Turtle.helpText7": "วาดรูปดาว แล้ววาดเส้นสีขาวสี่เส้น", "Turtle.helpText8": "การวาดเส้นสีขาว 360 เส้นจะดูเหมือนพระจันทร์เต็มดวง", "Turtle.helpText9": "คุณลองวาดรูปวงกลมสีดำอีกหนึ่งวงเพื่อให้พระจันทร์เต็มดวงกลายเป็นพระจันทร์ครึ่งเสี้ยวได้หรือไม่?", "Turtle.helpText10": "วาดอะไรก็ได้ที่คุณต้องการ คุณได้รับบล็อกจำนวนมหาศาลมาไว้ให้คุณสำรวจ ขอให้สนุก!", "Turtle.helpText10Reddit": "ปุ่ม ดูคลังภาพ มีไว้สำหรับดูสิ่งที่คนอื่นๆได้วาดไป ถ้าคุณวาดอะไรก็ตามที่คิดว่าน่าสนใจ ให้ใช้ปุ่ม ส่งไปที่คลังภาพ เพื่อที่จะเผยแพร่ภาพของคุณลงไปใน Gallery นั้น", "Turtle.helpToolbox": "เลือกหมวดหมู่เพื่อดูบล็อก", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x เริ่มต้น", "Movie.y1": "y เริ่มต้น", "Movie.x2": "x สิ้นสุด", "Movie.y2": "y สิ้นสุด", "Movie.radius": "รัศมี", "Movie.width": "ความกว้าง", "Movie.height": "ความสูง", "Movie.circleTooltip": "วาดวงกลมด้วยตำแหน่งและรัศมีที่ระบุ", "Movie.circleDraw": "วงกลม", "Movie.rectTooltip": "วาดสี่เหลี่ยมมุมฉากด้วยตำแหน่ง ความสูง และความยาวที่ระบุ", "Movie.rectDraw": "สี่เหลี่ยมมุมฉาก", "Movie.lineTooltip": "วาดเส้นตรงจากจุดหนึ่งไปอีกจุดหนึ่งด้วยความกว้างที่ระบุ", "Movie.lineDraw": "เส้นตรง", "Movie.timeTooltip": "คืนค่าเป็นเวลาปัจจุบันในอนิเมชั่น(0-100)", "Movie.colourTooltip": "เปลี่ยนสีของปากกา", "Movie.setColour": "ตั้งสีเป็น", "Movie.submitDisabled": "ภาพยนตร์ของคุณไม่มีการเคลื่อนไหว ใช้บล็อกเพื่อทำให้บางสิ่งบางอย่างดูน่าสนใจ แล้วส่งภาพยนตร์ของคุณไปที่คลังภาพยนตร์", "Movie.galleryTooltip": "เปิดแกลเลอรี่ภาพยนตร์", "Movie.galleryMsg": "ดูคลังภาพยนตร์", "Movie.submitTooltip": "ส่งภาพยนตร์ของคุณไปที่คลังภาพ", "Movie.submitMsg": "ส่งไปที่คลังภาพยนตร์", "Movie.helpLayer": "ย้ายวงกลมพื้นหลังไปที่ด้านบนของโปรแกรม จากนั้นจะปรากฏอยู่ข้างหลังบุคคล", "Movie.helpText1": "วาดรูปคนนี้โดยใช้รูปร่างอย่างง่าย", "Movie.helpText2a": "ระดับนี้เป็นด่านเกี่ยวกับภาพยนตร์ และคุณต้องการให้แขนของบุคคลเคลื่อนผ่านหน้าจอไป กดปุ่ม Play เพื่อดูตัวอย่าง", "Movie.helpText2b": "\nในขณะที่ภาพยนตร์เล่นค่าของ 'เวลา' บล็อกนับจาก 0 ถึง 100 เนื่องจากคุณต้องการตำแหน่ง 'y' ของแขนเริ่มต้นที่ 0 และไปที่ 100 สิ่งนี้น่าจะง่าย", "Movie.helpText3": "บล็อก 'เวลา' นับจาก 0 ถึง 100 แต่ตอนนี้คุณต้องการตำแหน่ง 'y' ของแขนอีกข้างเริ่มต้นที่ 100 และไปที่ 0 คุณสามารถหาสูตรทางคณิตศาสตร์อย่างง่ายที่พลิกทิศทางได้หรือไม่", "Movie.helpText4": "ใช้สิ่งที่คุณเรียนรู้ในระดับก่อนหน้าเพื่อทำให้ขาไขว้กัน", "Movie.helpText5": "สูตรทางคณิตศาสตร์สำหรับแขนนั้นซับซ้อน นี่คือคำตอบ:", "Movie.helpText6": "ให้มือสองข้างกับคน", "Movie.helpText7": "ใช้บล็อก 'if' เพื่อวาดหัวเล็กในช่วงครึ่งแรกของภาพยนตร์ จากนั้นวาดหัวโตในช่วงครึ่งหลังของภาพยนตร์", "Movie.helpText8": "ทำให้ขากลับทิศทางไปครึ่งทางผ่านภาพยนตร์", "Movie.helpText9": "\nวาดวงกลมขยายด้านหลังบุคคล", "Movie.helpText10": "วาดอะไรก็ได้ที่คุณต้องการ คุณได้รับบล็อกจำนวนมหาศาลมาไว้ให้คุณสำรวจ ขอให้สนุก!", "Movie.helpText10Reddit": "สามารถใช้ปุ่ม ดูคลังภาพ เพื่อดู movie ที่คนอื่นๆสร้างได้ ถ้าหากคุณสร้าง movie ที่คิดว่ามันน่าสนใจขึ้นมาล่ะก็คุณสามารถกดปุ่ม ดูคลังภาพยนตร์ เพื่อเผยแพร่ movie ของคุณได้", "Music.playNoteTooltip": "กำหนดให้เล่นโน้ตดนตรีในระดับเสียงและในระยะเวลาที่กำหนด", "Music.playNote": "เล่น %1 ตัวโน้ต %2", "Music.restTooltip": "ให้รอตามระยะเวลาที่กำหนด", "Music.restWholeTooltip": "รอตัวโน้ตทั้งหมด", "Music.rest": "พัก %1", "Music.setInstrumentTooltip": "เปลี่ยนเครื่องเล่นไปยังเครื่องเล่นที่กำหนดไว้เมื่อจะต้องเล่นโน้ตดนตรีที่เรียงต่อกันตามลำดับ", "Music.setInstrument": "ตั้งค่าให้กับเครื่องเล่นดนตรีเป็น %1", "Music.startTooltip": "ทำการรันบล็อกที่อยู่ข้างในเมื่อปุ่ม Run Program ถูกคลิก", "Music.start": "เมื่อ %1 คลิก", "Music.pitchTooltip": "โน้ตดนตรี 1 ตัว (C4 คือ 7)", "Music.firstPart": "ส่วนแรก", "Music.piano": "เปียโน", "Music.trumpet": "แตร", "Music.banjo": "แบนโจ", "Music.violin": "ไวโอลิน", "Music.guitar": "กีตาร์", "Music.flute": "ขลุ่ย", "Music.drum": "กลอง", "Music.choir": "คณะประสานเสียง", "Music.submitDisabled": "เรียกใช้โปรแกรมของคุณจนกว่าโปรแกรมของคุณจะหยุดทำงาน จากนั้นคุณสามารถส่งภาพวาดของคุณไปที่คลังภาพได้", "Music.galleryTooltip": "เปิดคลังเพลง", "Music.galleryMsg": "ดูคลังภาพ", "Music.submitTooltip": "ส่งเพลงของคุณไปที่คลังภาพ", "Music.submitMsg": "ส่งไปที่คลังภาพ", "Music.helpUseFunctions": "วิธีแก้ของคุณก็ถือว่าโอเคนะ แต่คุณสามารถทำได้ดีกว่านี้ ลองใช้ฟังก์ชันเพื่อลดจำนวนของโค้ดที่ซ้ำกันดู", "Music.helpUseInstruments": "เพลงจะฟังดูดีขึ้นหากคุณใช้เครื่องมือที่แตกต่างกันในแต่ละบล็อกเริ่มต้น", "Music.helpText1": "ให้ประพันธ์โน้ตดนตรี 4 ตัวแรกของ <PERSON><PERSON> ขึ้นมา", "Music.helpText2a": "ฟังก์ชันจะช่วยให้คุณสามารถจับกลุ่มบล็อกไว้ด้วยกัน แถมยังทำการแสดงผลได้มากกว่าหนึ่งครึ่งอีกด้วย", "Music.helpText2b": "สร้างฟังก์ชันขึ้นมาหนึ่งฟังก์ชันเพื่อที่จะเล่นโน้ตเพลงสี่ตัวแรกของ <PERSON><PERSON> Jacques จากนั้นให้ทำการแสดงผลของฟังก์ชันนั้นสองครั้งและห้ามเพิ่มบล็อกโน้ตดนตรีใดๆเข้าไป", "Music.helpText3": "สร้างฟังก์ชันที่สองสำหรับท่อนต่อไปของ <PERSON><PERSON> Jacques ซึ่งมีโน้ตตัวสุดท้ายเป็นโน้ตเสียงยาว", "Music.helpText4": "สร้างฟังก์ชันที่สามสำหรับท่อนต่อไปของ <PERSON><PERSON> Jacques โน้ตสี่ตัวแรกจะเป็นโน้ตเสียงสั้น", "Music.helpText5": "เล่นทำนองของ <PERSON><PERSON> Jacques ให้เสร็จโดยสมบูรณ์", "Music.helpText6a": "บล็อกใหม่นี้จะให้คุณสามารถเปลี่ยนไปใช้เครื่องดนตรีอื่นได้", "Music.helpText6b": "เล่นทำนองเพลงของคุณด้วยไวโอลิน", "Music.helpText7a": "บล็อกตัวใหม่นี้จะเพิ่มความหน่วงของเสียงเข้าไป", "Music.helpText7b": "ให้สร้างบล็อกตัวที่สองโดยที่แรกเริ่มจะมีบล็อกของความหน่วงอยู่สองตัวแล้วจากนั้นก็ให้เล่นทำนองเพลง <PERSON><PERSON>", "Music.helpText8": "แต่ละบล็อกจะเริ่มต้นด้วยการเล่น <PERSON><PERSON> Jacques สองครั้ง", "Music.helpText9": "สร้างบล็อกขึ้นมา 4 บล็อกโดยที่แต่ละบล็อกจะเริ่มต้นด้วยการเล่น <PERSON><PERSON> Jacques เป็นจำนวน 2 ครั้ง จากนั้นก็ให้เพิ่มการแก้ไขจำนวนของบล็อกที่ล่าช้าด้วย", "Music.helpText10": "จะแต่งเพลงอะไรก็ได้ที่ต้องการนอกจากนั้นคุณยังจะค้นพบว่าคุณได้รับบล็อกใหม่ๆเป็นจำนวนมากอีกด้วย ขอให้สนุกนะ!", "Music.helpText10Reddit": "ปุ่ม ดูคลังภาพ มีไว้สำหรับดูเพลงที่คนอื่นๆได้แต่งไป ถ้าคุณแต่งอะไรก็ตามที่คิดว่าน่าสนใจ ให้ใช้ปุ่ม ส่งไปที่คลังภาพ เพื่อที่จะเผยแพร่เพลงของคุณลงไปใน Gallery นั้น", "Pond.scanTooltip": "กวาดสายตาดูศัตรูและกำหนดทิศทาง(0-360) จากนั้นให้คืนค่ากลับเป็นระยะทางที่ใกล้ศัตรูที่สุดในทิศนั้นๆหรือถ้าหากตรวจดูแล้วไม่พบศัตรูก็ให้คืนค่ากลับเป็น infinity", "Pond.cannonTooltip": "ยิงปืนใหญ่ซะ อย่าลืมกำหนดทิศทาง(0-360)และขอบเขต(0-70)การยิงด้วยนะ", "Pond.swimTooltip": "จงว่ายน้ำไปข้างหน้าและกำหนดทิศทาง(0-360)ด้วย", "Pond.stopTooltip": "จงหยุดว่ายน้ำ ผู้เล่นจะว่ายช้าลงเพื่อที่จะสามารถหยุดได้", "Pond.healthTooltip": "ให้คืนค่ากลับมาเป็นค่าพลังชีวิตของผู้เล่น ณ ขณะนั้น (0 คือตาย , 100 คือแข็งแรงดี)", "Pond.speedTooltip": "ให้คืนค่ากลับมาเป็นความเร็วของผู้เล่น ณ ขณะนั้น (0 คือ หยุดเคลื่อนไหว , 100 คือเร่งความเร็วเต็มที่)", "Pond.locXTooltip": "คืนค่ากลับมาเป็นพิกัดในแนวแกนนอนของผู้เล่น (0 คือขอบด้านซ้ายสุด , 100 คือขอบด้านขวาสุด)", "Pond.locYTooltip": "คืนค่ากลับเป็นค่าพิกัดในแนวแกนตั้งของผู้เล่น (0 คือ ขอบด้านล่างสุด , 100 คือขอบด้านบนสุด)", "Pond.logTooltip": "แสดงผลตัวเลขบนคอนโซลของเบราว์เซอร์ของคุณ", "Pond.docsTooltip": "แสดงเอกสารอ้างอิงในเรื่องภาษา", "Pond.documentation": "เอกสารประกอบ", "Pond.playerName": "ผู้เล่น", "Pond.targetName": "เป้าหมาย", "Pond.pendulumName": "ลูกตุ้ม", "Pond.scaredName": "กลัว", "Pond.helpUseScan": "วิธีแก้ของคุณมันโอเคนะ แต่คุณสามารถทำได้ดีกว่านี้ ลองใช้ 'scan' เพื่อบอกระยะยิงให้กับปืนใหญ่ดูสิ", "Pond.helpText1": "ใช้ปืนใหญ่ในการสั่งยิงเป้าหมาย ตัวแปรแรกเลยคือมุม ตัวแปรที่สองคือขอบเขตการยิง จากนั้นก็ให้จับสองตัวแปรนี้มาคำนวณให้ได้ค่าที่ถูกต้อง", "Pond.helpText2": "สำหรับเป้าหมายนี้เราจะต้องยิงหลายๆครั้ง ดังนั้นให้ใช้ การวนรอบ'while (true)' เพื่อทำบางสิ่งที่ไม่สามารถกำหนดจำนวนครั้งได้อย่างแน่นอน", "Pond.helpText3a": "คู่ต่อสู้คนนี้จะเคลื่อนที่กลับไปกลับมา จึงทำให้ยากต่อการยิง ดังนั้นนิพจน์ 'scan' จะคืนค่าขอบเขตที่แท้จริงที่คู่ต่อสู้นั้นจะอยู่ในทิศทางยิงที่ถูกต้องและแม่นยำ", "Pond.helpText3b": "ขอบเขตนี้คือสิ่งที่คำสั่งปืนใหญ่ต้องการเพื่อที่จะสามารถยิงได้อย่างแม่นยำ", "Pond.helpText4": "คู่ต่อสู้อยู่ไกลเกินกว่าที่จะใช้ปืนใหญ่ (ซึ่งปืนใหญ่นี้มีขอบเขตจำกัดที่ 70 เมตร) ดังนั้นเราจึงใช้คำสั่งว่ายน้ำแทนเพื่อที่จะว่ายไปข้างหน้าและประทะกับคู่ต่อสู้โดยตรง", "Pond.helpText5": "คู่ต่อสู้อยู่ไกลเกินกว่าที่จะใช้ปืนใหญ่ แต่คุณก็อ่อนแรงเกินกว่าที่จะรอดจากการปะทะได้ ดังนั้นให้คุณว่ายน้ำไปยังด้านหน้าของคู่ต่อสู้ขณะที่ตำแหน่งในทิศแนวนอนของคุณอยู่น้อยกว่า 50 เมตร จากนั้นให้หยุดว่ายและใช้ปืนใหญ่ในการยิงคู่ต่อสู้", "Pond.helpText6": "คู่ต่อสู้เคลื่อนตัวออกไปหลังจากที่มันถูกยิง ดังนั้นเราจึงต้องว่ายน้ำมุ่งหน้าไปที่คู่ต่อสู้แต่ต้องเป็นในกรณีที่มันอยู่ห่างออกไปเกิน 70 เมตร", "Gallery": "ระเบียงภาพ"}