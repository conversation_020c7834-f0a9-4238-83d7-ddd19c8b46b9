{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Beta16", "Black Sky83", "Gbonanome", "<PERSON><PERSON><PERSON><PERSON>", "Isiond", "<PERSON>", "Nerimic", "Segaz3", "<PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON>", "Games.turtle": "Tartaruga", "Games.movie": "Film", "Games.music": "Musica", "Games.pondTutor": "Esercitazione stagno", "Games.pond": "Stagno", "Games.linesOfCode1": "Hai risolto questo livello con 1 riga di codice JavaScript:", "Games.linesOfCode2": "Hai risolto questo livello con %1 righe di codice JavaScript:", "Games.nextLevel": "Sei pronto per il livello %1?", "Games.finalLevel": "Sei pronto per la prossima sfida?", "Games.submitTitle": "<PERSON><PERSON>:", "Games.linkTooltip": "Salva e collega ai blocchi.", "Games.runTooltip": "Ese<PERSON>i il programma che hai scritto.", "Games.runProgram": "Esegui programma", "Games.resetTooltip": "Interrompi il programma e ripristina il livello.", "Games.resetProgram": "Reimposta", "Games.help": "<PERSON><PERSON>", "Games.catLogic": "Logica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematica", "Games.catText": "<PERSON><PERSON>", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "Colore", "Games.catVariables": "Variabili", "Games.catProcedures": "Funzioni", "Games.httpRequestError": "La richiesta non è stata soddisfatta.", "Games.linkAlert": "Condividi i tuoi blocchi con questo collegamento:\n\n%1", "Games.hashError": "<PERSON> spiace, '%1' non corrisponde ad alcun programma salvato.", "Games.xmlError": "Non è stato possibile caricare il documento.  Forse è stato creato con una versione diversa di Blockly?", "Games.submitted": "Grazie per questo programma! Se il nostro staff di scimmie addestrate lo gradirà, verrà pubblicato nella galleria entro un paio di giorni.", "Games.listVariable": "elenco", "Games.textVariable": "testo", "Games.breakLink": "Una volta che hai iniziato a modificare in JavaScript, non puoi tornare indietro a modificare i blocchi. Va bene?", "Games.blocks": "<PERSON><PERSON>", "Games.congratulations": "Complimenti!", "Games.helpAbort": "<PERSON>o livello è molto difficile. Desideri saltarlo e andare al successivo? Puoi sempre tornare indietro più tardi.", "Index.clear": "Cancella tutte le sue soluzioni?", "Index.subTitle": "Giochi per i programmatori di domani.", "Index.moreInfo": "Informazioni per gli educatori...", "Index.startOver": "Vuoi ricominciare?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://it.wikipedia.org/wiki/Anatra", "Puzzle.animal2": "Gatto", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://it.wikipedia.org/wiki/<PERSON><PERSON>_silves<PERSON><PERSON>_catus", "Puzzle.animal3": "Ape", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "Pungig<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://it.wikipedia.org/wiki/Anthophila", "Puzzle.animal4": "Lumaca", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://it.wikipedia.org/wiki/Chiocciola", "Puzzle.picture": "immagine:", "Puzzle.legs": "gambe:", "Puzzle.legsChoose": "scegli...", "Puzzle.traits": "caratteristiche:", "Puzzle.error0": "Perfetto!\nTutti i %1 blocchi sono corretti.", "Puzzle.error1": "Quasi! Un blocco non è corretto.", "Puzzle.error2": "%1 blocchi non sono corretti.", "Puzzle.tryAgain": "Il blocco evidenziato non è corretto.\nProva ancora.", "Puzzle.checkAnswers": "Controlla le risposte", "Puzzle.helpText": "Per ogni animale (verde), attaccaci la sua immagine, scegli il numero delle gambe e fai la lista dei suoi tratti.", "Maze.moveForward": "sposta in avanti", "Maze.turnLeft": "svolta a sinistra", "Maze.turnRight": "svolta a destra", "Maze.doCode": "fai", "Maze.helpIfElse": "Le istruzioni if-else cercheranno di compiere la prima azione o la seconda.", "Maze.pathAhead": "se c'è strada avanti", "Maze.pathLeft": "se c'è strada a sinistra", "Maze.pathRight": "se c'è strada a destra", "Maze.repeatUntil": "ripeti fino a", "Maze.moveForwardTooltip": "Avanza la pedina di una casella.", "Maze.turnTooltip": "Gira la pedina verso destra o sinistra di 90 gradi.", "Maze.ifTooltip": "Se c'è strada nella direzione indicata, allora fa' qualcosa.", "Maze.ifelseTooltip": "Se c'è strada nella direzione indicata, Allora esegui la prima serie di azioni. Altrimenti esegui la seconda serie di azioni.", "Maze.whileTooltip": "Ripeti l'azione scelta sino al punto di arrivo.", "Maze.capacity0": "Puoi usare altri %0 blocchi.", "Maze.capacity1": "Puoi usare %1 altro blocco.", "Maze.capacity2": "Puoi usare altri %2 blocchi.", "Maze.runTooltip": "Fa fare al giocatore ciò che dicono i blocchi", "Maze.resetTooltip": "Riposiziona il giocatore all'inizio del labirinto.", "Maze.helpStack": "Impila un paio di blocchi \"sposta in avanti\" per aiutarmi a raggiungere l'arrivo.", "Maze.helpOneTopBlock": "In questo livello dovrai impilare tutti i blocchi nella zona bianca.", "Maze.helpRun": "Esegui il programma per vedere cosa succede.", "Maze.helpReset": "Il programma non ha risolto il labirinto. Premi 'Reset' e prova di nuovo.", "Maze.helpRepeat": "I computer hanno una memoria limitata. Raggiungi l'arrivo usando solo due blocchi. Scegli 'ripeti' per eseguire un blocco più di una volta.", "Maze.helpCapacity": "Hai utilizzato tutti i blocchi per questo livello. Per creare un nuovo blocco, è necessario prima eliminare un blocco esistente.", "Maze.helpRepeatMany": "È possibile inserire più di un blocco all'interno di un blocco 'Ripeti'.", "Maze.helpSkins": "<PERSON><PERSON><PERSON> il tuo giocatore preferito da questo menu.", "Maze.helpIf": "Con 'se' l'azione verrà eseguita solo se la condizione è soddisfatta. Prova a girare a sinistra se c'è una strada a sinistra.", "Maze.helpMenu": "Fai clic su %1 nel blocco 'se' per cambiare la sua condizione.", "Maze.helpWallFollow": "Sai risolvere questo labirinto intricato? Prova a seguire il muro di sinistra. Solo per programmatori avanzati!", "Bird.noWorm": "non ha il verme", "Bird.heading": "direzione", "Bird.noWormTooltip": "La condizione quando l'uccello non ha preso il verme.", "Bird.headingTooltip": "Sposta nella direzione di un certo angolo: 0 sposta a destra orizzontalmente, 90 sposta perpendicolarmente verso l'alto e 270 perpendicolarmente verso il basso.", "Bird.positionTooltip": "x e y individuano la posizione dell'uccello. Quando x=0 l'uccello si trova nello spigolo a sinistra, quando x=100 è vicino allo spigolo a destra. Quando y=0 l'uccello si trova in fondo in basso, quando y=100 è in alto.", "Bird.helpHeading": "Modifica il valore dell'angolo nell'etichetta in modo che l'uccello possa raggiungere il verme e arrivare nel suo nido.", "Bird.helpHasWorm": "Usa questo blocco per andare in una direzione se hai il verme, o in una direzione differente se non hai il verme.", "Bird.helpX": "'x' è la posizione orizzontale attuale. Utilizza questo blocco per andare in una direzione se 'x' è minore di un numero, o altrimenti in una direzione diversa.", "Bird.helpElse": "Fai clic sull'icona per modificare il blocco 'se'.", "Bird.helpElseIf": "<PERSON>o livello ha bisogno sia di un blocco 'altrimenti se' sia di un blocco 'altrimenti'.", "Bird.helpAnd": "Il blocco 'e' è vero solo se entrambi i suoi input sono veri.", "Bird.helpMutator": "Trascina un blocco 'altrimenti' in un blocco 'se'.", "Turtle.moveTooltip": "Muove avanti e indietro la tartaruga secondo quanto specificato.", "Turtle.moveForward": "sposta in avanti di", "Turtle.moveBackward": "sposta indietro di", "Turtle.turnTooltip": "<PERSON>ira la tartaruga a destra o a sinistra, secondo l'angolazione suggerita.", "Turtle.turnRight": "gira a destra di", "Turtle.turnLeft": "gira a sinistra di", "Turtle.widthTooltip": "Cambia la larghezza della penna.", "Turtle.setWidth": "imposta la larghezza a", "Turtle.colourTooltip": "Cambia il colore della penna.", "Turtle.setColour": "imposta colore a", "Turtle.penTooltip": "Alza o abbassa la penna, per iniziare a disegnare o fermarti.", "Turtle.penUp": "penna su", "Turtle.penDown": "penna giù", "Turtle.turtleVisibilityTooltip": "Rende la tartaruga (cerchio con la freccia), visibile o invisibile.", "Turtle.hideTurtle": "nascondi tartaruga", "Turtle.showTurtle": "<PERSON>ra tartaruga", "Turtle.printHelpUrl": "https://it.wikipedia.org/wiki/Stampa", "Turtle.printTooltip": "disegna il testo nella direzione e alla posizione della tartaruga.", "Turtle.print": "stampa", "Turtle.fontHelpUrl": "https://it.wikipedia.org/wiki/Tipo_di_carattere", "Turtle.fontTooltip": "Imposta il tipo di carattere utilizzato dal blocco di stampa.", "Turtle.font": "tipo di carattere", "Turtle.fontSize": "dimensione carattere", "Turtle.fontNormal": "normale", "Turtle.fontBold": "grassetto", "Turtle.fontItalic": "corsivo", "Turtle.submitDisabled": "Esegui il programma fino alla fine. Successivamente potrai inviare il tuo disegno alla galleria.", "Turtle.galleryTooltip": "Apri la galleria dei disegni.", "Turtle.galleryMsg": "Vedi galleria", "Turtle.submitTooltip": "Invia il tuo disegno alla galleria.", "Turtle.submitMsg": "Invia alla galleria", "Turtle.helpUseLoop": "La tua soluzione funziona, ma puoi fare di meglio.", "Turtle.helpUseLoop3": "Disegna la forma usando solo tre blocchi.", "Turtle.helpUseLoop4": "Disegna la stella usando solo quattro blocchi.", "Turtle.helpText1": "Crea un programma che disegna un quadrato.", "Turtle.helpText2": "Cambia il tuo programma per disegnare un pentagono invece di un quadrato.", "Turtle.helpText3a": "C'è un nuovo blocco che ti permette di cambiare il colore:", "Turtle.helpText3b": "Disegna una stella gialla.", "Turtle.helpText4a": "C'è un nuovo blocco che ti permette di sollevare la penna dalla carta quando ti sposti.", "Turtle.helpText4b": "Disegna una piccola stella gialla, quindi disegna una linea sopra di essa.", "Turtle.helpText5": "Invece di una stella, riesci a disegnare quattro stelle disposte a quadrato?", "Turtle.helpText6": "Disegna tre stelle gialle e una linea bianca.", "Turtle.helpText7": "Disegna le stelle, quindi disegna quattro linee bianche.", "Turtle.helpText8": "Disegnando 360 linee bianche assomiglierà alla luna piena.", "Turtle.helpText9": "<PERSON><PERSON><PERSON> ad aggiungere un cerchio nero per far diventare la luna crescente?", "Turtle.helpText10": "Disegna quello che vuoi. Hai un sacco di nuovi blocchi che puoi esplorare. Divertiti!", "Turtle.helpText10Reddit": "Usa il pulsante \"Vedi galleria\" per vedere cosa hanno disegnato gli altri. Se disegni qualcosa di interessante usa il pulsante \"Invia alla galleria\" per pubblicarlo.", "Turtle.helpToolbox": "Scegli una categoria per vedere i blocchi.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "inizio X", "Movie.y1": "inizio y", "Movie.x2": "fine x", "Movie.y2": "fine y", "Movie.radius": "raggio", "Movie.width": "<PERSON><PERSON><PERSON><PERSON>", "Movie.height": "altezza", "Movie.circleTooltip": "Disegna un cerchio nella posizione specificata e con il raggio specificato.", "Movie.circleDraw": "cerchio", "Movie.rectTooltip": "Disegna un rettangolo nella posizione specificata e con la larghezza e l'altezza specificate.", "Movie.rectDraw": "rettangolo", "Movie.lineTooltip": "Disegna una linea da un punto ad un altro con la larghezza specificata.", "Movie.lineDraw": "linea", "Movie.timeTooltip": "Restituisce il tempo attuale dell'animazione (0-100).", "Movie.colourTooltip": "Cambia il colore della penna.", "Movie.setColour": "imposta colore a", "Movie.submitDisabled": "Il tuo filmato non si muove. Usa i blocchi per fare qualcosa di interessante. Successivamente potrai inviare il tuo filmato alla galleria.", "Movie.galleryTooltip": "Apri la galleria dei filmati.", "Movie.galleryMsg": "Vedi galleria", "Movie.submitTooltip": "Invia il tuo filmato alla galleria.", "Movie.submitMsg": "Invia alla galleria", "Movie.helpLayer": "Sposta il cerchio dello sfondo nella parte superiore del tuo programma. Quindi apparirà dietro la persona.", "Movie.helpText1": "Usa forme semplici per disegnare questa persona.", "Movie.helpText2a": "<PERSON><PERSON> livello è un film. Vuoi il braccio di una persona che si muove attraverso la finestra. <PERSON><PERSON> il tasto play per vedere un'anteprima.", "Movie.helpText2b": "Durante la riproduzione del filmato, il valore del blocco 'tempo' conta da 0 a 100. Poiché desideri che la posizione 'y' del braccio rosso inizi a 0 e finisca a 100, dovre<PERSON> essere facile.", "Movie.helpText3": "Il blocco 'tempo' conta da 0 a 100. Ma ora volta vuoi che la posizione 'y' dell'altro braccio inizi a 100 e finisca a 0. Hai in mente una semplice formula matematica che inverta la direzione?", "Movie.helpText4": "Utilizza quello che hai imparato nel livello precedente per fare le gambe che si incrociano.", "Movie.helpText5": "La formula matematica per il braccio è complicata. Ecco la soluzione:", "Movie.helpText6": "Dai alla persona un paio di mani.", "Movie.helpText7": "Usa il blocco 'if' per disegnare una piccola testa per la prima metà del film. Quindi disegna una grande testa per la seconda metà del film.", "Movie.helpText8": "Fai invertire la direzione delle gambe a metà del film.", "Movie.helpText9": "Disegna un cerchio in espansione dietro la persona.", "Movie.helpText10": "Fai un film su qualunque cosa tu voglia. Hai un numero enorme di nuovi blocchi che puoi esplorare. Buon divertimento!", "Movie.helpText10Reddit": "Usa il pulsante \"Vedi galleria\" per vedere i video creati da altri. Se crei qualcosa di interessante, usa il pulsante \"Invia alla galleria\" per pubblicarlo.", "Music.playNoteTooltip": "Riproduce una nota musicale della durata e del tono specificato.", "Music.playNote": "riproduce nota %2 per %1", "Music.restTooltip": "Aspetta la durata specificata.", "Music.restWholeTooltip": "Aspetta una nota completa.", "Music.setInstrumentTooltip": "Passa allo strumento specificato mentre sta suonando note musicali.", "Music.setInstrument": "imposta lo strumento a %1", "Music.startTooltip": "Aziona i blocchi all'interno quando il pulsante \"Esegui programma\" viene cliccato.", "Music.start": "quando %1 viene cliccato", "Music.pitchTooltip": "<PERSON> nota (C4 è 7).", "Music.firstPart": "prima parte", "Music.piano": "pianoforte", "Music.trumpet": "tromba", "Music.banjo": "banjo", "Music.violin": "violino", "Music.guitar": "chitarra", "Music.flute": "flauto", "Music.drum": "tamburo", "Music.choir": "coro", "Music.submitDisabled": "Esegui il programma fino a che non termina. Successivamente potrai pubblicare la tua musica sulla galleria.", "Music.galleryTooltip": "Apri la galleria della musica.", "Music.galleryMsg": "Vedi galleria", "Music.submitTooltip": "Invia la tua musica alla galleria.", "Music.submitMsg": "Invia alla galleria", "Pond.scanTooltip": "Cerca i nemici. Specifica una direzione (0-360). Restituisce la distanza dal nemico più vicino in quella direzione. Restituisce Infinito se non trova nessun nemico.", "Pond.cannonTooltip": "Spara il cannone. Specifica una direzione (0-360) e una gittata (0-70).", "Pond.swimTooltip": "Nuotare in avanti. Specifica una direzione (0-360).", "Pond.stopTooltip": "Smettere di nuotare. Il giocatore rallenterà sino a fermarsi.", "Pond.healthTooltip": "Restituisce la salute attuale del giocatore (0 è morto, 100 è sano).", "Pond.speedTooltip": "Restituisce la velocità attuale del giocatore (0 è fermo, 100 è alla velocità massima).", "Pond.locXTooltip": "Restituisce la coordinata X del giocatore (0 è il bordo sinistro, 100 è il bordo destro).", "Pond.locYTooltip": "Restituisce la coordinata Y del giocatore (0 è il bordo inferiore, 100 è il bordo superiore).", "Pond.logTooltip": "Stampa un numero sulla console del tuo browser.", "Pond.docsTooltip": "Mostra la documentazione in lingua.", "Pond.documentation": "Documentazione", "Pond.playerName": "Giocatore", "Pond.targetName": "Obiettivo", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "La tua soluzione funziona, ma puoi fare di meglio. Usa 'scan' per dire al cannone quanto lontano deve sparare.", "Pond.helpText1": "Utilizza il comando 'cannon' per colpire il bersaglio. Il primo parametro è l'angolo, il secondo parametro è la gittata. Trova la combinazione giusta.", "Pond.helpText2": "Questo obiettivo deve essere colpito molte volte. Utilizza un ciclo 'while (true)' per fare qualcosa per un tempo indefinito.", "Pond.helpText3a": "Questo avversario si muove avanti e indietro, il che lo rende difficile da colpire. L'espressione 'scan' restituisce la distanza esatta dall'avversario nella direzione specificata.", "Pond.helpText3b": "Questa gittata è esattamente ciò di cui ha bisogno il comando 'cannon' per fare fuoco con precisione.", "Pond.helpText4": "Questo avversario è troppo lontano per usare il cannone (che ha un limite di 70 metri). Utilizza, invece, il comando 'swim' per iniziare a nuotare verso l'avversario e scontrarcisi.", "Pond.helpText5": "Anche questo avversario è troppo lontano per usare il cannone. Ma sei troppo debole per sopravvivere a una collisione. Nuota verso l'avversario mentre la tua posizione orizzontale è minore di 50. Poi fermati ('stop') e usa il cannone.", "Pond.helpText6": "Questo avversario si allontanerà quando verrà colpito. Nuota verso di esso se è fuori della tua portata (70 metri).", "Gallery": "Galleria"}