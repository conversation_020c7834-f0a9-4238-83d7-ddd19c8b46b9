{"@metadata": {"authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Kuwaity26", "Meno25", "Mgi92", "MuratTheTurkish", "<PERSON><PERSON>", "Test Create account", "دي<PERSON><PERSON><PERSON>"]}, "Games.name": "الالعاب الكتلية", "Games.puzzle": "أحجية", "Games.maze": "المتاهة", "Games.bird": "طائر", "Games.turtle": "السلحفاة", "Games.movie": "فلم", "Games.music": "موسيقى", "Games.pondTutor": "مدرب البركة", "Games.pond": "البركة.", "Games.linesOfCode1": "لقد وجدت حل هذا المستوى بسطر واحد من جافا سكريبت :", "Games.linesOfCode2": "لقد حللت هذا المستوى بـ %1 أسطر من جافا سكريبت :", "Games.nextLevel": "هل أنت جاهز للمستوى %1 ؟", "Games.finalLevel": "هل أنت مستعد للتحدي التالي؟", "Games.submitTitle": "العنوان:", "Games.linkTooltip": "احفظ وأعطي وصلة إلى البلوكات.", "Games.runTooltip": "لتشغيل البرنامج الذي كتبته.", "Games.runProgram": "شغِّل البرنامج", "Games.resetTooltip": "لايقا<PERSON> البرنامج وأعادة تعيين المرحلة.", "Games.resetProgram": "إعادة ضبط", "Games.help": "مساعدة", "Games.catLogic": "منطق", "Games.catLoops": "الحلقات", "Games.catMath": "رياضيات", "Games.catText": "نص", "Games.catLists": "قوائم", "Games.catColour": "لون", "Games.catVariables": "متغيرات", "Games.catProcedures": "إجراءات", "Games.httpRequestError": "كانت هناك مشكلة مع هذا الطلب.", "Games.linkAlert": "مشاركة كود بلوكلي الخاص بك مع هذا الرابط:\n %1", "Games.hashError": "عذراً،ال '%1' لا تتوافق مع أي برنامج تم حفظه.", "Games.xmlError": "تعذر تحميل الملف المحفوظة الخاصة بك.  ربما تم إنشاؤه باستخدام إصدار مختلف من بلوكلي؟", "Games.submitted": "شكرا لك على هذا البرنامج! إذا احبه فريقنا من القرود المدربين، فسوف ينشرونه ضمن المعرض في غضون ايام.", "Games.listVariable": "قائمة", "Games.textVariable": "نص", "Games.breakLink": "بمجرد البدء في التعديل على الجافا سكريبت، لا يمكنك الرجوع إلى التعديل على البلوكات. هل هذا جيد؟", "Games.blocks": "البلوكات", "Games.congratulations": "تهانينا!", "Games.helpAbort": "هذه المرحلة صعبة للغاية، هل ترغب في تخطي ذلك والذهاب إلى اللعبة التالية؟ يمكنك دائما العودة في وقت لاحق.", "Index.clear": "حذ<PERSON> كل حلولك؟", "Index.subTitle": "ألعاب لمبرمجي الغد.", "Index.moreInfo": "معلومات للمعلمين...", "Index.startOver": "هل ترغب في البدأ من جديد؟", "Index.clearData": "ام<PERSON> البيانات", "Puzzle.animal1": "بطة", "Puzzle.animal1Trait1": "ريش", "Puzzle.animal1Trait2": "منق<PERSON>ر", "Puzzle.animal1HelpUrl": "https://ar.wikipedia.org/wiki/بط", "Puzzle.animal2": "قطة", "Puzzle.animal2Trait1": "شارب", "Puzzle.animal2Trait2": "فرو", "Puzzle.animal2HelpUrl": "https://ar.wikipedia.org/wiki/قط", "Puzzle.animal3": "نحلة", "Puzzle.animal3Trait1": "عسل", "Puzzle.animal3Trait2": "اللاسع", "Puzzle.animal3HelpUrl": "https://ar.wikipedia.org/wiki/نحل", "Puzzle.animal4": "حلزون", "Puzzle.animal4Trait1": "صدفة", "Puzzle.animal4Trait2": "مخاط", "Puzzle.animal4HelpUrl": "https://ar.wikipedia.org/wiki/حلزون_(حيو<PERSON>)", "Puzzle.picture": "الصورة:", "Puzzle.legs": "أرجل:", "Puzzle.legsChoose": "اختر...", "Puzzle.traits": "سمة:", "Puzzle.error0": "ممتاز!\nكل الكتل %1 صحيحة.", "Puzzle.error1": "اقتربت! كتلة واحدة غير صحيح.", "Puzzle.error2": "%1 كتل غير صحيحة.", "Puzzle.tryAgain": "الكتلّة المُبرَزَة غير صحيحة.\nواصل المحاولة.", "Puzzle.checkAnswers": "تحقّق من الإجابات", "Puzzle.helpText": "لكل حيوان(بالأخضر) صل صورته و اختر عدد ارجله و اجمع سماته.", "Maze.moveForward": "تحرّك إلى الأمام", "Maze.turnLeft": "استدر إلى اليسار", "Maze.turnRight": "استدر الى اليمين", "Maze.doCode": "افعل", "Maze.helpIfElse": "كتل (إذا-وإلا) ستفعل شيئا محددا أو شيئا آخر.", "Maze.pathAhead": "إذا هناك مسار في الأمام", "Maze.pathLeft": "إذا كان المسار إلى اليسار", "Maze.pathRight": "إذا كان المسار إلى اليمين", "Maze.repeatUntil": "كرِّر حتى", "Maze.moveForwardTooltip": "يحرك اللاعب مسافة واحدة إلى الأمام.", "Maze.turnTooltip": "يدور اللاعب الى اليمين أو اليسار بمقدار 90 درجة.", "Maze.ifTooltip": "إذا كان هناك مسار في الاتجاه المحدد، قم ببعض الإجراءات.", "Maze.ifelseTooltip": "اذا كان هناك مسار في الاتجاه المٌحدد، يٌمكنك تنفيذ أول بلوك من الأوامر، والا قم بتنفيذ البلوك الثاني من الأوامر.", "Maze.whileTooltip": "قم بتكرار العمليات المحددة حتى الوصول إلى نقطة النهاية.", "Maze.capacity0": "لديك %0 بلوكات متبقية.", "Maze.capacity1": "لديك %1 بلوك متبقي.", "Maze.capacity2": "لديك %2 بلوك متبقي.", "Maze.runTooltip": "يجعل اللاعب يقوم بما تقوله البلوكات.", "Maze.resetTooltip": " استرجاع اللاعب الى  بداية المتاهة.", "Maze.helpStack": "الصق بلوكتي \"تحرك إلى الأمام\" معا لمساعدتي في بلوغ الهدف.", "Maze.helpOneTopBlock": "في هذه المرحلة, ستحتاج الى تجميع كل البلوكات مع بعضها البعض في مساحة العمل البيضاء.", "Maze.helpRun": "قم بتشغيل البرنامج الخاص بك لرؤية ما يحدث.", "Maze.helpReset": "برنامجك لم يحل المتاهة.  اضغط 'إعادة' وحاول مرة أخرى.", "Maze.helpRepeat": "أبلغ نهاية المسار باستخدام بلوكتين فقط. استخدم 'كرر حتى' لتشغيل الكتلة أكثر من مرة.", "Maze.helpCapacity": "لقد قمت باستخدام جميع الكتل في هذه المرحلة. لعمل كتلة جديدة، عليك اولا مسح احد الكتل الموجودة.", "Maze.helpRepeatMany": "يٌمكنك ضبط اكثر من كتلة واحدة داخل كتلة 'كرر حتى'.", "Maze.helpSkins": "اختر لاعبك المفضل من هذه القائمة.", "Maze.helpIf": "كتلة 'إذا ستقوم بعمل شيء ما في حالة كن الشرط صحيحا. حاول الدوران إلى اليسار إذا كان هناك مسار إلى اليسار.", "Maze.helpMenu": "انقر على %1 في كتلة \"إذا\" لتغيير الحالة.", "Maze.helpWallFollow": "هل يمكنك حل هذه المتاهة المعقدة؟  حاول ان تلحق الجدار الأيسر.  للمبرمجين المتقدمين فقط!", "Bird.noWorm": "ليس لديه دودة", "Bird.heading": "يشير", "Bird.noWormTooltip": "الطير يريد الحصول على دودة.", "Bird.headingTooltip": "التحرك في اتجاه زاوية معينة: 0 إلى اليمين و90 على التوالي، إلخ", "Bird.positionTooltip": "س و ص يحددون مكان الطائر فعندما تكون س = 0 يكون الطائر عند اليسار بينما عند س = 100 يكون عند اليمين. وعندما تكون ص = 0 يكون الطائر عند الاسفل اما عند ص = 100 يكون عند الأعلى.", "Bird.helpHeading": "تغيير اتجاه الزاوية لجعل الطائر يحصل على دودة وأرض في عشه.", "Bird.helpHasWorm": "استخدم هذه المجموعة للتحرك في اتجاه واحد إذا كانت لديك دودة، أو اتجاه مختلف إذا لم تكن لديك دودة.", "Bird.helpX": "\"x\" هو وضعك الأفقي الحالي، استخدم هذا الحظر للانتقال في عنوان واحد إذا كان \"x\" أقل من رقم، أو عنوان مختلف خلاف ذلك.", "Bird.helpElse": "انقر فوق الرمز لتعديل المجموعة \"if\".", "Bird.helpElseIf": "هذا المستوى يحتاج كلا من المجموعة \"else if\" والمجموعة  \"else'.", "Bird.helpAnd": "المجموعة \"and\" صحيحة فقط إذا كانت كل مدخلاتها صحيحة.", "Bird.helpMutator": "سحب المجموعة 'else' في المجموعة 'if'.", "Turtle.moveTooltip": "تحريك السلحفاة إلى الأمام أو إلى الخلف بمقدار محدد.", "Turtle.moveForward": "انتقل إلى الأمام بواسطة", "Turtle.moveBackward": "انتقل إلى الخلف بواسطة", "Turtle.turnTooltip": "تحويل السلحفاة الى الأيمن أو الأيسر بعدد معين من الدرجات.", "Turtle.turnRight": "استدر غلى اليمين بواسطة", "Turtle.turnLeft": "استدر إلى اليسار بواسطة", "Turtle.widthTooltip": "تغيير عرض القلم.", "Turtle.setWidth": "تعيين العرض إلى", "Turtle.colourTooltip": "تغيير لون القلم.", "Turtle.setColour": "تعيين اللون إلى", "Turtle.penTooltip": "يرفع أو يخفض القلم، ليتم إيقاف أو بدء الرسم.", "Turtle.penUp": "رفع القلم", "Turtle.penDown": "انزال القلم", "Turtle.turtleVisibilityTooltip": "جعل السلحفاة (دائرة وسهم) مرئية أو غير مرئية.", "Turtle.hideTurtle": "إخفاء السلحفاة", "Turtle.showTurtle": "إظهار السلحفاة", "Turtle.printHelpUrl": "https://ar.wikipedia.org/wiki/طباعة", "Turtle.printTooltip": "رسم/طباعة النص في في موقع اتجاه سلحفاة .", "Turtle.print": "إطبع", "Turtle.fontHelpUrl": "https://ar.wikipedia.org/wiki/خط_(كتابة)", "Turtle.fontTooltip": "يضبط الخط المستخدم بواسطة بلوك الطباعة.", "Turtle.font": "الخط", "Turtle.fontSize": "حج<PERSON> الخط", "Turtle.fontNormal": "عادي", "Turtle.fontBold": "غامق", "Turtle.fontItalic": "مائل", "Turtle.submitDisabled": "تشغيل البرنا<PERSON>ج حتى يتوقف، ثم يمكنك تقديم الرسم إلى المعرض.", "Turtle.galleryTooltip": "افتح معرض الرسومات.", "Turtle.galleryMsg": "انظر معرض الصور", "Turtle.submitTooltip": "أرسل رسمك إلى المعرض.", "Turtle.submitMsg": "إرسال إلى معرض", "Turtle.helpUseLoop": "حلك يعمل، لكن يمكنك أن تقوم بالأفضل.", "Turtle.helpUseLoop3": "رسم الشكل في ثلاث مجموعات فقط.", "Turtle.helpUseLoop4": "رسم النجم في أربع مجموعات فقط.", "Turtle.helpText1": "إنشاء البرنامج الذي يرسم مربعا.", "Turtle.helpText2": "تغيير البرنامج لرسم خماسي الأضلاع بدلا من المربع.", "Turtle.helpText3a": "هناك مجموعة جديدة تسمح لك بتغيير اللون:", "Turtle.helpText3b": "إرسم نجمة صفراء.", "Turtle.helpText4a": "هناك مجموعة جديدة تسمح لك برفع القلم بعيدا عن الورقة عند الانتقال:", "Turtle.helpText4b": "رسم نجمة صفراء صغيرة، ثم رسم خط فوقها.", "Turtle.helpText5": "بدلا من نجم واحد، هل يمكنك رسم النجوم الأربعة على شكل مربع؟", "Turtle.helpText6": "رسم ثلاثة نجوم صفراء، وخط واحد أبيض.", "Turtle.helpText7": "رسم النجوم، ثم رسم أربعة خطوط بيضاء.", "Turtle.helpText8": "رسم 360 خطًا بيضاء سيبدون كالقمر الكامل.", "Turtle.helpText9": "هل يمكنك إضافة دائرة سوداء بحيث يصبح القمر هلالا؟", "Turtle.helpText10": "ارسم أي شيء تريده; كنت قد حصلت على عدد كبير من القطع الجديدة التي يمكن استكشافها.استمتع!", "Turtle.helpText10Reddit": "استخدم الزر \"؛مشاهدة المعرض\" لرؤية ما رسمه الآخرون، إذا رسمت شيئا مثيرا للاهتمام، فاستخدم الزر \"إرسال إلى المعرض\" لنشره.", "Turtle.helpToolbox": "اختر تصنيفًا لرؤية المجموعة.", "Movie.x": "س", "Movie.y": "ص", "Movie.x1": "بداية س", "Movie.y1": "بداية ص", "Movie.x2": "نهاية س", "Movie.y2": "نهاية ص", "Movie.radius": "نصف القطر", "Movie.width": "العرض", "Movie.height": "الارتفاع", "Movie.circleTooltip": "إرسم دائرةفي المكان المحدد وبنصف القطر المحدد.", "Movie.circleDraw": "دائرة", "Movie.rectTooltip": "إرسم مستطيل في المكان المحدد وبالطول والعرض المحددين.", "Movie.rectDraw": "مستطيل", "Movie.lineTooltip": "إرسم خط من نقطة الى نقطة اخرى وبالعرض المحدد.", "Movie.lineDraw": "خط", "Movie.timeTooltip": "إرجاع الوقت الحالي في الرسوم المتحركة (0-100).", "Movie.colourTooltip": "تغيير لون القلم.", "Movie.setColour": "تعيين اللون إلى", "Movie.submitDisabled": "الفيلم لا يتحرك; استخدم مجموعة لصنع شيئا مثيرا للاهتمام، ثم يمكنك تقديم فيلمك إلى المعرض.", "Movie.galleryTooltip": "افتح معرض الأفلام.", "Movie.galleryMsg": "انظر معرض الصور", "Movie.submitTooltip": "أرسل فيلمك إلى المعرض.", "Movie.submitMsg": "إرسال إلى معرض", "Movie.helpLayer": "انقل الدائرة الخلفية إلى أعلى البرنامج، ثم سوف تظهر خلف الشخص.", "Movie.helpText1": "استخدام الأشكال البسيطة لرسم هذا الشخص.", "Movie.helpText2a": "هذا المستوى فيلم، تريد أن يتحرك ذراع الشخص عبر الشاشة، اضغط على زر التشغيل لرؤية معاينة.", "Movie.helpText2b": "أثناء تشغيل الفيلم، يتم حساب قيمة كتلة \"الوقت\" من 0 إلى 100، بما أنك تريد أن يبدأ الوضع 'y' عند 0 والذهاب إلى 100، يجب أن يكون ذلك سهلا.", "Movie.helpText3": "يتم حساب كتلة \"الوقت\" من 0 إلى 100، ولكنك الآن تريد أن يبدأ الوضع \"y\" في الذراع الأخرى عند 100 ويذهب إلى 0، هل يمكنك معرفة صيغة رياضية بسيطة تقلب الاتجاه؟", "Movie.helpText4": "استخدم ما تعلمته في المستوى السابق لجعل الساقين تتقاطعان.", "Movie.helpText5": "الصيغة الرياضية للذراع معقدة، وهنا الجواب:", "Movie.helpText6": "امنح الشخص يدين.", "Movie.helpText7": "استخدم الكتلة \"if\" لرسم رأس صغير للنصف الأول من الفيلم، ثم ارسم رأسا كبيرا للنصف الثاني من الفيلم.", "Movie.helpText8": "جعل الساقين في الاتجاه المعاكس في منتصف الطريق من خلال الفيلم.", "Movie.helpText9": "ارسم دائرة متوسعة خلف الشخص.", "Movie.helpText10": "اصنع فيلم من أي شيء تريده; كنت قد حصلت على عدد كبير من القطع الجديدة التي يمكن استكشافها.استمتع!", "Movie.helpText10Reddit": "استخدم الزر \"؛مشاهدة المعرض\" لمشاهدة الأفلام التي صنعها أشخاص آخرون، إذا صنعت فيلما مثيرا للاهتمام، فاستخدم الزر \"إرسال إلى المعرض\" لنشره.", "Music.playNoteTooltip": "يقوم بتشغيل ملاحظة موسيقية واحدة للمدة المحددة و درجة الصوت.", "Music.playNote": "تشغيل %1 ملاحظة %2", "Music.restTooltip": "ينتظر لمدة محددة.", "Music.restWholeTooltip": "ينتظر ملاحظة واحدة كاملة.", "Music.rest": "الباقي %1", "Music.setInstrumentTooltip": "التبديل إلى الجها<PERSON> المحدد عند تشغيل الدرجات الموسيقية التالية.", "Music.setInstrument": "ضبط آلة موسيقية على %1", "Music.startTooltip": "ينفذ الكتل الداخلية عندما يتم النقر على زر \"تشغيل البرنامج\".", "Music.start": "عند النقر على %1", "Music.pitchTooltip": "درجة واحدة (C4 تكون 7).", "Music.firstPart": "الجز<PERSON> الاول", "Music.piano": "بيانو", "Music.trumpet": "بوق", "Music.banjo": "بانجو", "Music.violin": "كمان", "Music.guitar": "قيثارة", "Music.flute": "مزمار", "Music.drum": "طبل", "Music.choir": "جوقة", "Music.submitDisabled": "قم بتشغيل البرنامج حتى يتوقف، ثم يمكنك إرسال موسيقاك إلى المعرض.", "Music.galleryTooltip": "افتح معرض الموسيقى.", "Music.galleryMsg": "<PERSON><PERSON><PERSON><PERSON> المعرض", "Music.submitTooltip": "أرسل موسيقاك إلى المعرض.", "Music.submitMsg": "إرسال إلى المعرض", "Music.helpUseFunctions": "يعمل حلك، ولكن يمكنك القيام به بشكل أفضل، استخدم الدالات لتقليل مقدار الكود المتكرر.", "Music.helpUseInstruments": "ستظهر الموسيقى بشكل أفضل إذا كنت تستخدم آلةة مختلفة في كل كتلة بداية.", "Music.helpText1": "يل<PERSON>ن الملاحظات الأربعة الأولى من 'الأخ جيمس'.", "Music.helpText2a": "تتيح لك \"الوظيفة\" تجميع الكتل معا، ثم تشغيلها أكثر من مرة.", "Music.helpText2b": "أنشئ وظيفة لتشغيل الدرجات الأربعة الأولى من 'الأخ جيمس'، تشغيل هذه الوظيفة مرتين، لا تضف أية كتل ملاحظات جديدة.", "Music.helpText3": "أنشئ وظيفة ثانية للجزء التالي من 'الأخ جيمس'، الدرجة الأخيرة أطول.", "Music.helpText4": "أنشئ وظيفة ثالثة للجزء التالي من 'الأخ جيمس'، الدرجات الأربع الأولى أقصر.", "Music.helpText5": "أكمل اللحن الكامل لـ'الأخ جيمس'.", "Music.helpText6a": "تتيح لك هذه المجموعة الجديدة التغيير إلى آلة أخرى.", "Music.helpText6b": "تشغيل نغمتك مع كمان.", "Music.helpText7a": "تضيف هذه الكتلة الجديد تأخيرا صامتا.", "Music.helpText7b": "إنشاء كتلة بداية ثانية تحتوي على كتلتي تأخير، ثم تشغل أيضا 'الأخ جاك'.", "Music.helpText8": "كل بداية كتلة يجب أن تشغل 'الأخ جاك' مرتين.", "Music.helpText9": "أنشئ أربع كتل بداية تشغل كل منها 'الأخ جاك' مرتين، إضافة العدد الصحيح من كتل التأخير.", "Music.helpText10": "يلحن أي شيء تريده، لديك عدد كبير من الكتل الجديدة التي يمكنك استكشافها. استمتع!", "Music.helpText10Reddit": "استخدم الزر \"مشاهدة المعرض\" لرؤية ما لحنه الآخرون، إذا أنشأت شيئا مثيرا للاهتمام، فاستخدم الزر \"إرسال إلى المعرض\" لنشره.", "Pond.scanTooltip": "مسح للأعداء. تحديد الاتجاه (0-360). يعيد المسافة إلى أقرب العدو في هذا الاتجاه. يعود إلى ما لا نهاية إذا لم يجد أي عدو.", "Pond.cannonTooltip": "إطلاق المدفع. تحديد الاتجاه (0-360) ومجموعة (0-70).", "Pond.swimTooltip": "السباحة إلى الأمام. تحديد الاتجاه (0-360).", "Pond.stopTooltip": "أوق<PERSON> السباحة. اللاعب يتباطأ حتى يتوقف.", "Pond.healthTooltip": "يعيد الصحة الحالية اللاعب (0 ميت، 100 غير صحي).", "Pond.speedTooltip": "يرجع السرعة الحالية للاعب (0 توقف، 100 سرعة كاملة).", "Pond.locXTooltip": "يرجع تنسيق اللاعب X (0 الحافة اليسرى، و 100 الحافة اليمنى).", "Pond.locYTooltip": "يرجع تنسيق اللاعب Y (0 الحافة السفلية، و 100 الحافة العلوية).", "Pond.logTooltip": "يطبع رقما لمتصفحك.", "Pond.docsTooltip": "عرض وثائق اللغة.", "Pond.documentation": "توثيق", "Pond.playerName": "اللاعب", "Pond.targetName": "الهدف", "Pond.pendulumName": "رقاص الساعة", "Pond.scaredName": "خائف", "Pond.helpUseScan": "حلك يعمل، لكن يمكنك أن تقوم بالأفضل. استخدم 'مسح' لتقول للمدفع إلى أي مدى يطلق النار.", "Pond.helpText1": "استخدم الأمر \"مدفع\" لضرب الهدف. المعلمة الأولى زاوية، والمعلمة الثانية مدى. جد التركيبة الصحيحة.", "Pond.helpText2": "يحتاج هذا الهدف لأن يتم ضربه عدة مرات، استخدم حلقة \"while true)\" لتفعل شيئا لأجل غير مسمى.", "Pond.helpText3a": "هذا الخصم يتحرك ذهابا وإيابا، مما يجعل من الصعب ضربه. التعبير \"مسح\" يعيد المدى المحدد لمنافسه في الاتجاه المحدد.", "Pond.helpText3b": "هذا النطاق هو بالضبط ما يحتاجه \"مدفع\" الأوامر لإطلاق النار بدقة.", "Pond.helpText4": "هذا الخصم بعيد جدا لاستخدام مدفع (التي لديها حدود 70 مترا)، بدلا من ذلك، استخدم الأمر 'سباحة' لبدء السباحة نحو الخصم وتحطيمه.", "Pond.helpText5": "هذا الخصم هو أيضا بعيد لاستخدام المدفع، ولكنك أضعف من أن تنجو من التصادم; اسبح نحو الخصم بينما أنت في وضع أفقي أقل من 50، ثم \"قف\" واستخدم المدفع.", "Pond.helpText6": "وهذا الخصم الابتعاد عندما ضُرِب، اسبح نحوه إذا كان خارج النطاق (70 متر).", "Gallery": "معر<PERSON>"}