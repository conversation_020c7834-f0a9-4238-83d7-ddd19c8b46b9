{"@metadata": {"authors": ["Chmee2", "<PERSON>lon", "<PERSON><PERSON><PERSON>", "Dvorapa", "Emis-cz", "Ilimanaq29", "<PERSON><PERSON><PERSON>", "Michaelbrabec", "Robins7", "Vorkronor"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Skládačka", "Games.maze": "Bludiště", "Games.bird": "Pták", "Games.turtle": "Želva", "Games.movie": "Film", "Games.pondTutor": "Rybník - cvičení", "Games.pond": "Rybník", "Games.linesOfCode1": "Vyřešil jste úroveň jednou řádkou JavaScriptu:", "Games.linesOfCode2": "Vyřešil jste úroveň %1 řádkami JavaScriptu:", "Games.nextLevel": "Připraveni na úroveň %1?", "Games.finalLevel": "Připraveni na další výzvu?", "Games.linkTooltip": "<PERSON><PERSON><PERSON> a spoj bloky..", "Games.runTooltip": "Spustí program, který jste napsali.", "Games.runProgram": "Spusť program", "Games.resetTooltip": "Zastaví program a vrátí úroveň do výchozího stavu.", "Games.resetProgram": "Reset", "Games.help": "Nápověda", "Games.catLogic": "Logika", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "<PERSON><PERSON>", "Games.catText": "Text", "Games.catLists": "Seznamy", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "Procedury", "Games.httpRequestError": "Došlo k potížím s požadavkem.", "Games.linkAlert": "Sdílej bloky tímto odkazem: \n\n%1", "Games.hashError": "Omlouváme se, '%1' nesouhlasí s žádným z uložených souborů.", "Games.xmlError": "Nepodařilo se načíst váš uložený soubor. Pravděpodobně byl vytvořen jinou verz<PERSON>?", "Games.listVariable": "seznam", "Games.textVariable": "text", "Games.breakLink": "Jakmile začnete upravovat JavaScript, nemůžete se vrátit k editaci bloků. Je to v POŘÁDKU?", "Games.blocks": "Bloky", "Games.congratulations": "Gratulace!", "Games.helpAbort": "<PERSON>to <PERSON> je velmi obtížná. Chceš ji přeskočit a pokračovat na další hru? Kdykoli se můžeš vrátit zpět.", "Index.clear": "Smazat všechna řešení?", "Index.subTitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Index.moreInfo": "Informace pro vyučující...", "Index.startOver": "<PERSON><PERSON>š začít znovu?", "Index.clearData": "Vymazat data", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "Peří", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://cs.wikipedia.org/wiki/Ka<PERSON>na", "Puzzle.animal2": "Kočka", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Srst", "Puzzle.animal2HelpUrl": "https://cs.wikipedia.org/wiki/Ko%C4%8Dka_dom%C3%A1c%C3%AD", "Puzzle.animal3": "Včela", "Puzzle.animal3Trait1": "Med", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://cs.wikipedia.org/wiki/V%C4%8Dela", "Puzzle.animal4": "Šnek", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Sliz", "Puzzle.animal4HelpUrl": "https://cs.wikipedia.org/wiki/Hlem%C3%BD%C5%BE%C4%8F_zahradn%C3%AD", "Puzzle.picture": "obrázek:", "Puzzle.legs": "nohy:", "Puzzle.legsChoose": "vyber...", "Puzzle.traits": "vlastnosti:", "Puzzle.error0": "Výborně!\nVšech %1 bloků je umístěno správně.", "Puzzle.error1": "Skoro! Jeden blok je špatně.", "Puzzle.error2": "%1 bloky jsou chybně.", "Puzzle.tryAgain": "Zvýrazněný blok není správně.\n<PERSON><PERSON><PERSON><PERSON><PERSON> to dál.", "Puzzle.checkAnswers": "Zkontrolovat odpovědi", "Puzzle.helpText": "Ke každému <PERSON> (zeleně) přiřaď obrázek, počet nohou a seznam vlastností.", "Maze.moveForward": "p<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "Maze.turnLeft": "<PERSON><PERSON><PERSON><PERSON>", "Maze.turnRight": "otočit doprava", "Maze.doCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Maze.helpIfElse": "<PERSON><PERSON><PERSON><PERSON> 'pokud-jinak' <PERSON>e buď něco, nebo něco jiného.", "Maze.pathAhead": "pokud cesta vpřed", "Maze.pathLeft": "pokud cesta doleva", "Maze.pathRight": "pokud cesta doprava", "Maze.repeatUntil": "opakuj až do", "Maze.moveForwardTooltip": "Pohne hráčem vpřed o jedno pole.", "Maze.turnTooltip": "Otočí hráče vlevo nebo vpravo o 90 stupňů.", "Maze.ifTooltip": "Pokud je v daném směru cesta, pak proveď nějakou akci.", "Maze.ifelseTooltip": "Pokud je v danném směru cesta, pak proveď posloupnost akcí. V opačném případě proveď druhou posloupnost akcí.", "Maze.whileTooltip": "Opakuj obsažené akce do té doby, dokud není <PERSON>en cílový bod.", "Maze.capacity0": "Počet zbývajících bloků %0.", "Maze.capacity1": "Počet zbývajících bloků %1.", "Maze.capacity2": "Počet zbývajících bloků %2.", "Maze.runTooltip": "<PERSON><PERSON><PERSON><PERSON> dě<PERSON><PERSON> to, co bloky říkají.", "Maze.resetTooltip": "Postav hráče zpět na začátek bludiště.", "Maze.helpStack": "Poskládej několik pohybů vpřed dohromady a pomoc mi dosáhnout cíle.", "Maze.helpOneTopBlock": "V tomto levelu musíš posbírat všechny bloky na bílém pozadí.", "Maze.helpRun": "Spusťte váš program, abyste zjistili, co se stane.", "Maze.helpReset": "Váš program ne<PERSON><PERSON><PERSON><PERSON><PERSON> blu<PERSON>. Stiskněte \"Reset\" a opakujte akci.", "Maze.helpRepeat": "Počítače mají o<PERSON> p<PERSON>. Dosáhni cíle s použitím pouze dvou bloků. Použij příkaz 'opakuj' pro zopakování příkazu.", "Maze.helpCapacity": "Použili jste všechny bloky povolené pro tuto úroveň. Chcete-li přidat da<PERSON><PERSON> blo<PERSON>, musíte smazat nějaký stávající.", "Maze.helpRepeatMany": "Do bloku \"opakuj\" m<PERSON><PERSON><PERSON>š vložit více než jeden blok.", "Maze.helpSkins": "Vyberte si oblíbeného hráče z nabídky.", "Maze.helpIf": "Podmínka 'pokud' udělá něco pouze v případě, že je splněna její podmínka. Zkus se otočit vlevo, pokud je nalevo cesta.", "Maze.helpMenu": "Kliknutí na %1 dovolí v bloku \"když\" změnit podmínku.", "Maze.helpWallFollow": "Dok<PERSON>žeš vyřešit toto komplikované bludiště? Zkus se přidržovat zdi po levé ruce. Pouze pro pokročilé programátory!", "Bird.noWorm": "<PERSON><PERSON><PERSON>", "Bird.heading": "s<PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "Podmínka, k<PERSON>ž pták nedostal červa.", "Bird.headingTooltip": "<PERSON><PERSON> ve <PERSON>, <PERSON><PERSON><PERSON> udává úhel ve stupních: 0 je vodorovně doprava, 90 je nahoru apod.", "Bird.positionTooltip": "x a y označuj<PERSON> pozici ptáka. Pokud x = 0, pták je na levém okraji, pokud x = 100, je na pravém okraji. K<PERSON><PERSON> y = 0, pták je dole, kdy<PERSON> y = 100, je naho<PERSON>e.", "Bird.helpHeading": "Změň směr letu ptáka tak aby ulovil červa a přistál do hnízda.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON><PERSON><PERSON> tento blok pro pohyb jedním směrem když máš červa a jiným pokud ho nemáš,", "Bird.helpX": "'x' je tvoje současná vodorovná poloha, <PERSON><PERSON><PERSON><PERSON><PERSON> tento blok pro pohyb určeným směrem pokud je 'x' men<PERSON><PERSON> než dané <PERSON>, nebo pro jiný pohyb v opačné případě.", "Bird.helpElse": "Pro úpravu bloku „if“ klikněte na ikonu", "Bird.helpElseIf": "V této úrovni jsou třeba bloky 'nebo pokud' i 'jinak'.", "Bird.helpAnd": "Blo<PERSON> 'a tak<PERSON>' je pravdivý jen pokud jsou oba vstupy pravdivé.", "Bird.helpMutator": "Přetáhni blok 'jinak' do bloku 'pokud'.", "Turtle.moveTooltip": "Posune želvu vpřed nebo vzad o udaný počet kroků.", "Turtle.moveForward": "Přesunout vpřed", "Turtle.moveBackward": "přesunout zpět", "Turtle.turnTooltip": "Otočí želvu doleva nebo doprava o zadaný počet stupňů.", "Turtle.turnRight": "otočit doprava o", "Turtle.turnLeft": "otočit doleva o", "Turtle.widthTooltip": "Změní tloušťku pera.", "Turtle.setWidth": "nastavit šířku na", "Turtle.colourTooltip": "Změní barvu pera.", "Turtle.setColour": "nastavit barvu na", "Turtle.penTooltip": "Zvedne nebo položí <PERSON>o, aby začalo nebo př<PERSON>lo k<PERSON>.", "Turtle.penUp": "zvednout pero", "Turtle.penDown": "<PERSON><PERSON><PERSON> pero", "Turtle.turtleVisibilityTooltip": "Udě<PERSON><PERSON><PERSON> (kruh a šip<PERSON>) viditelnou nebo nevid<PERSON>ln<PERSON>.", "Turtle.hideTurtle": "sk<PERSON><PERSON><PERSON>", "Turtle.showTurtle": "zobrazit želvu", "Turtle.printHelpUrl": "https://cs.wikipedia.org/wiki/Tiska%C5%99stv%C3%AD", "Turtle.printTooltip": "Vypíše text na místě želvy v jejím směru.", "Turtle.print": "vytisknout", "Turtle.fontHelpUrl": "https://cs.wikipedia.org/wiki/Font", "Turtle.fontTooltip": "Nastaví písmo používané tiskovým blokem.", "Turtle.font": "písmo", "Turtle.fontSize": "velikost písma", "Turtle.fontNormal": "norm<PERSON>ln<PERSON>", "Turtle.fontBold": "tučné", "Turtle.fontItalic": "kurzíva", "Turtle.submitDisabled": "Spustit program dokud nezastaví. Potom můžeš svůj výtvor poslat do galerie.", "Turtle.galleryTooltip": "Otev<PERSON><PERSON><PERSON> galerii k<PERSON>.", "Turtle.galleryMsg": "Prohl<PERSON><PERSON><PERSON> galerii", "Turtle.submitTooltip": "Vystavte svůj výkres v galerie.", "Turtle.submitMsg": "Odeslat do Galerie", "Turtle.helpUseLoop": "<PERSON>aš<PERSON> řešení fun<PERSON>, ale můž<PERSON> to ud<PERSON>lat lépe.", "Turtle.helpUseLoop3": "Nakreslete tvar jen se třemi bloky.", "Turtle.helpUseLoop4": "K<PERSON><PERSON> hvězdy jen pomo<PERSON><PERSON> blo<PERSON>.", "Turtle.helpText1": "Vytvořte program, který nakreslí čtverec.", "Turtle.helpText2": "Změnňte program tak aby kreslil pětiúhelník místo čtverce.", "Turtle.helpText3a": "Je tu nový blok, k<PERSON><PERSON> vám umožní změnit barvu:", "Turtle.helpText3b": "Nakreslete žlutou hvězdu.", "Turtle.helpText4a": "Je tu nový blok, k<PERSON><PERSON> vám umožní zvednout pero z papíru, k<PERSON><PERSON> se budete pohybovat:", "Turtle.helpText4b": "Nakreslete malou žlut<PERSON> hv<PERSON>zdu, a pak nakreslete čáru nad ní.", "Turtle.helpText5": "<PERSON><PERSON><PERSON> j<PERSON> hvě<PERSON>dy, nakreslete čtyři hvězdy uspořádáné do čtverce?", "Turtle.helpText6": "Nakreslete t<PERSON>i <PERSON> h<PERSON>, a jednu b<PERSON><PERSON> linku.", "Turtle.helpText7": "Nakreslete hvězdy, a pak čtyři bílé <PERSON>.", "Turtle.helpText8": "Nakreslení 360 bílých čar bude vypadat jako měsíc v úplňku.", "Turtle.helpText9": "Přidejte černý kruh tak, že z úplňku zbude srpek?", "Turtle.helpText10": "<PERSON><PERSON><PERSON><PERSON>, co chcete. Máte obrovské množství nových bloků které si můžete prohlédnout. Bavte se!", "Turtle.helpText10Reddit": "Použijte tlačítko \"Viz Galerie\", aby j<PERSON> vidě<PERSON>, co vytvořili ostatní lidé. Nakreslíte-li něco zajímavého, použijte tlačítko \"Odeslat do Galerie\" pro publikování.", "Turtle.helpToolbox": "Vyberte kategorii pro zobrazení bloků.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "počátek x", "Movie.y1": "počátek y", "Movie.x2": "konec x", "Movie.y2": "konec y", "Movie.radius": "<PERSON><PERSON><PERSON><PERSON>", "Movie.width": "šířka", "Movie.height": "výška", "Movie.circleTooltip": "Na zadané pozici nakreslí kruh o vybraném poloměru.", "Movie.circleDraw": "kruh", "Movie.rectTooltip": "Na zadané pozici nakreslí obdélník s vybranou šířkou a výškou.", "Movie.rectDraw": "obdélník", "Movie.lineTooltip": "Nakreslí čáru z jednoho bodu do druhého se zvolenou šířkou.", "Movie.lineDraw": "<PERSON><PERSON><PERSON>", "Movie.timeTooltip": "Vrátí aktuální čas v animaci (0-100).", "Movie.colourTooltip": "Změní barvu pera.", "Movie.setColour": "nastavit barvu na", "Movie.submitDisabled": "Tvůj film se nehýbá. Použij bloky a vytvoř něco zajímavého. Pak můžeš své dílo vložit do galerie.", "Movie.galleryTooltip": "Otevřít galerii filmů.", "Movie.galleryMsg": "Prohl<PERSON><PERSON><PERSON> galerii", "Movie.submitTooltip": "Vložit film do galerie.", "Movie.submitMsg": "Odeslat do galerie", "Movie.helpText1": "Použijte jednoduché tvary na nakreslení této postavy.", "Movie.helpText2a": "<PERSON><PERSON> je film. <PERSON>cete aby se panáčkova paže pohybovala přes obrazovku. Stiskněte tlačítko přehrát pro zobrazení náhledu.", "Movie.helpText2b": "B<PERSON>hem přehrávání filmu se hodnota bloku 'čas' mění od 0 do 100. <PERSON><PERSON><PERSON> chceš měnit polohu paže v ose 'y' od 0 do 100, m<PERSON><PERSON> to by b<PERSON><PERSON> sna<PERSON>.", "Movie.helpText3": "Blok 'čas' počítá od 0 do 100. <PERSON><PERSON><PERSON><PERSON><PERSON>, aby vodorov<PERSON> poloha druhé paže šla od 100 k 0. <PERSON><PERSON><PERSON>, k<PERSON><PERSON> oto<PERSON> směr pohy<PERSON>.", "Movie.helpText4": "Použij znalosti z předešlé úrovně tak, aby se nohy křížily.", "Movie.helpText5": "Matematický výraz pro paži je komplikovaný. <PERSON><PERSON> je odpověď:", "Movie.helpText6": "Přidej osobě pár p<PERSON>.", "Movie.helpText7": "Použij blok 'if' pro nakreslení malé hlavy v první půli filmu. Potom nakresli velkou hlavu v druhé půli.", "Movie.helpText8": "Změň směr pohybu nohou v druhé půli filmu.", "Movie.helpText9": "Nakresli rostoucí kruh za osobou.", "Movie.helpText10": "Vyt<PERSON><PERSON> si vlastní film. <PERSON><PERSON><PERSON> mnoho nových bloků které může prozkoumat. Bav se!", "Movie.helpText10Reddit": "Použijte tlačítko \"Viz Galerie\", aby j<PERSON> vidě<PERSON>, co vytvořili ostatní lidé. Nakreslíte-li něco zajímavého, použijte tlačítko \"Odeslat do Galerie\" pro publikování.", "Pond.scanTooltip": "Hledá nepřítele. Zadej směr (0-360). Vráti vzdálenost od najbližšího nepřítele v daném směru. Vráti nekonečno pokud v daném směru není žádný nepřítel.", "Pond.cannonTooltip": "Vypálí z děla. <PERSON><PERSON><PERSON> (0-360) a s<PERSON><PERSON> (0-70).", "Pond.swimTooltip": "<PERSON><PERSON><PERSON> vp<PERSON><PERSON>. <PERSON>r<PERSON><PERSON> sm<PERSON> (0-360).", "Pond.stopTooltip": "Přestat plavat. Hráč se postupně zastaví.", "Pond.healthTooltip": "Vrací hráčův současný zdravotní stav (0 je mrtvý, 100 je zcela zdravý).", "Pond.speedTooltip": "<PERSON><PERSON><PERSON>ti aktuální rychlost hráče (0 stojí, 100 plná rychlost).", "Pond.locXTooltip": "Vrátí hráčovu souřadnici X (0 je levý okraj, 100 je pravý okraj).", "Pond.locYTooltip": "Vrátí hráčovu souřadnici Y (0 je spodní okraj, 100 je horní okraj).", "Pond.docsTooltip": "Zobrazí dokumentaci jazyka.", "Pond.documentation": "Dokumentace", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Cíl", "Pond.pendulumName": "Kyvadlo", "Pond.scaredName": "Strašpytel", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON> ře<PERSON><PERSON> fun<PERSON>, ale m<PERSON><PERSON><PERSON> to ud<PERSON><PERSON> lépe. <PERSON>už<PERSON><PERSON><PERSON> 'scan', a<PERSON><PERSON>, jak da<PERSON> s<PERSON>.", "Pond.helpText1": "Na zasáhnutí cíle p<PERSON>ž<PERSON>j p<PERSON> 'cannon'. První parametr je úhel, dru<PERSON><PERSON> je síla. Najdi správnou kombinaci obou čísel.", "Pond.helpText2": "<PERSON>to cíl je třeba z<PERSON>hnout mnohokrát. <PERSON><PERSON><PERSON><PERSON>j s<PERSON> 'while (true)' pokud ch<PERSON>š něco opakovat donekonečna.", "Pond.helpText3a": "Tento soupeř se pohybuje sem a tam, takže je těžké se trefit. Výraz 'scan' vrací přesnou vzdálenost od soupeře v zadaném směru.", "Pond.helpText3b": "Tato vzdálenost je přesně to, co př<PERSON>az 'cannon' potřebuje aby stř<PERSON>lel přesně.", "Pond.helpText4": "Tento soupeř je pro dě<PERSON> (které má limit 70 metrů). <PERSON>ísto toho použij př<PERSON>az 'swim' aby jste začali plavat směrem k oponentovi, a narazil do něj.", "Pond.helpText5": "Tento protivník je též mimo dosahu děla. Jste pří<PERSON> slab<PERSON> a náraz nepřežijete. Plavte tedy směrem k soupeři, dokud je vaše vodorovná souřadnice menší než 50. Potom použijte 'stop' a dělo.", "Pond.helpText6": "Tento soupeř bude po zásahu utíkat. <PERSON><PERSON><PERSON> k  němu, pokud je mimo dosah (70 metrů).", "Gallery": "Galerie"}