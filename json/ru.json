{"@metadata": {"authors": ["Eleferen", "Facenapalm", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MS", "<PERSON><PERSON><PERSON>", "MustangDSG", "Okras", "<PERSON>", "SimondR", "Staspotanin2", "Tourorist", "Wikisaurus", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Иг<PERSON><PERSON>ly", "Games.puzzle": "Головоломка", "Games.maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Птица", "Games.turtle": "Черепашка", "Games.movie": "Фильм", "Games.music": "Музыка", "Games.pondTutor": "Учебный Пруд", "Games.pond": "Пруд", "Games.linesOfCode1": "Ты решил задачу на этом уровне одной строкой на JavaScript.", "Games.linesOfCode2": "Ты решил этот уровень. Число строк кода на JavaScript - %1:", "Games.nextLevel": "Ты готов к уровню %1?", "Games.finalLevel": "Ты готов к следующему испытанию?", "Games.submitTitle": "Заголовок:", "Games.linkTooltip": "Сохранить и показать ссылку на блоки.", "Games.runTooltip": "Запусти написанную тобой программу.", "Games.runProgram": "Запустить Программу", "Games.resetTooltip": "Прерывает программу и сбрасывает в начальное состояние.", "Games.resetProgram": "Сбросить", "Games.help": "Справка", "Games.catLogic": "Логика", "Games.catLoops": "Циклы", "Games.catMath": "Математика", "Games.catText": "Текст", "Games.catLists": "Списки", "Games.catColour": "Цвет", "Games.catVariables": "Переменные", "Games.catProcedures": "Функции", "Games.httpRequestError": "Произошла проблема при запросе.", "Games.linkAlert": "Поделитесь своими блоками по этой ссылке:\n\n%1", "Games.hashError": "К сожалению, '%1' не соответствует ни одному сохраненному файлу Блокли.", "Games.xmlError": "Не удалось загрузить ваш сохраненный файл.  Возможно, он был создан в другой версии Блокли?", "Games.submitted": "Спасибо за эту программу! Если нашим тренированным мартышкам она понравится, то они опубликуют программу в галерее в течение нескольких дней.", "Games.listVariable": "список", "Games.textVariable": "текст", "Games.breakLink": "Как только вы начнёте редактировать JavaScript, вы не сможете вернуться к редактированию блоков. Продолжить?", "Games.blocks": "Блоки", "Games.congratulations": "Поздравляем!", "Games.helpAbort": "Этот уровень очень сложен. Может, ты хочешь пропустить его и перейти к следующей игре? Ты всегда можешь вернуться сюда позже.", "Index.clear": "Удалить все решения?", "Index.subTitle": "Игры для будущих программистов.", "Index.moreInfo": "Информация для преподавателей ...", "Index.startOver": "Хотите начать заново?", "Index.clearData": "Очистить данные", "Puzzle.animal1": "Утка", "Puzzle.animal1Trait1": "Перья", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://ru.wikipedia.org/wiki/Утки", "Puzzle.animal2": "Кошка", "Puzzle.animal2Trait1": "Усы", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://ru.wikipedia.org/wiki/Кошка", "Puzzle.animal3": "Пчела", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON>а<PERSON>о", "Puzzle.animal3HelpUrl": "https://ru.wikipedia.org/wiki/Пчёлы", "Puzzle.animal4": "Улитка", "Puzzle.animal4Trait1": "Ракушка", "Puzzle.animal4Trait2": "Слизь", "Puzzle.animal4HelpUrl": "https://ru.wikipedia.org/wiki/Улитка_(жизненная_форма)", "Puzzle.picture": "картинка:", "Puzzle.legs": "ноги:", "Puzzle.legsChoose": "выбери...", "Puzzle.traits": "особенности:", "Puzzle.error0": "Идеально!\nВсе блоки (%1) расположены правильно.", "Puzzle.error1": "Почти! Один блок расположен неправильно.", "Puzzle.error2": "Несколько блоков (%1) расположены неправильно.", "Puzzle.tryAgain": "Выделенный блок расположен неправильно.\nПопробуй ещё.", "Puzzle.checkAnswers": "Проверить результат", "Puzzle.helpText": "Для каждого животного (зелёный блок), прикрепи его изображение, выбери число ног и собери его отличительные черты.", "Maze.moveForward": "шагнуть вперед", "Maze.turnLeft": "повернуть налево", "Maze.turnRight": "повернуть направо", "Maze.doCode": "выполнять", "Maze.helpIfElse": "Команда \"если-иначе\" выполнит одно или другое действие.", "Maze.pathAhead": "если путь впереди", "Maze.pathLeft": "если путь cлева", "Maze.pathRight": "если путь cправа", "Maze.repeatUntil": "повторять, пока не", "Maze.moveForwardTooltip": "Продвигает путника вперёд на один шаг.", "Maze.turnTooltip": "Повернуть путника на 90 градусов влево или вправо.", "Maze.ifTooltip": "Если путь в указанном направлении открыт, то выполнить некоторые действия.", "Maze.ifelseTooltip": "Если путь в указанном направлении открыт, то выполнить первый блок действий. Иначе, выполнить второй блок действий.", "Maze.whileTooltip": "Повторять действия, заключенные в блоке, до достижения конечной точки.", "Maze.capacity0": "У вас осталось %0 блоков.", "Maze.capacity1": "У вас остался %1 блок.", "Maze.capacity2": "У вас осталось %2 блоков.", "Maze.runTooltip": "Путник сделает всё, что скажут ему блоки.", "Maze.resetTooltip": "Вернуть путника в начало лабиринта.", "Maze.helpStack": "Сгруппируйте несколько блоков \"шагнуть вперёд\", чтобы помочь мне достичь цели.", "Maze.helpOneTopBlock": "На данном уровне вам необходимо сложить вместе все блоки на белом рабочем поле.", "Maze.helpRun": "Запустите программу, чтобы посмотреть, что происходит.", "Maze.helpReset": "Ваша программа не решила задачу. Нажмите кнопку «Сбросить» и попробуйте снова.", "Maze.helpRepeat": "Пройдите до конца этого пути, используя только два блока. Для выполнения блока более одного раза используйте \"повторять\".", "Maze.helpCapacity": "Вы использовали все блоки для этого уровня. Чтобы добавить новый блок, вначале необходимо удалить существующий.", "Maze.helpRepeatMany": "Вы можете расположить более одного блока внутри блока «повторять».", "Maze.helpSkins": "Выберите в этом меню своего любимого путника.", "Maze.helpIf": "Блок \"если\" выполнит что-то только в случае верного условия. Попробуйте повернуть налево, если путь влево доступен.", "Maze.helpMenu": "Нажмите на %1 в блоке \"если\" для изменения его условия.", "Maze.helpWallFollow": "Можешь ли ты решить этот сложный лабиринт? Попробуй придерживаться левой стены. Только для опытных программистов!", "Bird.noWorm": "червяк не пойман", "Bird.heading": "направление", "Bird.noWormTooltip": "Состояние, когда птица ещё не поймала червя.", "Bird.headingTooltip": "Двигаться в направлении, заданном углом: 0 - вправо, 90 - вверх и т.д.", "Bird.positionTooltip": "x и y отмечают положение птицы. При x = 0 птица находится у левого края, при x = 100 она у правого края. При y = 0 птица находится в нижней части, при y = 100 - на самом верху.", "Bird.helpHeading": "Измени курс, чтобы птица поймала червяка и села в своё гнездо.", "Bird.helpHasWorm": "Используй этот блок для движения в одном направлении, если червяк пойман, и в другом - если не пойман.", "Bird.helpX": "'x' - это текущее горизонтальное положение. Используй этот блок, чтобы двигаться в одном направлении, если 'x' меньше числа, или в другом направлении, в противном случае.", "Bird.helpElse": "Щелкните на иконке, чтобы изменить блок 'если'.", "Bird.helpElseIf": "На этом уровне требуются блоки \"иначе если\" и \"иначе\".", "Bird.helpAnd": "Блок \"и\" имеет значения истина, если оба параметра истинны.", "Bird.helpMutator": "Перетащи блок \"иначе\" в блок \"если\".", "Turtle.moveTooltip": "Переместить черепашку вперёд или назад на заданное расстояние.", "Turtle.moveForward": "переместить вперёд на", "Turtle.moveBackward": "переместить назад на", "Turtle.turnTooltip": "Повернуть черепашку налево или направо на заданный угол в градусах.", "Turtle.turnRight": "повернуть направо на", "Turtle.turnLeft": "повернуть налево на", "Turtle.widthTooltip": "Изменить ширину пера.", "Turtle.setWidth": "установить ширину", "Turtle.colourTooltip": "Изменяет цвет пера.", "Turtle.setColour": "установить цвет", "Turtle.penTooltip": "Поднимает или опускает перо, чтобы становить или начать рисование.", "Turtle.penUp": "поднять перо", "Turtle.penDown": "опустить перо", "Turtle.turtleVisibilityTooltip": "Сделать черепашку (окружность и стрелку) видимой или невидимой.", "Turtle.hideTurtle": "скрыть черепашку", "Turtle.showTurtle": "показать черепашку", "Turtle.printHelpUrl": "https://ru.wikipedia.org/wiki/Книгопечатание", "Turtle.printTooltip": "Нарисовать текст в положении и направлении черепашки.", "Turtle.print": "напечатать", "Turtle.fontHelpUrl": "https://ru.wikipedia.org/wiki/Шрифт", "Turtle.fontTooltip": "Устанавливает шрифт для блока печати.", "Turtle.font": "шрифт", "Turtle.fontSize": "размер шрифта", "Turtle.fontNormal": "обычный", "Turtle.fontBold": "жир<PERSON><PERSON>й", "Turtle.fontItalic": "кур<PERSON><PERSON>в", "Turtle.submitDisabled": "Запусти программу и жди её завершение. Затем можно поместить полученный рисунок в галерею.", "Turtle.galleryTooltip": "Открывает галерею рисунков.", "Turtle.galleryMsg": "См. Га<PERSON><PERSON><PERSON><PERSON>ю", "Turtle.submitTooltip": "Загрузить ваш рисунок в галерею.", "Turtle.submitMsg": "Загрузить в Галерею.", "Turtle.helpUseLoop": "Твоё решение работает, но можно сделать лучше.", "Turtle.helpUseLoop3": "Нарисуй фигуру, используя всего три блока.", "Turtle.helpUseLoop4": "Нарисуй звёздочку, используя всего четыре блока.", "Turtle.helpText1": "Создай программу, которая нарисует квадрат.", "Turtle.helpText2": "Измени программу, чтобы вместо квадрата нарисовать пятиугольник.", "Turtle.helpText3a": "Это новый блок, который позволяет изменить цвет.", "Turtle.helpText3b": "Нари<PERSON><PERSON>й жёлтую звезду.", "Turtle.helpText4a": "Есть новый блок, который позволяет поднять перо от бумаги при движении:", "Turtle.helpText4b": "нарисуй небольшую жёлтую звёздочку, потом линию над ней.", "Turtle.helpText5": "Вместо одной звезды, можешь нарисовать четыре звезды в углах квадрата?", "Turtle.helpText6": "Нарисуй три жёлтых звезды и одну белую линию.", "Turtle.helpText7": "Нарисуй звёзды, потом четыре белые линии.", "Turtle.helpText8": "Прорисовка 360-и белых линий будет выглядеть как полная луна.", "Turtle.helpText9": "Можешь добавить чёрный круг, чтобы луна превратилась в полумесяц?", "Turtle.helpText10": "Нари<PERSON><PERSON><PERSON>, что пожелаешь. У тебя появилось много новых блоков, которые стоит изучить. Приятно провести время!", "Turtle.helpText10Reddit": "Используй кнопку \"Смотреть Галерею\", чтобы посмотреть рисунки других. Если ты нарисовал что-то интересное - используй кнопку \"Сохранить в Галерее\", чтобы поделиться рисунком с другими.", "Turtle.helpToolbox": "Выбери группу, чтобы увидеть блоки.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "начальный x", "Movie.y1": "начальный y", "Movie.x2": "конечный x", "Movie.y2": "конечный y", "Movie.radius": "радиус", "Movie.width": "длина", "Movie.height": "ши<PERSON><PERSON><PERSON>", "Movie.circleTooltip": "Рисует круг в заданном месте с заданным радиусом.", "Movie.circleDraw": "круг", "Movie.rectTooltip": "Рисует прямоугольник в заданном месте с заданной длиной и шириной.", "Movie.rectDraw": "прямоугольник", "Movie.lineTooltip": "Рисует отрезок заданной ширины между указанными точками.", "Movie.lineDraw": "линия", "Movie.timeTooltip": "Возвращает текущее время в фильме (0-100).", "Movie.colourTooltip": "Меняет цвет пера.", "Movie.setColour": "установить цвет", "Movie.submitDisabled": "Твой фильм неподвижен. Используй блоки, чтобы создать что-то интересное. После этого ты можешь поместить свой фильм в галерею.", "Movie.galleryTooltip": "Открыть галерею фильмов.", "Movie.galleryMsg": "см. Га<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.submitTooltip": "Загрузить ваш фильм в галерею.", "Movie.submitMsg": "Представить в Галерею", "Movie.helpLayer": "Передвиньте фоновый круг наверх вашей программы. Тогда он окажется позади человека.", "Movie.helpText1": "Используй простые фигуры, чтобы нарисовать этого человечка.", "Movie.helpText2a": "Этот уровень представляет собой кино. Требуется, чтобы рука двигалась по экрану. Нажми кнопку «Старт», чтобы посмотреть.", "Movie.helpText2b": "Во время показа фильма значение блока \"time\" меняется от нуля до 100. Из-за того, что координата 'y' также должна начинаться с 0 и дойти то 100, это должно быть просто.", "Movie.helpText3": "Блок \"time\" отсчитывает от 0 до 100. Но теперь требуется менять координату 'y' другой руки от 100 до 0. Можешь найти простую математическую формулу, чтобы развернуть направление движения?", "Movie.helpText4": "Используй то, что ты узнал на предыдущем уровне, чтобы ноги пересеклись.", "Movie.helpText5": "Математическая формула для руки непроста. Вот ответ:", "Movie.helpText6": "Добавь человеку пару рук.", "Movie.helpText7": "Используйте блок «если», чтобы нарисовать маленькую голову для первой половины фильма. Затем нарисуйте большую голову для второй половины фильма.", "Movie.helpText8": "Сделай, чтобы ноги были в обратном порядке на середине фильма.", "Movie.helpText9": "Нарисуй расширяющийся круг за человеком.", "Movie.helpText10": "Сделай фильм, какой пожелаешь. У тебя появилось много новых блоков, которые стоит изучить. Приятно провести время!", "Movie.helpText10Reddit": "Используй кнопку \"Смотреть Галерею\", чтобы посмотреть фильмы других. Если ты создал что-то интересное - используй кнопку \"Сохранить в Галерее\", чтобы поделиться фильмом с другими.", "Music.playNoteTooltip": "Проигрывает одну музыкальную ноту с определенной длительностью и тональностью.", "Music.playNote": "играть %1 ноту %2", "Music.restTooltip": "Ожидает определённое время", "Music.restWholeTooltip": "Ожида<PERSON>т одну целую.", "Music.rest": "пауза, протяжённостью %1", "Music.setInstrumentTooltip": "Переключает на заданный инструмент, во время проигрывания последующих музыкальных нот.", "Music.setInstrument": "установить инструмент %1", "Music.startTooltip": "Выполняет блоки внутри, когда кнопка 'Запустить программу' нажата.", "Music.start": "когда %1 нажата", "Music.pitchTooltip": "Одна нота (C4 это 7).", "Music.firstPart": "первая часть", "Music.piano": "пианино", "Music.trumpet": "труба", "Music.banjo": "банджо", "Music.violin": "скрипка", "Music.guitar": "гитара", "Music.flute": "флейта", "Music.drum": "бар<PERSON><PERSON><PERSON>н", "Music.choir": "хор", "Music.submitDisabled": "Запусти программу и жди её завершение. Затем можно поместить полученный рисунок в галерею.", "Music.galleryTooltip": "Открыть галерею музыки.", "Music.galleryMsg": "См. Га<PERSON><PERSON><PERSON><PERSON>ю", "Music.submitTooltip": "Загрузить вашу музыку в галерею.", "Music.submitMsg": "Загрузить в Галерею.", "Music.helpUseFunctions": "Ваше решение работает, но вы можете сделать лучше. Используйте функции для уменьшения количества повторяющегося кода.", "Music.helpUseInstruments": "Музыка будет звучать лучше, если вы будете использовать разные инструменты в каждом стартовом блоке.", "Music.helpText1": "Исполните первые четыре ноты 'Братца Якоба'", "Music.helpText2a": "'Функция' позволяет тебе сгруппировать блоки вместе, а затем запускать их более одного раза.", "Music.helpText2b": "Создайте функцию, чтобы сыграть первые четыре ноты песни «Братец Якоб». Запустите эту функцию дважды. Не добавляйте новые нотные блоки.", "Music.helpText3": "Создайте вторую функцию для следующей части песни «Братец Якоб». Последняя нота длиннее.", "Music.helpText4": "Создайте третью функцию для следующей части песни «Братец Якоб». Первые четыре ноты короче.", "Music.helpText5": "Закончите мелодию песни «Братец Якоб» до конца.", "Music.helpText6a": "Этот новый блок позволяет вам использовать другой музыкальный инструмент.", "Music.helpText6b": "Сыграйте свою мелодию на скрипке.", "Music.helpText7a": "Этот новый блок добавляет беззвучную задержку.", "Music.helpText7b": "Создайте второй стартовый блок с двумя блоками задержки, а затем сыграйте песню «Братец Якоб».", "Music.helpText8": "Каждый стартовый блок должен дважды играть песню «Братец Якоб».", "Music.helpText9": "Создайте четыре стартовых блока, каждый из которых дважды исполняет песню «Братец Якоб». Добавьте правильное количество блоков задержки.", "Music.helpText10": "Сочини всё, что пожелаешь. У тебя появилось много новых блоков, которые стоит изучить. Приятно провести время!", "Music.helpText10Reddit": "Используй кнопку \"Смотреть Галерею\", чтобы посмотреть работы других. Если ты написал что-то интересное — используй кнопку \"Сохранить в Галерее\", чтобы поделиться работой с другими.", "Pond.scanTooltip": "Ищет врагов. Укажи направление (0-360). Возвращает расстояние до ближайшего противника в этом направлении. Возвращает бесконечность, если противник не найден.", "Pond.cannonTooltip": "Стрелять из пушки. Укажи направление (0-360) и расстояние (0-70).", "Pond.swimTooltip": "Плыть вперед. Укажи направление (0-360).", "Pond.stopTooltip": "Прекратить плыть. Игрок будет замедляться до остановки.", "Pond.healthTooltip": "Возвращает текущее здоровье игрока (0 - труп, 100 - здоров).", "Pond.speedTooltip": "Возвращает текущую скорость игрока (0 - неподвижность, 100 - максимальная скорость).", "Pond.locXTooltip": "Возвращает координату X игрока (0 - левый край, 100 - правый край).", "Pond.locYTooltip": "Возвращает координату Y игрока (0 - нижний край, 100 - верхний край).", "Pond.logTooltip": "Печатает число в консоль вашего браузера.", "Pond.docsTooltip": "Открывает документацию по языку.", "Pond.documentation": "Документация", "Pond.playerName": "Игрок", "Pond.targetName": "Цель", "Pond.pendulumName": "Маятник", "Pond.scaredName": "Пуган<PERSON>й", "Pond.helpUseScan": "Твоё решение работает, но можно сделать лучше. Используй 'scan', чтобы указать расстояние для стрельбы пушки.", "Pond.helpText1": "Используйте команду 'cannon', чтобы поразить цель. Первый параметр — угол, второй — расстояние. Найдите правильное сочетание.", "Pond.helpText2": "Эта цель должна быть поражена много раз. Используй цикл 'while (true)', чтобы делать что-то бесконечно.", "Pond.helpText3a": "Этот противник двигается взад-вперёд, из-за чего в него трудно попасть. Выражение 'scan' возвращает точное расстояние до противника в указанном направлении.", "Pond.helpText3b": "Расстояние — именно то, что нужно команде 'cannon' для точной стрельбы.", "Pond.helpText4": "Этот противник слишком далеко, чтобы использовать пушку (которая имеет ограничение 70 метров). Вместо этого, используй команду 'swim', чтобы плыть в сторону противника и врезаться в него.", "Pond.helpText5": "Этот противник тоже слишком далеко, чтобы использовать пушки. Но ты слишком слаб, чтобы выжить в столкновении. Плыви в сторону противника, пока твоё горизонтальное положение меньше 50. Затем 'stop' и используй пушки.", "Pond.helpText6": "Этот противник будет отходить, когда в него попали. Плыви вперед, если он находится вне диапазона (70 метров).", "Gallery": "Галерея"}