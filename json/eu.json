{"@metadata": {"authors": ["An13sa", "<PERSON><PERSON><PERSON>", "EukeneFL", "<PERSON><PERSON>", "Subi", "<PERSON><PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "Labirintoa", "Games.bird": "Txoria", "Games.turtle": "Do<PERSON>oka", "Games.movie": "Filma", "Games.music": "<PERSON><PERSON><PERSON>", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "<PERSON><PERSON><PERSON>", "Games.linesOfCode1": "Maila hau gainditu duzu JavaScript lerro bat erabiliz:", "Games.linesOfCode2": "Maila hau gainditu duzu %1 JavaScript lerro erabiliz:", "Games.nextLevel": "Prest zaude %1. <PERSON><PERSON><PERSON>?", "Games.finalLevel": "Prest zaude hurrengo er<PERSON>?", "Games.submitTitle": "Izenburua:", "Games.linkTooltip": "<PERSON><PERSON>, eta estekatu blokeetara.", "Games.runTooltip": "Zuk idatzitako programa exekutatu", "Games.runProgram": "Programa exekutatu", "Games.resetTooltip": "Gelditu programa eta berrezarri maila.", "Games.resetProgram": "<PERSON><PERSON><PERSON> hasi", "Games.help": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Logika", "Games.catLoops": "Begiztak", "Games.catMath": "Matematika", "Games.catText": "Test<PERSON>", "Games.catLists": "Zerrendak", "Games.catColour": "Kolorea", "Games.catVariables": "Aldagaiak", "Games.catProcedures": "Prozedurak", "Games.httpRequestError": "Eskaerarekin arazo bat egon da.", "Games.linkAlert": "Parteka itzazu zure blokeak, esteka honekin:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON>, «%1» ez dator bat gordetako ezein programarekin.", "Games.xmlError": "<PERSON><PERSON> izan da zure fitxategia kargatu. Agian Blockly-ren beste bertsio batekin sortua izan zen?", "Games.submitted": "Eskerrik asko programa honengatik! Gure tximino langile talde prestatuak gustoko badu, arg<PERSON><PERSON><PERSON>o dute galerian egun batzuren buruan.", "Games.listVariable": "zerrenda", "Games.textVariable": "testua", "Games.breakLink": "<PERSON>hin Java Script aldatzen hasi, e<PERSON>o du<PERSON> atzera egin aldatze blokeetara. <PERSON><PERSON> zaude hone<PERSON>?", "Games.blocks": "Blokeak", "Games.congratulations": "Zorionak!", "Games.helpAbort": "Maila hau izu<PERSON>ri zaila da. <PERSON><PERSON> zenuke salto egin hurrengo jokora? <PERSON>i izanez gero, itzul zaitez<PERSON> geroago.", "Index.clear": "Emaitza guztiak ezabatu?", "Index.subTitle": "Biharko programatzai<PERSON><PERSON><PERSON> j<PERSON>.", "Index.moreInfo": "Pedagogoentzako informazioa...", "Index.startOver": "<PERSON><PERSON><PERSON> hasi nahi duzu?", "Index.clearData": "Datuak <PERSON>", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "Lumak", "Puzzle.animal1Trait2": "Mokoa", "Puzzle.animal1HelpUrl": "https://eu.wikipedia.org/wiki/<PERSON>ate", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "Biboteak", "Puzzle.animal2Trait2": "Ilajea", "Puzzle.animal2HelpUrl": "https://eu.wikipedia.org/wiki/Katu", "Puzzle.animal3": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "Eztia", "Puzzle.animal3Trait2": "Eztena", "Puzzle.animal3HelpUrl": "https://eu.wikipedia.org/wiki/Erle", "Puzzle.animal4": "Barraskiloa", "Puzzle.animal4Trait1": "Oskola", "Puzzle.animal4Trait2": "Lirdinga", "Puzzle.animal4HelpUrl": "https://eu.wikipedia.org/wiki/Barraskilo", "Puzzle.picture": "<PERSON><PERSON><PERSON>:", "Puzzle.legs": "hankak:", "Puzzle.legsChoose": "aukeratu...", "Puzzle.traits": "ezaugarriak:", "Puzzle.error0": "Bikain!\n%1 blokeak zuzen daude.", "Puzzle.error1": "Ia-ia! Bloke bat ez dago ondo.", "Puzzle.error2": "%1 bloke ez dira zuzenak.", "Puzzle.tryAgain": "Markatuta agertzen den blokea ez da zuzena.\nSaiat<PERSON> berriro.", "Puzzle.checkAnswers": "Erantzunak egiaztatu", "Puzzle.helpText": "<PERSON><PERSON> b<PERSON> (be<PERSON><PERSON>), a<PERSON><PERSON><PERSON><PERSON> jar<PERSON>, bere hanka kop<PERSON>a aukerat<PERSON>, eta bere berez<PERSON> ere.", "Maze.moveForward": "<PERSON>i aurrera", "Maze.turnLeft": "biratu e<PERSON>", "Maze.turnRight": "biratu eskuin", "Maze.doCode": "egin", "Maze.helpIfElse": "If-else blokeek gauza bat edo bestea egingo dute.", "Maze.pathAhead": "bidea badago aurrera", "Maze.pathLeft": "bidea badago e<PERSON>", "Maze.pathRight": "bidea badago eskuinera", "Maze.repeatUntil": "arte errepikatu", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> au<PERSON><PERSON>z tarte bat mugitzen du.", "Maze.turnTooltip": "Pegman ezkerrerantz edo eskuinerantz 90 gradu biratzen du.", "Maze.ifTooltip": "<PERSON><PERSON><PERSON><PERSON> bidea badago, eki<PERSON><PERSON> bat<PERSON> burutu.", "Maze.ifelseTooltip": "<PERSON><PERSON><PERSON><PERSON> bidea badago, ekintzen lehenengo blokea burutu. <PERSON><PERSON>, ekintzen bigarren blokea burutu.", "Maze.whileTooltip": "Errepi<PERSON><PERSON> barruko eki<PERSON> bukaera punturaino iritsi arte.", "Maze.capacity0": "%0 bloke geratzen dira.", "Maze.capacity1": "Bloke %1 geratzen da.", "Maze.capacity2": "%2 bloke geratzen dira.", "Maze.runTooltip": "Jokalariari eginaraziko dio blokeek diotena.", "Maze.resetTooltip": "<PERSON><PERSON><PERSON> be<PERSON> j<PERSON>ia labiri<PERSON>n hasieran.", "Maze.helpStack": "<PERSON><PERSON><PERSON> 'mugi aurrera' bloke pare bat helmugara iritsi ahal i<PERSON>.", "Maze.helpOneTopBlock": "<PERSON><PERSON>, bloke guztiak lan-eremu txurian pilatu behar dituzu.", "Maze.helpRun": "Zure programa exekutatu ikusteko zer gertatzen den.", "Maze.helpReset": "Zure programak ez du labirintoa ebatzi. Sakatu 'Reset' eta saiatu berriz.", "Maze.helpRepeat": "Ordenagailuek memoria-kantitate mugatua dute. <PERSON><PERSON> z<PERSON>ez bide honen bukaerara iristen soilik bi bloke erabiliz. Bloke bat behin baino gehiagot<PERSON> erabil<PERSON>, erabili 'errepikatu'.", "Maze.helpCapacity": "Maila honetarako existitzen diren bloke guztiak erabili dituzu. Bloke berria era<PERSON>, lehenengo existitzen den blokea ezabatu beharko duzu.", "Maze.helpRepeatMany": "'errepikatu' bloke baten barruan bloke bat baino gehiago erabili de<PERSON>.", "Maze.helpSkins": "<PERSON><PERSON> ezazu zure jokalaririk gogokoena menu honetatik.", "Maze.helpIf": "'if' bloke batek zeozer egingo du bakarrik baldintza betetzen bada. Saiatu ezkerrera biratzen ezkerrera bidea baldin badago.", "Maze.helpWallFollow": "Labirinto zail honi irtenbidea aurkitu diezaiokezu? Saia zaitez ez<PERSON>-pareta jarrai<PERSON>.", "Bird.noWorm": "Ez dauka zizarerik", "Bird.heading": "goiburua", "Bird.noWormTooltip": "Txoriak zizarea lortu ez duene<PERSON>.", "Bird.helpElse": "Klikatu ikonoan 'ba' blokean aldaketak egiteko.", "Turtle.setWidth": "Aldatu pisua hurrengora", "Turtle.colourTooltip": "Boligrafoaren kolorea aldatzen du.", "Turtle.setColour": "hurrengo kolo<PERSON> aldatu", "Turtle.penTooltip": "Boligrafoa igotzen edo jaisten du, marrazten hasteko edo marrazteari uzteko.", "Turtle.penUp": "Boligrafoa gora", "Turtle.penDown": "Boligrafo<PERSON> behera", "Turtle.turtleVisibilityTooltip": "Dordoka (borobila eta gezia) ikusgarri ala ikustezin egin du.", "Turtle.hideTurtle": "ezkutatu dortoka", "Turtle.showTurtle": "erakutsi dortoka", "Turtle.printHelpUrl": "https://eu.wikipedia.org/wiki/Inprimatze", "Turtle.printTooltip": "Testua irudikatzen du dordokaren norabidean eta haren kokapena.", "Turtle.print": "inprimatu", "Turtle.fontHelpUrl": "https://en.wikipedia.org/wiki/Iturri", "Turtle.font": "letra mota", "Turtle.fontSize": "let<PERSON>en tamaina", "Turtle.fontNormal": "normala", "Turtle.fontBold": "Lodia", "Turtle.fontItalic": "italikoa", "Turtle.submitDisabled": "Exekutatu zure programa gelditu arte. Ondoren, zure marrazkia galeriara bidal dezakezu.", "Turtle.galleryTooltip": "Marrazkien galeria ireki.", "Turtle.galleryMsg": "Ikusi galeria", "Turtle.submitTooltip": "<PERSON><PERSON>i zure marrazkia galeriara.", "Turtle.submitMsg": "Bidali galeriara", "Turtle.helpUseLoop": "Zure soluzioak funtzionatzen du, baina hobeto egin de<PERSON>ezu.", "Turtle.helpUseLoop3": "Marraz ezazu forma hiru blokerekin besterik ez.", "Turtle.helpUseLoop4": "Marraz ezazu izarra lau blokerekin besterik ez.", "Turtle.helpText1": "Sortu karratua marrazten duen programa.", "Turtle.helpText2": "Alda ezazu zure programa pentagonoa marrazteko karra<PERSON>n ordez.", "Turtle.helpText3a": "Bloke berri bat dago kolorea aldatzen ahalbidetzen dizuna:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON> e<PERSON><PERSON> izar hori bat.", "Turtle.helpText4a": "Bloke berri bat dago mugitzean boligrafoa paperetik altxatzea ahalbidetzen dizuna:", "Turtle.helpText4b": "<PERSON><PERSON> e<PERSON><PERSON> izar hori bat, gero marraz<PERSON> marra bat haren gain.", "Turtle.helpText5": "<PERSON>zar baten ordez, lau izar karratu batean kokatuta marraz <PERSON>?", "Turtle.helpText6": "<PERSON><PERSON> itzazu hiru izar hori eta marra zuri bat.", "Turtle.helpText7": "<PERSON><PERSON>, gero marra<PERSON><PERSON> lau marra <PERSON>.", "Turtle.helpText8": "360 marra zuri marra<PERSON><PERSON>an ilbetearen antza izango du.", "Turtle.helpText9": "<PERSON><PERSON><PERSON> de<PERSON>u borobil beltza ilargia ilgora bilaka dadin?", "Turtle.helpText10": "Marraz ezazu nahi duzuna. Milaka bloke dauzkazu exploratzeko. Ondo pasa!", "Turtle.helpToolbox": "Hautatu kategoria blokeak ikusteko.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "hasiera x", "Movie.y1": "hasiera y", "Movie.x2": "amaiera x", "Movie.y2": "amaiera y", "Movie.radius": "erradioa", "Movie.width": "zabalera", "Movie.height": "garaiera", "Movie.circleTooltip": "Borobil bat marraztu du zehaztutako kokapenean eta zehaztutako erradioarekin.", "Movie.circleDraw": "zirkulua", "Movie.rectTooltip": "Laukizuzen bat marraztu du zehaztutako kokapenean eta zehaztutako zabalera eta altuerarekin.", "Movie.rectDraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.lineTooltip": "Puntu batetik bestera marra bat marraztu du zehaztutako zabalerarekin.", "Movie.lineDraw": "lerroa", "Movie.timeTooltip": "Animazioko oraingo denborara buel<PERSON>zen da(0-100).", "Movie.colourTooltip": "Boligrafoaren kolorea aldatzen du.", "Movie.setColour": "hurrengo kolo<PERSON> aldatu", "Movie.submitDisabled": "Zure pelikula ez da mugitzen. Erabil itzazu blokeak zerbait interesgarria sortzeko. Orduan pelikula bidali ahalko duzu galeriara.", "Movie.galleryTooltip": "Ireki filmen galeria.", "Movie.galleryMsg": "Ikusi galeria", "Movie.submitTooltip": "<PERSON><PERSON><PERSON> zure filma galeriara.", "Movie.submitMsg": "Bidali galeriara", "Movie.helpLayer": "<PERSON><PERSON><PERSON> atzealdeko borobila zure programako goialdera. Orduan pertsonaren atzean agertuko da.", "Movie.helpText1": "<PERSON><PERSON> itzazu forma sinpleak pertsona hau marrazteko.", "Movie.helpText2a": "Maila hau film bat da. <PERSON><PERSON><PERSON><PERSON> besoa pantailatik zehar mugi dadin nahi duzu. <PERSON><PERSON><PERSON> play b<PERSON><PERSON> aurreik<PERSON><PERSON>.", "Movie.helpText2b": "Filma martxan da<PERSON><PERSON>, 'den<PERSON><PERSON>' balioak zenbatzen du 0tik 100era. Besoaren 'y' posizioa 0tik hastea eta 100era joatea nahi zenuenez gero hau erraza izan beharko litzateke.", "Movie.helpText3": "'denbora' blokeak 0tik 100era zenbatzen du. <PERSON><PERSON> orain besoaren 'y' posizioa 100ean hasi eta 0ra joan dadin nahiko zenuke. Norabide honetara iraul dadin formula matematiko sinplea irudika de<PERSON>?", "Movie.helpText4": "Erabili aurreko mailan ikasi zenuena hankak gurutzarazteko.", "Movie.helpText5": "Besorako formula matematikoa zaila da. Hona hemen erantzuna:", "Movie.helpText6": "Pertsonari esku pare bat ematen dio.", "Movie.helpText8": "<PERSON>gin hankak alderantzikatzeko norabidea har dezaten filmaren erditik zehar.", "Movie.helpText9": "<PERSON><PERSON><PERSON> zabaltzen den borobila pertsonaren atzean.", "Movie.helpText10": "<PERSON>raz ezazu nahi duzunari buruzko filma. <PERSON>laka bloke berri da<PERSON> exploratzeko. Ondo pasa!", "Music.playNoteTooltip": "Zehaztutako iraupena eta tonua duen nota musikala jotzen du.", "Music.playNote": "jo %1 %2 ohar", "Music.restTooltip": "Zehaztutako iraupenaren zain dago.", "Music.restWholeTooltip": "<PERSON><PERSON> oso baten zain dago.", "Music.rest": "%1 falta da", "Music.setInstrumentTooltip": "Zehaztutako instrumentura aldatzen da ondorengo musika notak jotzen direnean.", "Music.setInstrument": "Aldatu tresna %1(e)ra", "Music.startTooltip": "Barruko blokeak exekutatzen ditu 'Programa exekutatu' botoia sakatzen duenean", "Music.start": "%1 sakatzen duenean", "Music.pitchTooltip": "<PERSON><PERSON> bat (C4 7 da).", "Music.firstPart": "lehenengo atala", "Music.piano": "pianoa", "Music.trumpet": "tronpeta", "Music.banjo": "<PERSON>a", "Music.violin": "bibolina", "Music.guitar": "gitarra", "Music.flute": "t<PERSON><PERSON><PERSON>", "Music.drum": "dan<PERSON><PERSON>", "Music.choir": "<PERSON><PERSON><PERSON><PERSON>", "Music.submitDisabled": "Exekutatu programa gelditu arte. Ondoren, zure musika galeriara bidal dezakezu.", "Music.galleryTooltip": "<PERSON>reki musika galeria.", "Music.galleryMsg": "Ikusi Gale<PERSON>", "Music.submitTooltip": "Bidali zure musika galeriara.", "Music.submitMsg": "<PERSON><PERSON><PERSON>", "Music.helpUseFunctions": "Zure soluzioak funtzionatzen du, baina hobe egin dezakezu. Erabili kudeaketa funtzioak errepikatutako kode kopurua txikitzeko.", "Music.helpUseInstruments": "Musika hobeto entzungo da hasiera bloke bakoitzean instrumentu ezberdina erabiltzen baduzu.", "Music.helpText1": "Konposa itzatz<PERSON> '<PERSON><PERSON><PERSON> le<PERSON>o lau notak.", "Music.helpText2a": "'funtzio' batek blokeak batera pilatzea eta gero behin baino gehiagotan exekutatzea ahalbidetzen dizu.", "Music.helpText2b": "Sortu funtzio bat '<PERSON><PERSON>' a<PERSON><PERSON><PERSON> le<PERSON>engo lau notak jotzeko. Exekutatu funtzio hori birritan. Ez gehitu ohar bloke berririk.", "Music.helpText3": "Sortu bigarren funtzio bat '<PERSON><PERSON>' abes<PERSON>ren hurrengo atalerako. Azken nota luzeagoa da.", "Music.helpText4": "Sortu hirugarren funtzio bat '<PERSON><PERSON>' abes<PERSON>ren hurrengo atalerako. Lehenengo lau notak laburragoak dira.", "Music.helpText5": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>' melodia osoa.", "Music.helpText6a": "Bloke berri honek beste instrumentu batera aldatzea ahalbidetzen dizu.", "Music.helpText6b": "<PERSON> zure melodia bibolinarekin.", "Music.helpText7a": "Bloke berri honek is<PERSON>asun atzerapena gehitzen du.", "Music.helpText7b": "<PERSON><PERSON><PERSON> hasiera blokea sortu bi atzerapen bloke dituena, gero ere '<PERSON><PERSON>' jotzen duena.", "Music.helpText8": "Hasiera bloke b<PERSON><PERSON><PERSON> '<PERSON><PERSON>' jo beharko luke birritan.", "Music.helpText9": "Sortu lau hasiera blokeak bak<PERSON><PERSON> '<PERSON><PERSON>' birritan jotzen duena. Gehitu atzerapen bloke kantitate zuzena.", "Music.helpText10": "Konposa ezazu nahi duzuna. Milaka bloke berri da<PERSON> exploratzeko. Ondo pasa!", "Pond.swimTooltip": "<PERSON>geri egin aurrera. Zehaztu norabidea (0-360).", "Pond.stopTooltip": "Gelditu i<PERSON>. Jokalaria gelditu egingo da <PERSON>.", "Pond.healthTooltip": "<PERSON><PERSON><PERSON><PERSON>ko o<PERSON> b<PERSON>zen zaio (0 hilda da, 100 osasuntsu da).", "Pond.speedTooltip": "Jokalaria uneko abiadurara bueltatzen da (0 geldi da, 100 guztizko abiadura da).", "Pond.locXTooltip": "Jokalariaren X koordenada bueltatzen da (0 ezkerreko muturra da, 100 eskumako muturra da).", "Pond.locYTooltip": "Jokalariaren Y koordenada bueltatzen da (0 beheko muturra da, 100 goiko muturra da).", "Pond.docsTooltip": "Hizkuntza dokumentazioa erakutsi.", "Pond.documentation": "Dokumentazioa", "Pond.playerName": "Jokalaria", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Beldurtuta", "Pond.helpUseScan": "Zure soluzioak funtzionatzen du, baina hobeto egin dezakezu. Erabili 'scan' cannonari zein urrun joan behar den adierazteko.", "Gallery": "Galeria"}