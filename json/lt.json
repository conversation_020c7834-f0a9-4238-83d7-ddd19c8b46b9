{"@metadata": {"authors": ["Eitvys200", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gediminas", "Hugo.arg", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Vilius2001", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "„Blockly“ žaid<PERSON>i", "Games.puzzle": "Dėlionė", "Games.maze": "<PERSON><PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Filmas", "Games.music": "Muzika", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON>", "Games.pond": "<PERSON><PERSON><PERSON><PERSON>", "Games.linesOfCode1": "Jūs įveikėte šį lygį su 1 JavaScript kodo eilute:", "Games.linesOfCode2": "Jūs įveikėte šį lygį su %1 JavaScript kodo eilutėmis:", "Games.nextLevel": "<PERSON>r <PERSON><PERSON><PERSON> lygi<PERSON> %1?", "Games.finalLevel": "Ar esate pasirengęs kitam i<PERSON>?", "Games.submitTitle": "Pavadinimas:", "Games.linkTooltip": "Išsaugoti (sugeneruoti URL nuorodą).", "Games.runTooltip": "Paleisti jūsų parašytą programą.", "Games.runProgram": "Paleisti programą", "Games.resetTooltip": "Paleisti programą ir atkurti lygį.", "Games.resetProgram": "<PERSON><PERSON>", "Games.help": "Pagalba", "Games.catLogic": "Logika", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Matematika", "Games.catText": "Tekstas", "Games.catLists": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catColour": "Spalva", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "Funkcijos", "Games.httpRequestError": "Iškilo problema su prašymu.", "Games.linkAlert": "%1", "Games.hashError": "<PERSON><PERSON>, '%1' neatitinka jokios išsaugotos programos.", "Games.xmlError": "Nesuprantu pateikto failo. Gal jis buvo sukurtas su kita Blocky versija?", "Games.submitted": "Ačiū už programą! Jei mūsų protingų beždžionių komandai ji patiks, mes ją publikuosime savo galerijoje per artimiaus<PERSON> dienas.", "Games.listVariable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.textVariable": "tekstas", "Games.breakLink": "Jei pradėsite redaguoti JavaScript nebegalėsite redaguoti blokų, sutinkate?", "Games.blocks": "Blokai", "Games.congratulations": "Sveikiname!", "Games.helpAbort": "Šis lygis yra itin sudėtingas. Ar norėtumėte jį praleisti ir pereiti prie kito žaidimo? Visada galite sugrįžti vėliau.", "Index.clear": "<PERSON><PERSON><PERSON><PERSON> visus sprendimus?", "Index.subTitle": "Žaidimai rytojaus programuotojams.", "Index.moreInfo": "Informacija švietėjams...", "Index.startOver": "<PERSON><PERSON> p<PERSON> iš naujo?", "Index.clearData": "Išvalyti duomenis", "Puzzle.animal1": "<PERSON>s", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Snapas", "Puzzle.animal1HelpUrl": "https://lt.wikipedia.org/wiki/Antis", "Puzzle.animal2": "Kat<PERSON>", "Puzzle.animal2Trait1": "Ūsai", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://lt.wikipedia.org/wiki/Katė", "Puzzle.animal3": "Bitė", "Puzzle.animal3Trait1": "Medus", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://lt.wikipedia.org/wiki/Bitė", "Puzzle.animal4": "Sraigė", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://lt.wikipedia.org/wiki/Sraigė", "Puzzle.picture": "nuotrauka:", "Puzzle.legs": "kojos:", "Puzzle.legsChoose": "pasirinkti...", "Puzzle.traits": "požymiai:", "Puzzle.error0": "Puiku!\nVisi %1 blokai yra teisingi.", "Puzzle.error1": "Beveik! Vienas blokas yra neteisingas.", "Puzzle.error2": "%1 blokai yra neteisingi.", "Puzzle.tryAgain": "Paryškintas blokas yra neteising<PERSON>.\nToliau bandykite.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.helpText": "Kiekvienam gyvūnui (žali) pridėk jo paveikslėlį, parink kojų skaičių ir padaryk jo požymių krūvelę.", "Maze.moveForward": "jud<PERSON><PERSON> į priekį", "Maze.turnLeft": "sukti į kairę", "Maze.turnRight": "sukti į dešinę", "Maze.doCode": "da<PERSON><PERSON>", "Maze.helpIfElse": "Leidžia pasirinkti vieną iš dviejų variantų", "Maze.pathAhead": "jei galima eiti pirmyn", "Maze.pathLeft": "jei yra kelias į kairę", "Maze.pathRight": "jei yra kelias į dešinę", "Maze.repeatUntil": "kartoti iki", "Maze.moveForwardTooltip": "Veikėjas paeis į priekį per vieną langelį", "Maze.turnTooltip": "Veikėjas Pasisuks į kairę/dešinę 90 laipsnių", "Maze.ifTooltip": "<PERSON>i yra kelias n<PERSON> k<PERSON>, atlikt veiksmus.", "Maze.ifelseTooltip": "<PERSON>i nurodyta kryptimi yra keli<PERSON>, atlik pirmosios šak<PERSON> k<PERSON>. O jei ne - antrosios.", "Maze.whileTooltip": "Kartot<PERSON> išvardintus veiksmus pagal sąlygą.", "Maze.capacity0": "Jums liko %0 blokų.", "Maze.capacity1": "Jums liko %1 blokas.", "Maze.capacity2": "Jums liko %2 blokai.", "Maze.runTooltip": "Veik<PERSON><PERSON> da<PERSON>, kas nuro<PERSON>ta blo<PERSON>ose-komandos<PERSON>.", "Maze.resetTooltip": "<PERSON><PERSON><PERSON> veikėją į pradinį tašką.", "Maze.helpStack": "Sujunkite kelis 'pirmyn' blokus kartu, kad pasiek<PERSON>u tikslą.", "Maze.helpOneTopBlock": "Šiame lygyje visi naudojami blokai turi būti su<PERSON>.", "Maze.helpRun": "Paspauskite „Vykdyti”, ir pamatysite, ką suprogramavote :)", "Maze.helpReset": "<PERSON><PERSON>, ne<PERSON>vyko :( Patobulinkite programą ir bandykite iš naujo ;)", "Maze.helpRepeat": "Įveikite atstumą naudodami tik 2 blokus. Panaudokite 'kartok' bloką ;)", "Maze.helpCapacity": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON> blokų kiekį. Nor<PERSON>dami pan<PERSON>oti dar vieną, turite pa<PERSON>linti kažkurį esantį.", "Maze.helpRepeatMany": "'kartok' bloko viduje galima įdėti daugiau negu vieną bloką.", "Maze.helpSkins": "Pasirinkite savo mėgstamą žaidėją iš <PERSON>io meniu.", "Maze.helpIf": "'jei' blokas bus vykdomas tik esant nurodytoms aplinkybėms. Pabandyk sukt į kairę, jei ten yra kelias..", "Maze.helpMenu": "Norėdami p<PERSON> \"jei\" b<PERSON><PERSON>, spustelėkite %1.", "Maze.helpWallFollow": "<PERSON><PERSON> pav<PERSON><PERSON> įveiki šį sudėtingą labirintą. Pamėgink visada laikytis kairė<PERSON> si<PERSON> ;) Skirta pažengusiems programuotojamsǃ", "Bird.noWorm": "neturi kirmino", "Bird.heading": "<PERSON>raš<PERSON><PERSON>", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON><PERSON>, kai pauk<PERSON> neturi kirmino.", "Bird.headingTooltip": "Judėti duotojo kampo kryptimi: 0 į de<PERSON><PERSON><PERSON>, 90 tiesiai ir t.t.", "Bird.positionTooltip": "x ir y žymi paukščio poziciją. Kai x = 0 paukštis yra netoli kairiojo krašto, kai x = 100 jis yra netoli dešiniojo krašto. Kai y = 0 paukštis yra apačioje, kai y = 100 jis yra viršuje.", "Bird.helpElse": "Paspauskite ant i<PERSON>, kad p<PERSON><PERSON> „arba“ blok<PERSON>.", "Turtle.moveTooltip": "Perkelia vėžliuką pirmyn/atgal nurodytu atstumu (skaičiuojamu ekrano taškais).", "Turtle.moveForward": "p<PERSON><PERSON>n", "Turtle.moveBackward": "atgal", "Turtle.turnTooltip": "Pasuka vėžliuką ka<PERSON>/de<PERSON><PERSON><PERSON><PERSON> tiek, kiek nurodyta laipsnių.", "Turtle.turnRight": "<PERSON><PERSON><PERSON>", "Turtle.turnLeft": "kairėn", "Turtle.widthTooltip": "Keičia pieštuko storį.", "Turtle.setWidth": "nustatyti plotį", "Turtle.colourTooltip": "Keičia pieštuko spalvą", "Turtle.setColour": "nustatyti spalvą", "Turtle.penTooltip": "Įjungia/išjungia piešimą. Tarsi nuleidžia/pakelia pie<PERSON> ant/nuo lapo.", "Turtle.penUp": "atitrauk pieštuką nuo lapo", "Turtle.penDown": "priliesk pieštuką prie lapo", "Turtle.turtleVisibilityTooltip": "Paslėpia/parodo \"vėžliuką\"-pieštuką.", "Turtle.hideTurtle": "slėpti vėžlį", "Turtle.showTurtle": "<PERSON><PERSON><PERSON> vėžlį", "Turtle.printTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (nupiešia) tekstą savo žiūrima kryptimi.", "Turtle.print": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontTooltip": "<PERSON>ustato <PERSON>imo komandai šriftą.", "Turtle.font": "<PERSON><PERSON><PERSON>", "Turtle.fontSize": "<PERSON><PERSON><PERSON> d<PERSON>", "Turtle.fontNormal": "normalus", "Turtle.fontBold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.fontItalic": "pasvirasis", "Turtle.submitDisabled": "Vykdykite programą, kol ji sustos. Tada galite pateikti savo piešinį į galeriją.", "Turtle.galleryTooltip": "Atidaryti piešinių galeriją.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.submitTooltip": "Pateikti savo piešinį į galeriją.", "Turtle.submitMsg": "Įkelti galerijon", "Turtle.helpUseLoop": "<PERSON><PERSON> sprendinys veikia, tačiau tu gali geriau.", "Turtle.helpUseLoop3": "Nupiešk figūrą tik iš trijų blokų.", "Turtle.helpUseLoop4": "Nupiešk žvaigždė tik su keturiais blokais.", "Turtle.helpText1": "Sukurk programą, kuri piešia kvadratą.", "Turtle.helpText2": "Pakeisk savo programą, kad ji pieš<PERSON> penkiakampį vietoje kvadrato.", "Turtle.helpText3a": "<PERSON><PERSON>, k<PERSON><PERSON> <PERSON> jums keisti spalvą:", "Turtle.helpText3b": "Pieškite geltoną žvaigždę.", "Turtle.helpText4a": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> j<PERSON> pakelti rašiklį nuo popieriaus kai judate:", "Turtle.helpText4b": "Nupiešk mažą gel<PERSON>ą žvaigždę, tada nupiešk liniją virš jos.", "Turtle.helpText5": "Vietoje vienos ž<PERSON>igždės ar gali nupiešti keturia<PERSON>, kurios sudarytų kvadratą?", "Turtle.helpText6": "Nupiešk tris geltonas žvaigždes ir vieną liniją.", "Turtle.helpText7": "Nupiešk ž<PERSON>ig<PERSON>, tada nupiešk keturias baltas linijas.", "Turtle.helpText8": "Nupiešiant 360 baltų linijų jos atrodys kaip pilnatis.", "Turtle.helpText9": "Ar gal<PERSON> p<PERSON> juod<PERSON> a<PERSON>k<PERSON>, kad mėnulis taptu pusm<PERSON>?", "Turtle.helpText10": "Pieškite tai, ką norite. Turite daug naujų blokų tyrinėjimui. Pasilinksminkite!", "Turtle.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON>, ką kiti <PERSON>, naudokite mygtuką „Ži<PERSON>r<PERSON><PERSON> galeriją“. Jei nupiešite ką nors įdomaus, paskelbkite jį naudodami mygtuką „Pateikti į galeriją“.", "Turtle.helpToolbox": "Pasirinkite kategorij<PERSON> norėdami p<PERSON>ti blo<PERSON>.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "prad<PERSON><PERSON> x", "Movie.y1": "<PERSON><PERSON><PERSON><PERSON> y", "Movie.x2": "pabaiga x", "Movie.y2": "pabaiga y", "Movie.radius": "spindulys", "Movie.width": "plotis", "Movie.height": "<PERSON><PERSON><PERSON><PERSON>", "Movie.circleTooltip": "Nupiešia apskritimą nurodytoje vietoje ir su nurodytu spinduliu.", "Movie.circleDraw": "apskritimas", "Movie.rectTooltip": "Nupiešia stačiakampį nurodytoje vietoje ir su nurodytu pločiu ir aukščiu.", "Movie.rectDraw": "stačiakamp<PERSON>", "Movie.lineTooltip": "Nupiešia liniją iš vieno taško į kitą su nurodytu pločiu.", "Movie.lineDraw": "linija", "Movie.timeTooltip": "Gr<PERSON>ž<PERSON> es<PERSON>ą animaci<PERSON> la<PERSON> (0–100).", "Movie.colourTooltip": "Keičia pieštuko spalvą.", "Movie.setColour": "nustatyti spalvą į", "Movie.submitDisabled": "Jūsų filmas nejuda. Na<PERSON><PERSON><PERSON> blo<PERSON>, kad sukurtumėte kažką įdomaus. Tada <PERSON>site siųsti savo filmą į galeriją.", "Movie.galleryTooltip": "Atidaryti filmų galeriją.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.submitTooltip": "Pateikti savo filmą į galeriją.", "Movie.submitMsg": "Įkelti galerijon", "Movie.helpLayer": "Perkelkite fono apskritimą į programos viršų.  Tada jis atsiras už žmogaus.", "Movie.helpText1": "Naudokite paprastas formas žmogaus piešimui.", "Movie.helpText2a": "Šis lygis yra filmas. <PERSON><PERSON>, kad <PERSON> ranka judėtų per ekraną. Paspauskite paleidimo mygt<PERSON>, kad pamatytum<PERSON>te peržiūrą.", "Movie.helpText2b": "<PERSON><PERSON>, „laiko“ bloko reikšmė keičiasi nuo 0 iki 100. <PERSON><PERSON><PERSON> nor<PERSON>, kad rankos „y“ padėtis prasidėtų nuo 0 ir pereitų iki 100, tai turėtų būti papras<PERSON>.", "Movie.helpText3": "„Laiko“ blokas keičiasi nuo 0 iki 100. <PERSON> dabar norite, kad kitos rankos „y“ padėtis prasidėtų nuo 100 ir pereitų iki 0. Ar galite išsiaiškinti paprastą matematinę formulę, kuri pakeičia kryptį?", "Movie.helpText4": "Panaudokite tai ko išmokote praeitame lygyje, kad ž<PERSON> sukryž<PERSON>otumėte kojas.", "Movie.helpText5": "Matematinė rankos formulė yra sudėtinga. Štai atsakymas:", "Movie.helpText6": "Suteikti žmogui porą rankų.", "Movie.helpText7": "Naudokite bloką „jei“, kad nupieštumėte mažą galvą pirmai filmo pusei. Tada nupieškite didelę galvą antrajai filmo pusei.", "Movie.helpText8": "Įpusėjus filmui, pakeiskite kojų kryptį.", "Movie.helpText9": "Nubrėžkite besiplečiantį ratą už žmogaus.", "Movie.helpText10": "Sukurkite filmą apie bet ką. Turite prieigą prie didelio kiekio naujų blokų tyrinėjimui. Pasilinksminkite!", "Movie.helpText10Reddit": "Norėdami pamatyti kitų žmonių sukurtus filmus, naudokite mygtuką „Žiūr<PERSON>ti galeriją“. Jei sukursite įdomų filmą, naudokite mygtuką „Pateikti į  galeriją“, kad jį paskelbtumėte.", "Music.playNoteTooltip": "Sugrokite vieną nurodytos trukmės ir aukščio muzikinę natą.", "Music.playNote": "groti %1 natą %2", "Music.restTooltip": "Laukia nurodytą laik<PERSON>.", "Music.restWholeTooltip": "Laukia vienos pilnos natos.", "Music.rest": "ilsėtis %1", "Music.setInstrumentTooltip": "Perjungiamas į nurodytą instrumentą grojant vėlesnes muzikos natas.", "Music.setInstrument": "Nustatyti instrumentą į %1", "Music.startTooltip": "<PERSON><PERSON><PERSON> viduje <PERSON> b<PERSON>, kai paspaud<PERSON><PERSON> mygtukas „Vykdyti programą“.", "Music.start": "kai %1 yra paspaudžiamas", "Music.pitchTooltip": "Viena nata (C4 yra 7).", "Music.firstPart": "p<PERSON><PERSON><PERSON> da<PERSON>", "Music.piano": "<PERSON><PERSON><PERSON>", "Music.trumpet": "trimitas", "Music.banjo": "bandža", "Music.violin": "s<PERSON><PERSON><PERSON>", "Music.guitar": "gitara", "Music.flute": "fleita", "Music.drum": "bugnas", "Music.choir": "choras", "Music.submitDisabled": "Vykdykite programą, kol ji sustos. Tada galite pateikti savo muziką į galeriją.", "Music.galleryTooltip": "Atidaryti muzikos galeriją.", "Music.galleryMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Music.submitTooltip": "Pateikti savo muziką į galeriją.", "Music.submitMsg": "Pateikti į Galeriją", "Music.helpUseFunctions": "Jūsų sprendimas veikia, bet jūs galite padaryti geriau.  Naudokite <PERSON>, kad suma<PERSON><PERSON>umėte pasikartojančio kodo skaičių.", "Music.helpUseInstruments": "<PERSON><PERSON><PERSON> s<PERSON>, jei kiek<PERSON><PERSON> pradžios bloke naudosite skirtingą instrumentą.", "Music.helpText1": "Sukurkite pirmąsias ket<PERSON> „Frère Jacques“ natas.", "Music.helpText2a": "„Funkcija“ leidžia sugrupuoti blokus kartu ir paleisti juos daugiau nei vieną kartą.", "Music.helpText2b": "Sukurkite funkciją, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> groti pirmą<PERSON> keturia<PERSON> „Frère Jacques“ natas. Paleiskite šią funkciją du kartus.  Nepridėkite jokių naujų natų blokų.", "Music.helpText3": "Sukurkite antrą funk<PERSON> kitai „Frère Jacques“ daliai. Paskutinė nata ilgesnė.", "Music.helpText4": "Sukurkite trečią funkciją kitai „Frère Jacques“ daliai. Pirmosios keturios natos yra trumpesnės.", "Music.helpText5": "Užbaikite visą „<PERSON><PERSON>“ melodiją.", "Music.helpText6a": "Šis naujas blokas leidžia pakeisti į kitą instrumentą.", "Music.helpText6b": "Grokite savo melodiją smuiku.", "Music.helpText7a": "Šis naujas blokas prideda tylų uždelsimą.", "Music.helpText8": "Kiekvienas starto blokas turi sugroti „Frère Jacques“ du kartus.", "Music.helpText9": "Sukurkite keturis pradžios blokus, kurių kiekvienas du kartus groja „Fr<PERSON> Jacques<PERSON>. Pridėkite tinkamą uždelsimo blokų skaičių.", "Music.helpText10": "Sukurkite ką norite. Turite daugybe naujų blokų, kuriuos galite išbandyti. Sėkmės!", "Music.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON>, ką kiti ž<PERSON> su<PERSON>, naudokite mygtuką „Žiūr<PERSON>ti galeriją“. Jei sukursite ką nors įdomaus, paskelbkite tai naudodami mygtuką „Pateikti į galeriją“.", "Pond.scanTooltip": "Ieškokite priešų. Nurodykite kryptį (0–360). Grąžina atstumą iki artimiausio priešo ta kryptimi. <PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON> prie<PERSON>.", "Pond.cannonTooltip": "Iššauki<PERSON> patranką. Nurodykite kryptį (0–360) ir <PERSON><PERSON><PERSON> (0–70).", "Pond.swimTooltip": "Plaukti į priekį. Nurodykite kryptį (0-360).", "Pond.stopTooltip": "Sustabdyti plaukimą. Žaidėjas sulėtės ir sustos.", "Pond.healthTooltip": "Grąžina es<PERSON> sveikatos būklę (0 miręs, 100 sveikas).", "Pond.speedTooltip": "Grąžina es<PERSON>ą ž<PERSON>jo greitį (0 sustoj<PERSON>s, 100 juda pildu grei<PERSON>).", "Pond.locXTooltip": "Grąžina žaidėjo X koordinatę (0 yra kairysis kraštas, 100 yra dešinysis kraštas).", "Pond.locYTooltip": "Grąžina žaidėjo Y koordinatę (0 yra apatinis kraštas, 100 yra viršutinis kraštas).", "Pond.logTooltip": "Išspausdina numerį į narš<PERSON>lės konsolę.", "Pond.docsTooltip": "<PERSON><PERSON><PERSON> kalbos dokumentus.", "Pond.documentation": "Dokumentacija", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Švytuoklė", "Pond.scaredName": "Bailus", "Pond.helpUseScan": "Jūsų sprendimas veikia, bet jūs galite geriau. Naudokite „nuskaitymą“, kad nurodytum<PERSON>te patrankai kaip to<PERSON>.", "Pond.helpText1": "Norėdam<PERSON> į taikinį, naudokite komandą „patranka“. Pirmasis parametras yra kampas, antrasis parametras yra diapazonas. Raskite tinkamą derinį.", "Pond.helpText2": "Į šį taikinį reikia pataikyti daug kartų. Norėdami ką nors daryti neri<PERSON> la<PERSON>, naudokite kilpą „kol (taip)“.", "Pond.helpText3a": "Šis priešininkas juda pirmyn ir atgal, to<PERSON><PERSON><PERSON>u pat<PERSON>ky<PERSON>. „Nuskaityti“ išraiška grąžina priešininkui tikslų diapazoną nurodyta kryptimi.", "Pond.helpText3b": "Šis diapazonas yra būtent tai, ko reikia „patrankos“ komandai, kad būtų galima tik<PERSON>liai iššauti.", "Pond.helpText4": "Šis priešininkas yra per to<PERSON>, kad galėtume panaudoti patrank<PERSON> (kurios riba yra 70 metrų). Vietoj to naudokite komandą „plaukti“, kad pradėtumėte plaukti link prie<PERSON><PERSON><PERSON> ir atsitrenktumėte į jį.", "Pond.helpText5": "Šis priešininkas taip pat per toli, kad panaudotume patranką. Bet jūs esate per silpnas, kad išgyventumėte susidūrimą. Plaukite link p<PERSON><PERSON><PERSON><PERSON>, kol jūsų horizontali padėtis yra mažesnė nei 50. <PERSON><PERSON> „sustoti“ ir naudokite patranką.", "Pond.helpText6": "Šis priešininkas p<PERSON>uk<PERSON>, kai į jį bus pataikyta. Plaukite link jo, jei jis yra u<PERSON> (70 metrų).", "Gallery": "<PERSON><PERSON><PERSON>"}