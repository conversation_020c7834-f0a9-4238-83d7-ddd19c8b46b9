{"@metadata": {"authors": ["<PERSON>"]}, "Games.puzzle": "Puzzle", "Games.maze": "Labyrinth", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.linkTooltip": "S<PERSON><PERSON><PERSON> und auf Bausten verlinke.", "Games.runProgram": "Programm ausführe", "Games.resetProgram": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON>", "Games.catLogic": "Logik", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Mathematik", "Games.catText": "Text", "Games.catLists": "Liste", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variable", "Games.catProcedures": "Funktione", "Games.httpRequestError": "<PERSON><PERSON> <PERSON> Oonfroch hots en Problem geb.", "Games.linkAlert": "Tel von dein Bausten mit dem Link:\n\n%1", "Games.hashError": "„%1“ stimmt leider mit kenem üweren gespeicherte Programm.", "Games.xmlError": "Dein gespeicherte Datei könnt net gelood sin. Vielleicht woard se mit ener annre <PERSON> von <PERSON> erste<PERSON>t.", "Games.listVariable": "List", "Games.textVariable": "Text", "Games.blocks": "<PERSON><PERSON><PERSON>", "Puzzle.legsChoose": "wähl …", "Puzzle.error0": "Perfekt! All %1 Bausten sind richtich.", "Puzzle.error1": "Nächst! En Baustein ist falsch.", "Puzzle.error2": "%1 Bausten sind falsch.", "Puzzle.tryAgain": "Der hervoargehobne Bausten ist falsch. Versuch das noch enmol.", "Puzzle.checkAnswers": "Antworte üwerprüfe", "Puzzle.helpText": "Häng für jeden Stoot (grün) sein Flagg an, wähl sein Sp<PERSON>ch aus und mach einen Stapel mit seine Städte.", "Maze.moveForward": "vorwäärts loofe", "Maze.turnLeft": "links abbieche", "Maze.turnRight": "rechts abbieche", "Maze.doCode": "ausfüre", "Maze.helpIfElse": "Wenn-Sonst-Bausten führe das ene orrer das annre aus.", "Maze.pathAhead": "wenn Pad davoar ist", "Maze.pathLeft": "wenn Pad noh links ist", "Maze.pathRight": "wenn Pad noh rechts ist", "Maze.repeatUntil": "repetiere bis", "Maze.moveForwardTooltip": "Bewecht den Spieler en Feld vor.", "Maze.turnTooltip": "Dreht den Spieler um 90 Grad noh links orrer rechts.", "Maze.ifTooltip": "Falls es ene Pad in der oongeb Richtung gebt, dann eniche Aktione ausführe.", "Maze.ifelseTooltip": "Falls es ene Pad in der oongeb Richtung gebt, dann den earschte Aktionebausten ausführe, anneref<PERSON>s den zwooite.", "Maze.whileTooltip": "Die beigefüchte Aktione wiederhole, bis das Ziel erreicht woard.", "Maze.capacity0": "Du host noch %0 Bausten.", "Maze.capacity1": "Du host noch %1 Bausten.", "Maze.capacity2": "Du host noch %2 Bausten.", "Maze.runTooltip": "<PERSON> Spieler macht das, was die Bausten soogn.", "Maze.resetTooltip": "Setzt den Spieler zurück an den Labyrinth sein Oonfang.", "Maze.helpStack": "<PERSON>erbinn en<PERSON> 'loofe vorwäärts' <PERSON><PERSON><PERSON><PERSON>, um mir zu helfe um das Ziel zu erreiche.", "Maze.helpOneTopBlock": "In dem Sc<PERSON>ht musst du die ganze Bausten in dem weisse Oorweitsbereich zusammerstaple.", "Maze.helpRun": "<PERSON><PERSON><PERSON> dein Programm aus, um zu siehn, was passiert.", "Maze.helpReset": "<PERSON><PERSON><PERSON> dein Programm aus, um zu siehn, was passiert.", "Maze.helpRepeat": "Computador hoon begrenzte Speicher. <PERSON><PERSON><PERSON> das End von dem Pad mit nuar zwooi Bausten. Benutz \"Wiederhole\", um en Bausten meahr als emol auszuführe.", "Maze.helpCapacity": "Du host für die Schicht all Bausten uffgebraucht. Um en neie Bausten zu erstelle, musst du zuearscht en vorhandne Bausten lösche.", "Maze.helpRepeatMany": "Du kannst mehrer Blöcke innerlich ene Block 'repeat' passe.", "Maze.helpSkins": "Wähl dein Lieblingsspieler vom Menü aus.", "Maze.helpIf": "En „Wenn“-<PERSON><PERSON><PERSON> macht etwas, falls die Bedingung woahr ist. Versuch links abzubieche, falls es enen Pad noh links gebt.", "Maze.helpMenu": "Klick uff %1 im 'Wenn'-Bausten um die Bedingung zu ännere.", "Maze.helpWallFollow": "Kannst du das komplizierte Labyrinth löse? Follich der linke Wand. Nuar für fortgeschrittne Programmierer!", "Turtle.moveTooltip": "Bewecht die Schildkrott um den oongeb Weart voarwäarts orrer rückwäarts.", "Turtle.moveForward": "vorwäarts beweche um", "Turtle.moveBackward": "rückwäarts beweche um", "Turtle.turnTooltip": "Dreht die Schildkrott noh links orrer rechts um die oongeb Gradoonzo<PERSON>l.", "Turtle.turnRight": "noh rechts drehe um", "Turtle.turnLeft": "noh links drehe um", "Turtle.widthTooltip": "Ännert der Stift sein Breit.", "Turtle.setWidth": "<PERSON><PERSON><PERSON> setze uff", "Turtle.colourTooltip": "Ännert der Stift sein <PERSON>.", "Turtle.setColour": "<PERSON><PERSON>t die Farreb in", "Turtle.penTooltip": "<PERSON>bt orrer senkt den Stift zum Stopp orrer Start von der Zeichnung.", "Turtle.penUp": "Stift noh uwe", "Turtle.penDown": "Stift noh unne", "Turtle.turtleVisibilityTooltip": "Macht die Schildkrott (Kreis und Peil) sichtbar orrer unsichtbar.", "Turtle.hideTurtle": "Schildkrott ausblenne", "Turtle.showTurtle": "Sc<PERSON>dkrott oonzeiche", "Turtle.printHelpUrl": "https://herx.wikipedia.org/wiki/Buchdruck", "Turtle.printTooltip": "Zeichnet Text in der Richtung von der Schildkrott bei ehrem Standplatz.", "Turtle.print": "Druck", "Turtle.fontHelpUrl": "https://hrx.wikipedia.org/wiki/Schriftschnitt", "Turtle.fontTooltip": "Leht die Schriftoort fest, die vom Druck-Bausten verwennet weard.", "Turtle.font": "Schriftoort", "Turtle.fontSize": "Schriftgröss", "Turtle.fontNormal": "normal", "Turtle.fontBold": "fett", "Turtle.fontItalic": "kursiv"}