{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Dbc334", "Eleassar", "HairyFotr", "<PERSON><PERSON>", "MaxiMouse", "<PERSON><PERSON>"]}, "Games.name": "<PERSON>g<PERSON><PERSON>", "Games.puzzle": "Sestavljanka", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON>č", "Games.turtle": "Želva", "Games.movie": "Film", "Games.music": "Glasba", "Games.pondTutor": "Vadnica Ribnik", "Games.pond": "Ribnik", "Games.linesOfCode1": "Raven ste rešili z 1 vrstico JavaScripta:", "Games.linesOfCode2": "To raven ste rešili z %1 vrsticami JavaScripta:", "Games.nextLevel": "Ste pripravljeni na raven %1?", "Games.finalLevel": "Ste pripravljeni na naslednji izziv?", "Games.submitTitle": "Naslov:", "Games.linkTooltip": "<PERSON><PERSON>ni in poveže z bloki.", "Games.runTooltip": "Požene program, ki ste ga napisali.", "Games.runProgram": "Poženi program", "Games.resetTooltip": "Ustavi program in ponastavi raven.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logika", "Games.catLoops": "Zanke", "Games.catMath": "Matematika", "Games.catText": "<PERSON><PERSON><PERSON>", "Games.catLists": "Seznami", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Spremenljivke", "Games.catProcedures": "Funkcije", "Games.httpRequestError": "V zvezi z zahtevkom je prišlo do težave.", "Games.linkAlert": "Delite svoje bloke s povezavo: %1", "Games.hashError": "»%1« se žal ne ujema z nobenim shranjenim programom.", "Games.xmlError": "Vaše shranjene datoteke ni mogoče naložiti. Mogoče je bila ustvarjena z drugo različico Blocklyja?", "Games.submitted": "Hvala za ta program! Če bo našemu osebju usposobljenih opic všeč, ga bomo v nekaj dneh objavili v galeriji.", "Games.listVariable": "seznam", "Games.textVariable": "be<PERSON><PERSON>", "Games.breakLink": "Ko boste začeli urejati JavaScript, se ne boste več mogli vrniti na urejanje blokov. Bo tako v redu?", "Games.blocks": "Bloki", "Games.congratulations": "Čestitke!", "Games.helpAbort": "Ta raven je zelo težka. <PERSON><PERSON> preskoč<PERSON> in začeti naslednjo igro? Pozneje se lahko vedno vrnete.", "Index.clear": "<PERSON><PERSON><PERSON>š<PERSON> vse vaše rešitve?", "Index.subTitle": "Igre za jutrišnje programerje.", "Index.moreInfo": "Informacije za izobraževalce ...", "Index.startOver": "Želite začeti znova?", "Index.clearData": "Počisti podatke", "Puzzle.animal1": "Raca", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://sl.wikipedia.org/wiki/Raca", "Puzzle.animal2": "Mač<PERSON>", "Puzzle.animal2Trait1": "Brki", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://sl.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "Puzzle.animal3": "Čebela", "Puzzle.animal3Trait1": "Med", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://sl.wikipedia.org/w/index.php?title=<PERSON><PERSON><PERSON>", "Puzzle.animal4": "Polž", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Sluz", "Puzzle.animal4HelpUrl": "https://sl.wikipedia.org/wiki/Pol%C5%BEi", "Puzzle.picture": "slika:", "Puzzle.legs": "noge:", "Puzzle.legsChoose": "izberite ...", "Puzzle.traits": "lastnosti:", "Puzzle.error0": "Odlično!\n{{PLURAL:$1|one=Blok %1 je pravilen|two=Oba bloka sta pravilna|few=Vsi %1 bloki so pravilni|Vseh %1 blokov je pravilnih}}.", "Puzzle.error1": "Skoraj! En blok je napačen.", "Puzzle.error2": "%1 blokov je nepravilnih.", "Puzzle.tryAgain": "Označeni blok ni pravilen.\nPoskusite znova.", "Puzzle.checkAnswers": "Preverite odgovore", "Puzzle.helpText": "Za vsako ž<PERSON> (zelena) pripnite ustrezno sliko, izberite število nog in ustvarite seznam njenih lalstnosti.", "Maze.moveForward": "poj<PERSON>j", "Maze.turnLeft": "zavij levo", "Maze.turnRight": "<PERSON><PERSON><PERSON>", "Maze.doCode": "na<PERSON>i", "Maze.helpIfElse": "Bloki če-potem bodo izvedli eno ali drugo stvar.", "Maze.pathAhead": "če pot naprej", "Maze.pathLeft": "če pot na levo", "Maze.pathRight": "če pot na desno", "Maze.repeatUntil": "pona<PERSON><PERSON><PERSON><PERSON>, dokler ni", "Maze.moveForwardTooltip": "Prestavi igralca naprej za eno polje.", "Maze.turnTooltip": "Obrne igralca levo ali desno za 90 stopinj.", "Maze.ifTooltip": "Če je pot v določeno smer, potem izvede določena dejanja.", "Maze.ifelseTooltip": "Če je pot v določeno smer, potem izvede prvi sklop dejanj. \nSicer izvede drugi sklop dejanj.", "Maze.whileTooltip": "Ponavljan<PERSON><PERSON><PERSON>, dokler ni <PERSON>en cilj.", "Maze.capacity0": "Na voljo imate še %0 blokov.", "Maze.capacity1": "Na voljo imate še %1 blok.", "Maze.capacity2": "Na voljo imate še %2 blokov.", "Maze.runTooltip": "Igralec začne izvajati, kar je zapisano v blokih.", "Maze.resetTooltip": "Postavi igralca na začetek labirinta.", "Maze.helpStack": "Da mi boste pomagali dose<PERSON> cilj, sestavite nekaj blo<PERSON> »premakni naprej«.", "Maze.helpOneTopBlock": "Na tej ravni morate sestaviti vse bloke v belem delovnem območju.", "Maze.helpRun": "Zaženite program, da vidite, kaj se zgodi.", "Maze.helpReset": "Vaš program ni rešil labirinta. Pritisnite »Ponastavi« in poskusite znova.", "Maze.helpRepeat": "Dosezite konec te poti z uporabo samo dveh blokov. Za večkratni zagon bloka kliknite »Ponovi«.", "Maze.helpCapacity": "Porabili ste vse bloke na tej ravni. Nov blok boste lahko ustvarili, če izbrišete katerega od obstoječih blokov.", "Maze.helpRepeatMany": "V blok »ponovi« lahko postavite več kot en blok.", "Maze.helpSkins": "Izberite najljubšega igralca v tem meniju.", "Maze.helpIf": "Blok »if« bo nekaj storil samo, če je pogoj resničen. Če je pot levo, se poskusite obrniti levo.", "Maze.helpMenu": "Za spremembo pogoja bloka »if« kliknite %1.", "Maze.helpWallFollow": "Ali lahko rešite ta zapleteni labirint? Poskusite slediti levi steni. Samo za izkušene programerje!", "Bird.noWorm": "<PERSON>ma <PERSON>", "Bird.heading": "smer", "Bird.noWormTooltip": "Pogoj, ko ptica ni dobila črva.", "Bird.headingTooltip": "Premik v smeri določenega kota: 0 je desno, 90 naravnost gor itn.", "Bird.positionTooltip": "x in y označujeta položaj ptice. Ko je x = 0, je ptica blizu levega roba. Ko je x = 100, je ptica blizu desnega roba. Ko je y = 0, je ptica pri dnu. Ko je y = 100, je ptica pri vrhu.", "Bird.helpHeading": "Spremenite smerni kot, da bo ptica ujela črva in pristala v gnezdu.", "Bird.helpHasWorm": "Uporabite ta blok, da greste v eno smer, če imate črva, in v drugo smer, če črva nimate.", "Bird.helpX": "»x« je vaš trenutni vodoravni položaj. Uporabite ta blok, da greste v eno smer, če je »x« manj<PERSON><PERSON> od <PERSON>, sicer pa v drugo smer.", "Bird.helpElse": "<PERSON><PERSON><PERSON><PERSON>, da spremenite blok »če«.", "Bird.helpElseIf": "Na tej ravni morate uporabiti bloka »sicer-če« in »sicer«.", "Bird.helpAnd": "Blok »in« je resničen samo, če sta oba vhoda resnična.", "Bird.helpMutator": "Povlecite blok »sicer« v blok »če«.", "Turtle.moveTooltip": "Prestavi želvo naprej ali nazaj za določeno količino.", "Turtle.moveForward": "poj<PERSON>e naprej za", "Turtle.moveBackward": "poj<PERSON>e nazaj za", "Turtle.turnTooltip": "<PERSON><PERSON>rne želvo levo ali desno za določeno število stopinj.", "Turtle.turnRight": "obrni desno za", "Turtle.turnLeft": "obrni levo za", "Turtle.widthTooltip": "Spremeni širino pisala.", "Turtle.setWidth": "nastavi širino na", "Turtle.colourTooltip": "Spremeni barvo pisala.", "Turtle.setColour": "nastavi barvo na", "Turtle.penTooltip": "Dvigne ali spusti pisalo za začetek ali konec risanja.", "Turtle.penUp": "pisalo gor", "Turtle.penDown": "p<PERSON>lo dol", "Turtle.turtleVisibilityTooltip": "Skrije ali prikaže želvo (krožec in puščico).", "Turtle.hideTurtle": "sk<PERSON>j <PERSON>el<PERSON>", "Turtle.showTurtle": "prikaži želvo", "Turtle.printHelpUrl": "https://sl.wikipedia.org/wiki/Tisk", "Turtle.printTooltip": "Nariše besedilo v želvini smeri in na njenem mestu.", "Turtle.print": "izpiši", "Turtle.fontHelpUrl": "https://sl.wikipedia.org/wiki/Pisava", "Turtle.fontTooltip": "<PERSON><PERSON><PERSON> pisavo, ki jo bo uporabljal blok za izpis.", "Turtle.font": "pisava", "Turtle.fontSize": "velikost pisave", "Turtle.fontNormal": "običajno", "Turtle.fontBold": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontItalic": "ležeče", "Turtle.submitDisabled": "Zaženite program. <PERSON> ustavi, lahko svojo risbo dodate v galerijo.", "Turtle.galleryTooltip": "Odprite galerijo risb.", "Turtle.galleryMsg": "<PERSON><PERSON> gal<PERSON>", "Turtle.submitTooltip": "Dodajte svojo risbo v galerijo.", "Turtle.submitMsg": "Objavi v galeriji", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON>, vendar jo lahko izbolj<PERSON>.", "Turtle.helpUseLoop3": "Narišite obliko s samo tremi bloki.", "Turtle.helpUseLoop4": "Narišite zvezdo s štirimi bloki.", "Turtle.helpText1": "Ustvarite program, ki nariše kvadrat.", "Turtle.helpText2": "Spremenite program tako, da bo namesto kvadrata narisal petkotnik.", "Turtle.helpText3a": "To je nov blok, ki vam omogoča spremembo barve:", "Turtle.helpText3b": "Narišite rumeno zvezdo.", "Turtle.helpText4a": "Na voljo je nov blok, ki vam omogoča dvig pisala, ko se premaknete:", "Turtle.helpText4b": "Narišite majhno rumeno zvezdo in nato nad njo črto.", "Turtle.helpText5": "Ali lahko namesto ene zvezde narišete štiri zvezde, razporejene v kvadrat?", "Turtle.helpText6": "Narišite tri rumene zvezde in eno belo črto.", "Turtle.helpText7": "Narišite zvezde in nato še štiri bele črte.", "Turtle.helpText8": "Narišite 360 belih črt, ki bodo videti kot polna luna.", "Turtle.helpText9": "<PERSON> lahko dodate <PERSON><PERSON> krog tako, da bo nastal lunin kraj<PERSON>?", "Turtle.helpText10": "<PERSON><PERSON><PERSON><PERSON>, kar koli <PERSON>. Na voljo imate številne nove bloke, ki jih lahko raziš<PERSON>ete. Veliko zabave!", "Turtle.helpText10Reddit": "Z gumbom »Ogled galerije« si lahko ogledate, kaj so narisali drugi uporabniki. Če ste narisali kaj zanimivega, lahko to objavite z gumbom »Objavi v galeriji«.", "Turtle.helpToolbox": "Izberite kategorijo za prikaz blokov.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "začetni x", "Movie.y1": "začetni y", "Movie.x2": "končni x", "Movie.y2": "končni y", "Movie.radius": "polmer", "Movie.width": "<PERSON><PERSON><PERSON>", "Movie.height": "v<PERSON>š<PERSON>", "Movie.circleTooltip": "Nariše krog na določenem mestu in z določenim polmerom.", "Movie.circleDraw": "krog", "Movie.rectTooltip": "Nariše pravokotnik na določenem mestu ter z določeno širino in višino.", "Movie.rectDraw": "pravokotnik", "Movie.lineTooltip": "Nariše črto določene širine od ene do druge točke.", "Movie.lineDraw": "črta", "Movie.timeTooltip": "Vrne trenutni čas v animaciji (0–100).", "Movie.colourTooltip": "Spremeni barvo pisala.", "Movie.setColour": "nastavi barvo na", "Movie.submitDisabled": "Vaš film se ne premika. Uporabite bloke, da ustvarite nekaj zanimivega. Potem lahko oddate vaš film v galerijo.", "Movie.galleryTooltip": "Odprite galerijo filmov.", "Movie.galleryMsg": "<PERSON><PERSON> gal<PERSON>", "Movie.submitTooltip": "Oddajte film v galerijo.", "Movie.submitMsg": "Objavi v galeriji", "Movie.helpLayer": "Premaknite krog v ozadju na vrh svojega programa. Po tem se bo pojavil za osebo.", "Movie.helpText1": "To osebo narišite s preprostimi oblikami.", "Movie.helpText2a": "Na tej ravni boste ustvarili film. <PERSON><PERSON><PERSON>, da se roka osebe premakne prek zaslona. <PERSON><PERSON> predogled pritisnite gumb za predvajanje.", "Movie.helpText2b": "Ob predvajanju filma bo vrednost bloka »time« tekla od 0 do 100. <PERSON><PERSON><PERSON><PERSON>, da se položaj »y« roke začne na položaju 0 in gre do 100, to ne bi smelo biti prezahtevno.", "Movie.helpText3": "Blok 'time' šteje od 0 do 100. <PERSON><PERSON> »y« drugega kraka se mora začeti na 100 in doseči 0. <PERSON> la<PERSON> u<PERSON>ite preprosto matemat<PERSON>č<PERSON>, ki obrne smer?", "Movie.helpText4": "Uporabite vse, kar ste se naučili na prejšnji ravni, da prekrižate noge.", "Movie.helpText5": "Matematična formula za roko je zapletena. Tu je odgovor:", "Movie.helpText6": "<PERSON><PERSON><PERSON> doda<PERSON>te nekaj rok.", "Movie.helpText7": "Z blokom »če« narišite majhno glavo za prvo polovico filma. Nato narišite veliko glavo za drugo polovico filma.", "Movie.helpText8": "Na polovici filma obrnite smer nog v nasprotno smer.", "Movie.helpText9": "Za osebo narišite rastoč krog.", "Movie.helpText10": "Ustvarite film po svojih željah. Na voljo imate številne nove bloke, ki jih lahko raziščete. Veliko zabave!", "Movie.helpText10Reddit": "Z gumbom »Ogled galerije« si lahko ogledate filme, ki so jih ustvarili drugi. Če ste ustvarili zanimiv film, ga lahko objavite z gumbom »Objavi v galeriji«.", "Music.playNoteTooltip": "Predvaja posamezen glasbeni ton določenega trajanja in višine.", "Music.playNote": "zaigraj ton %1 višine %2", "Music.restTooltip": "Počaka določen čas.", "Music.restWholeTooltip": "Počaka eno c<PERSON>.", "Music.rest": "pavza %1", "Music.setInstrumentTooltip": "Pri predvajanju naslednjih glasbenih tonov preklopi na določeno glasbilo.", "Music.setInstrument": "nastavi glasbilo na %1", "Music.startTooltip": "Klik gumba »Zaženi program« izvede vsebovane bloke.", "Music.start": "ob kliku %1", "Music.pitchTooltip": "En ton (C4 je 7).", "Music.firstPart": "prvi del", "Music.piano": "klavir", "Music.trumpet": "trobenta", "Music.banjo": "banjo", "Music.violin": "violina", "Music.guitar": "kitara", "Music.flute": "flavta", "Music.drum": "boben", "Music.choir": "zbor", "Music.submitDisabled": "Zaženite program. <PERSON> se ustavi, lahko ustvarjeno glasbo dodate v galerijo.", "Music.galleryTooltip": "<PERSON>d<PERSON><PERSON> gal<PERSON> g<PERSON>.", "Music.galleryMsg": "<PERSON><PERSON> gal<PERSON>", "Music.submitTooltip": "Dodaj glasbo v galerijo.", "Music.submitMsg": "Objavi v galeriji", "Music.helpUseFunctions": "<PERSON><PERSON><PERSON><PERSON>, vendar bi lahko bila bolj<PERSON>. Z uporabo funkcij zmanjšajte ponavljanje kode.", "Music.helpUseInstruments": "Glasba bo z<PERSON> bolje, če boste pri vsakem začetnem bloku uporabili drugo glasbilo.", "Music.helpText1": "Sestavite prve štiri tone melodije Mojster Jaka.", "Music.helpText2a": "»Funkcija« vam o<PERSON>, da zdr<PERSON>žite bloke in jih nato zaženete večkrat.", "Music.helpText2b": "Ustvarite funkcijo za predvajanje prvih štirih tonov melodije Mojster Jaka. Zaženite jo dvakrat. Ne dodajajte novih blokov za tone.", "Music.helpText3": "Ustvarite drugo funkcijo za naslednji del melodije Mojster Jaka. Zadnji ton je daljši.", "Music.helpText4": "Ustvarite tretjo funkcijo za naslednji del melodije Mojster Jaka. Prvi štirje toni so krajši.", "Music.helpText5": "Dokončajte celotno melodijo Moj<PERSON> J<PERSON>.", "Music.helpText6a": "Z novim blokom lahko spremenite glasbilo.", "Music.helpText6b": "Zaigrajte pesem z <PERSON>o.", "Music.helpText7a": "Novi blok doda glasbeno pavzo (tišino).", "Music.helpText7b": "Ustvarite drugi začetni blok, ki ima dve pavzi in nato zaigra melodijo Mojster Jaka.", "Music.helpText8": "Vsak začetni blok naj melodijo Mojster Jaka zaigra dvakrat.", "Music.helpText9": "Ustvarite štiri začetne bloke. Vsak od njih naj dvakrat zaigra melodijo Mojster Jaka. Dodajte ustrezno število pavz.", "Music.helpText10": "<PERSON><PERSON><PERSON><PERSON>, kar koli <PERSON>. Na voljo imate ogromno novih <PERSON>, ki jih lahko raziščete. Veliko zabave!", "Music.helpText10Reddit": "Z gumbom  »Ogled galerije« si lahko ogledate, kaj zanimivega so ustvarili drugi. Če ste uglasbili kaj zanimivega, lahko to objavite z gumbom »Objavi v galeriji«.", "Pond.scanTooltip": "Išče nasprotnike. <PERSON><PERSON><PERSON><PERSON> smer (0–360). Vrne razdaljo do najbližjega nasprotnika v tej smeri. Če ni nobenega nasprotnika, vrne Neskončno.", "Pond.cannonTooltip": "Ustrelite s topom. Določite smer (0–360) in razdaljo (0–70).", "Pond.swimTooltip": "Plavajte naprej. <PERSON><PERSON><PERSON><PERSON> smer (0–360).", "Pond.stopTooltip": "Ustavite plavanje. Igralec se bo počasi ustavil.", "Pond.healthTooltip": "Vrne trenutno igralčevo zdravje (0 je mrtev, 100 je zdrav).", "Pond.speedTooltip": "Vrne trenutno igralčevo hitrost (0 je ustavljen, 100 polna hitrost).", "Pond.locXTooltip": "Vrne koordinato X igralca (0 je levi rob, 100 je desni rob).", "Pond.locYTooltip": "Vrne koordinato Y igralca (0 je spodnji rob, 100 je zgornji rob).", "Pond.logTooltip": "V konzoli brskalnika izpiše številko.", "Pond.docsTooltip": "Prikažite dokumentacijo jezika.", "Pond.documentation": "Dokumentacija", "Pond.playerName": "Igralec", "Pond.targetName": "Tarča", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Prestrašeni", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON>u<PERSON>, vendar lahko nalogo opravite bolje. Z ukazom »scan« dolo<PERSON><PERSON>, kako da<PERSON> naj top strelja.", "Pond.helpText1": "Uporabite blok »cannon«, da zadanete tarčo. Prvi parameter je kot, drugi parameter je razdalja. Poiščite pravo kombinacijo.", "Pond.helpText2": "To tarčo je treba zadeti večkrat. Da se bo dejanje i<PERSON>skončnokrat, uporabite zanko »while (true)«.", "Pond.helpText3a": "Ta nasprotnik se premika nazaj in naprej, zato ga je tež<PERSON> z<PERSON>. Blok »scan« v določeni smeri vrne natančno razdaljo do nasprotnika.", "Pond.helpText3b": "Prav ta je to, kar »cannon« (top) potrebuje, da lahko to<PERSON>no strelja.", "Pond.helpText4": "Ta nasprotnik je predaleč za uporabo topa (ki lahko strelja do razdalje 70 metrov). Namesto tega uporabite blok »swim«, da boste začeli plavati proti nasprotniku in se boste zaleteli vanj.", "Pond.helpText5": "Ta nasprotnik je prav tako pre<PERSON>, da bi uporabil top. <PERSON><PERSON> preš<PERSON> ste, da bi preživeli trk. Plavajte proti nasprotniku, dokler je vaša vodoravna koordinata manjša od 50. Potem uporabite »stop« in ustrelite s topom.", "Pond.helpText6": "Ta nasprotnik se bo um<PERSON>, <PERSON>e ga boste z<PERSON>. <PERSON>e je zunaj <PERSON>ta (70 metrov), z<PERSON><PERSON>j<PERSON> proti njemu.", "Gallery": "<PERSON><PERSON><PERSON>"}