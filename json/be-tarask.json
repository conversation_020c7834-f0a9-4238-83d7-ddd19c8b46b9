{"@metadata": {"authors": ["<PERSON>-<PERSON>", "Nerogaf", "Red Winged Duck", "Rene<PERSON><PERSON><PERSON>", "Wizardist"]}, "Games.name": "Г<PERSON><PERSON><PERSON><PERSON><PERSON>ly", "Games.puzzle": "Мазгатня", "Games.maze": "Ля<PERSON><PERSON><PERSON>ынт", "Games.bird": "Птушка", "Games.turtle": "Чарапашка", "Games.movie": "Фільм", "Games.music": "Музыка", "Games.pondTutor": "Прудок", "Games.pond": "Сажалка", "Games.linesOfCode1": "Узровень пройдзены з дапамогай аднаго радка JavaScript:", "Games.linesOfCode2": "Узровень пройдзены з дапамогай %1 радкоў JavaScript:", "Games.nextLevel": "Вы гатовыя да ўзроўню %1?", "Games.finalLevel": "Вы гатовы да наступнага выпрабаваньня?", "Games.submitTitle": "Назва:", "Games.linkTooltip": "Захаваць і зьвязаць з блёкамі.", "Games.runTooltip": "Запусьціць праграму, якую Вы напісалі.", "Games.runProgram": "Запусьціць праграму", "Games.resetTooltip": "Спыніць праграму і скінуць у пачатковы стан.", "Games.resetProgram": "Скасаваць", "Games.help": "Дапамога", "Games.catLogic": "Лёгіка", "Games.catLoops": "Петлі", "Games.catMath": "Матэматычныя формулы", "Games.catText": "Тэкст", "Games.catLists": "Сьпісы", "Games.catColour": "<PERSON>о<PERSON><PERSON><PERSON>", "Games.catVariables": "Зьменныя", "Games.catProcedures": "Функцыі", "Games.httpRequestError": "Узьнікла праблема з запытам.", "Games.linkAlert": "Падзяліцца Вашым блёкам праз гэтую спасылку:\n\n%1", "Games.hashError": "Прабачце, '%1' не адпавядае ніводнай захаванай праграме.", "Games.xmlError": "Не атрымалася загрузіць захаваны файл. Магчыма, ён быў створаны з іншай вэрсіяй Блёклі?", "Games.submitted": "Дзя<PERSON><PERSON>й вам за гэтую праграму! Калі нашыя дасьведчаныя малпачкі ўпадабаюць яе, яны апублікуюць яе ў галерэі цягам наступных некалькіх дзён.", "Games.listVariable": "сьпіс", "Games.textVariable": "тэкст", "Games.breakLink": "Калі вы пачнеце рэдагаваць JavaScript, вы ня зможаце вярнуцца да рэдагаваньня блёкаў. Працягваем?", "Games.blocks": "Блёкі", "Games.congratulations": "Віншуем!", "Games.helpAbort": "Гэты ўзровень вельмі складаны. Магчыма Вы жадаеце прапусьціць яго і перайсьці да наступнай гульні? Вы заўсёды можаце сюды вярнуцца.", "Index.clear": "Выдаліць усе Вашыя рашэньні?", "Index.subTitle": "Гульні для будучых праграмістаў.", "Index.moreInfo": "Інфармацыя для пэдагогаў…", "Index.startOver": "Жадаеце пачаць занава?", "Index.clearData": "Ачысьціць зьвесткі", "Puzzle.animal1": "Качка", "Puzzle.animal1Trait1": "Пер'е", "Puzzle.animal1Trait2": "Дзюба", "Puzzle.animal1HelpUrl": "https://be-x-old.wikipedia.org/wiki/%D0%9A%D0%B0%D1%87%D0%BA%D1%96", "Puzzle.animal2": "Кот", "Puzzle.animal2Trait1": "Вусы", "Puzzle.animal2Trait2": "Футра", "Puzzle.animal2HelpUrl": "https://be-x-old.wikipedia.org/wiki/%D0%9A%D0%BE%D1%82", "Puzzle.animal3": "Пчала", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "Джала", "Puzzle.animal3HelpUrl": "https://be-x-old.wikipedia.org/wiki/%D0%9F%D1%87%D0%BE%D0%BB%D1%8B", "Puzzle.animal4": "Сьлімак", "Puzzle.animal4Trait1": "Ракушка", "Puzzle.animal4Trait2": "Сьлізь", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "выява:", "Puzzle.legs": "лапы:", "Puzzle.legsChoose": "выберыце...", "Puzzle.traits": "асаблівасьці:", "Puzzle.error0": "Выдатна!\nУсе блёкі (%1) зьмешчаныя правільна.", "Puzzle.error1": "Бязмала! Адз<PERSON>н блёк зьмешчаны няправільна.", "Puzzle.error2": "Некалькі блёкаў (%1) зьмешчаныя няправільна.", "Puzzle.tryAgain": "Вылучаны блёк зьмешчаны няправільна.\nСпрабуйце яшчэ.", "Puzzle.checkAnswers": "Праверыць адказы", "Puzzle.helpText": "Далучыце да кожнай жывёлы (зялён<PERSON> блёк), выберыце колькасьць лап і падайце іншыя асаблівасьці.", "Maze.moveForward": "рухацца наперад", "Maze.turnLeft": "паварот налева", "Maze.turnRight": "павярнуць направа", "Maze.doCode": "выканаць", "Maze.helpIfElse": "Блёкі „калі-інакш“ будуць выконваць адно ці іншае дзеяньне.", "Maze.pathAhead": "калі шлях наперадзе", "Maze.pathLeft": "калі шлях налева", "Maze.pathRight": "калі шлях направа", "Maze.repeatUntil": "паўтараць, пакуль", "Maze.moveForwardTooltip": "Перамяшчае гульца наперад на адно поле.", "Maze.turnTooltip": "Павярнуць гульца налева або направа на 90 градусаў.", "Maze.ifTooltip": "Калі шлях у паказаным кірунку існуе, то выканаць пэўныя дзеяньні.", "Maze.ifelseTooltip": "Калі існуе шлях у паказаным кірунку, то выканаць першы блёк дзеяньняў. У адваротным выпадку, выканаць другі блёк дзеяньняў.", "Maze.whileTooltip": "Паўтарыце закрытыя дзеяньні, пакуль не будзе дасягнутая канцавая кропка.", "Maze.capacity0": "У Вас засталося %0 блёкаў.", "Maze.capacity1": "У Вас застаўся %1 блёк.", "Maze.capacity2": "У Вас засталося %2 блёкі(аў).", "Maze.runTooltip": "Дазваляе гульцу рабіць тое, што скажуць блёкі.", "Maze.resetTooltip": "Вярнуць гульца у пачатак лябірынту.", "Maze.helpStack": "Складзіце некалькі блёкаў «рухацца наперад», каб дапамагчы мне дасягнуць мэты.", "Maze.helpOneTopBlock": "На гэтым узроўні, неабходна, скласьці разам усе блёкі белай працоўнай прасторы.", "Maze.helpRun": "Запусьціць праграму, каб паглядзець, што адбываецца.", "Maze.helpReset": "Ваша праграма не вырашае лябірынт. Націсьніце кнопку 'Ськінуць', і паспрабуйце зноў.", "Maze.helpRepeat": "Прайдзіце лябірынт карыстаючыся толькі двума блёкамі. Для запуску блёку больш аднаго разу, карыстайцеся камандай 'паўтарыць'.", "Maze.helpCapacity": "Вы выкарысталі ўсе блёкі для гэтага ўзроўню. Каб стварыць новы блёк, Вам спачатку неабходна выдаліць існуючы блёк.", "Maze.helpRepeatMany": "Вы можаце разьмясьціць больш аднаго блёку ў блёку 'паўтарыць'.", "Maze.helpSkins": "Выберыце ўлюблёнага гульца ў гэтым мэню.", "Maze.helpIf": "Блёк 'калі' выканае што-небудзь толькі ў выпадку слушнай умовы. Паспрабуйце павярнуць улева калі гэта магчыма.", "Maze.helpMenu": "Націсьніце блёк %1 'калі', каб зьмяніць яго ўмову.", "Maze.helpWallFollow": "Вы можаце вырашыць гэты складаны лябірынт? Паспрабуйце прытрымлівацца левай сьцяны. Толькі для прасунутых праграмістаў!", "Bird.noWorm": "ня мае чарвяка", "Bird.heading": "нак<PERSON><PERSON><PERSON><PERSON>к", "Bird.noWormTooltip": "Стан, калі птушка яшчэ не злавіла чарвяка.", "Bird.headingTooltip": "Рухацца ў кірунку, зададзеным кутом: 0 направа, 90 уверх і г. д.", "Bird.positionTooltip": "x і y адзначаюць пазыцыю птушкі. Калі x = 0 птушка знаходзіцца каля левага краю, калі x = 100 яна каля правага краю. Калі y = 0 птушка знаходзіцца ўнізе, калі y = 100 на самым версе.", "Bird.helpHeading": "Зьмяніць кірунак руху птушкі, каб яна злавіла чарвяка і села ў сваё гняздо.", "Bird.helpHasWorm": "Выкарыстоўвайце гэты блёк, каб рухацца ў адным кірунку, калі чарвяк злоўлены і ў іншым, калі чарвяк ня злоўлены.", "Bird.helpX": "«x» — гэта актуальная гарызантальная пазыцыя. Выкарыстоўвайце гэты блёк, каб рухацца ў адным кірунку, калі «x» меней за лік, ці ў іншым кірунку ў адваротным выпадку.", "Bird.helpElse": "Націсьніце на іконку, каб зьмяніць блёк «калі».", "Bird.helpElseIf": "На гэтым узроўні патрабуюцца блёкі «інакш калі» і «інакш».", "Bird.helpAnd": "Блёк «і» мае значэньне ісьціна, калі абодва парамэтры ісьцінныя.", "Bird.helpMutator": "Перацягніце блёк «інакш» у блёк «калі».", "Turtle.moveTooltip": "Перамясьціць чарапахка наперад або назад на зададзеную адлегласьць.", "Turtle.moveForward": "рухацца наперад на", "Turtle.moveBackward": "рухацца назад на", "Turtle.turnTooltip": "Павярнуць чарапаху налева ці направа на паданую колькасьць градусаў.", "Turtle.turnRight": "павярнуць направа на", "Turtle.turnLeft": "павярнуць налева на", "Turtle.widthTooltip": "Зьмена шырыні пяра.", "Turtle.setWidth": "усталяваць шырыню", "Turtle.colourTooltip": "Зьмяніць колер пяра.", "Turtle.setColour": "усталяваць колер", "Turtle.penTooltip": "Паднімае або апускае пяро, каб спыніць або пачаць маляваць.", "Turtle.penUp": "падняць пяро", "Turtle.penDown": "апусьціць пяро", "Turtle.turtleVisibilityTooltip": "Зрабіць чарапаху (круг і стрэлка) бачнай або нябачнай.", "Turtle.hideTurtle": "схаваць чарапаху", "Turtle.showTurtle": "паказаць чарапаху", "Turtle.printHelpUrl": "https://be-x-old.wikipedia.org/wiki/Друкарства", "Turtle.printTooltip": "Малюе тэкст у кірунку чарапахі ад яе знаходжаньня.", "Turtle.print": "друк", "Turtle.fontHelpUrl": "https://be-x-old.wikipedia.org/wiki/Шрыфт", "Turtle.fontTooltip": "Вызначае шрыфт, які выкарыстоўваецца друку блёку.", "Turtle.font": "шрыфт", "Turtle.fontSize": "памер шрыфту", "Turtle.fontNormal": "звычайны", "Turtle.fontBold": "тлусты", "Turtle.fontItalic": "курсіў", "Turtle.submitDisabled": "Запусьціце Вашую праграму і чакайце яе спыненьня. Потым Вы можаце разьмясьціць Ваш малюнак у галерэю.", "Turtle.galleryTooltip": "Адкрыць галерэю малюнкаў.", "Turtle.galleryMsg": "Глядзіце галерэю", "Turtle.submitTooltip": "Разьмясьціць ваш малюнак у галерэі.", "Turtle.submitMsg": "Загрузіць у галерэю", "Turtle.helpUseLoop": "Ваша рашэньне працуе, але можна зрабіць лепей.", "Turtle.helpUseLoop3": "Намалюйце фігуру з трох блёкаў.", "Turtle.helpUseLoop4": "Намалюйце зорку з чатырох блёкаў.", "Turtle.helpText1": "Стварыце праграму, якая намалюе квадрат.", "Turtle.helpText2": "Зьмяніце праграму, каб замест квадрата намаляваць пяцікутнік.", "Turtle.helpText3a": "Гэта новы блёк, які дазваляе Вам зьмяняць колер:", "Turtle.helpText3b": "Намалюй жоўтую зорку.", "Turtle.helpText4a": "Гэта новы блёк, які дазваляе Вам падняць пяро ад паперы пад час руху:", "Turtle.helpText4b": "Намалюйце малую жоўтую зорку, а потым лінію над ёй.", "Turtle.helpText5": "Зможаце замест адной зоркі намаляваць чатыры зоркі ў кутах квадрата?", "Turtle.helpText6": "Намалюйце тры жоўтыя зоркі і адну белую лінію.", "Turtle.helpText7": "Нама<PERSON><PERSON><PERSON><PERSON>ё зоркі, а потым чатыры белыя лініі.", "Turtle.helpText8": "Малюнак з 360 белых лініяў будзе выглядаць як поўны месяц.", "Turtle.helpText9": "Можаце дадаць чорны круг, каб месяц пераўтварыўся ў паўмесяц?", "Turtle.helpText10": "Намалюйце ўсё што заўгодна. Вы атрымаеце вялікую колькасьць новых блёкаў, якія варта вывучыць. Усяго найлепшага!", "Turtle.helpText10Reddit": "Выкарыстоўвайце кнопку «Глядзець галерэю», каб праглядзець малюнкі іншых. Калі Вы намалявалі што-небудзь цікавае, карыстайцеся кнопкай «Захаваць у галерэю», каб апублікаваць малюнак.", "Turtle.helpToolbox": "Выберыце катэгорыю, каб убачыць блёкі.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "пачатковы x", "Movie.y1": "пачатковы y", "Movie.x2": "канчатковы x", "Movie.y2": "канчатковы y", "Movie.radius": "радыюс", "Movie.width": "шырыня", "Movie.height": "вышыня", "Movie.circleTooltip": "Малюе круг у пазначаным месцы з зададзеным радыюсам.", "Movie.circleDraw": "круг", "Movie.rectTooltip": "Малюе прастакутнік у пазначаным месцы з вызначанай даўжынёй і шырынёй.", "Movie.rectDraw": "праста<PERSON><PERSON><PERSON>нік", "Movie.lineTooltip": "Малюе лінію вызначанай шырыні з адной кропкі ў іншую.", "Movie.lineDraw": "лінія", "Movie.timeTooltip": "Вяртае актуальны час у анімацыі (0—100).", "Movie.colourTooltip": "Мяняе колер пяра.", "Movie.setColour": "усталяваць колер", "Movie.submitDisabled": "Ваш фільм нерухомы. Выкарыстоўвайце блёкі, каб зрабіць што-небудзь цікавае. Потым Вы можаце разьмясьціць Ваш фільм у галерэі.", "Movie.galleryTooltip": "Адкрыць галерэю фільмаў.", "Movie.galleryMsg": "Глядзіце галерэю", "Movie.submitTooltip": "Разьмясьціць ваш фільм у галерэі.", "Movie.submitMsg": "Загрузіць у галерэю", "Movie.helpLayer": "Перамясьціце фонавае кола ўверх вашай праграмы. Тады яно зьявіцца за чалавекам.", "Movie.helpText1": "Карыстайцеся простымі фігурамі, каб намаляваць гэтага чалавечка.", "Movie.helpText2a": "Гэты ўзровень зьяўляецца фільмам. Трэба, каб рука чалавека рухалася па экране. Націсьніце кнопку «Прайграць», каб праглядзець.", "Movie.helpText2b": "Падчас прагляду фільму, значэньне блёку «time» мяняецца ад 0 да 100. З-за таго што каардыната «y» рукі таксама павінна пачынацца з 0 і дайсьці да 100, гэта павінна быць лёгка.", "Movie.helpText3": "Блёк «time» лічыць да 0 да 100. Але цяпер патрабуецца мяняць каардынату «y» іншай рукі ад 100 да 0. Ці можаце вы знайсьці простую матэматычную формулу, каб зьмяніць кірунак руху?", "Movie.helpText4": "Карыстайцеся тым, што вы даведаліся на папярэднім узроўні, каб ногі перасекліся.", "Movie.helpText5": "Матэматычная формула для рукі складаная. Вось адказ:", "Movie.helpText6": "Дадайце чалавеку пару рук.", "Movie.helpText7": "Ужывайце блёк «if», каб намаляваць маленькую галаву для першай паловы фільму. Потым намалюйце вялікую галаву для другой паловы фільму.", "Movie.helpText8": "Зрабіце, каб ногі былі ў адваротным кірунку ў сярэдзіне фільму.", "Movie.helpText9": "Намалюйце кола, якое будзе павялічвацца, за чалавекам.", "Movie.helpText10": "Стварыце фільм які пажадаеце. У Вас ёсьць вялікая колькасьць новых блёкаў, якія Вы можаце выкарыстоўваць. Атрымлівайце асалоду!", "Movie.helpText10Reddit": "Карыстайцеся кнопкай «Глядзець галерэю», каб паглядзець фільмы іншых людзей. Калі Вы стварылі цікавы фільм, карыстайцеся кнопкай «Захаваць у галерэі», каб яго апублікаваць.", "Music.playNoteTooltip": "Грае адну музычную ноту вызначанай працягласьці і вышыні.", "Music.playNote": "граць %1 ноту %2", "Music.restTooltip": "Чакае вызначаную працягласьць.", "Music.restWholeTooltip": "Чакае адну цэлую ноту.", "Music.rest": "адпачыць %1", "Music.setInstrumentTooltip": "Пераключае на вызначаны інструмэнт пры прайграваньні наступных музычных нотаў.", "Music.setInstrument": "вызначыць інструмэнт %1", "Music.startTooltip": "Выконвае блёкі ўнутры, калі была націснутая кнопка «Запусьціць праграму».", "Music.start": "калі %1 націснутая", "Music.pitchTooltip": "Адна нота (C4 — гэта 7)", "Music.firstPart": "першая частка", "Music.piano": "піян<PERSON>на", "Music.trumpet": "труба", "Music.banjo": "банджа", "Music.violin": "скрыпка", "Music.guitar": "гітара", "Music.flute": "флейта", "Music.drum": "бар<PERSON><PERSON><PERSON>н", "Music.choir": "хор", "Music.submitDisabled": "Запусьціце вашую праграму і чакайце яе спыненьня. Потым вы зможаце разьмясьціць вашую музыку ў галерэю.", "Music.galleryTooltip": "Адкрыць галерэю музыкі.", "Music.galleryMsg": "Глядзець галерэю", "Music.submitTooltip": "Даслаць сваю музыку ў галерэю.", "Music.submitMsg": "Загрузіць у галерэю", "Music.helpUseFunctions": "Вашае рашэньне працуе, але вы можаце зрабіць лепш. Ужывайце функцыі, каб паменшыць аб’ём коду, які паўтараецца.", "Music.helpUseInstruments": "Музыка будзе гучаць лепш, калі вы выкарыстаеце розныя інструмэнты для кожнага пачатковага блёку.", "Music.helpText1": "Выканаць першыя чатыры ноты «Браткі Якаба».", "Music.helpText2a": "«Функцыя» дазваляе вам групаваць блёкі разам, а потым запускаць больш, чым аднойчы.", "Music.helpText2b": "Стварыце функцыю, каб граць першыя чатыры ноты «Браткі Якаба». Запусьціце гэтую функцыю двойчы. Не дадавайце ніякіх новых блёкаў нотаў.", "Music.helpText3": "Стварыце другую функцыю для наступнай часткі «Браткі Якаба». Апошняя нота больш доўгая.", "Music.helpText4": "Стварыце трэцюю функцыю для наступнай часткі «Браткі Якаба». Першыя чатыры ноты больш кароткія.", "Music.helpText5": "Завяршыце мэлёдыю «Браткі Якаба» да канца.", "Music.helpText6a": "Гэты новы блёк дазваляе вам выкарыстаць іншы музычны інструмэнт.", "Music.helpText6b": "Зайграйце сваю мэлёдыю на скрыпцы.", "Music.helpText7a": "Гэты новы блёк дадае бязгукавую затрымку.", "Music.helpText7b": "Стварае другі пачатковы блёк, які мае два блёкі затрымкі, а потым таксама грае «Братку Якаба».", "Music.helpText8": "Кожны пачатковы блёк павінен граць «Братку Якаба» двойчы.", "Music.helpText9": "Стварыце чатыры пачатковыя блёкі, кожны зь якіх грае «Братку Якаба» двойчы. Дадайце правільную колькасьць блёкаў затрымкі.", "Music.helpText10": "Стварыце тое, што вам хочацца. Вы маеце вялізную колькасьць новых блёкаў для вывучэньня. Атрымлівайце асалоду!", "Music.helpText10Reddit": "Выкарыстоўвайце кнопку «Паглядзець галерэю», каб убачыць, што стварылі іншыя людзі. Калі вы складаеце нешта цікавае, выкарыстоўвайце кнопку «Адправіць у галерэю», каб апублікаваць.", "Pond.scanTooltip": "Пошук ворагаў. Падайце кірунак (0—360). Вяртае адлегласьць да найбліжэйшага ворага ў гэтым кірунку. Вяртае бясконцасьць, калі вораг ня знойдзены.", "Pond.cannonTooltip": "Страляць з гарматы. Падайце кірунак (0—360) і адлегласьць (0—70).", "Pond.swimTooltip": "Плысьці наперад. Падайце кірунак (0—360).", "Pond.stopTooltip": "Перастаць плысьці. Гулец будзе запавольвацца да спыненьня.", "Pond.healthTooltip": "Вяртае стан здароўя гульца (0 — мёртвы, 100 — здаровы).", "Pond.speedTooltip": "Вяртае хуткасьць гульца (0 — нерухомы, 100 — максымальная хуткасьць).", "Pond.locXTooltip": "Вяртае каардынату X гульца (0 — левы бок, 100 — правы бок).", "Pond.locYTooltip": "Вяртае каардынату Y гульца (0 — ніжні край, 100 — верхні край).", "Pond.logTooltip": "Друкуе лік у кансолі вашага браўзэру.", "Pond.docsTooltip": "Паказаць дакумэнтацыю па мове.", "Pond.documentation": "Дакумэнтацыя", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Мэта", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Настра<PERSON><PERSON>ны", "Pond.helpUseScan": "Вашае рашэньне працуе, але можна зрабіць лепей. Выкарыстайце «scan», каб паказаць адлегласьць стральбы гарматам.", "Pond.helpText1": "Выкарыстайце каманду «cannon», каб трапіць у мэту. Першы парамэтар — кут, другі парамэтар — адлегласьць. Знайдзі слушныя суадносіны.", "Pond.helpText2": "Гэтая мэта павінна быць абстраляная шмат разоў. Выкарыстайце «while (true)», каб рабіць што-небудзь бясконца.", "Pond.helpText3a": "Гэты праціўнік рухаецца туды-сюды, з-за чаго ў яго цяжка трапіць. Выраз «scan» вяртае дакладную адлегласьць да праціўніка ў пазначаным кірунку.", "Pond.helpText3b": "Адлегласьць — менавіта тое, што трэба камандзе «cannon» для дакладнай стральбы.", "Pond.helpText4": "Праціўнік занадта далёка, каб выкарыстаць гармату (якая мае абмежаваньне 70 мэтраў). Замест гэтага выкарыстоўвайце каманду «swim», каб плысьці ў бок праціўніка і зьнішчыць яго.", "Pond.helpText5": "Гэты праціўнік таксама занадта далёка, каб выкарыстоўваць гарматы. Але Вы занадта слабы, каб выжыць у сутыкненьні. Плывіце ў бок праціўніка, каб вашае гарызантальнае знаходжаньне было меней чым 50. Потым «stop» і выкарыстайце гармату.", "Pond.helpText6": "Гэты праціўнік будзе аддаляцца, калі ў яго трапяць. Плывіце наперад, калі ён знаходзіцца па-за дыяпазонам (70 мэтраў).", "Gallery": "Галер<PERSON>я"}