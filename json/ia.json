{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>"]}, "Games.name": "Jocos Blockly", "Games.puzzle": "Puzzle", "Games.maze": "Labyrintho", "Games.bird": "Ave", "Games.turtle": "Tortuca", "Games.movie": "Film", "Games.music": "Musica", "Games.pondTutor": "Intro a Stagno", "Games.pond": "Stagno", "Games.linesOfCode1": "Tu ha solvite iste nivello con 1 linea de JavaScript:", "Games.linesOfCode2": "Tu ha solvite iste nivello con %1 lineas de JavaScript:", "Games.nextLevel": "Es tu preste pro le nivello %1?", "Games.finalLevel": "Es tu preste pro le proxime defia?", "Games.submitTitle": "Titulo:", "Games.linkTooltip": "Salveguardar e ligar a blocos.", "Games.runTooltip": "Executar le programma que tu ha scribite.", "Games.runProgram": "Executar programma", "Games.resetTooltip": "Stoppar le programma e reinitialisar le nivello.", "Games.resetProgram": "Reinitialisar", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Mathematica", "Games.catText": "Texto", "Games.catLists": "Listas", "Games.catColour": "Color", "Games.catVariables": "Variabiles", "Games.catProcedures": "Functiones", "Games.httpRequestError": "Il habeva un problema con le requesta.", "Games.linkAlert": "Condivide tu blocos con iste ligamine:\n\n%1", "Games.hashError": "Infelicemente, '%1' non corresponde a alcun programma salveguardate.", "Games.xmlError": "Impossibile cargar le file salveguardate. Pote esser que illo ha essite create con un altere version de Blockly?", "Games.submitted": "<PERSON>ratias pro iste programma! Si nostre equipa lo ama, illes lo publicara in le galeria intra alcun dies.", "Games.listVariable": "lista", "Games.textVariable": "texto", "Games.breakLink": "Un vice que tu comencia a modificar JavaScript, tu non potera revenir a modificar blocos. De accordo?", "Games.blocks": "Blocos", "Games.congratulations": "Felicitationes!", "Games.helpAbort": "Iste nivello es extrememente difficile. Vole tu saltar lo e passar al proxime joco? Tu pote sempre revenir plus tarde.", "Index.clear": "Deler tote tu solutiones?", "Index.subTitle": "Jocos pro le programmatores del futuro.", "Index.moreInfo": "Info pro educatores...", "Index.startOver": "Vole recomenciar?", "Index.clearData": "<PERSON><PERSON> da<PERSON>", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://ia.wikipedia.org/wiki/Anate", "Puzzle.animal2": "Catto", "Puzzle.animal2Trait1": "Mustachios", "Puzzle.animal2Trait2": "Pellicia", "Puzzle.animal2HelpUrl": "https://ia.wikipedia.org/wiki/Catto", "Puzzle.animal3": "Ape", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "Aculeo", "Puzzle.animal3HelpUrl": "https://ia.wikipedia.org/wiki/Ape", "Puzzle.animal4": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://ia.wikipedia.org/wiki/<PERSON><PERSON>a", "Puzzle.picture": "imagine:", "Puzzle.legs": "gambas:", "Puzzle.legsChoose": "selige...", "Puzzle.traits": "tractos:", "Puzzle.error0": "Perfecte!\nTote le %1 blocos es correcte.", "Puzzle.error1": "Quasi! Un bloco es incorrecte.", "Puzzle.error2": "%1 blocos es incorrecte.", "Puzzle.tryAgain": "Le bloco marcate non es correcte.\nFace un altere essayo.", "Puzzle.checkAnswers": "Verificar responsas", "Puzzle.helpText": "Pro cata animal (in verde), attacha su imagine, selige su numero de gambas, e face un pila de su tractos.", "Maze.moveForward": "avantiar", "Maze.turnLeft": "girar a sinistra", "Maze.turnRight": "girar a dextra", "Maze.doCode": "face", "Maze.helpIfElse": "Un bloco \"si/si non\" face un cosa o le altere.", "Maze.pathAhead": "si ha cammino avante", "Maze.pathLeft": "si ha cammino a sinistra", "Maze.pathRight": "si ha cammino a dextra", "Maze.repeatUntil": "repeter usque a", "Maze.moveForwardTooltip": "Face le jocator avantiar un spatio.", "Maze.turnTooltip": "Gira le jocator a sinistra o a dextra de 90 grados.", "Maze.ifTooltip": "Si existe un cammino in le direction specificate, alora face certe actiones.", "Maze.ifelseTooltip": "Si existe un cammino in le direction specificate, alora face le prime bloco de actiones. Si non, face le secunde bloco de actiones.", "Maze.whileTooltip": "Repeter le actiones continite usque al puncto de fin.", "Maze.capacity0": "Te resta %0 blocos.", "Maze.capacity1": "Te resta %1 bloco.", "Maze.capacity2": "Te resta %2 blocos.", "Maze.runTooltip": "Face le jocator facer lo que le blocos dice.", "Maze.resetTooltip": "Remitter le jocator al initio del labyrintho.", "Maze.helpStack": "Impila un par de blocos 'avantiar' pro adjutar me a attinger le fin.", "Maze.helpOneTopBlock": "In iste nivello, tu debe impilar tote le blocos in le spatio de travalio blanc.", "Maze.helpRun": "Executa tu programma pro vider lo que eveni.", "Maze.helpReset": "Tu programma non ha solvite le labyrintho. Preme 'Reinitialisar' e proba de novo.", "Maze.helpRepeat": "Attinge le fin de iste cammino con solmente duo blocos. Usa 'repeter' pro executar un bloco plus de un vice.", "Maze.helpCapacity": "Tu ha usate tote le blocos pro iste nivello. Pro crear un nove bloco, tu debe primo deler un bloco existente.", "Maze.helpRepeatMany": "Tu pote mitter plus de un bloco intra un bloco 'repeter'.", "Maze.helpSkins": "Selige tu jocator favorite de iste menu.", "Maze.helpIf": "Un bloco 'si' face qualcosa solmente si le condition es satisfacite. Essaya girar a sinistra si existe un cammino a sinistra.", "Maze.helpMenu": "Clicca sur %1 in le bloco 'si' pro cambiar su condition.", "Maze.helpWallFollow": "Pote tu solver iste labyrintho complexe? Essaya a sequer le muro sinistre. Programmatores experte solmente!", "Bird.noWorm": "non ha verme", "Bird.heading": "in direction", "Bird.noWormTooltip": "Le condition quando le ave non ha obtenite le verme.", "Bird.headingTooltip": "Displaciar se in le direction del angulo date: 0 es a dextra, 90 es in alto, etc.", "Bird.positionTooltip": "x e y marca le position del ave. Quando x = 0 le ave es proxime al bordo sinistre, quando x = 100 illo es proxime al bordo dextre. Quando y = 0 le ave es in basso, quando y = 100 illo es in alto.", "Bird.helpHeading": "Modifica le angulo del indicator de maniera que le ave attrappa le verme e atterra in su nido.", "Bird.helpHasWorm": "<PERSON>a iste bloco pro vader in un direction si tu ha le verme, o in un altere direction si tu non ha le verme.", "Bird.helpX": "'x' es tu position horizontal actual. Usa iste bloco pro vader in un direction si 'x' es minor de un numero, o alteremente in un altere direction.", "Bird.helpElse": "Clicca sur le icone pro modificar le bloco 'si'.", "Bird.helpElseIf": "<PERSON><PERSON> nivello require e un bloco 'si non si' e un bloco 'si non'.", "Bird.helpAnd": "Le bloco 'e' es ver solmente si ambe su entratas es ver.", "Bird.helpMutator": "<PERSON>raher un bloco 'si non' al bloco 'si'.", "Turtle.moveTooltip": "Displacia le tortuca in avante o a retro in le quantitate specificate.", "Turtle.moveForward": "avantiar", "Turtle.moveBackward": "recular", "Turtle.turnTooltip": "Gira le tortuca a sinistra o a dextra del numero de grados specificate.", "Turtle.turnRight": "girar a dextra de", "Turtle.turnLeft": "girar a sinistra de", "Turtle.widthTooltip": "Cambia le latitude del stilo.", "Turtle.setWidth": "mitter latitude a", "Turtle.colourTooltip": "Cambia le color del stilo.", "Turtle.setColour": "mitter color a", "Turtle.penTooltip": "Leva o pone le stilo pro cessar o comenciar a designar.", "Turtle.penUp": "levar stilo", "Turtle.penDown": "poner stilo", "Turtle.turtleVisibilityTooltip": "Rende le tortuca (circulo e flecha) visibile o invisibile.", "Turtle.hideTurtle": "celar tortuca", "Turtle.showTurtle": "revelar tortuca", "Turtle.printHelpUrl": "https://ia.wikipedia.org/wiki/Impression", "Turtle.printTooltip": "Designa texto in le direction del tortuca in su loco.", "Turtle.print": "scriber", "Turtle.fontHelpUrl": "https://ia.wikipedia.org/wiki/Typo_de_litteras", "Turtle.fontTooltip": "Selige le typo de litteras usate per le bloco 'scriber'.", "Turtle.font": "typo de litteras", "Turtle.fontSize": "dimension de litteras", "Turtle.fontNormal": "normal", "Turtle.fontBold": "grasse", "Turtle.fontItalic": "italic", "Turtle.submitDisabled": "Executa le programma usque al fin. Postea tu pote inviar tu designo al galeria.", "Turtle.galleryTooltip": "Aperir le galeria de designos.", "Turtle.galleryMsg": "Vider galeria", "Turtle.submitTooltip": "Inviar tu designo al galeria.", "Turtle.submitMsg": "Inviar al galeria", "Turtle.helpUseLoop": "<PERSON><PERSON>a, ma tu pote facer melio.", "Turtle.helpUseLoop3": "Designa le forma con solmente tres blocos.", "Turtle.helpUseLoop4": "Designa le stella con solmente quatro blocos.", "Turtle.helpText1": "Crea un programma que designa un quadrato.", "Turtle.helpText2": "Modifica tu programma pro designar un pentagono in loco de un quadrato.", "Turtle.helpText3a": "Il ha un nove bloco que permitte cambiar le color:", "Turtle.helpText3b": "Designa un stella jalne.", "Turtle.helpText4a": "Il ha un nove bloco que permitte levar le stilo quando tu lo displacia:", "Turtle.helpText4b": "Designa un parve stella jalne, postea designa un linea supra illo.", "Turtle.helpText5": "In loco de un stella, pote tu designar quatro sellas arrangiate in quadrato?", "Turtle.helpText6": "Designa tres stellas jalne e un linea blanc.", "Turtle.helpText7": "Designa le stellas, postea designa quatro lineas blanc.", "Turtle.helpText8": "Designar 360 lineas blanc producera un apparentia de luna plen.", "Turtle.helpText9": "Pote tu adder un circulo nigre de maniera que le luna deveni un crescente?", "Turtle.helpText10": "Designa lo que tu vole. Tu ha un massa de nove blocos a explorar. Diverte te!", "Turtle.helpText10Reddit": "Usa le button \"Vider galeria\" pro vider lo que altere personas ha designate. Si tu designa qualcosa de interesse, usa le button \"Inviar al galeria\" pro publicar lo.", "Turtle.helpToolbox": "Elige un categoria pro vider le blocos.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "initio x", "Movie.y1": "initio y", "Movie.x2": "fin x", "Movie.y2": "fin y", "Movie.radius": "radio", "Movie.width": "latitude", "Movie.height": "altitude", "Movie.circleTooltip": "Designa un circulo in le position specificate e con le radio specificate.", "Movie.circleDraw": "circulo", "Movie.rectTooltip": "Designa un rectangulo in le position specificate e con le latitude e altitude specificate.", "Movie.rectDraw": "rectangulo", "Movie.lineTooltip": "Designa un linea ab un puncto verso un altere con le latitude specificate.", "Movie.lineDraw": "linea", "Movie.timeTooltip": "Retorna le tempore actual in le animation (0-100).", "Movie.colourTooltip": "Cambia le color del stilo.", "Movie.setColour": "mitter color a", "Movie.submitDisabled": "Tu animation non se anima. Usa le blocos pro facer qualcosa interessante. Postea tu poterea inviar tu animation al galeria.", "Movie.galleryTooltip": "Aperir le galeria de animationes.", "Movie.galleryMsg": "Vider galeria", "Movie.submitTooltip": "Inviar tu animation al galeria.", "Movie.submitMsg": "Inviar al galeria", "Movie.helpLayer": "Displacia le circulo de fundo al parte superior de tu programma. Illo apparera alora detra le persona.", "Movie.helpText1": "Usar formas simple pro designar iste persona.", "Movie.helpText2a": "<PERSON><PERSON> nivello es un animation. Tu vole displaciar le bracio del persona a transverso le schermo. Preme sur le button reproducer pro vider un exemplo.", "Movie.helpText2b": "Durante que le animation passa, le valor del bloco 'tempore' conta de 0 a 100. Post que tu vole que le position 'y' del bracio comencia a 0 e va usque a 100, isto deberea esser facile.", "Movie.helpText3": "Le bloco 'tempore' conta de 0 a 100. Ma ora tu vole que le position 'y' del altere bracio comencia a 100 e va usque a 0. Pote tu trovar un formula mathematic simple que inverte le direction?", "Movie.helpText4": "Usa lo que tu ha apprendite in le previe nivello pro facer gambas que se crucia.", "Movie.helpText5": "Le formula mathematic pro le bracio es complicate. Ecce le responsa:", "Movie.helpText6": "Da al persona un par de manos.", "Movie.helpText7": "Usa le bloco 'si' pro designar un micre capite pro le prime medietate del animation. Postea designa un grande capite pro le secunde medietate del animation.", "Movie.helpText8": "Face inverter le direction del gambas al medietate del animation.", "Movie.helpText9": "Designa un circulo crescente detra le persona.", "Movie.helpText10": "Face un animation de lo que tu vole. Tu ha un massa de nove blocos a explorar. Diverte te!", "Movie.helpText10Reddit": "Usa le button \"Vider galeria\" pro vider le animationes que altere personas ha facite. Si tu face un animation interessante, usa le button \"Inviar al galeria\" pro publicar lo.", "Music.playNoteTooltip": "Reproduce un nota musical del duration e tono specificate.", "Music.playNote": "reproducer nota %2 per %1", "Music.restTooltip": "Attende le duration specificate.", "Music.restWholeTooltip": "Attende un nota integre.", "Music.rest": "attender %1", "Music.setInstrumentTooltip": "Cambia al instrumento specificate quando se reproduce altere notas musical.", "Music.setInstrument": "mitter instrumento a %1", "Music.startTooltip": "Executa le blocos al interior quando se clicca sur le button 'Executar programma'.", "Music.start": "quando %1 es cliccate", "Music.pitchTooltip": "Un nota (C4 es 7).", "Music.firstPart": "prime parte", "Music.piano": "piano", "Music.trumpet": "trompetta", "Music.banjo": "banjo", "Music.violin": "violino", "Music.guitar": "guitarra", "Music.flute": "flauta", "Music.drum": "tambur", "Music.choir": "choro", "Music.submitDisabled": "Executa le programma usque al fin. Postea tu pote inviar tu musica al galeria.", "Music.galleryTooltip": "<PERSON><PERSON><PERSON> le galeria de musica.", "Music.galleryMsg": "Vider galeria", "Music.submitTooltip": "Inviar tu musica al galeria.", "Music.submitMsg": "Inviar al galeria", "Music.helpUseFunctions": "<PERSON><PERSON> functiona, ma tu pote facer melio. Usa functiones pro reducer le quantitate de codice repetite.", "Music.helpUseInstruments": "Le musica sonara melio si tu usa un instrumento differente in cata bloco initial.", "Music.helpText1": "Compone le prime quatro notas de 'Frère Jacques'.", "Music.helpText2a": "Un 'function' permitte gruppar blocos insimul, postea executar los plure vices.", "Music.helpText2b": "Crea un function pro reproducer le prime quatro notas de '<PERSON><PERSON> Jacques'. Executa le function duo vices. Non adde nove blocos de nota.", "Music.helpText3": "Crea un secunde function pro le proxime parte de 'Frère Jacques'. Le ultime nota es plus longe.", "Music.helpText4": "Crea un tertie function pro le proxime parte de 'Frère Jacques'. Le quatro prime notas es plus curte.", "Music.helpText5": "Completa tote le melodia de '<PERSON><PERSON>'.", "Music.helpText6a": "Iste nove bloco te permitte cambiar a un altere instrumento.", "Music.helpText6b": "<PERSON><PERSON> tu melodia con un violino.", "Music.helpText7a": "Iste nove bloco adde un pausa silente.", "Music.helpText7b": "Crear un secunde bloco de initio que ha duo blocos de pausa, postea etiam joca '<PERSON><PERSON>'.", "Music.helpText8": "Cata bloco de initio debe jocar '<PERSON><PERSON>' duo vices.", "Music.helpText9": "Crea quatro blocos de initio que joca cata uno 'Frère Jacques' duo vices. Adde le numero correcte de blocos de pausa.", "Music.helpText10": "Compone lo que tu vole. Tu ha un massa de nove blocos a explorar. Diverte te!", "Music.helpText10Reddit": "Usa le button 'Vider galeria' pro vider lo que altere personas ha componite. Si tu compone qualcosa de interesse, usa le button 'Inviar al galeria' pro publicar lo.", "Pond.scanTooltip": "Cercar inimicos. Specifica un direction (0-360). Retorna le distantia al inimico le plus proxime in ille direction. Retorna Infinite si non trova alcun inimico.", "Pond.cannonTooltip": "Discargar le cannon. Specifica un direction (0-360) e un portata (0-70).", "Pond.swimTooltip": "Natar in avante. Specifica un direction (0-360).", "Pond.stopTooltip": "Cessar de natar. Le jocator se arresta lentemente.", "Pond.healthTooltip": "Retorna le sanitate actual del jocator (0 es morte, 100 es in optime forma).", "Pond.speedTooltip": "Retorna le velocitate actual del jocator (0 es immobile, 100 es plen velocitate).", "Pond.locXTooltip": "Retorna le coordinata X del jocator (0 es le bordo sinistre, 100 es le bordo dextre).", "Pond.locYTooltip": "Retorna le coordinata Y del jocator (0 es le bordo inferior, 100 es le bordo superior).", "Pond.logTooltip": "Scribe un numero sur le consola de tu navigator.", "Pond.docsTooltip": "Monstrar le documentation del linguage.", "Pond.documentation": "Documentation", "Pond.playerName": "Jocator", "Pond.targetName": "Scopo", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON>", "Pond.helpUseScan": "Tu solution functiona, ma tu pote facer melio. Usa 'scan' pro indicar al cannon a que distantia tirar.", "Pond.helpText1": "Usa le commando 'cannon' pro attinger le scopo. Le prime parametro es le angulo, le secunde parametro es le portata. Trova le combination juste.", "Pond.helpText2": "<PERSON><PERSON> scopo debe esser attingite multe vices. Usa un bucla 'while (true)' pro facer qualcosa indefinitemente.", "Pond.helpText3a": "Iste opponente se move avante e retro, rendente lo difficile a toccar. Le expression 'scan' retorna le distantia exacte al opponente in le direction specificate.", "Pond.helpText3b": "Iste portata es exactemente lo que le commando 'cannon' require pro tirar con precision.", "Pond.helpText4": "Iste opponente es troppo lontan pro usar le cannon (que ha un limite de 70 metros). <PERSON><PERSON>, in vice, le commando 'swim' pro comenciar a natar verso le opponente e collider contra illo.", "Pond.helpText5": "Iste opponente es etiam troppo lontan pro usar le cannon. Ma tu es troppo debile pro superviver un collision. Nata verso le opponente durante que tu position horizontal es minus de 50. <PERSON><PERSON> 'stop' e usa le cannon.", "Pond.helpText6": "Iste opponente se allontanara quando es attingite. Nata verso illo si es foras de portata (70 metros).", "Gallery": "Galeria"}