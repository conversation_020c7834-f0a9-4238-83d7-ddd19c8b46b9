{"@metadata": {"authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Wallkicker", "Սերգեյ Սաֆարյան"]}, "Games.name": "Blockly Խաղեր", "Games.puzzle": "Գլուխկոտրուկ", "Games.maze": "Լաբիրինթոս", "Games.bird": "Թռչուն", "Games.turtle": "Կրիա", "Games.movie": "Ֆիլմ", "Games.music": "Երաժշտություն", "Games.pondTutor": "Լճակ", "Games.pond": "Լճակ", "Games.linesOfCode1": "Դու լուծեցիր խնդիրը այս մակարդակում JavaScript-ի 1 տողով:", "Games.linesOfCode2": "Դու լուծեցիր այս մակարդակը JavaScript-ի %1 տողով՝", "Games.nextLevel": "Պատրա՞ստ ես մակարդակ %1-ին:", "Games.finalLevel": "Պատրա՞ստ ես հաջորդ մարտահրավերին:", "Games.submitTitle": "Վերնագիր.", "Games.linkTooltip": "Պահպանել և ցուցադրել բլոկների հղումը", "Games.runTooltip": "Գործարկիր քո գրած ծրագիրը:", "Games.runProgram": "Գործարկիր Ծրագիրը", "Games.resetTooltip": "Դադարեցնել ծրագիրը և վերականգնել սկզբնական վիճակը:", "Games.resetProgram": "Վերակայել", "Games.help": "Օգնություն", "Games.catLogic": "Տրամաբանական", "Games.catLoops": "Կրկնող հանգույցներ", "Games.catMath": "Մաթեմատիկա", "Games.catText": "Տեքստ", "Games.catLists": "Ցանկեր", "Games.catColour": "Գույն", "Games.catVariables": "Փոփոխականներ", "Games.catProcedures": "Գործառույթներ", "Games.httpRequestError": "Հարցման հետ կապված խնդիր է առաջացել:", "Games.linkAlert": "Կիսվիր բլոկներով այս հղման միջոցով.\n\n%1", "Games.hashError": "Ցավոք, '%1' -ը չի համապատասխանում որևէ պահված ծրագրի:", "Games.xmlError": "Չստացվեց բեռնել ձեր պահած ֆայլը: Հնարավո՞ր է, որ այն ստեղծվել է Blockly-ի այլ տարբերակում:", "Games.submitted": "Շնորհակալությու՜ն այս ծրագրի համար։ Մի քանի օրվա ընթացքում այն կհայտնվի դարանում , եթե մեր մեր պարապած կապիկները հավանեն այն։", "Games.listVariable": "ցանկ", "Games.textVariable": "տեքստ", "Games.breakLink": "Այն պահից, երբ սկսես խմբագրել JavaScript-ը, դու չես կարող վերադառնալ բլոկների խմբագրմանը: Շարունակե՞լ:", "Games.blocks": "Բլոկներ", "Games.congratulations": "Շնորհավորում ենք", "Games.helpAbort": "Այս մակարդակը շատ բարդ է: Ցանկանու՞մ ես բաց թողնել այն և անցնել հաջորդ խաղին: Դու միշտ կարող ես ետ վերադառնալ:", "Index.clear": "Ջնջե՞լ բոլոր լուծումները:", "Index.subTitle": "Խաղեր ապագա ծրագրավորողների համար:", "Index.moreInfo": "Տեղեկատվություն ուսուցիչների համար ...", "Index.startOver": "Ցանկանու՞մ ես նորից սկսել:", "Index.clearData": "Մաքրել տվյալները", "Puzzle.animal1": "Բադ", "Puzzle.animal1Trait1": "Փետուրներ", "Puzzle.animal1Trait2": "Կտուց", "Puzzle.animal1HelpUrl": "https://hy.wikipedia.org/wiki/Բադեր", "Puzzle.animal2": "Կատու", "Puzzle.animal2Trait1": "Բեղ", "Puzzle.animal2Trait2": "Մորթի", "Puzzle.animal2HelpUrl": "https://hy.wikipedia.org/wiki/Կատուներ", "Puzzle.animal3": "Մեղու", "Puzzle.animal3Trait1": "Մեղր", "Puzzle.animal3Trait2": "Խայթ", "Puzzle.animal3HelpUrl": "https://hy.wikipedia.org/wiki/Մեղուներ", "Puzzle.animal4": "Խխունջ", "Puzzle.animal4Trait1": "Խեցի", "Puzzle.animal4Trait2": "Լորձ", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "պատկեր.", "Puzzle.legs": "ոտքեր", "Puzzle.legsChoose": "ընտրիր...", "Puzzle.traits": "հատկանիշներ.", "Puzzle.error0": "Կատարյալ:\nԲոլոր %1 բլոկները ճիշտ են:", "Puzzle.error1": "Գրեթե: Մեկ բլոկը ճիշտ չէ:", "Puzzle.error2": "%1 բլոկ սխալ է:", "Puzzle.tryAgain": "Նշված բլոկը ճիշտ չէ տեղադրված:\nԿրկին փորձիր:", "Puzzle.checkAnswers": "Ստուգել արդյունքը", "Puzzle.helpText": "Յուրաքանչյուր կենդանու համար(կանաչ բլոկ)  կցիր իր նկարը, ընտրիր ոտքերի քանակը և հավաքիր իր առանձնահատկությունները:", "Maze.moveForward": "շարժվել առաջ", "Maze.turnLeft": "թեքվել ձախ", "Maze.turnRight": "թեքվել աջ", "Maze.doCode": "կատարել", "Maze.helpIfElse": "\"Եթե֊հակառակ դեպքում\" հրամանը կկատարի գործողություններից մեկը։", "Maze.pathAhead": "եթե առջևը ուղի կա", "Maze.pathLeft": "եթե ուղին ձախ կողմում է", "Maze.pathRight": "եթե ուղին աջ կողմում է", "Maze.repeatUntil": "կրկնել մինչև", "Maze.moveForwardTooltip": "Տեղաշարժում է խաղացողին առաջ մեկ քայլով:", "Maze.turnTooltip": "Թեքում է խաղացողին ձախ կամ աջ 90 աստիճանով:", "Maze.ifTooltip": "Եթե կա ճանապարհ նշված ուղղությամբ, ապա կատարել որոշ գործողություններ:", "Maze.ifelseTooltip": "Եթե նշված ուղղությամբ ճանապարհ կա, ապա կատարել գործողությունների առաջին բլոկը: Հակառակ դեպքում կատարել գործողությունների երկրորդ բլոկը:", "Maze.whileTooltip": "Կրկնել ներդրված հրամանները, մինչև վերջնակետին հասնելը։", "Maze.capacity0": "Քեզ մնացել է %0 բլոկ:", "Maze.capacity1": "Քեզ մնացել է %1 բլոկ:", "Maze.capacity2": "Քեզ մնացել է %2 բլոկ:", "Maze.runTooltip": "Ճամփորդը կատարում է այն ամենը, ինչ կհրամայեն բլոկները։", "Maze.resetTooltip": "Վերադարձնել ճամփորդին լաբիրինթոսի սկիզբը։", "Maze.helpStack": "Խմբավորի՛ր մի քանի \"գնալ առաջ\" բլոկներ իրար հետ, ինձ նպատակին հասցնելու համար", "Maze.helpOneTopBlock": "Այս մակարդակում դու պետք է իրար միացնես սպիտակ աշխատասեղանի բոլոր բլոկները:", "Maze.helpRun": "Գործարկիր ծրագիրը, որպեսզի տեսնես, թե ինչ է կատարվում:", "Maze.helpReset": "Քո ծրագիրը չի լուծել խնդիրը: Սեղմիր «Վերականգնել» և կրկին փորձիր:", "Maze.helpRepeat": "Հասիր մինչև ճանապարհի վերջ, օգտագործելով միայն երկու բլոկ: Օգտագործիր «կրկնողություն»-ը, որ բլոկները աշխատեն մեկից ավելի անգամ:", "Maze.helpCapacity": "Դու օգտագործել ես բոլոր բլոկները այս մակարդակի համար: Նոր բլոկ ստեղծելու համար նախ պետք է ջնջել արդեն գոյություն ունեցող բլոկը:", "Maze.helpRepeatMany": "Դու կարող ես տեղադրել մեկից ավել բլոկներ «կրկնել» բլոկի ներսում:", "Maze.helpSkins": "Ընտրիր քո սիրելի խաղացողին այս ցանկից:", "Maze.helpIf": "«եթե» բլոկը գործողությունը կկատարի միայն, եթե պայմանը ճիշտ է: Փորձիր ձախ թեքվել, եթե դեպի ձախ թեքվելու ճանապարհ լինի:", "Maze.helpMenu": "Կտացրու «եթե» բլոկի %1-ի վրա, որպեսզի փոխենք դրա պայմանը:", "Maze.helpWallFollow": "Կարո՞ղ ես լուծել այս ծավալուն լաբիրինթը: Փորձիր հետևել ձախ պատին: Միայն փորձառու ծրագրավորողների համար:", "Bird.noWorm": "որդը չի բռնվել", "Bird.heading": "ուղղություն", "Bird.noWormTooltip": "Պայման այն դեպքի համար, երբ թռչունը որդին դեռ չի բռնել:", "Bird.headingTooltip": "Շարժվել տրված անկյան ուղղությամբ` 0-ն նշանակում է դեպի աջ, 90-ը` ուղիղ դեպի վերև, և այլն:", "Bird.positionTooltip": "x-ը և y-ը նշում են թռչնի դիրքը: Երբ x = 0, թռչունը գտնվում է ձախ եզրի մոտ, երբ x = 100, այն գտնվում է աջ եզրի մոտ: Երբ y = 0, թռչունը գտնվում է ներքևում, երբ y = 100, այն վերևում է:", "Bird.helpHeading": "Փոխիր ուղղությունը, որպիսի թռչյունը բռնի ճիճվին և նստի իր բնում։", "Bird.helpHasWorm": "Օգտագործիր այս բլոկը մեկ ուղղությամբ գնալու համար, եթե որդը բռնված է, կամ այլ ուղղությամբ, եթե որդը բռված չէ:", "Bird.helpX": "«x» - ը ներկա հորիզոնական դիրքն է: Օգտագործիր այս բլոկը մեկ ուղղությամբ գնալու համար, եթե «x» - ը թվից փոքր է, կամ այլ ուղղությամբ՝ հակառակ դեպքում:", "Bird.helpElse": "Կտցրու պատկերին \"եթե\" բլոկը փոխելու համար։", "Bird.helpElseIf": "Այս մակարդակում անրհաժեշտ են թե \"հակառակ դեպքում, եթե\" և թե \"հակառակ դեպքում\" բլոկները։", "Bird.helpAnd": "\"Եվ\" բլոկը ճշմարիտ է, եթե երկու մուտքերն էլ ճշմարիտ են", "Bird.helpMutator": "Քաշիր \"հակառակ դեպքում\" բլոկը \"եթե\" բլոկի ներսը։", "Turtle.moveTooltip": "Տեղափոխել կրիան առաջ կամ հետ նշված չափով։", "Turtle.moveForward": "շարժվել առաջ", "Turtle.moveBackward": "շարժվել ետ", "Turtle.turnTooltip": "Պտտել կրիան աջ կամ ձախ նշված աստիճանով։", "Turtle.turnRight": "պտտել աջ", "Turtle.turnLeft": "պտտել ձախ", "Turtle.widthTooltip": "Փոխում է գրիչի լայնությունը:", "Turtle.setWidth": "սահմանել լայնությունը", "Turtle.colourTooltip": "Փոխում է գրչի գույնը:", "Turtle.setColour": "սահմանել գույնը", "Turtle.penTooltip": "Բարձրացնում կամ իջեցնում է գրիչը, գծելը սկսել կամ ավարտելու նպատակով։", "Turtle.penUp": "բարձացնել գրիչը", "Turtle.penDown": "իջեցնել գրիչը", "Turtle.turtleVisibilityTooltip": "Կրիան(շրջան և սլաք) ցուցադրել կամ թաքցնել", "Turtle.hideTurtle": "թաքցնել կրիա", "Turtle.showTurtle": "ցուցադրել կրիան", "Turtle.printHelpUrl": "https://hy.wikipedia.org/wiki/Գրատպություն", "Turtle.printTooltip": "Գծում է տեքստ, կրիայի ուղղությամբ և դիրքում։", "Turtle.print": "տպել", "Turtle.fontTooltip": "Սահմանում տառատեսակ տպագրման բլոկի համար։", "Turtle.font": "տառատեսակ", "Turtle.fontSize": "տառաչափ", "Turtle.fontNormal": "նորմալ", "Turtle.fontBold": "թավատառ", "Turtle.fontItalic": "շեղատառ", "Turtle.submitDisabled": "Աշխատեցրու ծրագիրը մինչ այն կավարտվի։ Ապա կարող ես պահպանել այն պատկերասրահում։", "Turtle.galleryTooltip": "Բացել պատկերասրահը։", "Turtle.galleryMsg": "Տեսնել Պատկերասրահը", "Turtle.submitTooltip": "Պահպանել աշխատանքը պատկերասրահում։", "Turtle.submitMsg": "Պահպանել պատկերասրահում", "Turtle.helpUseLoop": "Քո լուծումը աշխատում է, բայց կարելի է ավելի լավ անել:", "Turtle.helpUseLoop3": "Նկարիր պատկերը՝ օգտագործելով ընդհամենը երեք բլոկ:", "Turtle.helpUseLoop4": "Նկարիր աստղ՝ օգտագործելով ընդհամենը չորս բլոկ:", "Turtle.helpText1": "Ստեղծիր ծրագիր, որը նկարում է քառակուսի:", "Turtle.helpText2": "Փոփոխիր ծրագիրը քառակուսու փոխարեն հնգանկյուն նկարելու համար:", "Turtle.helpText3a": "Սա նոր բլոկ է, որը թույլ է տալիս փոխել գույնը.", "Turtle.helpText3b": "Նկարիր դեղին աստղ:", "Turtle.helpText4a": "Սա նոր բլոկ է, որը թույլ է տալիս բարձրացնել գրիչը թղթից շարժման ժամանակ.", "Turtle.helpText4b": "Նկարիր փոքր դեղին աստղ, ապա նկարիր գիծ նրա վերևը:", "Turtle.helpText5": "Մեկ աստղի փոխարեն կարո՞ղ ես նկարել չորս աստղ քառակուսու անկյուններում:", "Turtle.helpText6": "Նկարիր երեք դեղին աստղ և մեկ սպիտակ գիծ:", "Turtle.helpText7": "Նկարիր աստղերը, ապա նկարիր չորս սպիտակ գիծ:", "Turtle.helpText8": "Նկարված 360 հատ սպիտակ գծերը ունենալու են լիալուսնի տեսք:", "Turtle.helpText9": "Կարո՞ղ ես ավելացնել սև շրջան այնպես, որ լուսինը դառնա կիսալուսին:", "Turtle.helpText10": "Նկարիր այն, ինչ ցանկանում ես: Դու ունես մեծ քանակությամբ նոր բլոկներ, որոնք կարող ես ուսումնասիրել: Զվարճացիր:", "Turtle.helpText10Reddit": "Օգտագործիր \"Տեսնել Պատկերասրահը\" կոճակը այլոց աշխատանքները տեսնելու համար։ Եթե դու որևէ հետաքրքիր բան ես կատարել, օգտագործիր \"Պահպանել պատկերասրահում\" կոճակը, որպիսի քո աշխատանքը նաև ուրիշները տեսնեն։", "Turtle.helpToolbox": "Ընտրիր բաժինը, բլոկները տեսնելու համար։", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "սկզբնական x", "Movie.y1": "սկզբնական y", "Movie.x2": "վերջնական x", "Movie.y2": "վերջնական y", "Movie.radius": "շառավիղ", "Movie.width": "լայնություն", "Movie.height": "բարձրություն", "Movie.circleTooltip": "Գծում է տրված դիրքում և տրված շառավիղով շրջանագիծ։", "Movie.circleDraw": "շրջանագիծ", "Movie.rectTooltip": "Գծում է ուղղանկյուն նշված դիրքում և նշված երկարությամբ և լայնությամբ։", "Movie.rectDraw": "ուղղանկյուն", "Movie.lineTooltip": "Գծում է հատված, մեկ կետից մյուսը, նշված երկարությամբ։", "Movie.lineDraw": "գիծ", "Movie.timeTooltip": "Վերադարձնում է անիմացիայի ընթացիկ ժամանակը։", "Movie.colourTooltip": "Փոխում է գրչի գույնը։", "Movie.setColour": "սահմանել գույնը", "Movie.submitDisabled": "Քո ֆիլմը անշարժ է։ Օգտագործիր բլոկները ավելի հետաքրքիր բան անելու համար, ապա պահպանիր քո աշխատանքը պատկերասրահում։", "Movie.galleryTooltip": "Բացել տեսանյութերի պատկերասրահը։", "Movie.galleryMsg": "Տեսնել Պատկերասրահը", "Movie.submitTooltip": "Պահպանել տեսանյութը պատկերասրահում։", "Movie.submitMsg": "Պահպանել պատկերասրահում", "Movie.helpLayer": "Շարժեք ետնաշերտի շրջանը ծրագրի վերևը։ Այն կհայտնվի անձի հետևում։", "Movie.helpText1": "Օգտագործիր պարզ պատկերներ մարդուկին գծելու համար։", "Movie.helpText2a": "Այս մակարդակը իրենից ներկայացնում է ֆիլմ։ Պահանջվում է, որ ձեռքը շարժվի էկրանով։ Սեղմիր «Սկիզբ» կոճակը տեսնելու համար։", "Movie.helpText2b": "Ֆիլմի դիտման ժամանակ \"ժամանակ\" բլոկի արժեքը փոփոխվում է սկսած 0֊ից մինչև 100։ Հեշտ է, որովհետև ձեռքի \"y\"-ը կորդինատը պիտի փոխվի 0֊ից 100։", "Movie.helpText3": "\"ժամանակ\" բլոկը հաշվում է 0֊ից մինչև 100։ Սակայն հիմա մյուս ձեռքի \"y\" կոդինատը պիտի սկսվի 100֊ից և ավարտվի 0֊ով։ Կարո՞ղ ես մտածել պարզ մաթեմատիկական բանաձև, որը կօգնի լուծել այս խնդիրը։", "Movie.helpText4": "Օգտագործի նախորդ մակադակում սովորածդ ոտքերը խաչաձևելու համար։", "Movie.helpText5": "Ձեռքի համար բանաձևը հեշտ չէ, ահա լուծումը՝", "Movie.helpText6": "Մարդուկին ավելացրու մի զույգ ձեռք։", "Movie.helpText7": "Օգտագործիր «եթե» բլոկը տեսանյութի առաջին մասի փոքր գլուխը նկարելու համար։ Հետո նկարիր մեծ գլուխը ֆիլմի երկրորդ մասի համար։", "Movie.helpText8": "Այնպես արա, որ ոտքերը հակառակ հաջորդականությամբ լինեն տեսանյութի կեսում։", "Movie.helpText9": "Նկարիր ընդարձակվող շրջան մարդու հետևը։", "Movie.helpText10": "Ստեղծիր այն ինչ ուզում ես: Դու ունես մեծ քանակությամբ նոր բլոկներ, որոնք կարող ես ուսումնասիրել: Զվարճացիր:", "Movie.helpText10Reddit": "Օգտագործիր \"Տեսնել Պատկերասրահը\" կոճակը այլոց աշխատանքները տեսնելու համար։ Եթե դու որևէ հետաքրքիր բան ես կատարել, օգտագործիր \"Պահպանել պատկերասրահում\" կոճակը, որպիսի քո աշխատանքը նաև ուրիշները տեսնեն։", "Music.playNoteTooltip": "Նվագում է մեկ երաժշտական նոտա նշված տևողությամբ և տոնայնությամբ։", "Music.playNote": "նվագել %1 նոտա՝ %2", "Music.restTooltip": "Սպասում է նշված տևողությամբ։", "Music.restWholeTooltip": "Սպասում է մեկ ամբողջ նոտա։", "Music.rest": "հագնիստ %1 տևողությամբ", "Music.setInstrumentTooltip": "Հաջորդ նոտաները նվագելուց առաջ միացնում է համապատասխան երաժշտական գործիքը։", "Music.setInstrument": "ընտրել %1 գործիքը", "Music.startTooltip": "Միացնում է ներսի բլոկները, երբ «Գործարկիր ծրագիրը» կոճակը սեղմված է։", "Music.start": "երբ %1 -ը սեղմված է", "Music.pitchTooltip": "Մեկ նոտա (C4-ը  7 է):", "Music.firstPart": "առաջին մաս", "Music.piano": "դաշնամուր", "Music.trumpet": "շեփոր", "Music.banjo": "բանջո", "Music.violin": "ջութակ", "Music.guitar": "կիթառ", "Music.flute": "ֆլեյտա", "Music.drum": "թմբուկ", "Music.choir": "երգչախումբ", "Music.submitDisabled": "Աշխատեցրու ծրագիրը մինչ այն կավարտվի։ Ապա կարող ես պահպանել այն պատկերասրահում։", "Music.galleryTooltip": "Բացել երաժշտության ձայնադարանը:", "Music.galleryMsg": "Տեսնել Ձայնադարանը", "Music.submitTooltip": "Պահպանել երաժշտությունը դարանում։", "Music.submitMsg": "Պահպանել Դարանում։", "Music.helpUseFunctions": "Քո լուծումը աշխատում է, բայց կարող ես ավելի լավ անել: Կիրառիր ֆունկցիաներ կրկնվող կոդի քանակը կրճատելու համար:", "Music.helpUseInstruments": "Մեղեդին ավելի լավ կհնչի, եթե յուրաքանչյուր սկսվող բլոկում օգտագործես տարբեր գործիքներ:", "Music.helpText1": "Կազմիր «<PERSON><PERSON>»֊ի առաջին 4 նոտաները։", "Music.helpText2a": "«Ֆունկցիան» թույլատրում է խմբաորել բլոկները և հետո բազմակի գործարկել դրանք։", "Music.helpText2b": "Ստեծիր ֆունկցիա, որը կնվագի «<PERSON><PERSON>»֊ի առաջին 4 նոտաները։ Գործարկիր այն 2 անգամ։ Մի՛ ավելացրու նոտաների հավելյալ բլոկներ։", "Music.helpText3": "Ստեղծիր երկրորդ ֆունկցիան '<PERSON><PERSON>'֊ի հաջորդ մասի համար։ Վերջին նոտան ավելի երկար է։", "Music.helpText4": "Ստեղծիր 3֊րդ ֆունկցիան «<PERSON><PERSON>» երգի հաջորդ մասի համար։ Առաջին 4 նոտաները ավելի կարճ են։", "Music.helpText5": "Ավարտիր «<PERSON><PERSON>»֊ի մեղեդին։", "Music.helpText6a": "Այս նոր բլոկը նոր գործիք վերցնելու համար է։", "Music.helpText6b": "Նվագիր քո մեղեդին ջութակի վրա:", "Music.helpText7a": "Այս բլոկը ավելացնում է լուռ ընդհատում։", "Music.helpText7b": "Ստեղծիր սկզբի 2֊րդ բլոկը, որը կունենա դադարի 2 բլոկներ, ապա նվագիր «<PERSON><PERSON>»֊ը։", "Music.helpText8": "Յուրաքանչյուր սկզբի բլոկ պիտի «<PERSON><PERSON>»֊ը կրկնակի նվագի։", "Music.helpText9": "Ստեղծիր 4 սկզբի բլոկ։ Յուրաքանչյուրը պիտի 2 անգամ նվագի «<PERSON><PERSON> Jacques»֊ը։ Ավելացրու ճիշտ քանակով դադարի բլոկներ։", "Music.helpText10": "Նվագիր այն, ինչ ցանկանում ես: Դու ունես մեծ քանակությամբ նոր բլոկներ, որոնք կարող ես ուսումնասիրել: Զվարճացիր:", "Music.helpText10Reddit": "Օգտագործիր \"Տեսնել Պատկերասրահը\" կոճակը այլոց աշխատանքները տեսնելու համար։ Եթե դու որևէ հետաքրքիր բան ես կատարել, օգտագործիր \"Պահպանել պատկերասրահում\" կոճակը, որպիսի քո աշխատանքը նաև ուրիշները տեսնեն։", "Pond.scanTooltip": "Որոնում է թշնամիներին։ Նշիր ուղղությունը (0-360): Վերադարձնում է մոտակա թշնամու հեռավորությունը տվյալ ուղղությամբ։ Վերադարձնում է անվերջություն, եթե թշնամի չի գտնվել։", "Pond.cannonTooltip": "Կրակել թնդանոթից։ Նշիր ուղղությունը(0-360) և հեռավորությունը (0-70)։", "Pond.swimTooltip": "Լողալ առաջ: Նշիր ուղղությունը (0-360):", "Pond.stopTooltip": "Դադարեցնել լողալը։ Խաղացողը կկանգնի դանդաղելով։", "Pond.healthTooltip": "Վերադարձնում է խաղացողի ներկա առողջությունը (0֊ն մահացած է, 100֊ը առողջ):", "Pond.speedTooltip": "Վերադարձնում է խաղացողի ընթացիք արագությունը (0 անշարժ է, 100 մաքսիմալ արագություն):", "Pond.locXTooltip": "Վերադարձնում է խաղացողի X կոորդինատը (0-ն ձախ եզրն է, 100-ը՝ աջ եզրը):", "Pond.locYTooltip": "Վերադարձնում է խաղացողի Y կոորդինատը (0-ն ստորին եզրն է, 100-ը՝ վերին եզրը):", "Pond.logTooltip": "Տպում է համարը Ձեր զննարկիչի վահանակում:", "Pond.docsTooltip": "Ցուցադրում է լեզվի ձեռնարկը։", "Pond.documentation": "Ձեռնարկ", "Pond.playerName": "Խաղացող", "Pond.targetName": "Նպատակ", "Pond.pendulumName": "Ճոճանակ", "Pond.scaredName": "Վախեցած", "Pond.helpUseScan": "Քո լուծումը աշխատում է, սակայն կարելի է ավելի լավ անել։ Օգտագործիր 'scan', թնդանոթի կրակի հեռավորությունը նշելու համար։", "Pond.helpText1": "Օգտագործիր 'cannon' հրամանը թիրախը խոցելու համար։ Առաջին պարամետրը անկյունն է, երկրորդը ՝ հեռավորությունը։ Գտիր ճիշտ համատեղությունը։", "Pond.helpText2": "Այս թիրը պետք է խոցել մի քանի անգամ։ Օգտագործիր \"քանի դեռ(ճշմարիտ է)\" հանգույցը որևէ բան անվերջ անելու համար։", "Pond.helpText3a": "Այս հակառակորդը շարժվում է առաջ֊հետ։ Այդ պատճառով դժվար խոցելի է։ \"Scan\" արտահայտությունը վերադարձնում է նշված ուղղությամբ մինչ հակառակորդը ընկած ճշգրիտ հեռավորությունը։", "Pond.helpText3b": "Հեռավորությունը անրհաժեշտ է թնդանոթին ճիշտ կրակելու համար։", "Pond.helpText4": "Այս թշնամին շատ հեռու է թնդանոթի համար (սահմանափակումն է 70 մետր)։ Կրակելու փոխարեն օգտագործիր «swim» հրամանը, որպիսի լողալով բախվես նրա հետ։", "Pond.helpText5": "Այս թշնամին նույնպես հեռու է թնդանոթը օգտագործելու համար։ Սակայն դու էլ չափազանց թույլ ես բախման համար։ Լողա քանի դեռ քո հորիզոնական դիրքը 50֊ից փոքր է, ապա կանգնիր և օգտագործիր թնդանոթը։", "Pond.helpText6": "Այս թշնամին կլողա խոցվելուց հետո։ Լողա առաջ, եթե այն գտնվում է խոցման տիրույթից դուրս (70 մետր)։", "Gallery": "Պատկերասրահ"}