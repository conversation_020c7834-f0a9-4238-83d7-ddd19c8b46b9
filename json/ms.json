{"@metadata": {"authors": ["Anakmalaysia", "Aurora", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Permainan <PERSON>ly", "Games.puzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.maze": "<PERSON><PERSON>", "Games.bird": "Burung", "Games.turtle": "Pepenyu", "Games.movie": "<PERSON><PERSON>", "Games.music": "Muzik", "Games.pond": "Pond", "Games.linesOfCode1": "<PERSON>a telah menyelesaikan  tahap ini dengan 1 baris JavaScript:", "Games.linesOfCode2": "<PERSON>a telah menyelesaikan  tahap ini dengan %1 baris JavaScript:", "Games.nextLevel": "Sedia untuk tahap %2?", "Games.finalLevel": "Sedia untuk cabaran seterusnya?", "Games.submitTitle": "Tajuk:", "Games.linkTooltip": "Simpan dan pautkan kepada blok.", "Games.runTooltip": "Jalankan atur cara anda.", "Games.runProgram": "Jalankan Program", "Games.resetTooltip": "<PERSON><PERSON><PERSON> atur cara dan reset tahap.", "Games.resetProgram": "Reset", "Games.help": "Bantuan", "Games.catLogic": "Logik", "Games.catLoops": "Gelung", "Games.catMath": "Matematik", "Games.catText": "Teks", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Pemboleh ubah", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "Permintaan itu terdapat masalah.", "Games.linkAlert": "<PERSON>sikan blok-blok anda dengan pautan ini:\n\n%1", "Games.hashError": "<PERSON><PERSON>, '%1' tidak berpadanan dengan sebarang aturcara yang disimpan.", "Games.xmlError": "<PERSON>ail simpanan anda tidak dapat dimuatkan. <PERSON><PERSON>-jangan ia dicipta dengan versi Blockly yang berlainan?", "Games.listVariable": "senarai", "Games.textVariable": "teks", "Games.blocks": "Blok", "Games.congratulations": "<PERSON><PERSON><PERSON>!", "Games.helpAbort": "<PERSON>hap ini sangat susah. <PERSON><PERSON><PERSON> anda ingin melangkaunya dan beralih ke permainan seterusnya? Anda boleh sentiasa kembali nanti.", "Index.clear": "Hapuskan semua pen<PERSON> anda?", "Index.subTitle": "Permainan untuk pengaturcara masa depan.", "Index.moreInfo": "Maklumat lanjut...", "Index.startOver": "Nak mula balik?", "Index.clearData": "Padamkan data", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "Bulu pelepah", "Puzzle.animal1Trait2": "Paru<PERSON>", "Puzzle.animal1HelpUrl": "https://ms.wikipedia.org/wiki/Itik", "Puzzle.animal2": "Kucing", "Puzzle.animal2Trait1": "<PERSON><PERSON>", "Puzzle.animal2Trait2": "Bulu", "Puzzle.animal2HelpUrl": "https://ms.wikipedia.org/wiki/Kucing", "Puzzle.animal3": "Lebah", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://ms.wikipedia.org/wiki/Lebah", "Puzzle.animal4": "Siput", "Puzzle.animal4Trait1": "Cangkerang", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://ms.wikipedia.org/wiki/Siput", "Puzzle.picture": "gambar:", "Puzzle.legs": "kaki:", "Puzzle.legsChoose": "pilih...", "Puzzle.traits": "ciri-ciri:", "Puzzle.error0": "Sempurna!\nKesemua %1 blok adalah betul.", "Puzzle.error1": "Sikit saja lagi! Satu blok tidak betul.", "Puzzle.error2": "%1 blok tidak betul.", "Puzzle.tryAgain": "Blok yang ditonjolkan itu tidak betul. Cuba lagi.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON>", "Puzzle.helpText": "<PERSON><PERSON><PERSON> (hijau), <PERSON><PERSON><PERSON> gam<PERSON>, pilih bilangan kakinya, dan susunkan ciri-cirinya.", "Maze.moveForward": "mara ke hadapan", "Maze.turnLeft": "belok kiri", "Maze.turnRight": "belok kanan", "Maze.doCode": "laku<PERSON>", "Maze.helpIfElse": "Blok 'jika-jika tidak' akan memilih salah satu tindakan yang diberikan.", "Maze.pathAhead": "jika ada laluan ke hadapan", "Maze.pathLeft": "jika ada laluan ke kiri", "Maze.pathRight": "jika ada laluan ke kanan", "Maze.repeatUntil": "ul<PERSON>i <PERSON>", "Maze.moveForwardTooltip": "Mengalihkan Pegman satu petak ke hadapan.", "Maze.turnTooltip": "Membelokkan Pegman 90 darjah ke kiri atau ke kanan.", "Maze.ifTooltip": "Jika terdapat laluan ke arah yang tertentu, lakukan beberapa tindakan.", "Maze.ifelseTooltip": "Jika terdapat laluan ke arah yang tertentu, ambil blok tindakan yang pertama. <PERSON><PERSON> tidak, ambil blok tindakan yang kedua.", "Maze.whileTooltip": "<PERSON><PERSON><PERSON> tindakan-tindakan yang dilampirkan sehingga titik penamat dicapai.", "Maze.capacity0": "Tinggal %0 blok.", "Maze.capacity1": "Tinggal %1 blok.", "Maze.capacity2": "Tinggal %2 blok.", "Maze.runTooltip": "Membuatkan pemain menu<PERSON>t pesan blok.", "Maze.resetTooltip": "Letakkn pemain kembali di pangkal pagar sesat.", "Maze.helpStack": "Program merupakan turutan blok. <PERSON>sun<PERSON> beberapa blok 'maju' untuk menolong saya mencapai tempat tujuan.", "Maze.helpOneTopBlock": "Di tingkat ini, anda perlu menyusun semua blok secara bertingkat-tingkat ini di dalam ruang kerja putih.", "Maze.helpRun": "Jalankan program anda untuk melihat apa yang berlaku.", "Maze.helpReset": "Program anda tidak menyelesaikan pagar sesat. <PERSON><PERSON> '<PERSON><PERSON>' dan cuba lagi.", "Maze.helpRepeat": "Komputer mempunyai memori yang terhad. Capai hujung jalan dengan menggunakan dua blok sahaja. Gunakan 'ulang' untuk menjalankan satu blok lebih daripada sekali.", "Maze.helpCapacity": "Anda telah menggunakan semua blok untuk tahap ini. Untuk membuat blok baru, anda perlu memadamkan satu blok yang sedia ada dahulu.", "Maze.helpRepeatMany": "<PERSON>a boleh memuatkan lebih daripada satu blok di dalam blok 'ulang'.", "Maze.helpSkins": "<PERSON><PERSON><PERSON> pemain kegemaran anda daripada menu ini.", "Maze.helpIf": "Blok 'jika' akan melakukan sesuatu hanya jika keadaannya benar. Cuba belok ke kiri jika terdapat laluan ke kiri.", "Maze.helpMenu": "Klik pada %1 di dalam blok 'if' untuk mengubah keadaannya.", "Maze.helpWallFollow": "<PERSON><PERSON><PERSON><PERSON><PERSON> anda menye<PERSON>kan pagar sesat yang rumit ini? Cuba ikut tembok sebelah kiri. Untuk pengaturcara pakar sahaja!", "Bird.noWorm": "tiada cacing", "Bird.heading": "<PERSON><PERSON>", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON> apabila burung tidak dapat cacing.", "Bird.headingTooltip": "<PERSON><PERSON><PERSON> ke arah darjah sudut yang diberikan: 0 ke kanan, 90 terus ke depan, dsb.", "Bird.positionTooltip": "x dan y menandakan kedudukan si burung. Jika x = 0 maka si burung dekat hujung kiri, dan jika x = 100 maka ia dekat hujung kanan. Jika y = 0 maka si burung di bawah sekali, dan jika y = 100 maka ia di atas sekali.", "Bird.helpHeading": "Ubah sudut haluan untuk membuat si burung tangkap cacing dan pulang ke sarangnya.", "Bird.helpHasWorm": "Gunakan blok ini untuk menuju ke satu arah jika sudah dapat cacing, ataupun ke satu lagi arah jika belum.", "Bird.helpX": "'x' ialah posisi melintang semasa anda. Gunakan blok ini untuk mengubah ke satu haluan jika 'x' kurang daripada suatu nombor, ataupun ke satu lagi haluan jika sebaliknya.", "Bird.helpElse": "Klik ikon untuk mengubah suai blok 'if'.", "Bird.helpElseIf": "<PERSON><PERSON>p ini memerlukan blok 'else if' dan juga 'else'.", "Bird.helpAnd": "Blo<PERSON> 'and' benar hanya jika kedua-dua inputnya benar.", "Bird.helpMutator": "<PERSON><PERSON> satu blok 'else' ke dalam blok 'if'.", "Turtle.moveTooltip": "Mengalihkan kekura ke hadapan atau ke belakang pada bilangan yang dinyatakan.", "Turtle.moveForward": "mara ke hadapan", "Turtle.moveBackward": "undur ke belakang", "Turtle.turnTooltip": "Membelokkan kekura ke kiri atau kanan dengan bilangan darjah yang ditetapkan.", "Turtle.turnRight": "belok kanan", "Turtle.turnLeft": "belok kiri", "Turtle.widthTooltip": "Men<PERSON><PERSON> keluasan pena.", "Turtle.setWidth": "laraskan lebarnya pada", "Turtle.colourTooltip": "Mengu<PERSON> warna pena.", "Turtle.setColour": "<PERSON><PERSON>n warna", "Turtle.penTooltip": "Mengangkat atau menurunkan pena untuk berhenti atau bersambung melukis.", "Turtle.penUp": "angkat pen", "Turtle.penDown": "turunkan pen", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> atau mengh<PERSON> kekura (bulatan dan anak panah).", "Turtle.hideTurtle": "halimunankan kekura", "Turtle.showTurtle": "per<PERSON><PERSON><PERSON> kekura", "Turtle.printHelpUrl": "https://ms.wikipedia.org/wiki/Pencetakan", "Turtle.printTooltip": "Melukis teks di arah kekura pada kedudukannya.", "Turtle.print": "cetak", "Turtle.fontHelpUrl": "https://ms.wikipedia.org/wiki/Tipografi", "Turtle.fontTooltip": "Menetapkan fon (rupa huruf) yang digunakan oleh blok cetak.", "Turtle.font": "fon", "Turtle.fontSize": "saiz fon", "Turtle.fontNormal": "biasa", "Turtle.fontBold": "te<PERSON>", "Turtle.fontItalic": "condong", "Turtle.submitDisabled": "Jalankan atur cara anda hingga ia ber<PERSON>ti. <PERSON><PERSON><PERSON> anda boleh hantarkan lukisan anda ke dalam galeri.", "Turtle.galleryTooltip": "<PERSON><PERSON> galeri lukisan di Reddit.", "Turtle.galleryMsg": "<PERSON><PERSON>", "Turtle.submitTooltip": "Han<PERSON><PERSON> lukisan anda ke Reddit.", "Turtle.submitMsg": "Hantarkan ke Galeri", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON><PERSON><PERSON> anda be<PERSON>, cuma perlu diperbaiki.", "Turtle.helpUseLoop3": "Lukiskan bentuk berkenaan dengan hanya tiga blok.", "Turtle.helpUseLoop4": "Lukiskan bintang dengan hanya tiga blok.", "Turtle.helpText1": "Ciptakan program yang melukis segi empat sama.", "Turtle.helpText2": "Ubah atur cara anda untuk melukis segi lima daripada segi empat.", "Turtle.helpText3a": "Terdapat blok baru untuk anda boleh menukar warna:", "Turtle.helpText3b": "Lukis<PERSON> bintang kuning.", "Turtle.helpText4a": "Terdapat blok baru untuk anda boleh mengangkat pen dari kertas sewaktu bergerak:", "Turtle.helpText4b": "Lukiskan satu bintang kuning kecil, disusuli satu garis di atasnya.", "Turtle.helpText5": "<PERSON><PERSON><PERSON> hanya satu bintang, cuba lukis empat bintang yang tersusun dalam segi empat sama.", "Turtle.helpText6": "Lukiskan tiga bintang kuning dan satu garis putih.", "Turtle.helpText7": "<PERSON><PERSON><PERSON>, kemudian melukis empat garis putih.", "Turtle.helpText8": "Lukisan 360 lorek putih akan menyerupai bulan mengambang.", "Turtle.helpText9": "Cuba tambah bulatan hitam supaya bulan menjadi berbentuk sabit.", "Turtle.helpText10": "Lu<PERSON>lah sesuka hati anda. Ada banyak blok baru untuk dicuba. Semoga ceria!", "Turtle.helpText10Reddit": "<PERSON><PERSON> butang \"<PERSON><PERSON> Galeri\" untuk meninjau hasil lukisan orang lain. <PERSON><PERSON> anda ada hasil lukisan menarik, <PERSON><PERSON> \"Hantarkan ke Galeri\" untuk menyiarkan lukisan anda.", "Turtle.helpToolbox": "<PERSON>lih satu kategori untuk melihat blok.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x permulaan", "Movie.y1": "y permulaan", "Movie.x2": "x penamat", "Movie.y2": "y penamat", "Movie.radius": "jejari", "Movie.width": "lebar", "Movie.height": "tinggi", "Movie.circleTooltip": "Melukis bulatan di lokasi tertentu dengan jejari (radius) tertentu.", "Movie.circleDraw": "bulatan", "Movie.rectTooltip": "Melukis segi empat tepat di lokasi tertentu dengan lebar dan tinggi tertentu.", "Movie.rectDraw": "segi empat tepat", "Movie.lineTooltip": "<PERSON><PERSON><PERSON> garisan dari satu titik ke satu lagi titik dalam lebar yang ditetapkan.", "Movie.lineDraw": "garis", "Movie.timeTooltip": "Menyatakan waktu semasa dalam animasi (0-100).", "Movie.colourTooltip": "Mengu<PERSON> warna pena.", "Movie.setColour": "<PERSON><PERSON>n warna", "Movie.submitDisabled": "<PERSON>m anda tidak bergerak. <PERSON><PERSON>n blok-blok untuk mencipta sesuatu yang menarik. <PERSON><PERSON><PERSON> anda boleh menghantar filem anda kepada galeri.", "Movie.galleryTooltip": "<PERSON><PERSON> galeri lukisan di Reddit.", "Movie.galleryMsg": "<PERSON><PERSON>", "Movie.submitTooltip": "Hantarkan gerakan anda ke Reddit.", "Movie.submitMsg": "Hantarkan ke Galeri", "Movie.helpText1": "<PERSON><PERSON><PERSON> bentuk-bentuk ringkas untuk melukis orang ini.", "Movie.helpText2a": "<PERSON>hap ini merupakan filem. Anda mahu melihat bola merah bergolek menyeberangi layar. <PERSON><PERSON> butang main untuk melihat previu.", "Movie.helpText2b": "<PERSON><PERSON><PERSON><PERSON> <PERSON>m dim<PERSON>, ni<PERSON> blok 'time' membilang dari 0 ke 100. Ini mesti senang kerana anda mahu kedudukan mendatar bola merah mula di 0 dan menuju ke 100.", "Movie.helpText3": "Blok 'time' membilang dari 0 ke 100. <PERSON><PERSON><PERSON> kali ini anda mahu kedudukan mendatar bola merah bermula di 100 dan menuju ke 0. Cuba anda hasilkan formula matematik ringkas yang menterbalikkan arahnya.", "Movie.helpText4": "Gunakan apa yang telah anda pelajari di tahap sebelumnya untuk membuat empat biji bola hijau yang bergerak ke semua arah.", "Movie.helpText5": "<PERSON><PERSON> saja nak gerakkan kepala tikus. <PERSON><PERSON><PERSON><PERSON><PERSON> anda membuat pengiraan untuk membuat telinga-telinganya bergerak juga?", "Movie.helpText6": "<PERSON><PERSON> garisan sahaja. Cuba teka apa yang dilakukan oleh hujung-hujung garisan itu.", "Movie.helpText7": "Formula matematik untuk bola jatuh ini rumit. <PERSON><PERSON><PERSON>:", "Movie.helpText8": "Gunakan blok 'if' untuk melukis bola merah dan bola biru untuk separuh pertama filem. Kemudian lukiskan bola hijau untuk separuh kedua filem.", "Movie.helpText9": "<PERSON><PERSON><PERSON><PERSON><PERSON> anda membuat bola yang mengikut dawai? <PERSON>wai sudah pun disediakan. <PERSON><PERSON> boleh, anda boleh berbuat sesuka hati.", "Movie.helpText10": "Bikinlah filem sesuka hati anda. Ada banyak blok baru untuk dicuba. Semoga ceria!", "Movie.helpText10Reddit": "<PERSON><PERSON> butang \"<PERSON><PERSON> Galeri\" untuk menonton filem bikinan orang lain. <PERSON><PERSON> anda ada filem yang menarik, <PERSON><PERSON> \"Hantarkan ke Galeri\" untuk menyiarkan filem anda.", "Music.piano": "piano", "Music.trumpet": "trompet", "Music.banjo": "banjo", "Music.violin": "biola", "Music.guitar": "gitar", "Music.flute": "seruling", "Music.drum": "dram", "Music.choir": "koir", "Music.galleryMsg": "<PERSON><PERSON>", "Pond.scanTooltip": "Imbaskan musuh. Tetapkan arah (0-360). Memaparkan jarak dengan musuh terdekat di arah tersebut. Memaparkan Infinity jika tidak terdapat musuh.", "Pond.cannonTooltip": "Tembakkan meriam. <PERSON><PERSON><PERSON><PERSON> a<PERSON> (0-360) dan jarak (0-70).", "Pond.swimTooltip": "Berenang ke depan. Nyatakan arah (0-360).", "Pond.stopTooltip": "<PERSON><PERSON><PERSON><PERSON> berenang. <PERSON><PERSON><PERSON> akan berhenti <PERSON>.", "Pond.healthTooltip": "Menyatakan kesihatan pemain (0 = mati, 100 = paling sihat).", "Pond.speedTooltip": "Menyatakan kelajuan semasa pemain (0 = pegun, 100 = kelajuan penuh).", "Pond.locXTooltip": "Menyatakan koordinat X pemain (0 = hujung kiri, 100 = hujung kanan).", "Pond.locYTooltip": "Mengembalikan koordinat Y pemain (0 = hujung bawah, 100 = hujung atas).", "Pond.docsTooltip": "<PERSON>rkan dokumentasi bahasa.", "Pond.documentation": "Dokumentasi", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Bandul", "Pond.scaredName": "<PERSON><PERSON><PERSON>", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON><PERSON> anda be<PERSON>, tetapi boleh diperbaiki. <PERSON><PERSON><PERSON> 'scan' untuk menentukan jarak tembakan meriam.", "Pond.helpText1": "<PERSON><PERSON><PERSON> perintah 'cannon' untuk mengena sasaran. Parameter pertama ialah sudut, parameter kedua ialah jarak. Dapatkan kombinasi yang tepat.", "Pond.helpText2": "<PERSON><PERSON><PERSON> mesti dikena berkali-kali. <PERSON><PERSON><PERSON> ulangan 'while (true)' untuk melakukan sesuatu tanpa henti-henti.", "Pond.helpText3a": "<PERSON><PERSON> bergerak mundar-mandir, oleh itu sukar ditembak. Ungkapan 'scan' men<PERSON><PERSON>lkan jarak tepat kepada lawan dalam arah yang ditentukan.", "Pond.helpText3b": "Jarak ini menepati apa yang diperlukan oleh perintah 'cannon' untuk menembak dengan tepat.", "Pond.helpText4": "Lawan ini terlalu jauh dari capaian meriam (dengan had 70 meter). <PERSON>h itu gunakan perintah 'swim' untuk mula berenang ke arah lawan hingga melanggarnya.", "Pond.helpText5": "Lawan ini juga terlalu jauh dari capaian meriam. <PERSON><PERSON><PERSON> anda terlalu lemah untuk terselamat dari pelanggaran. Berenang ke arah lawan sedangkan lokasi mendatar anda kurang daripada 50. <PERSON><PERSON><PERSON> 'stop' dan gunakan meriam.", "Pond.helpText6": "<PERSON>an ini akan berganjak apabila ditembak. <PERSON><PERSON> ke arahnya jika ia di luar jarak tembakan (70 meter).", "Gallery": "<PERSON><PERSON>"}