{"@metadata": {"authors": ["Etrapani", "<PERSON><PERSON>", "<PERSON>"]}, "Games.name": "<PERSON><PERSON><PERSON>", "Games.puzzle": "<PERSON><PERSON><PERSON>", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "Testudo", "Games.movie": "Filmo", "Games.music": "<PERSON><PERSON><PERSON>", "Games.pondTutor": "Gvid<PERSON><PERSON> de lageto", "Games.pond": "<PERSON>get<PERSON>", "Games.linesOfCode1": "Vi solvis tiun ĉi nivelon per 1 linio de JavaScript:", "Games.linesOfCode2": "Vi solvis tiun ĉi nivelon per %1 linioj de JavaScript:", "Games.nextLevel": "Ĉu vi pretas por nivelo %1?", "Games.finalLevel": "Ĉu vi pretas por la venonta defio?", "Games.submitTitle": "<PERSON><PERSON>:", "Games.linkTooltip": "Ko<PERSON><PERSON><PERSON> kaj ligi al blokoj.", "Games.runTooltip": "<PERSON><PERSON> la <PERSON>on, kiun vi skribis.", "Games.runProgram": "Ruli programon", "Games.resetTooltip": "Haltigi la programon kaj rekomenci la nivelon.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "Helpo", "Games.catLogic": "Logika", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "Matematika", "Games.catText": "Teksto", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variab<PERSON>j", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "Problemo okazis pri la peto.", "Games.linkAlert": "Disponigu viajn blo<PERSON>jn per la jena ligilo:\n\n%1", "Games.hashError": "Domaĝe, '%1' ne respondas al ajna konservita programo.", "Games.xmlError": "Ne povis ŝarĝi vian konservitan dosieron. Eble ĝi estis kreita per alia versio de <PERSON>?", "Games.submitted": "Dankon pro jena programo! Se ĝi plaĉas al nia stabo de trejnitaj simioj, ili publikigos ĝin ĉe la galerio en kelkaj tagoj.", "Games.listVariable": "listo", "Games.textVariable": "teksto", "Games.breakLink": "Se vi ekredaktas la JavaScript-kodon, vi ne povas rekomenci redaktadon de blokoj. Ĉu ekredakti?", "Games.blocks": "Blokoj", "Games.congratulations": "G<PERSON><PERSON>!", "Games.helpAbort": "Tiu nivelo estas malfacilega. Ĉu vi volas preterpasi ĝin al la sekva nivelo? Vi povas reveni poste.", "Index.clear": "Ĉu forigi ĉiujn viajn solvojn?", "Index.subTitle": "Ludoj por la programistoj de la morgaŭo.", "Index.moreInfo": "Informoj por edukistoj...", "Index.startOver": "Ĉu vi volas rekomenci?", "Index.clearData": "Viŝi datumojn", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://eo.wikipedia.org/wiki/<PERSON>so", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://eo.wikipedia.org/wiki/Hej<PERSON>_kato", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://eo.wikipedia.org/wiki/Abelo", "Puzzle.animal4": "Heliko", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "Ŝlimo", "Puzzle.animal4HelpUrl": "https://eo.wikipedia.org/wiki/Heliko", "Puzzle.picture": "bi<PERSON>:", "Puzzle.legs": "kruroj:", "Puzzle.legsChoose": "elektu...", "Puzzle.traits": "trajtoj:", "Puzzle.error0": "Perfekta! \nĈiuj %1 blokoj estas ĝustaj.", "Puzzle.error1": "Preskaŭ! Unu bloko estas malĝusta.", "Puzzle.error2": "%1 blokoj estas malĝustaj.", "Puzzle.tryAgain": "La elstarigita bloko estas malĝusta.\nPlu klopodu.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.helpText": "Por ĉiu besto (verda) aneksu ĝian bildon, elektu la nombron de kruroj kaj staku ĝiajn trajtojn.", "Maze.moveForward": "moviĝi antaŭen", "Maze.turnLeft": "turniĝi maldekstren", "Maze.turnRight": "turniĝi dekstren", "Maze.doCode": "fari", "Maze.helpIfElse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> / alie' faros ĉu unu aferon ĉu la alian.", "Maze.pathAhead": "se estas vojo antaŭen", "Maze.pathLeft": "se estas vojo maldekstren", "Maze.pathRight": "se estas vojo dekstren", "Maze.repeatUntil": "ripeti ĝis", "Maze.moveForwardTooltip": "<PERSON><PERSON> la ludanton antaŭen, unu spaco.", "Maze.turnTooltip": "<PERSON><PERSON> la ludanton dekstren aŭ maldekstren, 90 gradoj.", "Maze.ifTooltip": "Se vojo ekzistas en la specifita direkto, do fari iujn agojn.", "Maze.ifelseTooltip": "Se vojo ekzistas en la specifita direkto, do rulu la unuan blokon de agojn. <PERSON><PERSON>, rulu la duan blokon de agoj.", "Maze.whileTooltip": "<PERSON><PERSON><PERSON> la enhavatajn agojn ĝis la fino atingiĝas.", "Maze.capacity0": "Neniu bloko restas al vi.", "Maze.capacity1": "%1 bloko restas al vi.", "Maze.capacity2": "Restas al vi %2 blokoj.", "Maze.runTooltip": "Movi la ludanton laŭ la blokaj instrukcioj", "Maze.resetTooltip": "Remeti la ludanto ĉe la komenco de la labirinto.", "Maze.helpStack": "Kunst<PERSON>u du 'movi antaŭen'-blokojn por helpi min celen.", "Maze.helpOneTopBlock": "Dum ĉi tiu nivelo, vi devas stakigi ĉiujn blokojn en la blanka laborspaco.", "Maze.helpRun": "Rulu vian programon por vidi kio okazas.", "Maze.helpReset": "Via programo ne solvis la labirinton. Premu 'Rekomenci' kaj klopodu denove.", "Maze.helpRepeat": "Atingu la finon de la vojo per nur du blokoj. <PERSON><PERSON> 'ripeti'-blokon por ruli blokon plurfoje.", "Maze.helpCapacity": "Vi jam uzis ĉiujn da blokoj por ĉi tiu nivelo. Por krei novan blokon, vi devas unue forigi ekzistantan blokon.", "Maze.helpRepeatMany": "<PERSON><PERSON><PERSON> blokoj povas ĉeesti en 'ripeti'-bloko.", "Maze.helpSkins": "Elekti vian preferatan ludanton el jena menuo.", "Maze.helpIf": "La 'se'-bloko faras ion nur se la kondiĉo estas vera. Provu maldekstran turnon se ekzistas vojo al la maldekstro.", "Maze.helpMenu": "Alklaku la menuon %1 en la 'se'-bloko por ŝanĝi ĝian kondiĉon.", "Maze.helpWallFollow": "Jen komplikan labirinton. Ĉu vi povas solvi ĝin? Provu sekvi la maldekstran muron. Nur por altnivelaj programistoj!", "Bird.noWorm": "ne havas vermon", "Bird.heading": "direktata al", "Bird.noWormTooltip": "La kondiĉo, kiam la birdo ne jam havas la vermon.", "Bird.headingTooltip": "Moviĝi al la direkto de la specifita angulo: 0 estas dekstren, 90 supren, ktp.", "Bird.positionTooltip": "x kaj y markas la pozicion de la birdo. Kiam x = 0, la birdo estas apud la maldekstra rando; kiam x = 100, la birdo estas apud la dekstr rando. Kiam y = 0, la birdo estas apud la malsupra rando; kiam y = 100, la birdo estas apud la supra rando.", "Bird.helpHeading": "Ŝanĝu la direktan angulon por helpi la birdon kapti la vermon kaj iri nesten.", "Bird.helpHasWorm": "Uzu ĉi tiun blokon por iri unu direkon se vi havas la vermon, aŭ alian direkton se vi ne havas la vermon.", "Bird.helpX": "'x' estas via aktuala horizonta pozicio. Uzu ĉi tiun blokon por iri unu direkon se 'x' estas malpli granda ol iu nombro, aŭ alian direkton se 'x' estas ne malpli granda.", "Bird.helpElse": "Alklaku la ikonon por modifi la 'se'-blokon.", "Bird.helpElseIf": "Tiu ĉi nivelo bezonas kaj blokon 'alie se' kaj blokon 'alie'.", "Bird.helpAnd": "La 'kaj'-bloko estas vera nur se ambaŭ eniroj estas veraj.", "Bird.helpMutator": "Trenu blokon 'alie' al la bloko 'se'.", "Turtle.moveTooltip": "Movi la testudon antaŭen aŭ malantaŭen je la specifita kiomo.", "Turtle.moveForward": "moviĝi antaŭen de distanco", "Turtle.moveBackward": "moviĝi malanta<PERSON>en de distanco", "Turtle.turnTooltip": "Turni la testudon dekstren aŭ maldekstren je la specifita kiomo da gradoj.", "Turtle.turnRight": "turniĝi dekstren je angulo", "Turtle.turnLeft": "turniĝi maldekstren je angulo", "Turtle.widthTooltip": "Ŝanĝi la larĝon de la plumo.", "Turtle.setWidth": "difini larĝon kiel", "Turtle.colourTooltip": "Ŝanĝi la koloron de la plumo.", "Turtle.setColour": "kolo<PERSON>i kiel", "Turtle.penTooltip": "La plumo estos mallevita por komenci desegni aŭ mallevita por ne plu desegni.", "Turtle.penUp": "plumo supren", "Turtle.penDown": "plumo malsupren", "Turtle.turtleVisibilityTooltip": "Tio Igas la testudon (cirklon kaj <PERSON>) ĉu videbla ĉu nevidebla.", "Turtle.hideTurtle": "kaŝi testudon", "Turtle.showTurtle": "<PERSON><PERSON>", "Turtle.printHelpUrl": "https://eo.wikipedia.org/wiki/Presarto", "Turtle.printTooltip": "<PERSON><PERSON><PERSON> la pozicio de la testudo, kaj en ĝia direkto, teksto estos desegnita.", "Turtle.print": "presi", "Turtle.fontHelpUrl": "https://eo.wikipedia.org/wiki/Tiparo", "Turtle.fontTooltip": "Elekti la tiparon uzatan de la 'presi'-blokon.", "Turtle.font": "tiparo", "Turtle.fontSize": "tipara grando", "Turtle.fontNormal": "normala", "Turtle.fontBold": "grasa", "Turtle.fontItalic": "kurs<PERSON>", "Turtle.submitDisabled": "Rulu vian programon ĝis kiam ĝi haltas. Poste vi povas sendi vian desegnon al la galerio.", "Turtle.galleryTooltip": "Malfermi la galerion de desegnaĵoj.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON> gal<PERSON>on", "Turtle.submitTooltip": "Sendi vian desegnaĵon al la galerio.", "Turtle.submitMsg": "Sendi al la galerio", "Turtle.helpUseLoop": "Via solvo funkcias, sed vi povas plibonigi ĝin.", "Turtle.helpUseLoop3": "Desegni la formon per nur tri blokoj.", "Turtle.helpUseLoop4": "Desegni la stelon per nur kvar blokoj.", "Turtle.helpText1": "<PERSON><PERSON><PERSON>on, kiu desegnas k<PERSON>draton.", "Turtle.helpText2": "Ŝanĝi vian programon por desegni kvinangulon kaj ne kvadraton.", "Turtle.helpText3a": "Jen nova bloko kiu ŝanĝas la koloron:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON> flavan stelon.", "Turtle.helpText4a": "Jen nova bloko kiu forlevas la plumon for de la papero kiam vi moviĝas:", "Turtle.helpText4b": "Desegnu malgrandan flavan stelon, kaj desegnu linion super ĝi.", "Turtle.helpText5": "Anstataŭ unu stelo, ĉu vi povas desegni kvar stelojn aranĝitajn kiel kvadrato?", "Turtle.helpText6": "Desegnu tri flavajn stelojn kaj unu blankan linion.", "Turtle.helpText7": "Desegnu la stelojn, kaj desegnu kvar <PERSON>n liniojn.", "Turtle.helpText8": "Desegnaĵo de 360 blankaj linioj ŝajnos plenluno.", "Turtle.helpText9": "Ĉu vi povas aldoni nigran cirklon, por ke la luno estiĝas lunarko?", "Turtle.helpText10": "<PERSON><PERSON><PERSON> a<PERSON>. Jen variegon da novaj blokoj esploreblaj. Amuzu vin!", "Turtle.helpText10Reddit": "Uzu la butonon '<PERSON><PERSON><PERSON>' por vidi desegnaĵojn far aliuloj. Se vi desegnas ion interesan, uzu la butonon '<PERSON>i al Galerio' por publikigi ĝin.", "Turtle.helpToolbox": "Elektu kategorion por vidi la blokojn.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "komenca x", "Movie.y1": "komenca y", "Movie.x2": "fina x", "Movie.y2": "fina y", "Movie.radius": "radiuso", "Movie.width": "larĝo", "Movie.height": "alto", "Movie.circleTooltip": "Desegni cirklon ĉe specifita lokon kun la specifita radiuso.", "Movie.circleDraw": "cirk<PERSON>", "Movie.rectTooltip": "Desegni ortangulon ĉe la specifita loko kun specifita larĝo kaj alto.", "Movie.rectDraw": "ortan<PERSON>lo", "Movie.lineTooltip": "Desegni linion de unu punkto al alia kun specifitan larĝon.", "Movie.lineDraw": "linio", "Movie.timeTooltip": "Liveras la aktualan tempon en la movbildo (0-100).", "Movie.colourTooltip": "Ŝanĝi la koloron de la plumo.", "Movie.setColour": "kolo<PERSON>i kiel", "Movie.submitDisabled": "Via filmo ne moviĝas. <PERSON><PERSON> blokojn por fari ion interesan. Tiam vi povas sendi vian filmon al la galerio.", "Movie.galleryTooltip": "Malfermi la galerion de filmoj.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON>", "Movie.submitTooltip": "<PERSON>i vian filmon al la galerio.", "Movie.submitMsg": "Sendi al la galerio", "Movie.helpLayer": "Movu la fonan cirklon al la supro de via programo. Tiam ĝi aperos antaŭ la homo.", "Movie.helpText1": "<PERSON><PERSON> simplajn formojn por desegni ĉi tiun homon.", "Movie.helpText2a": "Jen filma nivelo. Vi volas movi la brakon de la homo tra la ekrano. Alklaku la 'ludi'-butonon por antaŭrigardi.", "Movie.helpText2b": "Dum la filmo ludiĝas, la valoro de la bloko 'time' kalk<PERSON>s ekde 0 ĝis 100. Ĉar la 'y'-koordinato de la brako estu 0 komence kaj 100 fine, tio estas facila.", "Movie.helpText3": "La 'time'-b<PERSON><PERSON> kalk<PERSON> ekde 0 ĝis 100. Sed nun vi volas ke la 'y'-koordinato de la alia brako komencu ĉe 100 kaj iru ĝis 10. Ĉu vi povas trovi simplan matematikan formulon por inversigi la direkton?", "Movie.helpText4": "Uzu tion kion vi lernis en la antaŭa nivelo por fari kruciĝantajn krurojn.", "Movie.helpText5": "La matematika formulo pri la brako estas komplika. Jen la solvo:", "Movie.helpText6": "<PERSON>u paron da manoj al la homo.", "Movie.helpText7": "U<PERSON> la 'se'-blokon por desegni malgranda kapon dum la unua duono de la filmo. Poste desegnu grandan kapon dum la dua duono de la filmo.", "Movie.helpText8": "Inversigu la direkton de la kruroj je la mezo de la filmo.", "Movie.helpText9": "Desegnu pligrandiĝantan cirklon antaŭ la homo.", "Movie.helpText10": "<PERSON><PERSON> ajnan filmon deziratan. Jen variegon da novaj blokoj esploreblaj. Amuzu vin!", "Movie.helpText10Reddit": "Uzu la butonon '<PERSON><PERSON><PERSON>' por vidi filmojn far aliuloj. Se vi faras interesan filmon, uzu la butonon '<PERSON>i al Galerio' por publikigi ĝin.", "Music.playNoteTooltip": "<PERSON>di unu noton de specifitaj da<PERSON> kaj tono.", "Music.playNote": "ludi noton de daŭro %1 kaj tono %2", "Music.restTooltip": "Atendi dum specifita <PERSON>.", "Music.restWholeTooltip": "<PERSON>as unu noton.", "Music.rest": "silenti dum %1", "Music.setInstrumentTooltip": "Ŝanĝi la instrumenton por sekvaj muzikaj notoj.", "Music.setInstrument": "ŝanĝi instrumenton al %1", "Music.startTooltip": "Ruli la enajn blokojn kiam oni alklakas la butonon 'Ruli <PERSON>on'", "Music.start": "kiam %1 alklakiĝas", "Music.pitchTooltip": "Unu noto (C4 estas 7).", "Music.firstPart": "unua parto", "Music.piano": "piano", "Music.trumpet": "trumpeto", "Music.banjo": "banĝo", "Music.violin": "violono", "Music.guitar": "g<PERSON>ro", "Music.flute": "fluto", "Music.drum": "tamburo", "Music.choir": "ĥoro", "Music.submitDisabled": "Rulu vian programon ĝis kiam ĝi haltas. Poste vi povas sendi vian muzikon al la galerio.", "Music.galleryTooltip": "Malfermi la galerion de muziko.", "Music.galleryMsg": "<PERSON><PERSON><PERSON>", "Music.submitTooltip": "Sendi vian muzikon al la galerio.", "Music.submitMsg": "Sendi al Galerio", "Music.helpUseFunctions": "Via solvo funkcias, sed vi povas plibonigi ĝin. <PERSON><PERSON> funkciojn por redukti la kvanton de ripetita kodo.", "Music.helpUseInstruments": "La muziko estus pli bona se vi uzus malsamajn instrumentojn en ĉiuj komencaj blokoj.", "Music.helpText1": "Komponu la unuajn kvar notojn de 'Frato Jakobo'.", "Music.helpText2a": "'Funkcio' grupigas blokojn kune, kaj vi povas ruli ĝin plurfoje.", "Music.helpText2b": "Kreu funkcion por ludi la unuajn kvar notojn de '<PERSON>ato <PERSON>'. Rulu tiun funkcioj dufoje. Ne aldonu novajn notoblokojn.", "Music.helpText3": "<PERSON><PERSON><PERSON> duan funkcion por la sekva parto de 'Frato Jakobo'. La lasta noto estas pli longa.", "Music.helpText4": "Kreu trian funkcion por la sekva parto de 'Frato Jakobo'. La unuaj kvar notoj estas pli mallongaj.", "Music.helpText5": "Kompletigi la melodion de 'Frato Jakob<PERSON>'.", "Music.helpText6a": "Ĉi tiu nova bloko ŝanĝas la instrumenton.", "Music.helpText6b": "Ludi vian melodion per violono.", "Music.helpText7a": "Ĉi tiu nova bloko aldonas iom da silento.", "Music.helpText7b": "<PERSON><PERSON><PERSON> duan komencan blo<PERSON>, kiu enhavantas du silento-blokojn kaj poste ludas '<PERSON><PERSON>'.", "Music.helpText8": "Ĉiu komenca bloko ludu '<PERSON><PERSON>' du<PERSON><PERSON>.", "Music.helpText9": "<PERSON><PERSON><PERSON> k<PERSON> blokojn kiuj ludas '<PERSON><PERSON>' po du fojoj. Aldonu la ĝustan nombron de silento-blokojn.", "Music.helpText10": "Komponu a<PERSON>. Jen variegon da novaj blokoj esploreblaj. Amuzu vin!", "Music.helpText10Reddit": "Uzu la butonon '<PERSON><PERSON><PERSON>' por aŭdi muzikojn far aliuloj. Se vi komponas ion interesan, uzu la butonon '<PERSON>i al Galerio' por publikigi ĝin.", "Pond.scanTooltip": "Skani por malamikoj. Specifu direkton (0-360). Liveri la distancon al la plej proksima malamiko en tiu direkto, aŭ Nefinion se neniu malamiko troviĝas.", "Pond.cannonTooltip": "<PERSON><PERSON> la kanonon al specifitaj direkto (0-360) kaj distanco (0-70).", "Pond.swimTooltip": "Naĝi antaŭen. Specifu direkton (0-360).", "Pond.stopTooltip": "Ĉesi naĝadon. La ludanto haltos.", "Pond.healthTooltip": "Liveri la aktualan sanon de la ludanto (0 estas mortinteco, 100 estas plena sano)", "Pond.speedTooltip": "Liveri la aktualan rapidon de la ludanto (0 estas haltinteco, 100 estas maksimuma rapido)", "Pond.locXTooltip": "Liveri la X-koordinaton de la ludanto (0 estas la maldekstra rando, 100 estas la dekstra rando)", "Pond.locYTooltip": "Liveri la Y-koordinaton de la ludanto (0 estas la malsupra rando, 100 estas la supra rando)", "Pond.logTooltip": "Montras nombron en la konzolo de via retumilo.", "Pond.docsTooltip": "Montri la dokumentaron pri la lingvo.", "Pond.documentation": "Dokumentado", "Pond.playerName": "Ludanto", "Pond.targetName": "<PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON>", "Pond.helpUseScan": "Via solvo funkcias, sed estas pliboniegbla. <PERSON><PERSON> 'scan' por agordi la pafan distancon de la kanono.", "Pond.helpText1": "U<PERSON> la komandon 'cannon' por pafi celen. La unua parametro estas la angulo; la dua, la distancon. Trovu la ĝustan kombinaĵon.", "Pond.helpText2": "La celo frapendas multfoje. Uzi la iteracion 'while (true)' por fari ion senfine.", "Pond.helpText3a": "Ĉi tiu oponanto movas tien kaj reen, kaj tial trafi ĝin estas malfacile. La esprimo 'scan' liveras la ekzaktan distancon al la oponanto en la specifita direkto.", "Pond.helpText3b": "Ĉi tiu distanco estas ekzakte tio kion la komando 'cannon' necesas por pafi precize.", "Pond.helpText4": "Ĉi tiu oponanto estas tro malproksima por la kanono (kiu nur povas pafi 70 metrojn). Anstataŭe. uzu la komandon 'swim' por naĝi al la oponanto kaj frakasi ĝin.", "Pond.helpText5": "Ĉi tiu oponanto estas ankaŭ tro malproksima por la kanono. Sed vi estas tro malfortika por supervivi kolizion. Naĝu al oponanto dum via horizonta pozicio estas malpli ol 50. Kaj uzu la komandon 'stop' kaj uzu la kanonon.", "Pond.helpText6": "Ĉi tiu oponanto foriras se ĝi estas trafita. Naĝu al ĝi se ĝi estas ne trafebla (pli malproksima ol 70 metroj).", "Gallery": "Gale<PERSON>"}