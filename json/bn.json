{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rakibul", "<PERSON><PERSON>", "আফতাবুজ্জামান"]}, "Games.name": "ব্লকলি খেলা", "Games.puzzle": "ধাঁধা", "Games.maze": "গোলকধাঁধা", "Games.bird": "পা<PERSON>ি", "Games.turtle": "কচ্ছপ", "Games.movie": "চলচ্চিত্র", "Games.music": "সঙ্গীত", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ু", "Games.pond": "পুকুর", "Games.linesOfCode1": "আপনি জাভাস্ক্রিপ্টের ১ লাইনের মাধ্যমে এই লেভেলটি সমাধান করেছেন:", "Games.linesOfCode2": "আপনি জাভাস্ক্রিপ্টের %1 লাইনের মাধ্যমে এই লেভেলটি সমাধান করেছেন:", "Games.nextLevel": "আপনি কি লেভেল %1-এর জন্য প্রস্তুত?", "Games.finalLevel": "আপনি কি প্রস্তুত পরবর্তী মোকাবেলার জন্য?", "Games.submitTitle": "শির<PERSON>নাম:", "Games.linkTooltip": "সংরক্ষণ করুন ও ব্লকের সাথে যুক্ত করুন।", "Games.runTooltip": "আপনি যে প্রোগ্রামটি লিখেছেন তা চালান।", "Games.runProgram": "প্রোগ্রাম চালান", "Games.resetTooltip": "প্রোগ্রামটি বন্ধ করুন এবং লেভেলটি পুন:স্থাপন করুন।", "Games.resetProgram": "পুন:স্থা<PERSON>ন করুন", "Games.help": "সাহায্য", "Games.catLogic": "যুক্তি", "Games.catLoops": "লুপসমূহ", "Games.catMath": "গণিত", "Games.catText": "লেখা", "Games.catLists": "তালিকাসমূহ", "Games.catColour": "রং", "Games.catVariables": "চলকগুলো", "Games.catProcedures": "ক্রিয়া", "Games.httpRequestError": "অনুরোধ বাস্তবায়নে একটি সমস্যা ছিল।", "Games.linkAlert": "এই সংযোগের মাধ্যমে আপনার ব্লকগুলি বণ্টন করুন:\n\n%1", "Games.hashError": "দুঃখিত, '%1'  কোনো সংরক্ষিত প্রোগ্রামের সাথে মিলেনি।", "Games.xmlError": "আপনার সেভ করা ফাইল লোড হয়নি। সম্ভবত এটি ব্লকলির অন্যকোন ভার্সনে তৈরি হয়েছিল?", "Games.listVariable": "তালিকা", "Games.textVariable": "লেখা", "Games.breakLink": "আপনি জাভাস্ক্রিপ্ট সম্পাদনার কাজ একবার শুরু করলে, ব্লক সম্পাদনায় আর ফিরে যেতে পারবেন না। ঠিক আছে তো?", "Games.blocks": "ব্লকগুলি", "Games.congratulations": "অভিনন্দন!", "Games.helpAbort": "এই লেভেলটি অত্যাধিক কঠিন। আপনি কি এটি বাদ দিয়ে পরবর্তী খেলায় যেতে চান? আপনি চাইলে যেকোনো সময় আবার ফিরে আসতে পারবেন।", "Index.clear": "আপনি কি আপনার সব সমাধান মুছে ফেলতে চান?", "Index.subTitle": "ভবিষ্যতের প্রোগ্রামারদের খেলা।", "Index.moreInfo": "বিস্তারিত তথ্য...", "Index.startOver": "পুন<PERSON><PERSON>য় শুরু করতে চান?", "Index.clearData": "তথ্য মুছুন", "Puzzle.animal1": "হাঁস", "Puzzle.animal1Trait1": "পালক", "Puzzle.animal1Trait2": "ঠোঁট", "Puzzle.animal1HelpUrl": "https://bn.wikipedia.org/wiki/হাঁস", "Puzzle.animal2": "বিড়াল", "Puzzle.animal2Trait1": "গোঁফ", "Puzzle.animal2Trait2": "পশম", "Puzzle.animal2HelpUrl": "https://bn.wikipedia.org/wiki/বিড়াল", "Puzzle.animal3": "মৌমাছি", "Puzzle.animal3Trait1": "মধু", "Puzzle.animal3Trait2": "পোকার আল", "Puzzle.animal3HelpUrl": "https://bn.wikipedia.org/wiki/মৌমাছি", "Puzzle.animal4": "<PERSON>া<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "খোল", "Puzzle.animal4Trait2": "আঠালো", "Puzzle.animal4HelpUrl": "https://bn.wikipedia.org/wiki/শামুক", "Puzzle.picture": "ছবি:", "Puzzle.legs": "পা:", "Puzzle.legsChoose": "পছন্দ করুন...", "Puzzle.traits": "বৈশিষ্ট্য:", "Puzzle.error0": "নিখুঁত!\nসকল %1 ব্লক সঠিক।", "Puzzle.error1": "প্রায় সব ঠিক! একটি ব্লক ভুল।", "Puzzle.error2": "%1 ব্লক ভুল।", "Puzzle.tryAgain": "বিশেষ স্পষ্ট ব্লকটি সঠিক নয়। অাবার চেষ্টা করুন।", "Puzzle.checkAnswers": "উত্তর যাচাই করুন", "Puzzle.helpText": "প্রতিটি প্রাণীর (সবু<PERSON>) জন্য, তার ছবি সংযুক্ত করুন, পায়ের সংখ্যা ঠিক করুন এবং তার বৈশিষ্ট্যগুলো একত্রিত করুন।", "Maze.moveForward": "সামনে যান", "Maze.turnLeft": "বামে যান", "Maze.turnRight": "ডানে যান", "Maze.doCode": "<PERSON><PERSON><PERSON>ন", "Maze.helpIfElse": "If-else ব্লকসমূহ একটি কাজ করবে অথবা অন্যটি।", "Maze.pathAhead": "যদি সামনে পথ থাকে", "Maze.pathLeft": "যদি বামে পথ থাকে", "Maze.pathRight": "যদি ডানে পথ থাকে", "Maze.repeatUntil": "পর্যন্ত পুনরাবৃত্তি", "Maze.moveForwardTooltip": "খেলোয়াড়কে এক ঘর সামনে নেয়।", "Maze.turnTooltip": "খেলোয়াড়কে ৯০ ডিগ্রি ডানে অথবা বামে ঘুরান।", "Maze.ifTooltip": "যদি নির্দেশিত দিকে পথ থাকে, তাহলে পদক্ষেপ নিন।", "Maze.ifelseTooltip": "যদি নির্দেশিত দিকে পথ থাকে, তাহলে প্রথম ব্লকের কাজ করুন। অন্যথায়, দ্বিতীয় ব্লকের কাজ করুন।", "Maze.whileTooltip": "সংযুক্ত পদক্ষেপ বারবার গ্রহন করুন, শেষ প্রান্তে না পৌঁছনো পর্যন্ত।", "Maze.capacity0": "আপনার %0 ব্লক বাকি আছে।", "Maze.capacity1": "আপনার %1টি ব্লক বাকি আছে।", "Maze.capacity2": "আপনার %2 ব্লক বাকি আছে।", "Maze.runTooltip": "ব্লকের নির্দেশনা অনুসারে খেলোয়াড়কে চালনা করে।", "Maze.resetTooltip": "খেলোয়াড়কে পুনরায় ধাঁধার শুরুতে নিয়ে যায়।", "Maze.helpStack": "বারবার 'সামনে নিন' ব্লক একত্রে লক্ষে পৌঁছতে সাহায্য করবে।", "Maze.helpOneTopBlock": "সাদা রঙের কাজেরস্থানে অাপনার এই স্তরের সবগুলো ব্লক একত্রিত করা প্রয়োজন।", "Maze.helpRun": "আপনার প্রগ্রামটি চালু করুন কি হয় তা দেখার জন্য।", "Maze.helpReset": "আপনার প্রোগ্রামটি গোলকধাঁধা সমাধান করতে পারে নাই। পুন:স্থাপনে চাপ দিন এবং আবার চেষ্টা করুন।", "Maze.helpRepeat": "এই পথের শেষে যান মাত্র দুটি ব্লক ব্যবহার করে। 'repeat' ব্যবহার করুন কোনো ব্লক একাধিকবার চালাতে।", "Maze.helpCapacity": "এই স্তরের জন্য অাপনি সবগুলো ব্লক ব্যবহার করেছেন। নতুন একটি ব্লক তৈরি করতে বিদ্যমান ব্লক হতে একটি বাতিল করুন।", "Maze.helpRepeatMany": "একটি 'repeat' ব্লকের মধ্যে আপনি একাধিক ব্লক লাগাতে পারেন।", "Maze.helpSkins": "মেনু থেকে আপনার প্রিয় খেলোয়াড়টি পছন্দ করুন।", "Maze.helpIf": "একটি 'if' ব্লক কেবল তখনই কাজ করবে, যখন শর্ত সঠিক। বা দিকে ঘুরাতে চেষ্টা করুন যদি বা দিকে পথ থাকে।", "Maze.helpMenu": "'if' ব্লকের %1-এ চাপ দিন এর শর্ত পরিবর্তনের জন্য।", "Maze.helpWallFollow": "এই জটিল ধাধা কি অাপনি মিলাতে পারেন? বা দিকের ওয়াল অনুসারে চেষ্টা করুন। শুধুমাত্র দক্ষ প্রোগ্রামারবৃন্দ!", "Bird.noWorm": "খাওয়ার জন্য পোকা নেই", "Bird.heading": "শি<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "সেই অবস্থা যখন পাখিটি খাবারের জন্য কোনো পোকা পায়নি।", "Bird.headingTooltip": "প্রদত্ত কোণ অনুসারে ঘুরান: যেমন, ০ হচ্ছে ডানদিকে অার খাড়া ভাবে ৯০", "Bird.positionTooltip": "X এবং Y পাখির অবস্থান চিহ্নিত করে। যখন X=0 পাখিটা বা প্রান্তের কাছে, যখন X=100 তখন ডান প্রান্তের কাছে। যখন Y=0, পাখিটা একেবারে নিচে, যখন Y=100, তখন তা একেবারে উপরে।", "Bird.helpHeading": "মাথার কোণ বদল করুন যাতে পাখিটা পোকা ধরতে পারে আর ঘরে যেতে পারে।", "Bird.helpHasWorm": "যদি খাওয়<PERSON>র মত পোকা থাকে, এই ব্লক ব্যবহার করুন সেখানে যেতে অথবা ভিন্নদিকে যদি পোকা না থাকে।", "Bird.helpX": "x  হচ্ছে ভূমি বরাবর অাপনার বর্তমান অবস্থান। এই ব্লক ব্যবহার করুন একদিকে যেতে যদি  x একটি নম্বরের কম হয়, নাহলে অন্যদিকে।", "Bird.helpElse": "'যদি' নামক ব্লকটি পরিবর্তন করার জন্য আইকনটিতে ক্লিক করুন।", "Bird.helpElseIf": "এই লেভেলে 'নতুবা যদি' এবং 'যদি' উভয় ব্লকই প্রয়োজন।", "Bird.helpAnd": "'এবং' ব্লকটি সত্য হবে যখন তার উভয় ইনপুটই সত্য হবে।", "Bird.helpMutator": "একটি 'else' ব্লক টেনে নিন 'if' ব্লকে।", "Turtle.moveTooltip": "নির্দেশিত পরিমানে কচ্ছপটি সামনে অথবা পিছনে যায়।", "Turtle.moveForward": "এই পথে সামনে নিন", "Turtle.moveBackward": "এই পথে পিছনে নিন", "Turtle.turnTooltip": "নির্দিষ্ট সংখ্যক ডিগ্রির দ্বারা কচ্ছপ বাম বা ডান দিকে ঘুরান।", "Turtle.turnRight": "এই পথে ডানদিকে ঘুরান", "Turtle.turnLeft": "এই পথে বা দিকে ঘুরান", "Turtle.widthTooltip": "কলমটির প্রস্থ পরিবর্তন করে।", "Turtle.setWidth": "অায়তন নির্দিষ্ট করুন", "Turtle.colourTooltip": "কলমের রং পরিবর্তন করতে।", "Turtle.setColour": "রং নির্দিষ্ট করুন", "Turtle.penTooltip": "অঙ্কন থামাতে বা শুরু করতে, কলম উচু বা নিচু করুন।", "Turtle.penUp": "রংপেন্সিলের রং বন্ধ", "Turtle.penDown": "রংপেন্সিল সক্রিয়", "Turtle.turtleVisibilityTooltip": "কচ্ছপটিকে (বৃত্ত ও তীর) দৃশ্যমান বা অদৃশ্য করে।", "Turtle.hideTurtle": "কচ্ছোপ লুকান", "Turtle.showTurtle": "কচ্ছোপ দেখান", "Turtle.printHelpUrl": "https://bn.wikipedia.org/wiki/মুদ্রণ", "Turtle.printTooltip": "এই স্থানে কচ্ছপের গতিপ্রকৃতি লেখা অংকন করুন।", "Turtle.print": "মুদ্রণ", "Turtle.fontHelpUrl": "https://bn.wikipedia.org/wiki/ফন্ট", "Turtle.fontTooltip": "মুদ্রণ ব্লক দ্বারা ব্যবহৃত অক্ষরের প্রকৃতি ঠিক করুন।", "Turtle.font": "ফন্ট", "Turtle.fontSize": "ফন্টের আকার", "Turtle.fontNormal": "স্বাভাবিক", "Turtle.fontBold": "গাঢ়", "Turtle.fontItalic": "বাঁকানো", "Turtle.submitDisabled": "আপনার প্রোগ্রামটি বন্ধ না হওয়া পর্যন্ত চালু রাখুন। তারপর আপনি আপনার অঙ্কনটি গ্যালারিতে জমা দিতে পারবেন।", "Turtle.galleryTooltip": "অঙ্কনের গ্যালারি খুলুন।", "Turtle.galleryMsg": "গ্যালারি দেখুন", "Turtle.submitTooltip": "Reddit এ আপনার অঙ্কনটি জমা করুন।", "Turtle.submitMsg": "গ্যালারিতে জমা করুন", "Turtle.helpUseLoop": "আপনার সমাধ<PERSON>ন কাজ করছে, কিন্তু আপনি আরও ভালো করতে পারেন।", "Turtle.helpUseLoop3": "মাত্র তিনটি ব্লক ব্যবহার করে আকৃতিটি আঁকেন।", "Turtle.helpUseLoop4": "মাত্র চারটি ব্লক ব্যবহার করে তারাটি আঁকেন।", "Turtle.helpText1": "একটি প্রোগ্রাম তৈরি করুন যা একটি বর্গক্ষেত্র আঁকে।", "Turtle.helpText2": "চতুর্ভুজের পরিবর্তে একটি পঞ্চভুজ অাঁকতে অাপনার প্রোগ্রাম পরিবর্তন করুন।", "Turtle.helpText3a": "একটি নতুন ব্লক অাছে যা অপনাকে রং পরিবর্তন করতে দিবে।", "Turtle.helpText3b": "একটি হলুদ তারা আঁকুন।", "Turtle.helpText4a": "একটি নতুন ব্লক অাছে যা অাপনাকে রং পেন্সিলটি কাগজ হতে উপরে তুলে অন্যত্র নিতে দিবে।", "Turtle.helpText4b": "একটি ছোট হলুদ তাঁরা অঙ্কন করুন, পারে তার উপর একটি রেখা অঙ্কন করুন।", "Turtle.helpText5": "একটি তাঁরার বদলে অাপনি কি চারটি তাঁরা অঙ্কন করতে পারেন, চতুর্ভুজ অাকারে সাজিয়ে?", "Turtle.helpText6": "তিনটি হলুদ তারা ও একটি সাদা রেখা অঙ্কন করুন।", "Turtle.helpText7": "তারা<PERSON>ুলো অঙ্কন করুন, তারপরে চারটি রেখা অঙ্কন করুন।", "Turtle.helpText8": "৩৬০টি সাদা রেখা অঙ্কনে পূর্ণ চাঁদের মত মনে হবে।", "Turtle.helpText9": "অাপনি কি একটি কালো বৃত্ত যুক্ত করতে পারেন যেন চাঁদটা বাঁকা হয়ে যায়।", "Turtle.helpText10": "আপনি যেকোনো কিছু অঙ্কন করুন। আপনি কাজে লাগানোর জন্য অনেক গুলো ব্লক পেয়েছেন। মজা নিন!", "Turtle.helpText10Reddit": "অন্যরা কি এঁকেছে তা দেখতে \"গ্যালারি দেখুন\" বোতাম ব্যবহার করুন। অাপনি যদি কোনো মজার জিনিস আঁকেন, তাহলে তা প্রকাশ করতে \"গ্যালারিতে জমা দিন\" বোতাম ব্যবহার করুন।", "Turtle.helpToolbox": "ব্লকগুলো দেখতে একটি বিভাগ পছন্দ করুন।", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "শুর<PERSON> x", "Movie.y1": "শুরু y", "Movie.x2": "শেষ x", "Movie.y2": "শেষ y", "Movie.radius": "ব্যাসার্ধ", "Movie.width": "প্রস্থ", "Movie.height": "উচ্চতা", "Movie.circleTooltip": "নির্দেশিত স্থানে এবং নির্দেশিত ব্যাসার্ধ নিয়ে একটি বৃত্ত অঙ্কন করুন।", "Movie.circleDraw": "বৃত্ত", "Movie.rectTooltip": "নির্দেশিত দৈর্ঘ্য ও প্রস্থ সহকারে নির্দেশিত স্থানে একটি চৌকা ঘর অঙ্কন করুন।", "Movie.rectDraw": "আয়তক্ষেত্র", "Movie.lineTooltip": "নির্দেশিত প্রস্থ সহকারে এক বিন্দু হতে অন্য বিন্দু পর্যন্ত একটি রেখা অঙ্কন করুন।", "Movie.lineDraw": "রেখা", "Movie.timeTooltip": "এনিমেশনে চলমান সময় দেখাবে (০-১০০)।", "Movie.colourTooltip": "কলমের রং পরিবর্তন করুন।", "Movie.setColour": "রং নির্দিষ্ট করুন", "Movie.submitDisabled": "অাপনার ভিডিও অনত্র যাচ্ছে না। অারো অাকর্ষনীয় করতে ব্লকগুলো ব্যবহার করুন। তারপর হয়ত অাপনি অাপনার ভিডিওটি গ্যালারিতে জমা করতে পারবেন।", "Movie.galleryTooltip": "Reddit এর চলচ্চিত্রের গ্যালারি খুলুন।", "Movie.galleryMsg": "গ্যালারি দেখুন", "Movie.submitTooltip": "Reddit এ আপনার চলচ্চিত্রটি জমা করুন।", "Movie.submitMsg": "গ্যালারিতে জমা করুন", "Movie.helpText1": "এই ব্যক্তিকে অঙ্কনের জন্য সহজ অাকার ব্যবহার করুন।", "Movie.helpText2a": "এটা একটা ভিডিও পর্ব। অাপনি একটি লাল বল সর্বত্র ঘুরাতে চাচ্ছেন। নমুনা দেখতে প্লে বাটনে চাপ দিন।", "Movie.helpText2b": "যখন ভিডিও চলে, 'Time' ব্লক ০ থেকে ১০০ পর্যন্ত মান গ্রহন করে। এটা অবশ্যই সহজ যেহেতু অাপনি ০ থেকে শুরু করে ১০০ পর্যন্ত যেতে লাল বলের অবস্থান পরিবর্তন করতে চাইবেন", "Movie.helpText3": "'time' ব্লক ০ থেকর ১০০ পর্যন্ত গণনা করে। কিন্তু এখন অাপনি চাচ্ছেন লাল বলের অবস্থানটা ১০০ থেকে শুরু করে ০ তে যাক। অাপনি কি এই গতি প্রকাশ করার জন্য একটা সহজ গাণিতিক সূত্র দিতে পারেন?", "Movie.helpText4": "অাগের স্তরে অাপনি যা শিখেছেন তা ব্যবহার করুন চারটা সবুজ বল তৈরিতে যা চারটি দিকেই যাবে।", "Movie.helpText5": "ইদুরের মাথা ঘুরানো সহজ। অাপনি কি কান গুলোকেও অনুরূপ ঘুরাবার জন্য একটা সমাধান দিতে পারেন?", "Movie.helpText6": "দুট<PERSON> সহজ রেখা। কেবল বের করুন রেখার প্রান্ত দুটো কি করছে।", "Movie.helpText7": "এই পতনশীল বলের গাণিতিক সূত্র জটিল। এখানে উত্তরটি দেয়া হল:", "Movie.helpText8": "'if' ব্লক ব্যবহার করুন ভিডিওর অর্ধেকাংশের জন্য লাল নীল বল অাঁকতে। পরবর্তী অর্ধাংশের জন্য একটি সবুজ বল অংকন করুন।", "Movie.helpText9": "তঁরে চলার মত কোনো বল কি অাপনি তৈরি করতে পারেন? অাপনার জন্য ইতমধ্যেই তাঁর অংকন করা হয়েছে। এটি পারলে, অাপনি যে কোনো কিছু পারবেন।", "Movie.helpText10": "আপনার ইচ্ছে অনুযায়ী যেকোনো কিছুর ভিডিও তৈরি করুন। আপনি খেলবার মত অনেক নতুন ব্লক পেয়েছেন। মজা নিন!", "Movie.helpText10Reddit": "অন্যদের তৈরি করা ভিডিও দেখতে \"গ্যালারি দেখুন\" বোতাম ব্যবহার করুন। অাপনি যদি কোনো মজার ভিডিও তৈরি করেন, তাহলে তা প্রকাশ করতে  \"গ্যালারিতে জমা দিন\" বোতাম ব্যবহার করুন।", "Pond.scanTooltip": "শত্রুদের দেখুন। গতিপথ নির্দেশিত করুন(০থেকে৩৬০)। গতিপথে সবথেকে কাছের শত্রুর দূরত্ব দেখাবে। অন্তহীন দূরত্ব দেখাবে যদি শত্রু না থাকে।", "Pond.cannonTooltip": "কামানের গোলা ছুড়ুন। দিক ঠিক করুন (০ থেকে ৩৬০) এবং দূরত্ব (০ থেকে ৭০)।", "Pond.swimTooltip": "সামনে সাঁতার দিন। দিক ঠিক করুন (০ থেকে ৩৬০)।", "Pond.stopTooltip": "সাঁতার বন্ধ করুন। খেলোয়াড় থামা পর্যন্ত মন্থর হতে থাকবে।", "Pond.healthTooltip": "প্লেয়ারকে বর্তমান সুস্থতা ফেরত দেয় (০ হচ্ছে মৃত্যু, ১০০ সুস্বাস্থ্য)।", "Pond.speedTooltip": "প্লেয়ারের বর্তমান গতি ফেরত দেয় (০ বন্ধ অার ১০০ পূর্ণ গতি)।", "Pond.locXTooltip": "X অবস্থানে প্লেয়ারের অবস্থান দেখায় (0 হচ্ছে বা প্রান্ত, ১০০ হচ্ছে ডান প্রান্ত)।", "Pond.locYTooltip": "Y অবস্থানে প্লেয়ারের অবস্থান দেখায়(০ হচ্ছে নিচের প্রান্ত,  ১০০ হচ্ছে উপরের প্রান্ত)।", "Pond.docsTooltip": "ভাষার তথ্যাদি দেখান।", "Pond.documentation": "দলিল", "Pond.playerName": "খেলোয়াড়", "Pond.targetName": "লক্ষ", "Pond.pendulumName": "দোলক", "Pond.scaredName": "কাঁচুমাচু", "Pond.helpUseScan": "অাপনার সমাধান কাজ করে, কিন্তু অাপনি অারো ভাল করতে পারেন। 'scan' ব্যবহার করুন কামানকে জানাতে যে কতদূরে শুট করতে হবে।", "Pond.helpText1": "'cannon' কমান্ড ব্যবহার করুন লক্ষে অাঘাত হানতে। প্রথমে বিবেচনা করুন এ্যাঙ্গেল, দ্বিতীয়ত দূরত্ব। দু'টির সঠিক সমন্বয় বের করুন।", "Pond.helpText2": "এই লক্ষবস্তুতে অনেকবার অাঘাত করা দরকার। একটি 'while(true)' লুপ ব্যবহার করুন অনির্দিষ্ট ভাবে কিছু করতে।", "Pond.helpText3a": "প্রতিপক্ষ সামনে অাবার পিছনে যায়, অাঘাত করা কঠিন। 'scan' নির্দেশিত দিকে প্রতিপক্ষের সঠিক দুরত্বে নিয়ে যায়।", "Pond.helpText3b": "এটা একেবারে সেই দুরত্ব যা 'কামান' কমান্ডের প্রয়োজন সঠিকভাবে অাঘাত হানতে।", "Pond.helpText4": "প্রতিপক্ষ এত দূরে যে তা কামানের অাঘাত সীমার বাহিরে (যার সীমা ৭০ মিটার পর্যন্ত)। বিকল্প হিসাবে, 'সাঁতার' কমান্ড ব্যবহার করুন, প্রতিপক্ষের দিকে সাঁতার শুরু করতে আর তা ধ্বংস করতে।", "Pond.helpText5": "কামান ব্যবহারের জন্য এই পতিপক্ষ অনেক দূরে। কিন্তু অাপনি এত দুর্বল যে সংঘাতে টিকে থাকার মত নয়। ভুমি বরাবর অাপনার অবস্থান ৫০ এর নিচে গেলে অাপনি প্রতিপক্ষ বরাবর সাঁতার কাটেন। তারপর 'থামুন' এবং কামান ব্যবহার করুন।", "Pond.helpText6": "এই প্রতিপক্ষ অন্যত্র সরে যাবে যখন এটি অাঘাত পাবে। যদি তা অাঘাত সীমার (৭০মিটার) বাহিরে চলে যায় তবে তার দিকে সাঁতার কাটুন।"}