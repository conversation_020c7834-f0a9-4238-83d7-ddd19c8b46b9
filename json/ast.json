{"@metadata": {"authors": ["Crucifunked", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "Games.name": "Blockly games", "Games.puzzle": "R<PERSON><PERSON><PERSON><PERSON>", "Games.maze": "Llaberintu", "Games.bird": "Páxaru", "Games.turtle": "Tortuga", "Games.movie": "<PERSON><PERSON><PERSON><PERSON>", "Games.music": "Música", "Games.pondTutor": "Tutor del estanque", "Games.pond": "Estanque", "Games.linesOfCode1": "Resolvisti esti nivel con 1 llinia de JavaScript:", "Games.linesOfCode2": "Resollvisti esti nivel con %1 llinies de JavaScript", "Games.nextLevel": "¿Tas llistu/a pal nivel %1?", "Games.finalLevel": "¿Tas llistu/a pal viniente nivel?", "Games.submitTitle": "Títulu:", "Games.linkTooltip": "Guardar y enllazar a los bloques.", "Games.runTooltip": "Executar el programa qu'escribisti.", "Games.runProgram": "Executar el programa", "Games.resetTooltip": "Detener el programa y restablecer el nivel.", "Games.resetProgram": "<PERSON><PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Lóxica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matemátiques", "Games.catText": "<PERSON><PERSON>", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "Color", "Games.catVariables": "Variables", "Games.catProcedures": "Funciones", "Games.httpRequestError": "Hubo un problema col pidimientu.", "Games.linkAlert": "Comparti los tos bloques con esti enllaz:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, '%1' nun correspuende con nengún programa guardáu.", "Games.xmlError": "Nun pudo cargase'l ficheru guardáu.\n¿Quiciabes foi creáu con otra versión de Blockly?", "Games.submitted": "¡Gracies por esti programa! Si gusta al nuesu grupu espertu de monos entrenaos, van espublizalu na galería nun par de díes.", "Games.listVariable": "Llista", "Games.textVariable": "<PERSON><PERSON>", "Games.breakLink": "En principiando la edición de JavaScript, nun vas poder volver a la edición de bloques. ¿Aceutes esto?", "Games.blocks": "Bloques", "Games.congratulations": "¡Norabona!", "Games.helpAbort": "Esti nivel ye desaxeradamente difícil. ¿Deseyes saltealu y dir al siguiente nivel? Siempres puedes tornar más palantre.", "Index.clear": "¿<PERSON><PERSON>r toles tos soluciones?", "Index.subTitle": "Xuegos pa los programadores del futuru.", "Index.moreInfo": "Información pa educadores...", "Index.startOver": "¿Quies entamar de nueves?", "Index.clearData": "<PERSON><PERSON><PERSON> da<PERSON>", "Puzzle.animal1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "Plumes", "Puzzle.animal1Trait2": "<PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://ast.wikipedia.org/wiki/Cor%C3%ADu", "Puzzle.animal2": "Gatu", "Puzzle.animal2Trait1": "Bigotes", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://ast.wikipedia.org/wiki/Gatu", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "Aguiyón", "Puzzle.animal3HelpUrl": "https://ast.wikipedia.org/wiki/<PERSON>ya", "Puzzle.animal4": "Cascoxu", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Baba", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "semeya:", "Puzzle.legs": "pates:", "Puzzle.legsChoose": "escueye...", "Puzzle.traits": "traces:", "Puzzle.error0": "¡Perfeutu!\nLos %1 bloques tán bien asitiaos.", "Puzzle.error1": "¡Casi! Un bloque ta mal asitiáu.", "Puzzle.error2": "%1 bloques tán mal asitiaos.", "Puzzle.tryAgain": "El bloque destacáu ta mal asitiáu.\nSigui tentando.", "Puzzle.checkAnswers": "Comprobar les respuestes", "Puzzle.helpText": "Pa cada animal (verde), axunta la so semeya, escueye la cantidá de pates y llista les sos traces.", "Maze.moveForward": "a<PERSON><PERSON>", "Maze.turnLeft": "xirar a la esquierda", "Maze.turnRight": "xirar a la drecha", "Maze.doCode": "faer", "Maze.helpIfElse": "Los bloques 'si-sinón' faen una cosa o la otra", "Maze.pathAhead": "si hai camín enfrente", "Maze.pathLeft": "si hai camín a la esquierda", "Maze.pathRight": "si hai camín a la drecha", "Maze.repeatUntil": "repitir fasta", "Maze.moveForwardTooltip": "Muevi al xugador un cuadru p'al<PERSON>re.", "Maze.turnTooltip": "Xira al xugador a esquierda o drecha 90 graos.", "Maze.ifTooltip": "Si hai un camín na direición especificada, entós executa unes aiciones.", "Maze.ifelseTooltip": "Si hai un camín na direición especificada, entós executa'l primer bloque d'aiciones. Sinón, executa'l segundu bloque d'aiciones.", "Maze.whileTooltip": "Repiti les aiciones conteníes fasta algamar el puntu final.", "Maze.capacity0": "Quédente %0 bloques.", "Maze.capacity1": "Quédate %1 bloque.", "Maze.capacity2": "Quédente %2 bloques.", "Maze.runTooltip": "Fai que'l xugador faiga lo que dicen los bloques.", "Maze.resetTooltip": "Llevar al xugador al empiezu'l llaberintu.", "Maze.helpStack": "Xune un par de bloques 'avanzar' p'ayudame algamar la meta.", "Maze.helpOneTopBlock": "Nesti nivel precises xunir todos los bloques nel espaciu de trabay blancu.", "Maze.helpRun": "Executa'l to programa pa ver qué pasa.", "Maze.helpReset": "El to programa nun resolvió'l llaberintu. Prima 'Reaniciar' y tenta otra vegada.", "Maze.helpRepeat": "Algama'l final d'esti camín usando sólo dos bloques. Usa 'repitir' pa executar un bloque mas d'una vegada.", "Maze.helpCapacity": "Usasti tolos bloques pa esti nivel. Pa crear un bloque nuevu tienes de desaniciar un bloque esistente.", "Maze.helpRepeatMany": "Pues usar más d'un bloque dientro d'un bloque 'repetir'.", "Maze.helpSkins": "Escueye'l to xugador favoritu dende esti menú.", "Maze.helpIf": "Un bloque 'si' va faer daqué sólo si la condición ye verdadera. Tenta xirar a la izquierda si hai camín a la izquierda.", "Maze.helpMenu": "Pulsia %1 nel bloque «si» pa camudar la so condición.", "Maze.helpWallFollow": "¿Pues resolver esti complicáu ll<PERSON>? Tenta siguir la paré de la izquierda. ¡Sólo pa programadores avanzaos!", "Bird.noWorm": "nun tien guxanu", "Bird.heading": "Cabecera", "Bird.noWormTooltip": "La condición cuandu'l páxari nun consiguió'l vierme.", "Bird.headingTooltip": "Mover na dirección del ángulu especificáu: 0 ye a la derecha, 90 ye de frente, etc.", "Bird.positionTooltip": "x y y marquen la posición del páxaru. Cuando x = 0 el páxaru ta pegando al cantu izquierdu, cuando x = 100 ta pegando al derechu. Cuando y = 0 el páxaru ta na parte inferior, cuando y = 100 ta na parte cimera.", "Bird.helpHeading": "Cambia l'ángulu de direición pa facer que'l páxaru consiga'l guxanu y aterrice nel so nial.", "Bird.helpHasWorm": "Usa esti bloque para siguir una direición si tienes el guxanu, o otra direición si nun lu tienes.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "entamu x", "Movie.y1": "entamu y", "Movie.x2": "final x", "Movie.y2": "final y", "Movie.radius": "radiu", "Movie.width": "anchor", "Movie.height": "altor", "Movie.circleDraw": "c<PERSON>rc<PERSON>", "Movie.rectDraw": "rectán<PERSON><PERSON>", "Movie.lineDraw": "llinia", "Movie.setColour": "Establecer color a", "Movie.galleryMsg": "<PERSON>er gal<PERSON>", "Pond.documentation": "Documentación", "Pond.playerName": "Xugador", "Pond.targetName": "Oxetivu", "Pond.pendulumName": "Pendilexu", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>"}