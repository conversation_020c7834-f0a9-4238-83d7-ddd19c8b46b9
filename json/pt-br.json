{"@metadata": {"authors": ["!Silent", "Amgauna", "<PERSON>", "Athena in Wonderland", "Cainamarques", "Caçador de Palavras", "<PERSON>", "Eduardoad<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Luk3", "<PERSON><PERSON><PERSON>", "Mordecaista", "<PERSON><PERSON><PERSON>", "Prilopes", "<PERSON> codi<PERSON>", "Slovato", "<PERSON><PERSON><PERSON>", "TheGabrielZaum", "Timarcos<PERSON><PERSON>"]}, "Games.name": "Jogos do <PERSON>ly", "Games.puzzle": "Quebra-Cabeça", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON><PERSON>", "Games.turtle": "Tartaruga", "Games.movie": "Filme", "Games.music": "Música", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "Lagoa", "Games.linesOfCode1": "Você solucionou esse nível com uma linha de JavaScript:", "Games.linesOfCode2": "Você solucionou esse nível com %1 linhas de JavaScript:", "Games.nextLevel": "Você esta preparado para o nível %1?", "Games.finalLevel": "Você esta preparado para o próximo desafio?", "Games.submitTitle": "Título:", "Games.linkTooltip": "Salvar e ligar aos blocos.", "Games.runTooltip": "Rodar o programa que você escreveu.", "Games.runProgram": "Executar o programa", "Games.resetTooltip": "Parar a execução do programa e resetar o nivel.", "Games.resetProgram": "Reiniciar", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Lógica", "Games.catLoops": "Laços", "Games.catMath": "Matemática", "Games.catText": "Texto", "Games.catLists": "Listas", "Games.catColour": "Cor", "Games.catVariables": "Variáveis", "Games.catProcedures": "Funções", "Games.httpRequestError": "Houve um problema com a requisição.", "Games.linkAlert": "Compartilhe seus blocos com este link:\n\n%1", "Games.hashError": "Des<PERSON><PERSON><PERSON>, '%1' não corresponde a um programa salvo.", "Games.xmlError": "Não foi possível carregar seu arquivo salvo. Talvez ele tenha sido criado com uma versão diferente do Blockly?", "Games.submitted": "Agradecemos pelo programa! Se a nossa equipa gostar dele, este será publicado na galeria por nós dentro de alguns dias.", "Games.listVariable": "lista", "Games.textVariable": "texto", "Games.breakLink": "Quando você começar a editar o JavaScript, você não poderá mais voltar a editar os blocos. Você tem certeza?", "Games.blocks": "Blocos", "Games.congratulations": "Parabéns!", "Games.helpAbort": "Este nível é extremamente difícil. Você deseja pulá-lo e ir ao próximo jogo? Você sempre pode voltar mais tarde.", "Index.clear": "<PERSON><PERSON><PERSON> to<PERSON> as suas soluções?", "Index.subTitle": "Jogos para os programadores de amanhã.", "Index.moreInfo": "Informações para educadores...", "Index.startOver": "Quer começar novamente?", "Index.clearData": "Apagar dados", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "Penas", "Puzzle.animal1Trait2": "Bico", "Puzzle.animal1HelpUrl": "http://pt.wikipedia.org/wiki/Pato", "Puzzle.animal2": "Gato", "Puzzle.animal2Trait1": "Bigodes", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "http://pt.wikipedia.org/wiki/Gato", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "http://pt.wikipedia.org/wiki/Abelha", "Puzzle.animal4": "Caracol", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "http://pt.wikipedia.org/wiki/Caracol", "Puzzle.picture": "imagem:", "Puzzle.legs": "patas:", "Puzzle.legsChoose": "escolha...", "Puzzle.traits": "traços:", "Puzzle.error0": "Perfeito!\nTodos os %1 blocos estão corretos.", "Puzzle.error1": "Quase! Um bloco está incorreto.", "Puzzle.error2": "%1 blocos estão incorretos.", "Puzzle.tryAgain": "O bloco destacado está incorreto. Continue tentando.", "Puzzle.checkAnswers": "Checa<PERSON> respostas", "Puzzle.helpText": "Para cada animal (verde), anexe sua imagem, escolha seu numero de pernas, e faça uma lista de seus traços.", "Maze.moveForward": "<PERSON><PERSON><PERSON><PERSON>", "Maze.turnLeft": "vire à esquerda", "Maze.turnRight": "vire à direita", "Maze.doCode": "faça", "Maze.helpIfElse": "Blocos se-senão farão uma coisa ou outra.", "Maze.pathAhead": "se caminho à frente", "Maze.pathLeft": "se caminho à esquerda", "Maze.pathRight": "se caminho à direita", "Maze.repeatUntil": "repetir até", "Maze.moveForwardTooltip": "Move o jogador um espaço para frente.", "Maze.turnTooltip": "Vira o jogador 90 graus para esquerda ou direita.", "Maze.ifTooltip": "Se há um caminho na direção especificada, então faça algumas ações.", "Maze.ifelseTooltip": "Se há um caminho na direção especificada, então faça o primeiro bloco de ações. Caso contrário, faça o segundo bloco de ações.", "Maze.whileTooltip": "Repetir as ações inclusas até que o ponto final seja alcançado.", "Maze.capacity0": "Restam %0 blocos.", "Maze.capacity1": "Resta %1 bloco.", "Maze.capacity2": "Restam %2 blocos.", "Maze.runTooltip": "Faz o jogador executar o que os blocos dizem.", "Maze.resetTooltip": "Coloca o jogador de volta ao início do labirinto.", "Maze.helpStack": "<PERSON><PERSON><PERSON><PERSON> alguns blocos 'avançar' juntos para me ajudar a alcançar o objetivo.", "Maze.helpOneTopBlock": "<PERSON><PERSON> nível, você deve empilhar todos os blocos na área de trabalho branca.", "Maze.helpRun": "Execute seu programa para ver o que acontece.", "Maze.helpReset": "Seu programa não resolveu o labirinto. Aperte 'Reiniciar' e tente novamente.", "Maze.helpRepeat": "Alcance o fim deste caminho usando apenas dois blocos. Use 'repetir' para executar um bloco mais de uma vez.", "Maze.helpCapacity": "Você usou todos os blocos para este nível. Para criar um novo bloco, você primeiro deve deletar um bloco existente.", "Maze.helpRepeatMany": "Você pode encaixar mais de um bloco dentro de um bloco 'repetir'.", "Maze.helpSkins": "Escolha o seu jogador favorito neste menu.", "Maze.helpIf": "Um bloco 'se' fará alguma coisa apenas se a condição for verdadeira. Tente virar à esquerda se houver um caminho para a esquerda.", "Maze.helpMenu": "Clique em %1 no bloco 'se' para mudar sua condição.", "Maze.helpWallFollow": "Você consegue resolver este labirinto complicado? Tente seguir a parede da mão esquerda. Apenas para programadores avançados!", "Bird.noWorm": "não tem minhocas", "Bird.heading": "direção", "Bird.noWormTooltip": "Esse bloco indica quando o pássaro não possui a minhoca.", "Bird.headingTooltip": "Mover na direção do ângulo indicado: 0 é para a direita, 90 é para frente, etc.", "Bird.positionTooltip": "x e y marcam a posição do pássaro. Quando x = 0 o pássaro esta próximo da margem esquerda, quando x = 100 esta perto da margem direita. Quando y = 0 o pássaro esta em baixo, quando  y = 100 ele está no topo.", "Bird.helpHeading": "Trocar o ângulo da direção para que o pássaro pegue a minhoca e aterrisse em seu ninho.", "Bird.helpHasWorm": "Usar este bloco para ir em uma posição se você tiver a minhoca, ou uma posição diferente se você não tiver a minhoca.", "Bird.helpX": "'x' e sua posição horizontal atual. Use este bloco para ir em uma direção se 'x' for menor que um numero, ou uma direção diferente caso contrário.", "Bird.helpElse": "Clique no ícone para modificar o bloco 'se'.", "Bird.helpElseIf": "Este nível precisa de um bloco 'senão se' e de um bloco 'senão'.", "Bird.helpAnd": "O bloco \"e\" somente é verdadeiro se as duas entradas forem verdadeiras.", "Bird.helpMutator": "Puxe um bloco \"senão\" em cima do bloco \"se\".", "Turtle.moveTooltip": "Move a tartaruga para frente ou para trás a quantidade especificada.", "Turtle.moveForward": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.moveBackward": "mover para trás", "Turtle.turnTooltip": "Gira a tartaruga para esquerda ou direita de acordo com o número de graus especificado.", "Turtle.turnRight": "vire à direita", "Turtle.turnLeft": "vire à esquerda", "Turtle.widthTooltip": "<PERSON><PERSON> o tamanho da cane<PERSON>.", "Turtle.setWidth": "configurar largura para", "Turtle.colourTooltip": "<PERSON>da a cor da caneta.", "Turtle.setColour": "Configurar cor para", "Turtle.penTooltip": "Levanta ou abaixa a caneta, para parar ou voltar a desenhar.", "Turtle.penUp": "le<PERSON><PERSON> caneta", "Turtle.penDown": "a<PERSON><PERSON><PERSON> cane<PERSON>", "Turtle.turtleVisibilityTooltip": "Torna a tartaruga (círculo e seta) visível ou invisível.", "Turtle.hideTurtle": "esconder tartaruga", "Turtle.showTurtle": "mostrar tartaruga", "Turtle.printHelpUrl": "https://pt.wikipedia.org/wiki/Impress%C3%A3o", "Turtle.printTooltip": "Desenha texto na direção da tartaruga em sua localização.", "Turtle.print": "imprimir", "Turtle.fontHelpUrl": "https://pt.wikipedia.org/wiki/Fonte_tipogr%C3%A1fica", "Turtle.fontTooltip": "Configurar a fonte usada pelo bloco de impressão na tela.", "Turtle.font": "fonte", "Turtle.fontSize": "ta<PERSON><PERSON> da fonte", "Turtle.fontNormal": "normal", "Turtle.fontBold": "negrito", "Turtle.fontItalic": "itálico", "Turtle.submitDisabled": "Rode seu programa até que ele pare. Depois você pode submeter seu desenho a galeria.", "Turtle.galleryTooltip": "Abrir a galeria de desenhos.", "Turtle.galleryMsg": "Ver a Galeria", "Turtle.submitTooltip": "Enviar seu desenho para a galeria.", "Turtle.submitMsg": "Enviar a Galeria", "Turtle.helpUseLoop": "Sua solução funciona, mas você pode fazer melhor.", "Turtle.helpUseLoop3": "Desenhe o contorno somente com esses três blocos.", "Turtle.helpUseLoop4": "Desenhe a estrela somente com 4 blocos.", "Turtle.helpText1": "Crie um programa que desenha um quadrado.", "Turtle.helpText2": "Modifique seu programa para que desenhe um pentágono em vez de um quadrado.", "Turtle.helpText3a": "Aqui está um novo bloco que te permite trocar a cor:", "Turtle.helpText3b": "Desenhe uma estrela amarela.", "Turtle.helpText4a": "Aqui esta um novo bloco que te permite suspender a caneta quando você move o cursor:", "Turtle.helpText4b": "Desenhe uma pequena estrela amarela, depois, desenhe uma linha em cima dela.", "Turtle.helpText5": "Em vez de uma estrela, você pode desenhar quatro estrelas dispostas como um quadrado?", "Turtle.helpText6": "Desenhe três estrelas amarelas e uma linha branca.", "Turtle.helpText7": "<PERSON><PERSON>he as estrelas, depois desenhe quatro linhas brancas.", "Turtle.helpText8": "Desenhar 360 linhas brancas vai parecer uma lua cheia.", "Turtle.helpText9": "Você pode adicionar um circulo preto para que a lua fique crescente?", "Turtle.helpText10": "Desenhe o que você quiser. Você conseguiu um grande numero de blocos novos para explorar. Divirta-se!", "Turtle.helpText10Reddit": "Use o botão \"Ver Galeria\" para ver o que as outras pessoas desenharam. Se você desenhar algo interessante,use o botão \"Enviar para a Galeria\" para publicar.", "Turtle.helpToolbox": "Escolha uma categoria para ver os blocos.", "Movie.x": "X", "Movie.y": "Y", "Movie.x1": "in<PERSON>cio x", "Movie.y1": "in<PERSON><PERSON> y", "Movie.x2": "fim X", "Movie.y2": "fim Y", "Movie.radius": "raio", "Movie.width": "largura", "Movie.height": "altura", "Movie.circleTooltip": "Desenha um circulo numa localidade específica e com o raio especificado.", "Movie.circleDraw": "circulo", "Movie.rectTooltip": "Desenhar um retângulo numa localidade específica com uma largura e uma altura especifica.", "Movie.rectDraw": "retangulo", "Movie.lineTooltip": "Desenha uma linha de um ponto ao outro com um comprimento especifico.", "Movie.lineDraw": "linha", "Movie.timeTooltip": "Retornar o tempo atual na animação (0-100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON> a cor da caneta.", "Movie.setColour": "mudar a cor para", "Movie.submitDisabled": "Seu filme não se move. Use blocos para fazer algo interessante. Depois você pode enviar seu filme para a galeria.", "Movie.galleryTooltip": "Abra a galeria de filmes.", "Movie.galleryMsg": "Ver a Galeria", "Movie.submitTooltip": "Enviar o seu filme para a galeria.", "Movie.submitMsg": "Enviar a Galeria", "Movie.helpLayer": "Mova o círculo de fundo para o topo do seu programa. Então aparecerá atrás da pessoa.", "Movie.helpText1": "Use formas simples para desenhar essa pessoa.", "Movie.helpText2a": "Este nível é um filme. Você quer que o braço da pessoa se mova pela tela. Pressione o botão play para ver uma prévia.", "Movie.helpText2b": "Enquanto o filme passa, o valor do bloco 'time' conta de 0 a 100. Como queira a posição 'y' do braço iniciar no 0 e que vá até 100 isto deve ser fácil.", "Movie.helpText3": "O bloco 'tempo' conta de 0 a 100. Mas agora você quer que a posição 'y' do outro braço comece em 100 e vá para 0. Você consegue descobrir uma fórmula matemática simples que inverta a direção?", "Movie.helpText4": "Use o que você aprendeu no nível anterior para fazer pernas cruzadas.", "Movie.helpText5": "A fórmula matemática para o braço é complicada. Aqui está a resposta:", "Movie.helpText6": "Dê a pessoa algumas mãos.", "Movie.helpText7": "Use o bloco 'se' para desenhar uma pequena cabeça na primeira metade do filme. Em seguida, desenhe uma grande cabeça para a segunda metade do filme.", "Movie.helpText8": "Faça as pernas inverterem a direção a meio do filme.", "Movie.helpText9": "Desenhe um círculo em expansão atrás da pessoa.", "Movie.helpText10": "Desenhe o que você quiser. Você conseguiu um grande numero de blocos novos para explorar. Divirta-se!", "Movie.helpText10Reddit": "Use o botão \"Ver Galeria\" para ver o que as outras pessoas desenharam. Se você desenhar algo interessante,use o botão \"Enviar para a Galeria\" para publicar.", "Music.playNoteTooltip": "Toque uma nota musical com a duração e altura específicas.", "Music.playNote": "tocar %1 nota %2", "Music.restTooltip": "Aguarde pela duração especificada.", "Music.restWholeTooltip": "Aguarde por uma semibreve.", "Music.rest": "resta %1", "Music.setInstrumentTooltip": "Alterne para o instrumento específico ao tocar as notas musicais posteriores.", "Music.setInstrument": "ajustar o instrumento para %1", "Music.startTooltip": "Execute os blocos dentro quando o botão de 'Executar programa' for clicado.", "Music.start": "quando %1 é clicado", "Music.pitchTooltip": "<PERSON>a nota (C4 é 7).", "Music.firstPart": "primeira parte", "Music.piano": "piano", "Music.trumpet": "trompete", "Music.banjo": "banjo", "Music.violin": "violino", "Music.guitar": "guitarra", "Music.flute": "flauta", "Music.drum": "tambor", "Music.choir": "coro", "Music.submitDisabled": "Execute o seu programa até ele parar. Então poderá enviar a sua música para a galeria.", "Music.galleryTooltip": "Abrir a galeria da música.", "Music.galleryMsg": "Ver a galeria", "Music.submitTooltip": "Envie sua música para a galeria.", "Music.submitMsg": "Enviar para a galeria", "Music.helpUseFunctions": "A sua solução funcionou, mas pode fazer melhor. Utilize as funções para reduzir a quantidade de código repetido.", "Music.helpUseInstruments": "A música soará melhor se usar um instrumento diferente em cada bloco inicial.", "Music.helpText1": "<PERSON><PERSON><PERSON><PERSON> as primeiras quatro notas de 'Frère Jacques'.", "Music.helpText2a": "Uma 'função' permite agrupar os blocos, e depois executá-los mais de uma vez.", "Music.helpText2b": "Crie uma função para tocar as quatro primeiras notas de 'Frère Jacques'. Execute essa função duas vezes. Não acrescente novos blocos de notas.", "Music.helpText3": "Crie uma segunda função para a próxima parte de 'Frère Jacques'. A última nota é mais longa.", "Music.helpText4": "Crie uma terceira função para a próxima parte de 'Frère Jacques'. As quatro primeiras notas são mais curtas.", "Music.helpText5": "Complete toda a cantiga de '<PERSON><PERSON> Jacques'.", "Music.helpText6a": "Este bloco novo permite que mude para outro instrumento.", "Music.helpText6b": "Tocar a sua ária com um violino.", "Music.helpText7a": "Este novo bloco acrescenta um temporizador silencioso.", "Music.helpText7b": "Crie um segundo bloco inicial que tenha dois blocos silenciosos, e então toque 'Frère Jacques'.", "Music.helpText8": "Cada bloco inicial deve tocar '<PERSON><PERSON>' duas vezes.", "Music.helpText9": "Crie quatro blocos iniciais que toquem 'Frère Jacques' duas vezes. Acrescente o número correto dos blocos silenciosos.", "Music.helpText10": "Componha o que o que você quiser. Você tem uma grande quantidade de blocos novos que pode explorar. Divirta-se!", "Music.helpText10Reddit": "Utilize o botão \"Ver Galeria\" para ver o que as outras pessoas compuseram. Se compor algo interessante, use o botão \"Enviar para a Galeria\" para publicar.", "Pond.scanTooltip": "Busque inimigos. Especifique uma direção(0-360). Retorna a distancia do inimigo mais proximo naquela direção. Retorna infinito se nenhum inimigo for encontrado.", "Pond.cannonTooltip": "Dispare o canhão. Especifique uma direção (0-360) e o alcance (0-70).", "Pond.swimTooltip": "<PERSON>dar para a frente. Especifique uma direção (0-360).", "Pond.stopTooltip": "<PERSON><PERSON> de nadar. O jogador fara uma parada lenta.", "Pond.healthTooltip": "Retorna a saude atual do jogador (0 esta morto, 100 esta saudável).", "Pond.speedTooltip": "Retorna a velocidade atual do jogador (0 esta parádo, 100 esta na velocidade maxima)", "Pond.locXTooltip": "Retorna a coordenada X do jogador (0 é a margem esquerda, 100 é a margem direita).", "Pond.locYTooltip": "Retorna a  coordenada Y do jogador (0 é a borda inferior, 100 é a borda superior).", "Pond.logTooltip": "Imprime um número no console do seu navegador.", "Pond.docsTooltip": "Mostrar a documentação de idioma.", "Pond.documentation": "Documentação", "Pond.playerName": "Jogador", "Pond.targetName": "Alvo", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "Sua solução funciona, mas você pode fazer melhor. Use 'scan' para mostrar ao canhão o quão longe atirar.", "Pond.helpText1": "Use o comando 'cannon' para atingir o alvo. O primeiro parâmetro é o ângulo, o segundo é o alcance. Ache a combinação correta.", "Pond.helpText2": "Este alvo deve ser acertado várias vezes. Use o ciclo 'while (true)' para fazer algo indefinidamente.", "Pond.helpText3a": "Esse oponente move-se para frente e para trás, fazendo ficar difícil acertá-lo. A Expressão 'scan' retorna o alcance exato do oponente naquela direção.", "Pond.helpText3b": "Este alcance é exatamente o que o comando 'cannon' precisa para atirar precisamente.", "Pond.helpText4": "Esse oponente está muito longe para usar o canhão (que tem um alcance de 70 metros). Invés disso, use o comando 'swim' para começar a nadar até o oponente e trombar nele.", "Pond.helpText5": "Este oponente também está muito longe para usar o canhão. Mas você está muito fraco para sobreviver à uma colisão. Nade até o oponente enquanto sua posição horizontal for menor que 50. Então 'pare' e use o canhão.", "Pond.helpText6": "Este oponente irá se mover para longe quando golpeado. Nade até ele se está fora do alcance (70 metros).", "Gallery": "Galeria"}