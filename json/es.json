{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "EDGI", "<PERSON><PERSON><PERSON><PERSON>", "Harvest", "Jesusmc", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "<PERSON> <<EMAIL>>", "Ryo567", "VegaDark", "WeSiToS"]}, "Games.name": "<PERSON><PERSON><PERSON>", "Games.puzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.maze": "Laberinto", "Games.bird": "Pájaro", "Games.turtle": "Tortuga", "Games.movie": "<PERSON><PERSON><PERSON><PERSON>", "Games.music": "Música", "Games.pondTutor": "Tutor del estanque", "Games.pond": "Estanque", "Games.linesOfCode1": "Resolviste este nivel con 1 línea de JavaScript:", "Games.linesOfCode2": "Resolviste este nivel con %1 líneas de JavaScript:", "Games.nextLevel": "¿Estás listo/a para el nivel %1?", "Games.finalLevel": "¿Estás listo/a para el siguiente desafío?", "Games.submitTitle": "Título:", "Games.linkTooltip": "Guardar y enlazar a los bloques.", "Games.runTooltip": "Ejecutar el programa que escribiste.", "Games.runProgram": "Ejecutar el programa", "Games.resetTooltip": "Detener el programa y restablecer el nivel.", "Games.resetProgram": "Restablecer", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Lógica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matemáticas", "Games.catText": "Texto", "Games.catLists": "Listas", "Games.catColour": "Color", "Games.catVariables": "Variables", "Games.catProcedures": "Funciones", "Games.httpRequestError": "Hubo un problema con la petición.", "Games.linkAlert": "Comparte tus bloques con este enlace:\n\n%1", "Games.hashError": "«%1» no corresponde con ningún programa guardado.", "Games.xmlError": "No se pudo cargar el archivo guardado.  ¿Quizá fue creado con otra versión de Blockly?", "Games.submitted": "Gracias por tu programa! Si a nuestro grupo experto de monos entrenados le gusta, lo publicarán en la galería en un par de días.", "Games.listVariable": "lista", "Games.textVariable": "texto", "Games.breakLink": "Una vez que comiences la edición de JavaScript, no podrás volver a la edición de bloques. ¿Estás seguro?", "Games.blocks": "Bloques", "Games.congratulations": "¡Felicitaciones!", "Games.helpAbort": "Este nivel es extremadamente difícil. ¿Deseas saltearlo e ir al siguiente nivel? Siempre puedes regresar más adelante.", "Index.clear": "¿<PERSON><PERSON>r todas tus soluciones?", "Index.subTitle": "Juegos para los programadores del futuro.", "Index.moreInfo": "Información para pedagogos…", "Index.startOver": "¿Quieres empezar de nuevo?", "Index.clearData": "<PERSON><PERSON><PERSON> da<PERSON>", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Pico", "Puzzle.animal1HelpUrl": "http://es.wikipedia.org/wiki/Pato", "Puzzle.animal2": "Gato", "Puzzle.animal2Trait1": "Bigotes", "Puzzle.animal2Trait2": "Pelaje", "Puzzle.animal2HelpUrl": "http://es.wikipedia.org/wiki/Gato", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "http://es.wikipedia.org/wiki/<PERSON>ja", "Puzzle.animal4": "Caracol", "Puzzle.animal4Trait1": "Caparazón", "Puzzle.animal4Trait2": "Baba", "Puzzle.animal4HelpUrl": "http://es.wikipedia.org/wiki/Caracol", "Puzzle.picture": "foto:", "Puzzle.legs": "patas:", "Puzzle.legsChoose": "elige...", "Puzzle.traits": "rasgos:", "Puzzle.error0": "¡Perfecto!\nLos %1 bloques están bien colocados.", "Puzzle.error1": "¡Casi! Un bloque está mal colocado.", "Puzzle.error2": "%1 bloques están mal colocados.", "Puzzle.tryAgain": "El bloque destacado está mal colocado.\n<PERSON><PERSON>.", "Puzzle.checkAnswers": "Comprobar las respuestas", "Puzzle.helpText": "Para cada animal (verde), adjunta su foto, elige la cantidad de patas y lista sus rasgos.", "Maze.moveForward": "a<PERSON><PERSON>", "Maze.turnLeft": "girar a la izquierda", "Maze.turnRight": "girar a la derecha", "Maze.doCode": "hacer", "Maze.helpIfElse": "Los bloques 'si-sino' hacen una cosa o la otra.", "Maze.pathAhead": "si hay camino enfrente", "Maze.pathLeft": "si hay camino a la izquierda", "Maze.pathRight": "si hay camino a la derecha", "Maze.repeatUntil": "repetir hasta", "Maze.moveForwardTooltip": "Mueve al jugador un cuadro hacia delante.", "Maze.turnTooltip": "Gira al jugador a izquierda o derecha 90 grados.", "Maze.ifTooltip": "Si hay un camino en la dirección especificada, entonces ejecuta unas acciones.", "Maze.ifelseTooltip": "Si hay un camino en la dirección especificada, entonces ejecuta el primer bloque de acciones. Sino, haz el segundo bloque de acciones.", "Maze.whileTooltip": "Repite las acciones contenidas hasta alcanzar el punto final.", "Maze.capacity0": "Te quedan %0 bloques.", "Maze.capacity1": "Te queda %1 bloque.", "Maze.capacity2": "Te quedan %2 bloques.", "Maze.runTooltip": "Hace que el jugador haga lo que dicen los bloques.", "Maze.resetTooltip": "Poner al jugador de nuevo al comienzo del laberinto.", "Maze.helpStack": "Une un par de bloques 'avanzar' para ayudarme a llegar a la meta.", "Maze.helpOneTopBlock": "En este nivel, necesitas unir los bloques en el espacio de trabajo en blanco.", "Maze.helpRun": "Ejecuta tu programa para ver qué pasa.", "Maze.helpReset": "Tu programa no resolvió el laberinto. Presiona \"Reiniciar\" e intenta otra vez.", "Maze.helpRepeat": "Llega al final de este camino usando tan sólo dos bloques. Utiliza 'repetir' para ejecutar un bloque más de una vez.", "Maze.helpCapacity": "Has usado todos los bloques para este nivel. Para crear un bloque nuevo, primero debes eliminar un bloque existente.", "Maze.helpRepeatMany": "Puedes usar más de un bloque dentro de un bloque 'repetir'.", "Maze.helpSkins": "Elige a tu jugador favorito en este menú.", "Maze.helpIf": "Un bloque 'si' hará algo solamente si la condición es verdadera. Intenta girar a la izquierda si hay camino a la izquierda.", "Maze.helpMenu": "Pulsa en %1 en el bloque «si» para cambiar su condición.", "Maze.helpWallFollow": "¿Puedes resolver este complicado laberinto? Intenta seguir la pared de la izquierda. ¡Solo para programadores avanzados!", "Bird.noWorm": "no tiene gusano", "Bird.heading": "rumbo", "Bird.noWormTooltip": "La condición cuando el pájaro no consiguió el gusano.", "Bird.headingTooltip": "Mover en la dirección del ángulo especificado: 0 es a la derecha, 90 es hacia arriba, etc.", "Bird.positionTooltip": "x e y marcan la posición del pájaro. Cuando x = 0 el pájaro está cerca del borde izquierdo, cuando x = 100 está cerca del derecho. Cuando y = 0 el pájaro está en la parte inferior, cuando y = 100 está en la parte superior.", "Bird.helpHeading": "Cambia el ángulo de rumbo para que el pájaro consiga el gusano y aterrice en su nido.", "Bird.helpHasWorm": "Usa este bloque para seguir un rumbo si tienes el gusano, u otro rumbo si no lo tienes.", "Bird.helpX": "'x' es tu posición horizontal actual. Usa este bloque para seguir un rumbo si 'x' es menor a un número, o un rumbo distinto de otro modo.", "Bird.helpElse": "Pulsa en el icono para modificar el bloque «si».", "Bird.helpElseIf": "Este nivel necesita un bloque 'sino si' y uno 'sino'.", "Bird.helpAnd": "El bloque 'y' es cierto solo cuando ambas de sus entradas son ciertas.", "Bird.helpMutator": "Arrastra un bloque 'sino' dentro del bloque 'si'.", "Turtle.moveTooltip": "Mueve la tortuga hacia adelante o hacia atrás en la cantidad especificada.", "Turtle.moveForward": "a<PERSON><PERSON>", "Turtle.moveBackward": "retroceder", "Turtle.turnTooltip": "Gira la tortuga hacia la izquierda o la derecha el número especificado de grados.", "Turtle.turnRight": "girar a la derecha", "Turtle.turnLeft": "girar a la izquierda", "Turtle.widthTooltip": "Cambia la anchura del bolígrafo.", "Turtle.setWidth": "establecer la anchura a", "Turtle.colourTooltip": "Cambia el color del bolígrafo.", "Turtle.setColour": "establecer el color a", "Turtle.penTooltip": "Levanta o baja el bolígrafo para detener o empezar a dibujar.", "Turtle.penUp": "levantar el bolígrafo", "Turtle.penDown": "bajar el bolígrafo", "Turtle.turtleVisibilityTooltip": "Hace a la tortuga (círculo y flecha) visible o invisible.", "Turtle.hideTurtle": "ocultar la tortuga", "Turtle.showTurtle": "mostrar la tortuga", "Turtle.printHelpUrl": "https://es.wikipedia.org/wiki/Impresi%C3%B3n", "Turtle.printTooltip": "Dibuja texto en la dirección de la tortuga en su ubicación.", "Turtle.print": "imprimir", "Turtle.fontHelpUrl": "https://es.wikipedia.org/wiki/Tipo_de_letra", "Turtle.fontTooltip": "Establece el tipo de letra utilizado por el bloque de impresión.", "Turtle.font": "tipo de letra", "Turtle.fontSize": "<PERSON><PERSON><PERSON> de <PERSON>ra", "Turtle.fontNormal": "normal", "Turtle.fontBold": "negrita", "Turtle.fontItalic": "cursiva", "Turtle.submitDisabled": "Ejecuta tu programa hasta que se detenga. <PERSON><PERSON> puedes enviar tu dibujo a la galería.", "Turtle.galleryTooltip": "Abre la galería de dibujos.", "Turtle.galleryMsg": "<PERSON>er gal<PERSON>", "Turtle.submitTooltip": "Envía tu dibujo a la galería.", "Turtle.submitMsg": "Enviar a la galería", "Turtle.helpUseLoop": "La solución funciona, pero se puede hacer mejor.", "Turtle.helpUseLoop3": "Dibuja la forma con solo tres bloques.", "Turtle.helpUseLoop4": "Dibuja la estrella con solo cuatro bloques.", "Turtle.helpText1": "Crea un programa que dibuje un cuadrado.", "Turtle.helpText2": "Cambia el programa para dibujar un pentágono en lugar de un cuadrado.", "Turtle.helpText3a": "Hay un bloque nuevo que permite cambiar el color:", "Turtle.helpText3b": "Dibuja una estrella amarilla.", "Turtle.helpText4a": "Hay un bloque nuevo que permite levantar el lápiz del papel cuando te mueves:", "Turtle.helpText4b": "Dibuja una estrella amarilla, luego dibuja una línea por encima de ella.", "Turtle.helpText5": "En lugar de una estrella, ¿puedes dibujar cuatro estrellas dispuestas en un cuadrado?", "Turtle.helpText6": "Dibuja tres estrellas amarillas y una línea blanca.", "Turtle.helpText7": "Dibuja las estrellas, luego dibuja cuatro líneas blancas.", "Turtle.helpText8": "Dibujar 360 líneas blancas se verá como la luna llena.", "Turtle.helpText9": "¿Puedes agregar un círculo negro para que la luna se convierta en una media luna?", "Turtle.helpText10": "Dibuja lo que quieras. Tienes muchos bloques nuevos para explorar ¡Diviértete!", "Turtle.helpText10Reddit": "Usa el botón \"Ver galería\" para ver lo que otros han dibujado. Si has dibujado algo interesante, usa el botón \"Enviar a la galería\" para publicarlo.", "Turtle.helpToolbox": "Elige una categoría para ver los bloques.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "inicio x", "Movie.y1": "inicio y", "Movie.x2": "final x", "Movie.y2": "final y", "Movie.radius": "radio", "Movie.width": "an<PERSON>ra", "Movie.height": "altura", "Movie.circleTooltip": "Dibuja un círculo en la ubicación especificada y con el radio especificado.", "Movie.circleDraw": "<PERSON><PERSON><PERSON><PERSON>", "Movie.rectTooltip": "Dibuja un rectángulo en la ubicación especificada y con la anchura y la altura especificadas.", "Movie.rectDraw": "rect<PERSON><PERSON><PERSON>", "Movie.lineTooltip": "Dibuja una línea desde un punto a otro con la anchura especificada.", "Movie.lineDraw": "línea", "Movie.timeTooltip": "Devuelve la hora actual en la animación (0-100).", "Movie.colourTooltip": "Cambia el color de la pluma.", "Movie.setColour": "establecer el color a", "Movie.submitDisabled": "La película no se mueve. Usa los bloques para hacer algo interesante. Luego puedes enviar tu película a la galería.", "Movie.galleryTooltip": "Abre la galería de películas.", "Movie.galleryMsg": "<PERSON>er gal<PERSON>", "Movie.submitTooltip": "Envía tu película a la galería.", "Movie.submitMsg": "Enviar a la galería", "Movie.helpLayer": "Mueve el círculo de fondo al tope de tu programa. Entonces aparecerá detrás de la persona.", "Movie.helpText1": "Usa formas simples para dibujar a esta persona.", "Movie.helpText2a": "Este nivel es una película. Quieres que el brazo de la persona se mueva por la pantalla. Presiona el botón reproducir para ver una vista previa.", "Movie.helpText2b": "Mientras se reproduce la película, el valor del bloque 'time' cuenta desde 0 a 100. Dad<PERSON> que quieres que la posición 'y' del brazo empieze en 0 y llegue a 100, esto debería ser fácil", "Movie.helpText3": "El bloque 'tiempo' cuenta desde 0 a 100. Pero ahora buscas que la posición 'y' del otro brazo empieze en 100 y valla a 0. ¿Puedes pensar en una fórmula matemática simple para dar vuelta la dirección?", "Movie.helpText4": "Usa lo aprendido en el nivel anterior para cruzar las piernas.", "Movie.helpText5": "La fórmula matemática para el brazo es muy complicada. Aquí esta la respuesta:", "Movie.helpText6": "<PERSON> a la persona un par de manos.", "Movie.helpText7": "Usa el bloque 'if' para dibujar una cabeza pequeña en la primera mitad de la película. Dibuja una cabeza grande en la segunda mitad de la película.", "Movie.helpText8": "Haz que las piernas vallan en dirección reversa a la mitad de la película.", "Movie.helpText9": "Dibuja un círculo creciente detrás de la persona.", "Movie.helpText10": "Haz una película sobre lo que quieras. Tienes muchos bloques nuevos para explorar ¡Diviértete!", "Movie.helpText10Reddit": "Usa el botón \"Ver galería\" para ver películas de otras personas. <PERSON> has hecho una película interesante, usa el botón \"Enviar a la galería\" para publicarla.", "Music.playNoteTooltip": "Reproduce una nota musical de la duración y el tono que se especifiquen.", "Music.playNote": "reproducir nota %2 por %1", "Music.restTooltip": "Espera la duración que se especifique.", "Music.restWholeTooltip": "Espera una nota completa.", "Music.rest": "descanso de %1", "Music.setInstrumentTooltip": "Cambia al instrumento especificado cuando reproduce notas musicales posteriores.", "Music.setInstrument": "definir el instrumento como %1", "Music.startTooltip": "Ejecuta los bloques interiores al pulsar el botón «Ejecutar programa».", "Music.start": "al hacer clic en %1", "Music.pitchTooltip": "<PERSON> nota (C4 es 7).", "Music.firstPart": "primera parte", "Music.piano": "piano", "Music.trumpet": "trompeta", "Music.banjo": "banyo", "Music.violin": "violín", "Music.guitar": "guitarra", "Music.flute": "flauta", "Music.drum": "percusión", "Music.choir": "coro", "Music.submitDisabled": "Corre tu programa hasta que termine. Entonces podrás subir tu musica a la galería.", "Music.galleryTooltip": "Abrir la galería de música.", "Music.galleryMsg": "<PERSON>er gal<PERSON>", "Music.submitTooltip": "Envía tu música a la galería.", "Music.submitMsg": "Enviar a la galería", "Music.helpUseFunctions": "Tu solución funciona, pero podría ser mejor. Utiliza funciones para reducir la cantidad de código repetido.", "Music.helpUseInstruments": "La música sonará mejor si usas un instrumento diferente en cada bloque inicial.", "Music.helpText1": "Componga las primeras cuatro notas de 'Fray Santiago'", "Music.helpText2a": "Una 'función' te permite agrupar varios bloques juntos, y poder correrlos más de una vez.", "Music.helpText2b": "Crear una función que toque las cuatro primeras notas de 'Fray Santiago'. Corre la función dos veces. No agregues ningun bloque de nota nuevo.", "Music.helpText3": "Crea una segunda función para la siguiente parte de 'Fray Santiago'. La última nota es larga.", "Music.helpText4": "Crea una tercera función para la siguiente parte de 'Fray Santiago'. Las primeras cuatro notas son cortas.", "Music.helpText5": "Completa el tema completo de 'Fray Santiago'.", "Music.helpText6a": "Este nuevo bloque te permite cambiar a otro instrumento.", "Music.helpText6b": "Reproduce tu tono con un violín.", "Music.helpText7a": "Este bloque nuevo agrega una pausa musical.", "Music.helpText7b": "Crea un segundo bloque de inicio que tenga dos bloques de silencio, y luego también toque 'Fray Santigo'.", "Music.helpText8": "Cada bloque reproducirá «Fr<PERSON> Jacques» dos veces.", "Music.helpText9": "Crea cuatro bloques de inicio, cada uno tocando 'Fray Santiago' dos veces. Agrega la cantidad correcta de bloques de silencio.", "Music.helpText10": "Compón lo que quieras. Tienes un inmenso número de bloques por descubrir. ¡Diviértete!", "Music.helpText10Reddit": "Utiliza el botón «Ver galería» para ver lo que otras personas han compuesto. Si compones una pieza interesante, utiliza el botón «Enviar a la galería» para publicarla.", "Pond.scanTooltip": "Busca enemigos. Especifica una dirección (0-360). Devuelve la distancia al enemigo más cercano en esa dirección. Devuelve infinito si no encuentra enemigos.", "Pond.cannonTooltip": "Dispara el cañón. Especifica una dirección (0-360) y un alcance (0-70).", "Pond.swimTooltip": "Nada hacia adelante. Especifica una dirección (0-360).", "Pond.stopTooltip": "<PERSON><PERSON> de nadar. El jugador hará una parada lenta.", "Pond.healthTooltip": "Devuelve la salud actual del jugador (0 está muerto, 100 está saludable).", "Pond.speedTooltip": "Devuelve la velocidad actual del jugador (0 está detenido, 100 es velocidad máxima).", "Pond.locXTooltip": "Devuelve la coordenada X del jugador (0 es el borde izquierdo, 100 es el borde derecho).", "Pond.locYTooltip": "Devuelve la coordenada Y del jugador (0 es el borde inferior, 100 es el borde superior).", "Pond.logTooltip": "Imprime un número en la consola de tu navegador.", "Pond.docsTooltip": "Mostrar la documentación del idioma.", "Pond.documentation": "Documentación", "Pond.playerName": "Jugador", "Pond.targetName": "<PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON>", "Pond.helpUseScan": "La solución funciona, pero se puede mejorar. Usa 'scan' para decirle al cañón a qué distancia disparar.", "Pond.helpText1": "Usa el comando 'cannon' para darle al blanco. El primer parámetro es el ángulo, el segundo es el alcance. Encuentra la combinación correcta.", "Pond.helpText2": "Este objetivo debe ser golpeado muchas veces. Usa un bucle 'while (true)'(mientras sea verdadero) para hacer algo de forma indefinida.", "Pond.helpText3a": "Este oponente se mueve hacia atrás y adelante, ha<PERSON><PERSON><PERSON><PERSON> di<PERSON> de golpear. La expresión 'scan' devuelve el alcance exacto al oponente en la dirección especificada.", "Pond.helpText3b": "Este alcance es exactamente lo que el comando 'cannon' necesita para disparar con precisión.", "Pond.helpText4": "Este oponente está demasiado lejos para usar el cañón (que tiene un límite de 70 metros). En su lugar, utiliza la orden «swim» para comenzar a nadar hacia el oponente y chocar contra él.", "Pond.helpText5": "Este oponente también está demasiado lejos como para usar el cañón. Pero eres demasiado débil para sobrevivir a una colisión. Nada hacia el oponente mientras su ubicación horizontal sea menor a 50. <PERSON><PERSON> detente (con 'stop') y usa el cañón.", "Pond.helpText6": "Este oponente se alejará cuando sea golpeado. Nada hacia él si está fuera de alcance (70 metros).", "Gallery": "Galería"}