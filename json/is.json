{"@metadata": {"authors": ["Jon<PERSON><PERSON>", "LoveIceLang", "Sveinn í Fell<PERSON>"]}, "Games.name": "<PERSON><PERSON> leiki<PERSON>", "Games.puzzle": "Púsl", "Games.maze": "Völundarhús", "Games.bird": "Fugl", "Games.turtle": "Skjaldbaka", "Games.movie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.music": "Tónlist", "Games.pondTutor": "<PERSON> kenns<PERSON>mi", "Games.pond": "Pollur", "Games.linesOfCode1": "<PERSON><PERSON> leystir þennan áfanga með 1 línu af JavaScript:", "Games.linesOfCode2": "<PERSON><PERSON> leystir þennan áfanga með %1 línum af JavaScript:", "Games.nextLevel": "Viltu fara í áfanga %1?", "Games.finalLevel": "Ertu til að fást við næstu á<PERSON>?", "Games.submitTitle": "Titill:", "Games.linkTooltip": "Vista og tengja við kubba.", "Games.runTooltip": "<PERSON><PERSON> forritið sem þú skrifaðir.", "Games.runProgram": "<PERSON><PERSON>", "Games.resetTooltip": "Stöðva forritið og núllstilla áfangann.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Rökvísi", "Games.catLoops": "Lykkjur", "Games.catMath": "Reikningur", "Games.catText": "Texti", "Games.catLists": "Listar", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "<PERSON><PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "<PERSON><PERSON>ð kom upp vandam<PERSON>l með beiðnina.", "Games.linkAlert": "<PERSON><PERSON><PERSON> kubbunum þínum með þessari krækju:", "Games.hashError": "<PERSON><PERSON><PERSON> miður, '%1' passar ekki við neitt vistað forrit.", "Games.xmlError": "Gat ekki hlaðið inn vistuðu skránni þinni. Var hún kannski útbúin í annarri út<PERSON> af <PERSON>?", "Games.submitted": "Ég þakka ykkur kærlega fyrir þetta forrit! Ef k<PERSON> starfsfólkinu okkar líkar við það, þá munu þau birta það í safninu innan skamms.", "Games.listVariable": "listi", "Games.textVariable": "texti", "Games.breakLink": "<PERSON><PERSON>ar þú byrjar að breyta JavaScript, geturðu ekki farið til baka í að breyta kubbum. Er það í lagi?", "Games.blocks": "<PERSON><PERSON><PERSON>", "Games.congratulations": "Til hamingju!", "Games.helpAbort": "<PERSON><PERSON><PERSON> er mjög erfiður. Viltu sleppa honum og fara í næsta leik? Þú getur alltaf reynt aftur seinna.", "Index.clear": "<PERSON><PERSON>ða öllum lausnum þínum?", "Index.subTitle": "<PERSON><PERSON><PERSON> fyrir forritara framtíðarinnar.", "Index.moreInfo": "Upplýsingar fyrir kennara...", "Index.startOver": "Viltu byrja upp á nýtt?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON> gögnum", "Puzzle.animal1": "Önd", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "http://is.wikipedia.org/wiki/%C3%96nd", "Puzzle.animal2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "http://is.wikipedia.org/wiki/K%C3%B6ttur", "Puzzle.animal3": "Býfluga", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON>rod<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "http://is.wikipedia.org/wiki/B%C3%BDflugur", "Puzzle.animal4": "Snigill", "Puzzle.animal4Trait1": "Skel", "Puzzle.animal4Trait2": "Slím", "Puzzle.animal4HelpUrl": "http://is.wikipedia.org/wiki/Sniglar", "Puzzle.picture": "mynd:", "Puzzle.legs": "fætur:", "Puzzle.legsChoose": "veldu...", "Puzzle.traits": "einkenni:", "Puzzle.error0": "Fullkomið!\nAllir %1 kubbarnir eru réttir.", "Puzzle.error1": "Næstum því! Einn kubbur er rangur.", "Puzzle.error2": "%1 kubbar eru rangir.", "Puzzle.tryAgain": "Upplýsti kubburinn er ekki r<PERSON>ttur.\n<PERSON><PERSON> að reyna.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON> s<PERSON>ör", "Puzzle.helpText": "<PERSON><PERSON><PERSON> hvert dýr (gr<PERSON><PERSON>) á tengja mynd, velja fjölda fóta og búa til stæðu úr einkennum þess.", "Maze.moveForward": "<PERSON><PERSON><PERSON>", "Maze.turnLeft": "snúa til vinstri", "Maze.turnRight": "snúa til hægri", "Maze.doCode": "gera", "Maze.helpIfElse": "Ef-an<PERSON><PERSON> kubbar gera eitt eða annað.", "Maze.pathAhead": "ef s<PERSON><PERSON><PERSON> f<PERSON>n", "Maze.pathLeft": "ef sl<PERSON><PERSON> til vinstri", "Maze.pathRight": "ef slóð til hægri", "Maze.repeatUntil": "endurtaka þar til", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON><PERSON> leikveru fram um eitt bil.", "Maze.turnTooltip": "<PERSON>n<PERSON>r le<PERSON> til vinstri eða hægri um 90 gráður.", "Maze.ifTooltip": "<PERSON>f það er slóð í valda stefnu, þá á að gera eitthvað.", "Maze.ifelseTooltip": "<PERSON>f það er slóð í valda stefnu þá á að gera fyrri kubbinn. Annars á að gera seinni kubbinn.", "Maze.whileTooltip": "Endurtaka innifaldar aðgerðir þar til endapunkti er náð.", "Maze.capacity0": "Þú átt %0 kubba eftir.", "Maze.capacity1": "Þú átt %1 kubb eftir.", "Maze.capacity2": "Þú átt %2 kubba eftir.", "Maze.runTooltip": "<PERSON><PERSON><PERSON> le<PERSON> gera það sem kubbarnir segja.", "Maze.resetTooltip": "<PERSON>ja le<PERSON> aftur á upphafsreit.", "Maze.helpStack": "Rað<PERSON><PERSON><PERSON> tveim<PERSON> 'fæ<PERSON>' kubbum saman til að hjálpa mér að ná takmarkinu.", "Maze.helpOneTopBlock": "Í þessum áfanga þarftu að raða öllum kubbunum saman á hvíta svæðinu.", "Maze.helpRun": "<PERSON><PERSON><PERSON><PERSON> forrit<PERSON> til að sjá hvað gerist.", "Maze.helpReset": "For<PERSON><PERSON><PERSON> þitt rataði ekki. Smelltu á 'Byrja aftur' og reyndu betur.", "Maze.helpRepeat": "Náðu út á enda slóðarinnar með því að nota bara tvo kubba. Notaðu 'endurtaka' til að keyra kubb oftar en einu sinni.", "Maze.helpCapacity": "<PERSON><PERSON> hefur notað alla kubbana í þessum áfanga. Til að losa kubb verður þú að eyða honum úr forritinu.", "Maze.helpRepeatMany": "<PERSON><PERSON> getur sett fleiri en einn kubb inn í 'endurtaka' kubb.", "Maze.helpSkins": "<PERSON><PERSON><PERSON> þér leikveru úr þessum lista.", "Maze.helpIf": "'<PERSON><PERSON>' k<PERSON><PERSON> gerir e<PERSON>ð aðeins ef skilyrðið er satt. T.d. beygja til vinstri ef það er slóð til vinstri.", "Maze.helpMenu": "Smelltu á %1 í 'ef' kubbnum til að breyta skilyrði hans.", "Maze.helpWallFollow": "Getur þú leyst þetta flókna völundarhús? Reyndu að fylgja veggnum á vinstri hönd. Aðeins fyrir reyndari forritara!", "Bird.noWorm": "he<PERSON>r ekki orm", "Bird.heading": "stefna", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> þegar fuglinn hefur ekki náð orminum.", "Bird.headingTooltip": "<PERSON>a í þá átt sem uppgefið horn sýnir: 0 er til hægri, 90 er beint upp, o.s.frv.", "Bird.positionTooltip": "x og y tákna stað fuglsins. Þegar x = 0 er fuglinn við vins<PERSON> brún<PERSON>, þegar x = 100 er hann við hægri brúnina. Þegar y = 0 er fuglinn neðst niðri, þegar y = 100 er hann efst uppi.", "Bird.helpHeading": "<PERSON><PERSON><PERSON>u gráðu stefnunnar til að láta fuglinn ná orminum og lenda í hreiðrinu.", "Bird.helpHasWorm": "Notaðu þennan kubb til að fara í eina átt ef þú ert með orminn en í aðra átt ef þú ert ekki með hann.", "Bird.helpX": "'x' er staður þinn nú lá<PERSON>. Notaðu þennan kubb til að fara í eina átt ef 'x' er minna en talan, annar<PERSON> í aðra átt.", "Bird.helpElse": "Smelltu á táknið til að breyta 'ef'-kubbnum.", "Bird.helpElseIf": "<PERSON> þessu stigi þarf bæði 'annars ef' og 'annars'-kub<PERSON>.", "Bird.helpAnd": "Til að 'og'-k<PERSON><PERSON>inn sé sannur þurfa bæði inntökin að vera sönn.", "Bird.helpMutator": "<PERSON>ag<PERSON><PERSON> 'annar<PERSON>'-kubb inn í 'ef' kubbinn.", "Turtle.moveTooltip": "<PERSON><PERSON><PERSON><PERSON> skjaldbökuna fram eða aftur um tiltekna vegalengd.", "Turtle.moveForward": "færa fram um", "Turtle.moveBackward": "færa aftur um", "Turtle.turnTooltip": "Snýr skjaldbökunni til vinstri eða hægri um tilt<PERSON>inn gráðufjölda.", "Turtle.turnRight": "snúa til hægri um", "Turtle.turnLeft": "snúa til vinstri um", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON><PERSON> breidd pennan<PERSON>.", "Turtle.setWidth": "stilla breidd <PERSON>", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON><PERSON> lit pennans.", "Turtle.setColour": "stilla lit á", "Turtle.penTooltip": "Lyftir pennanum til að hætta að teikna eða setur hann niður til að byrja.", "Turtle.penUp": "penni upp", "Turtle.penDown": "penni niður", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON><PERSON> (hringinn og píluna) sýnilega eða ósýnilega.", "Turtle.hideTurtle": "fela skjaldböku", "Turtle.showTurtle": "<PERSON><PERSON><PERSON> s<PERSON>", "Turtle.printTooltip": "<PERSON><PERSON><PERSON> texta þar sem skjaldbakan er og í stefnu hennar.", "Turtle.print": "prenta", "Turtle.fontTooltip": "<PERSON>ir leturgerðina sem prentað er með.", "Turtle.font": "let<PERSON>ð", "Turtle.fontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON>ð", "Turtle.fontNormal": "ven<PERSON><PERSON><PERSON>", "Turtle.fontBold": "feitletrað", "Turtle.fontItalic": "skáletrað", "Turtle.submitDisabled": "<PERSON><PERSON><PERSON><PERSON> forritið þar til það stöðvast. <PERSON><PERSON><PERSON><PERSON> mátt þú setja teikninguna þína í safnið.", "Turtle.galleryTooltip": "<PERSON>na safn te<PERSON>.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON>a safn", "Turtle.submitTooltip": "Senda teikninguna þína í safnið.", "Turtle.submitMsg": "<PERSON><PERSON> safn", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON> þín virkar, en þú getur gert betur.", "Turtle.helpUseLoop3": "<PERSON><PERSON><PERSON><PERSON><PERSON> formið með aðeins þremur kubbum.", "Turtle.helpUseLoop4": "Teiknaðu formið með aðeins fjórum kubbum.", "Turtle.helpText1": "<PERSON><PERSON><PERSON><PERSON> til forrit sem teiknar ferning.", "Turtle.helpText2": "<PERSON><PERSON><PERSON>u forritinu svo það teikni samsíðung í stað fernings.", "Turtle.helpText3a": "<PERSON><PERSON>r er nýr kubbur sem þú getur notað til að breyta litnum:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON><PERSON><PERSON> gula stjörnu.", "Turtle.helpText4a": "<PERSON><PERSON>r er nýr kubbur sem þú getur notað til að lyfta pennanum þegar þú færir hann:", "Turtle.helpText4b": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gula stjörn<PERSON>, og síðan línu fyrir ofan.", "Turtle.helpText5": "Í stað einnar stjörnu, getur þú teiknað fjórar stjörnur sem saman mynda ferning?", "Turtle.helpText6": "<PERSON><PERSON><PERSON><PERSON><PERSON> þrj<PERSON>r gular stjörnur og eina hvíta línu.", "Turtle.helpText7": "Teiknaðu stjörnurnar og teiknaðu svo fjórar hvítar línur.", "Turtle.helpText8": "360 teiknaðar hvítar línur munu líkjast fullu tungli.", "Turtle.helpText9": "Getur þú bætt við svörtum hring svo að tunglið breytist í há<PERSON>?", "Turtle.helpText10": "<PERSON><PERSON><PERSON><PERSON>u hvað sem þú vilt! Þú hefur fjöldan allan af kubbum til að prófa. Skemmtu þér!", "Turtle.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> \"Skoða safn\" hnappinn til að sjá hvað aðrir hafa teiknað. Ef þú teiknar eitthvað áhugavert getur þú notað \"Senda í safn\" hnappinn til að birta það.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON> flokk til að sjá kubbana.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "frá x", "Movie.y1": "frá y", "Movie.x2": "til x", "Movie.y2": "til y", "Movie.radius": "<PERSON><PERSON><PERSON><PERSON>", "Movie.width": "brei<PERSON>", "Movie.height": "hæð", "Movie.circleTooltip": "<PERSON><PERSON><PERSON> hring á tilteknum stað með tilteknum radíus.", "Movie.circleDraw": "hringur", "Movie.rectTooltip": "Teik<PERSON> rétthyrning á tilteknum stað og með tiltekna breidd og hæð.", "Movie.rectDraw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.lineTooltip": "<PERSON><PERSON><PERSON> l<PERSON>u frá einum punkti til annars með tilt<PERSON>na breidd.", "Movie.lineDraw": "lína", "Movie.timeTooltip": "<PERSON><PERSON> gil<PERSON> tí<PERSON> (0-100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON><PERSON> lit pennans.", "Movie.setColour": "stilla lit á", "Movie.submitDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> þín hreyfist ekki. Notaðu kubba til að búa til eitthvað áhugavert. <PERSON><PERSON><PERSON><PERSON> mátt þú setja hreyfimyndina þína í safnið.", "Movie.galleryTooltip": "<PERSON><PERSON> safn h<PERSON>.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON>a safn", "Movie.submitTooltip": "Senda hreyfimyndina í safnið.", "Movie.submitMsg": "<PERSON><PERSON> safn", "Movie.helpLayer": "<PERSON><PERSON><PERSON><PERSON><PERSON> bakgrun<PERSON>hr<PERSON><PERSON> efst í forritið þitt. Þá mun hann birtast á bakvið einsta<PERSON>inn.", "Movie.helpText1": "Notaðu einföld form til að teikna þessa veru.", "Movie.helpText2a": "<PERSON><PERSON><PERSON> er hreyfimynd. Þ<PERSON> vilt láta handlegg á manneskju færast yfir skjáinn. Ýttu á spilunarhnappinn til að forskoða.", "Movie.helpText2b": "<PERSON><PERSON><PERSON> myndin er spiluð, sýnir kubburinn 'tími' talningu frá 0 til 100. Þar sem þú vilt að 'y'-staða handleggsins byrji við 0 l<PERSON><PERSON>tt og fari til 100, ætti þetta að vera auðvelt.", "Movie.helpText3": "<PERSON><PERSON><PERSON><PERSON> 'tí<PERSON>' telur frá 0 til 100. En í þetta sinn vilt þú að 'y'-staða hins handleggsins byrji við 100 og endi á 0. Getur þú fundið út einfalda reikningsformúlu sem snýr stefnunni við?", "Movie.helpText4": "Notaðu það sem þú lærðir í síðasta áfanga til að búa til krosslagða fætur.", "Movie.helpText5": "Reikniformúlan fyrir handlegginn er flókin. Hér er svarið:", "Movie.helpText6": "<PERSON><PERSON><PERSON><PERSON> mannes<PERSON>ni tvær hendur.", "Movie.helpText7": "Notaðu'ef'-kubb til að teikna lítinn haus í fyrri helmingi hreyfimyndarinnar. Síðan skaltu teikna stóran haus í seinni helmingnum.", "Movie.helpText8": "Láttu fæturna fara í öfuga átt þegar hreyfimyndin er hálfnuð.", "Movie.helpText9": "Teik<PERSON>ðu útv<PERSON> hring á bakvið einsta<PERSON>inn.", "Movie.helpText10": "<PERSON><PERSON>ð<PERSON> til eigin h<PERSON>. Þ<PERSON> hefur fjöldan allan af kubbum til að prófa. Skemmtu þér!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> \"Skoða safn\" hnappinn til að skoða hreyfimyndir sem aðrir hafa búið til. Ef þú býrð til <PERSON>hugaverða hrey<PERSON>nd, getur þú notað \"Senda í safn\" hnappinn til að birta hana.", "Music.playNoteTooltip": "<PERSON><PERSON><PERSON> eina nótu í tiltekinn tíma og tónhæð.", "Music.playNote": "spila %1 nótu %2", "Music.restTooltip": "<PERSON><PERSON><PERSON><PERSON> í tilt<PERSON>inn tíma.", "Music.restWholeTooltip": "<PERSON><PERSON><PERSON><PERSON> eftir einni heilli n<PERSON>.", "Music.rest": "hvíld %1", "Music.setInstrument": "setja hljóðfærið sem %1", "Music.start": "<PERSON><PERSON>ar smellt er á %1", "Music.pitchTooltip": "<PERSON> nóta (C4 er 7).", "Music.firstPart": "fyrsti partur", "Music.piano": "<PERSON><PERSON><PERSON><PERSON>", "Music.trumpet": "trompet", "Music.banjo": "<PERSON><PERSON><PERSON>", "Music.violin": "<PERSON><PERSON><PERSON>", "Music.guitar": "g<PERSON><PERSON>", "Music.flute": "flauta", "Music.drum": "tromma", "Music.choir": "kór", "Music.submitDisabled": "<PERSON><PERSON><PERSON><PERSON> forritið þar til það stöðvast. <PERSON><PERSON><PERSON><PERSON> mátt þú setja tónlistina þína í safnið.", "Music.galleryTooltip": "Opna tónlistarsafnið.", "Music.galleryMsg": "<PERSON><PERSON><PERSON>a safn", "Music.submitTooltip": "Senda tónlistina þína í safnið.", "Music.submitMsg": "Senda í safn", "Music.helpUseFunctions": "<PERSON><PERSON><PERSON> þín virkar, en þú getur gert betur. Notaðu föll til að minnka magn endurtekins kóða.", "Music.helpUseInstruments": "Tónlistin mun hljóma betur ef þú hefur mismunandi hljóðfæri í hverjum byrjunark<PERSON>.", "Music.helpText1": "<PERSON><PERSON> saman fyrstu fjórar nóturnar í 'Meistari Jakob'.", "Music.helpText5": "Ljúktu við alla laglínuna í 'Meistari Jakob'.", "Music.helpText6a": "<PERSON><PERSON><PERSON> n<PERSON> kubbur gerir þ<PERSON>r kleift að skipta yfir í annað hljóð<PERSON>æri.", "Music.helpText6b": "<PERSON><PERSON><PERSON><PERSON><PERSON> lagl<PERSON>una þína með fiðlu.", "Music.helpText7a": "<PERSON><PERSON><PERSON> nýji kubbur bætir við þ<PERSON><PERSON><PERSON> töf.", "Music.helpText8": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ætti að spila „Meistari Jakob“ tvisvar.", "Music.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> \"Skoða safn\" hnappinn til að sjá hvað aðrir hafa samið. Ef þú semur eitthvað áhugavert getur þú notað \"Senda í safn\" hnappinn til að birta það.", "Pond.scanTooltip": "<PERSON><PERSON> að óvinum. Tilgre<PERSON><PERSON> (0-360). Gefur fjarlægðina að nálægasta óvini í þá stefnu. Gefur <PERSON>le<PERSON> (Infinity) ef enginn ó<PERSON>ur finnst.", "Pond.cannonTooltip": "Skjóta með fallbyssunni. Tilgreindu s<PERSON>nu (0-360) og vegalengd (0-70).", "Pond.swimTooltip": "<PERSON><PERSON><PERSON>. Tilg<PERSON><PERSON><PERSON> (0-360).", "Pond.stopTooltip": "Hætta að synda. Leikveran mun hægja á sér og stöðvast.", "Pond.healthTooltip": "Gefur upp heilsu leikveru núna (0 er dauð, 100 er full heilsa).", "Pond.speedTooltip": "Gefur upp hraða leik<PERSON>u núna (0 er kyrr, 100 er hámarkshraði).", "Pond.locXTooltip": "Gefur X hnit leikverunnar (0 er vinstri brún, 100 er hægri brún).", "Pond.locYTooltip": "Gefur Y hnit leikverunnar (0 er neðri brún, 100 er efri brún).", "Pond.logTooltip": "Birt<PERSON> tölu í stjórnborði vafrans þín<PERSON>.", "Pond.docsTooltip": "<PERSON><PERSON><PERSON> hand<PERSON><PERSON><PERSON>r fyrir tungum<PERSON>.", "Pond.documentation": "Leiðbeiningar", "Pond.playerName": "Leik<PERSON><PERSON>", "Pond.targetName": "<PERSON>", "Pond.pendulumName": "Dingull", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "<PERSON><PERSON><PERSON> þín virkar, en þú getur gert betur. Notaðu 'scan' til að segja fallbyssunni hve langt á að skjóta.", "Pond.helpText1": "Notaðu 'cannon' skip<PERSON><PERSON> til að hitta markið. <PERSON><PERSON><PERSON> brey<PERSON> er hornið, sú seinni er vegalengdin. Finndu réttu samset<PERSON>.", "Pond.helpText2": "Þetta mark verður að hitta mörgum sinnum. Notaðu 'while (true)' lykkju til að gera eitthvað endalaust.", "Pond.helpText3a": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> færist fram og aftur svo erfitt er að hitta. Að<PERSON><PERSON><PERSON> 'scan' gefur hve langt frá andst<PERSON><PERSON><PERSON><PERSON>nn er í tiltekna stefnu.", "Pond.helpText3b": "<PERSON><PERSON><PERSON> vegalengd er nákvæmlega það sem skipunin 'cannon' þarf til að skjóta nákvæmt.", "Pond.helpText4": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er of langt frá til að fall<PERSON>an nýtist (<PERSON><PERSON><PERSON> h<PERSON> er 70 metrar). Nota<PERSON>u þess í stað 'swim' skipunina til að synda í átt að andstæðingnum og rekast á hann.", "Pond.helpText5": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er líka og langt frá til að nota fallbyssuna. En þú ert of veikburða til að þola árekstur. Syntu í átt að andstæ<PERSON>ingnum á meðan staður þinn lárétt er minna en 50. Notaðu svo 'stöðva' og fallbyssuna.", "Pond.helpText6": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mun færa sig fjær þegar skot hæfir hann. <PERSON>yntu í átt til hans ef hann er utan skotfæris (70 metrar).", "Gallery": "Safn"}