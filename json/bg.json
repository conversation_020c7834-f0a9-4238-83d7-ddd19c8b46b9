{"@metadata": {"authors": ["Alpinistbg", "DCLXVI", "<PERSON><PERSON><PERSON>", "InsomniHat", "Stan<PERSON><PERSON>", "<PERSON>", "Vodnokon4e"]}, "Games.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.puzzle": "Пъзел", "Games.maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Птица", "Games.turtle": "Костенурка", "Games.movie": "<PERSON>ил<PERSON>", "Games.music": "Музика", "Games.pondTutor": "Езерце с инструктор", "Games.pond": "Езерце", "Games.linesOfCode1": "Ти реши това ниво с 1 ред на JavaScript:", "Games.linesOfCode2": "Ти реши това ниво с %1 редoве на JavaScript:", "Games.nextLevel": "Готов ли си за ниво %1?", "Games.finalLevel": "Готов ли си за следващото предизвикателство?", "Games.submitTitle": "Заглавие:", "Games.linkTooltip": "Запиши и покажи връзка към блоковете.", "Games.runTooltip": "Стартирай програмата, която написа.", "Games.runProgram": "Стартирай програмата", "Games.resetTooltip": "Спри програмата и върни в началото.", "Games.resetProgram": "Изчисти", "Games.help": "Помощ", "Games.catLogic": "Логика", "Games.catLoops": "Цикли", "Games.catMath": "Математика", "Games.catText": "Текст", "Games.catLists": "Списъци", "Games.catColour": "Цвят", "Games.catVariables": "Променливи", "Games.catProcedures": "Функции", "Games.httpRequestError": "Възникна проблем при заявката.", "Games.linkAlert": "Споделете блокове в тази връзка:\n\n%1", "Games.hashError": "За съжаление, „%1“ не отговаря на записани блокове.", "Games.xmlError": "Вашият записан файл не може да се зареди. Възможно ли е да е създаден с различна версия на Blockly?", "Games.submitted": "Благодарим Ви за тази програма! В случай че персоналът ни, състоящ се от дресирани маймунки, я хареса, ще я публикуват в галерията до няколко дни.", "Games.listVariable": "списък", "Games.textVariable": "текст", "Games.breakLink": "След като започнете да редактирате JavaScript, не можете да се върнете към редактиране на блокове. Искате ли да продължите?", "Games.blocks": "Блокове", "Games.congratulations": "Поздравления!", "Games.helpAbort": "Това ниво е изключително сложно. Искате ли да го пропуснете и да преминете към следващата игра? Винаги може да се върнете по-късно.", "Index.clear": "Изтриване на всички решения?", "Index.subTitle": "Игри за бъдещите програмисти.", "Index.moreInfo": "Информация за преподаватели...", "Index.startOver": "Искате да започнете отначало?", "Index.clearData": "Изчистване на данните", "Puzzle.animal1": "Патица", "Puzzle.animal1Trait1": "Пера", "Puzzle.animal1Trait2": "К<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Котка", "Puzzle.animal2Trait1": "Мустаци", "Puzzle.animal2Trait2": "Козина", "Puzzle.animal2HelpUrl": "https://bg.wikipedia.org/wiki/%D0%9A%D0%BE%D1%82%D0%BA%D0%B0", "Puzzle.animal3": "Пчела", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://bg.wikipedia.org/wiki/%D0%9F%D1%87%D0%B5%D0%BB%D0%B0", "Puzzle.animal4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "Черупка", "Puzzle.animal4Trait2": "Слуз", "Puzzle.animal4HelpUrl": "https://bg.wikipedia.org/wiki/%D0%9E%D1%85%D0%BB%D1%8E%D0%B2", "Puzzle.picture": "снимка:", "Puzzle.legs": "крака:", "Puzzle.legsChoose": "избиране...", "Puzzle.traits": "характеристики:", "Puzzle.error0": "Отлично!\nВсички %1 блокове са правилни.", "Puzzle.error1": "Почти! Само един блок е неправилен.", "Puzzle.error2": "%1 блокове са неправилни.", "Puzzle.tryAgain": "Отбелязаният блок е неправилен.\nПродължавай да опитваш.", "Puzzle.checkAnswers": "Провери отговорите", "Puzzle.helpText": "За всяко животно (в зеления блок), прикачи картинка, посочи броя на краката и събери отличителните му черти.", "Maze.moveForward": "напред", "Maze.turnLeft": "наляво", "Maze.turnRight": "надясно", "Maze.doCode": "изпълни", "Maze.helpIfElse": "Ако-иначе блоковете правят едно от двете действия.", "Maze.pathAhead": "ако има път напред", "Maze.pathLeft": "ако има път наляво", "Maze.pathRight": "ако има път надясно", "Maze.repeatUntil": "повтаряй докато", "Maze.moveForwardTooltip": "Премества играча една позиция напред", "Maze.turnTooltip": "Завърта играча наляво или надясно на 90 градуса", "Maze.ifTooltip": "Ако има път в тази посока, тогава извърши тези действия.", "Maze.ifelseTooltip": "Ако има път в тази посока, тогава изпълни първия блок. Иначе, изпълни втория блок.", "Maze.whileTooltip": "Повтори вложените действия докато се достигне крайната точка.", "Maze.capacity0": "Остават %0 блока.", "Maze.capacity1": "Остава %1 блок.", "Maze.capacity2": "Остават %2 блока.", "Maze.runTooltip": "Играчът ще изпълни описаното в блока.", "Maze.resetTooltip": "Върни играча обратно в началото на лабиринта.", "Maze.helpStack": "Сглоби няколко блока „напред“, за да ми помогнеш да стигна до целта.", "Maze.helpOneTopBlock": "На това ниво трябва да сглобиш всички блокове в бялата работна област.", "Maze.helpRun": "Изпълни програмата си, за да видиш какво ще се случи.", "Maze.helpReset": "Програмата ти не намери път в лабиринта. Натисни „Изчисти“ и опитай отново.", "Maze.helpRepeat": "Стигни до края на пътя, като използваш само два блока. Използвай „повтори“, за да изпълниш даден блок повече от веднъж.", "Maze.helpCapacity": "Ти използва всички налични блокове за това ниво. За да създадеш нов блок, първо трябва да премахнеш някой друг.", "Maze.helpRepeatMany": "Можете да сложите повече от един блок в блок \"повтори\".", "Maze.helpSkins": "Избери предпочитан играч от това меню.", "Maze.helpIf": "Блок \"ако\" ще изпълни действието само тогава, когато условието е изпълнено. Опитайте да завиеш наляво, ако има път натам.", "Maze.helpMenu": "Щракнете върху %1 в блок „ако“, за да промените неговото условие.", "Maze.helpWallFollow": "Можеш ли да намериш път в този сложен лабиринт? Опитай да следваш стената отляво. Само за напреднали прогрмисти!", "Bird.noWorm": "няма червей", "Bird.heading": "посока", "Bird.noWormTooltip": "Условието, когато птицата не е намерила червея.", "Bird.headingTooltip": "Премести в посоката на зададения ъгъл: 0 е надясно, 90 е право нагоре и т.н.", "Bird.positionTooltip": "x и y бележат позицията на птицата. Когато x=0 птицата е близо до левия край, когато x=100 тя е близо до десния край. При y=0 птицата е в дъното, при y=100 тя е на върха.", "Bird.helpHeading": "Промени ъгъла на направлението за да накараш птицата да улови червея и да се приземи в гнездото си.", "Bird.helpHasWorm": "Използвай този блок за движение в една посока, когато е уловен червей и в друга когато не е уловен.", "Bird.helpX": "'x' - това е текущата хоризонтална позиция. Използвай този блок за движение в една посока ако 'x' по-малко от зададеното число, или в друга посока ако не е по-малко.", "Bird.helpElse": "Щракнете върху иконата за да промените блок 'ако'.", "Bird.helpElseIf": "Това ниво изисква блок 'иначе ако' и блок 'иначе'.", "Bird.helpAnd": "Блокът 'и' е истина ако и двата параметъра са истини.", "Bird.helpMutator": "Завлачи блок 'иначе' в блок 'ако'.", "Turtle.moveTooltip": "Преместване на костенурката напред или назад на определено разстояние.", "Turtle.moveForward": "напред с", "Turtle.moveBackward": "назад с", "Turtle.turnTooltip": "Завърта костенурката наляво или надясно със съотвения ъгъл в градуси.", "Turtle.turnRight": "надясно с", "Turtle.turnLeft": "няляво с", "Turtle.widthTooltip": "Промени дебелината на писалката.", "Turtle.setWidth": "Задай ширина", "Turtle.colourTooltip": "Променя цвета на писалката.", "Turtle.setColour": "смени цвета на", "Turtle.penTooltip": "Вдига или сваля писалката, за да спре или започне рисуването.", "Turtle.penUp": "вдигни писалката", "Turtle.penDown": "свали писалката", "Turtle.turtleVisibilityTooltip": "Направи костенурката (кръг и стрелка), видима или невидима.", "Turtle.hideTurtle": "Скрий костенурката", "Turtle.showTurtle": "Покажи костенурката", "Turtle.printHelpUrl": "https://bg.wikipedia.org/wiki/%D0%9F%D0%B5%D1%87%D0%B0%D1%82%D0%B0%D1%80%D1%81%D1%82%D0%B2%D0%BE", "Turtle.printTooltip": "Изчертава текст в посоката и позицията на костенурката.", "Turtle.print": "печат", "Turtle.fontHelpUrl": "https://bg.wikipedia.org/wiki/%D0%A8%D1%80%D0%B8%D1%84%D1%82", "Turtle.fontTooltip": "Указване на шрифта, използван от блока \"печат\".", "Turtle.font": "шрифт", "Turtle.fontSize": "Размер на шрифта", "Turtle.fontNormal": "нормален", "Turtle.fontBold": "удебелен", "Turtle.fontItalic": "кур<PERSON><PERSON>в", "Turtle.submitDisabled": "Изпълнете Вашата програма докато тя приключи. Тогава можете да поставите Вашата рисунка в галерията.", "Turtle.galleryTooltip": "Отворете картинната галерия.", "Turtle.galleryMsg": "Виж Галерия", "Turtle.submitTooltip": "Публикувайте своята рисунка в галерията.", "Turtle.submitMsg": "Постави в Галерия", "Turtle.helpUseLoop": "Решението работи, но може да се направи и по-добре.", "Turtle.helpUseLoop3": "Изчертай формата само с три блока.", "Turtle.helpUseLoop4": "Изчертай звездата само с четири блока.", "Turtle.helpText1": "Направи програма, която чертае квадрат.", "Turtle.helpText2": "Промени програмата да чертае петоъгълник вместо квадрат.", "Turtle.helpText3a": "Това е нов блок, който позволява да се променя цвета:", "Turtle.helpText3b": "Изчертай жълта звезда.", "Turtle.helpText4a": "Има нов блок, който позволява да се повдигне писалката от хартията при придвижване:", "Turtle.helpText4b": "Изчертай малка жълта звезда, след това изчертай линия над нея.", "Turtle.helpText5": "Вместо една звезда, може ли да се нарисуват четири звезди разположени в квадрат?", "Turtle.helpText6": "Изчертай три жълти звезди и една бяла линия.", "Turtle.helpText7": "Изчертай звездите, после изчертай четири бели линии.", "Turtle.helpText8": "Изчертаването на 360 бели линии ще изглежда като пълнолуние.", "Turtle.helpText9": "Може ли да добавите черен кръг, така че луната да се превърне в полумесец?", "Turtle.helpText10": "Начертайте каквото си пожелаете. Имате голям брой нови блокове за изследване. Приятно чертане!", "Turtle.helpText10Reddit": "Използвайте бутона \"Виж Галерия\" за да разгледате какво са начертали другите. Ако сте начертали нещо интересно, използвайте \"Постави в Галерия\" за да споделите рисунката.", "Turtle.helpToolbox": "Изберете категория за да видите блоковете.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "начално x", "Movie.y1": "начално y", "Movie.x2": "крайно x", "Movie.y2": "крайно y", "Movie.radius": "радиус", "Movie.width": "ши<PERSON><PERSON><PERSON>", "Movie.height": "височина", "Movie.circleTooltip": "Чертае кръг на указаната позиция и с указания радиус.", "Movie.circleDraw": "кръг", "Movie.rectTooltip": "Чертае правоъгълник на указаното място с указаната дължина и ширина.", "Movie.rectDraw": "правоъгълник", "Movie.lineTooltip": "Чертае линия от една точка до друга с указаната дебелина.", "Movie.lineDraw": "линия", "Movie.timeTooltip": "Връща текущото време във филма (0-100).", "Movie.colourTooltip": "Променя цвета на писалката.", "Movie.setColour": "смени цвета на", "Movie.submitDisabled": "Вашето фимче не се помръдва. Използвайте блокове за да направите нещо интересно. Тогава можете да поставите филмчето в галерията.", "Movie.galleryTooltip": "Отворете филмовата галерия.", "Movie.galleryMsg": "Виж Галерия", "Movie.submitTooltip": "Публикувайте ваш филм в галерията.", "Movie.submitMsg": "Постави в Галерия", "Movie.helpLayer": "Преместете фоновия кръг най-отгоре в програмата си. Тогава той ще се вижда зад човека.", "Movie.helpText1": "Използвай прости фигури за да се изчертаеш този човек.", "Movie.helpText2a": "Това ниво е филмче. Вие искате ръката на човека да се придвижи през екрана. Натиснете бутон play, за да видите преглед.", "Movie.helpText2b": "По време на филмчето, стойността на блока 'time' се променя от 0 до 100. Понеже искате 'y' позицията на ръката да започне от 0 и да стигне до 100, това би трябвало да е лесно.", "Movie.helpText3": "Блокът 'time' отброява от 0 до 100. Този път обаче, Вие искате 'y' позицията на другата ръка да започне от 100 и да намалее до 0. Можете ли да откриете проста математическа формула, която да обърне посоката?", "Movie.helpText4": "Използвайте това, което сте научили на предишното ниво, за да направите крака, които се кръстосват.", "Movie.helpText5": "Математическата формула за ръката е сложна. Ето и отговора:", "Movie.helpText6": "Дайте на човека двойка ръце.", "Movie.helpText7": "Използвайте 'if' блока, за да нарисувате малка глава за първата половина на филма. След това нарисувайте голяма глава за втората половина на филма.", "Movie.helpText8": "Направете така, че краката, да обърнат посоката по средата на филма.", "Movie.helpText9": "Нарисувайте увеличаваща се окръжност зад човека.", "Movie.helpText10": "Направете филмче на каквото си пожелаете. Имате голям брой нови блокове за изследване. Приятно чертане!", "Movie.helpText10Reddit": "Използвайте бутона \"Виж Галерия\" за да разгледате какви филмчета са направили другите. Ако сте начертали нещо интересно, използвайте \"Постави в Галерия\" за да споделите филмчето.", "Music.playNoteTooltip": "Изсвирва една музикална нота със зададена продължителност и тон.", "Music.playNote": "изсвири %1 нота %2", "Music.restTooltip": "Изчаква зададеното време.", "Music.restWholeTooltip": "Изчаква за една цяла нота.", "Music.rest": "затишие %1", "Music.setInstrumentTooltip": "Превключва към зададения инструмент, когато изсвирва последващите музикални ноти.", "Music.setInstrument": "задайте инструмента да бъде %1", "Music.startTooltip": "Изпълнява включените блокове, когато бъде натиснат бутон 'Run Program'.", "Music.start": "при натискане на %1", "Music.pitchTooltip": "Една нота (C4 е 7).", "Music.firstPart": "първа част", "Music.piano": "пиано", "Music.trumpet": "тромпет", "Music.banjo": "банджо", "Music.violin": "цигулка", "Music.guitar": "китара", "Music.flute": "флейта", "Music.drum": "бар<PERSON><PERSON><PERSON>н", "Music.choir": "хор", "Music.submitDisabled": "Пуснете своята програма, докато тя спре. След това можете да публикувате своята музика в галерията.", "Music.galleryTooltip": "Отворете музикалната галерия.", "Music.galleryMsg": "Разгледайте галерията", "Music.submitTooltip": "Публикувайте своята музика в галерията.", "Music.submitMsg": "Публикувайте в галерията", "Music.helpUseFunctions": "Вашето решение е работещо, но бихте могли да справите и по-добре. Използвайте функции, за да намалите количеството повтарящ се код.", "Music.helpUseInstruments": "Музиката ще звучи по-добре, ако използвате различен инструмент във всеки стартов блок.", "Music.helpText1": "Композирайте първите ноти от '<PERSON><PERSON>'.", "Music.helpText2a": "Фунцията или на английски 'function', ви позволява да групирате заедно блокове, след което да ги изпълнявате многократно.", "Music.helpText2b": "Създайте функция, която да изсвирва първите четири ноти от '<PERSON><PERSON>'. Изпулнете тази функция два пъти. Не добавяйте никакви нови нотни блокове.", "Music.helpText3": "Създайте втора функция за следващата част от '<PERSON><PERSON>'. Последната нота е по-продължителна.", "Music.helpText4": "Създайте трета функция за следващата част от '<PERSON><PERSON>'. Първите четири ноти са по-кратки.", "Music.helpText5": "Завършете пълната мелодия на '<PERSON><PERSON> Jacques'.", "Music.helpText6a": "Този нов блок ви позволява да използвате друг инструмент.", "Music.helpText6b": "Изсвирете своята мелодия с цигулка.", "Music.helpText7a": "Този нов блок добавя период на затишие.", "Music.helpText7b": "Създайте втори стартов блок, който има два блока за затишие и след това изсвирва  '<PERSON><PERSON> Jacques'.", "Music.helpText8": "Всеки стартов блок трябва да изсвирва '<PERSON><PERSON>' два пъти.", "Music.helpText9": "Създайте четири стартови блока, всеки от които просвирва 'Fr<PERSON>' два пъти. Добавете точният брой блокове за затишие.", "Music.helpText10": "Композирайте каквото си искате. Разполагате с огромен брой нови блокове за изучаване. Приятно прекарване!", "Music.helpText10Reddit": "Използвайте бутон \"Разгледайте галерията\" за да видите какво са композирали другите. Ако композирате нещо интересно, използвайте бутон \"Публикувай в галерията\", за да го публикувате.", "Pond.scanTooltip": "Търсене на врагове. Укажете посока (0-360). Връща дистанцията до най-близкия враг в тази посока. Връща безкрайност, ако не е намерен враг.", "Pond.cannonTooltip": "Стреля с оръдието. Укажи посока (0-360) и разстояние (0-70).", "Pond.swimTooltip": "Плуване напред. Укажи посока (0-360).", "Pond.stopTooltip": "Спри плуването. Играчът ще се забави и ще спре.", "Pond.healthTooltip": "Връща текущото здраве на участника (0 - мъртъв, 100 - здрав).", "Pond.speedTooltip": "Връща текущата скорост на играча (0 е спрял, 100 е пълна скорост).", "Pond.locXTooltip": "Връща координатата X на играча(0 - ляв край, 100 - десен край).", "Pond.locYTooltip": "Връща координатата Y на играча (0 е долния край, 100 е горния).", "Pond.logTooltip": "Извежда число в конзолата на браузъра ви.", "Pond.docsTooltip": "Показва документация за езика", "Pond.documentation": "Документация", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Цел", "Pond.pendulumName": "Ма<PERSON><PERSON><PERSON>о", "Pond.scaredName": "Изплашен", "Pond.helpUseScan": "Твоето решение работи, но може да се направи по-добре. Използвайте 'scan', за да укажете на оръдието колко далеч да стреля.", "Pond.helpText1": "Използвайте командата 'cannon', за да поразите целта. Първият параметър е ъгъл, вторият - разстояние. Открий правилната комбинация.", "Pond.helpText2": "Тази цел трябва да се удари много пъти. Използвайте цикъл 'while (true)', за да правите нещо безкрайно.", "Pond.helpText3a": "Този опонент се движи напред-назад, което го прави труден за уцелване. Изразът 'scan' връща точното разстояние до опонента в указаната посока.", "Pond.helpText3b": "Това разстояние е точно колкото е нужно на командата 'cannon' за точна стрелба.", "Pond.helpText4": "Този опонент е твърде далеч за да използвате оръдието (то има ограничение от 70 метра). Вместо това използвайте команда 'swim' за да заплувате към опонента и да се блъснете в него.", "Pond.helpText5": "Този опонент е също твърде далеч за да използвате оръдието. Но Вие сте твърде слаб за да преживеете сблъсък. Плувайте към опонента докато хоризонталната позиция стане по-малка от 50. Тогава 'stop' и използвайте оръдието.", "Pond.helpText6": "Този опонент ще се отдалечи, ако бъде ударен. Плувайте към него, ако той е извън обсег (70 метра).", "Gallery": "Галерия"}