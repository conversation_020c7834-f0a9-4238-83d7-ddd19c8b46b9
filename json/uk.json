{"@metadata": {"authors": ["<PERSON>", "Andriykopanytsia", "G<PERSON><PERSON>", "<PERSON>", "Lxlalexlxl", "<PERSON><PERSON><PERSON>", "Piramidion"]}, "Games.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.puzzle": "Головоломка", "Games.maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Пташка", "Games.turtle": "Черепаха", "Games.movie": "Фільм", "Games.music": "Музика", "Games.pondTutor": "Ставочок", "Games.pond": "Став", "Games.linesOfCode1": "Рівень пройдено за допомогою 1 рядка JavaScript:", "Games.linesOfCode2": "Рівень пройдено за допомогою %1 рядків JavaScript:", "Games.nextLevel": "Ви готові до рівня %1?", "Games.finalLevel": "Ви готові до наступного випробування?", "Games.submitTitle": "Заголовок:", "Games.linkTooltip": "Зберегти і пов'язати з блоками.", "Games.runTooltip": "Запустити написану програму.", "Games.runProgram": "Запустити програму", "Games.resetTooltip": "Зупинити програму і скинути в початковий стан.", "Games.resetProgram": "Очистити", "Games.help": "Довідка", "Games.catLogic": "Логіка", "Games.catLoops": "Цикли", "Games.catMath": "Математика", "Games.catText": "Текст", "Games.catLists": "Списки", "Games.catColour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catVariables": "Змінн<PERSON>", "Games.catProcedures": "Функції", "Games.httpRequestError": "Виникла проблема із запитом.", "Games.linkAlert": "Поділитися вашими блоками через посилання:\n\n%1", "Games.hashError": "На жаль, \"%1\" не відповідає жодній збереженій програмі.", "Games.xmlError": "Не вдалося завантажити ваш збережений файл. Можливо, він був створений з іншої версії Blockly?", "Games.submitted": "Дякуємо за цю програму! Якщо нашим дресованим мавпочкам вона сподобається, то вони опублікують її в галереї упродовж декількох днів.", "Games.listVariable": "список", "Games.textVariable": "текст", "Games.breakLink": "Розпочавши редагувати JavaScript, ви не зможете повернутися до редагування блоків. Добре?", "Games.blocks": "Блоки", "Games.congratulations": "Вітаємо!", "Games.helpAbort": "Цей рівень дуже складний. Бажаєте пропустити його і перейти до наступної гри? Ви завжди можете повернутися пізніше.", "Index.clear": "Видалити всі ваші розв'язки?", "Index.subTitle": "Ігри для майбутніх програмістів.", "Index.moreInfo": "Інформація для викладачів ...", "Index.startOver": "Бажаєте почати заново?", "Index.clearData": "Очистити дані", "Puzzle.animal1": "Качка", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>'я", "Puzzle.animal1Trait2": "Дзь<PERSON>б", "Puzzle.animal1HelpUrl": "https://uk.wikipedia.org/wiki/Качки", "Puzzle.animal2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait1": "Вуса", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://uk.wikipedia.org/wiki/Кіт_свійський", "Puzzle.animal3": "Бджола", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON>а<PERSON>о", "Puzzle.animal3HelpUrl": "https://uk.wikipedia.org/wiki/Бджоли", "Puzzle.animal4": "Равлик", "Puzzle.animal4Trait1": "Черепашка", "Puzzle.animal4Trait2": "Слиз", "Puzzle.animal4HelpUrl": "https://uk.wikipedia.org/wiki/Равлик", "Puzzle.picture": "зображення:", "Puzzle.legs": "лапи:", "Puzzle.legsChoose": "виберіть...", "Puzzle.traits": "ознаки:", "Puzzle.error0": "Відмінно!\n\nВсі блоки (%1) розміщені правильно!", "Puzzle.error1": "Майже! Один блок розміщений неправильно.", "Puzzle.error2": "Декілька блоків (%1) розміщено неправильно.", "Puzzle.tryAgain": "Виділений блок розміщений неправильно.\nСпробуйте ще.", "Puzzle.checkAnswers": "Перевірити відповіді", "Puzzle.helpText": "Для кожної тварини (зелені блоки) прикріпіть її зображення, виберіть кількість лап і зберіть ознаки.", "Maze.moveForward": "рухатися вперед", "Maze.turnLeft": "повернути ліворуч", "Maze.turnRight": "повернути праворуч", "Maze.doCode": "виконати", "Maze.helpIfElse": "Блоки „якщо-інакше“ дозволяють виконати одне або інше.", "Maze.pathAhead": "якщо шлях попереду", "Maze.pathLeft": "якщо шлях ліворуч", "Maze.pathRight": "якщо шлях праворуч", "Maze.repeatUntil": "повторювати, доки не", "Maze.moveForwardTooltip": "Переміщує гравця вперед на одне місце.", "Maze.turnTooltip": "Повертає гравця ліворуч або праворуч на 90 градусів.", "Maze.ifTooltip": "Якщо існує шлях у вказаному напрямку, то виконати певні дії.", "Maze.ifelseTooltip": "Якщо існує шлях у вказаному напрямку, то виконати перший блок дій. В іншому випадку виконати другий блок дій.", "Maze.whileTooltip": "Повторювати вкладені дії, доки не буде досягнуто кінцевої точки.", "Maze.capacity0": "У вас лишилося %0 блоків.", "Maze.capacity1": "У вас лишився %1 блок.", "Maze.capacity2": "У вас лишилося %2 блоки(ів).", "Maze.runTooltip": "Гравець робить усе, що кажуть блоки.", "Maze.resetTooltip": "Поставити гравця назад на початок лабіринту.", "Maze.helpStack": "Складіть разом кілька блоків руху вперед, щоб допомогти мені досягти мети.", "Maze.helpOneTopBlock": "На цьому рівні вам необхідно скласти разом всі блоки в білій робочій області.", "Maze.helpRun": "Запустіть програму, щоб подивитися, що відбувається.", "Maze.helpReset": "Ваша програма не розв'язала лабіринт. Натисніть кнопку 'Очистити' і спробуйте знову.", "Maze.helpRepeat": "Пам'ять комп'ютерів обмежена. Пройдіть до кінця цього шляху, використовуючи тільки два блоки. Для запуску блоку більше одного разу, використовуйте команду 'повторити'.", "Maze.helpCapacity": "Ви вже використовуєте всі блоки на цьому рівні. Щоб створити новий блок, спочатку необхідно видалити наявний блок.", "Maze.helpRepeatMany": "Ви можете встановити більше одного блоку всередині блоку 'повторити'.", "Maze.helpSkins": "Виберіть вашого улюбленого гравця в цьому меню.", "Maze.helpIf": "Блок „якщо“ буде робити щось, лише якщо ця умова є істинною.  Спробуйте повернути вліво, якщо існує шлях ліворуч.", "Maze.helpMenu": "Натисніть на %1 у блоку \"якщо\", щоб змінити його стан.", "Maze.helpWallFollow": "Ви можете вирішити цей складний лабіринт?  Спробуйте триматися лівої стіни.  Тільки для досвідчених програмістів!", "Bird.noWorm": "немає хробака", "Bird.heading": "напрямок", "Bird.noWormTooltip": "Ста<PERSON>, коли пташка не взяла хробака.", "Bird.headingTooltip": "Рухатися в напрямку заданого кута: 0 - вправо, 90 - вгору, і т.д.", "Bird.positionTooltip": "x та y визначають положення пташки. Якщо x = 0, то пташка перебуває біля лівого краю, якщо x = 100 - вона біля правого краю. Якщо y = 0, то пташка внизу, якщо y = 100 - вгорі.", "Bird.helpHeading": "Змінюйте напрямок руху пташки так, щоб вона взяла хробака і опустилась у своє гніздо.", "Bird.helpHasWorm": "Використайте цей блок, щоб вибрати один напрямок, якщо у пташки є хробак, або інший напрямок, якщо хробака немає.", "Bird.helpX": "'x' - це ваше поточне горизонтальне положення. Використовуйте цей блок, щоб рухатись в одному напрямі, якщо 'x' менше, ніж число або в іншому, у протилежному випадку.", "Bird.helpElse": "Клацніть по значку, щоб змінити блок 'якщо'.", "Bird.helpElseIf": "На цьому рівні потрібні блоки \"інакше якщо\" та \"інакше\".", "Bird.helpAnd": "Блок \"і\" має значення істина, якщо обидва параметри істинні.", "Bird.helpMutator": "Перетягніть блок \"інакше\" у блок \"якщо\".", "Turtle.moveTooltip": "Переміщує черепаху вперед або назад на задану відстань.", "Turtle.moveForward": "рухатися вперед на", "Turtle.moveBackward": "рухатися назад на", "Turtle.turnTooltip": "Повертає черепаху вліво або вправо на вказану кількість градусів.", "Turtle.turnRight": "повернути праворуч на", "Turtle.turnLeft": "повернути ліворуч на", "Turtle.widthTooltip": "Змінює ширину пера.", "Turtle.setWidth": "встановити ширину", "Turtle.colourTooltip": "Змінює колір пера.", "Turtle.setColour": "встановити колір", "Turtle.penTooltip": "Піднімає або опускає перо, щоб припинити або почати малювати.", "Turtle.penUp": "підняти перо", "Turtle.penDown": "опустити перо", "Turtle.turtleVisibilityTooltip": "Робить черепаху (коло і стрілку) видимою або невидимою.", "Turtle.hideTurtle": "приховати черепаху", "Turtle.showTurtle": "показати черепаху", "Turtle.printHelpUrl": "https://uk.wikipedia.org/wiki/Друкарство", "Turtle.printTooltip": "Малює текст в напрямку руху черепахи на місці її розташування.", "Turtle.print": "друкувати", "Turtle.fontHelpUrl": "https://uk.wikipedia.org/wiki/Шрифт", "Turtle.fontTooltip": "Визначає шрифт, що використовується блоком друку.", "Turtle.font": "шрифт", "Turtle.fontSize": "розмір шрифту", "Turtle.fontNormal": "звичайний", "Turtle.fontBold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.fontItalic": "кур<PERSON><PERSON>в", "Turtle.submitDisabled": "Запустіть програму і чекайте її зупинки. Потім ви зможете розмістити свій малюнок в галереї.", "Turtle.galleryTooltip": "Відкрити галерею малюнків.", "Turtle.galleryMsg": "Переглянути Галерею", "Turtle.submitTooltip": "Надіслати свій малюнок до галереї.", "Turtle.submitMsg": "Завантажити в Галерею", "Turtle.helpUseLoop": "Ваш розв’язок працює, але можна зробити краще.", "Turtle.helpUseLoop3": "Намалюйте фігуру, використавши всього три блоки.", "Turtle.helpUseLoop4": "Намалюйте зірку, використавши всього чотири блоки.", "Turtle.helpText1": "Створіть програму, яка намалює квадрат.", "Turtle.helpText2": "Змініть програму, щоб замість квадрата намалювати п'ятикутник.", "Turtle.helpText3a": "Це новий блок, який дозволяє змінити колір:", "Turtle.helpText3b": "Намал<PERSON><PERSON><PERSON><PERSON> жовту зірку.", "Turtle.helpText4a": "Це новий блок, який дозволяє підняти перо над папером при переміщенні:", "Turtle.helpText4b": "Намалюйте невелику жовту зірку, потім лінію над нею.", "Turtle.helpText5": "Зможете замість однієї зірки намалювати чотири зірки, розташовані в кутах квадрата?", "Turtle.helpText6": "Намалюйте три жовті зірки і одну білу лінію.", "Turtle.helpText7": "Намалюйте зірки, потім чотири білі лінії.", "Turtle.helpText8": "Малюнок з 360 білих ліній буде виглядати як повний місяць.", "Turtle.helpText9": "Зможете додати чорний круг так, щоб Місяць перетворився на півмісяць?", "Turtle.helpText10": "Малюйте все, що завгодно. Ви отримали величезну кількість нових блоків, які варто дослідити. Всього найкращого!", "Turtle.helpText10Reddit": "Використовуйте кнопку \"Переглянути Галерею\", щоб переглянути малюнки інших. Якщо намалювали щось цікаве - скористайтесь кнопкою \"Зберегти в Галереї\", щоб поділитись малюнком з іншими людьми.", "Turtle.helpToolbox": "Оберіть категорію, щоб побачити блоки.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "початковий x", "Movie.y1": "початковий y", "Movie.x2": "кінцевий x", "Movie.y2": "кінцевий y", "Movie.radius": "радіус", "Movie.width": "ши<PERSON><PERSON><PERSON>", "Movie.height": "висота", "Movie.circleTooltip": "Малює коло в зазначеному місці та з заданим радіусом.", "Movie.circleDraw": "коло", "Movie.rectTooltip": "Малює прямокутник в зазначеному місці з заданою шириною та висотою.", "Movie.rectDraw": "прямокутник", "Movie.lineTooltip": "Малює лінію вказаної ширини від однієї точки до іншої.", "Movie.lineDraw": "лінія", "Movie.timeTooltip": "Повертає поточний час у анімації (0-100).", "Movie.colourTooltip": "Змінює колір пера.", "Movie.setColour": "встановити колір", "Movie.submitDisabled": "Ваш фільм нерухомий. Використовуйте блоки, щоб зробити щось цікаве. Потім ви зможете розмістити фільм в галереї.", "Movie.galleryTooltip": "Відкрити галерею фільмів.", "Movie.galleryMsg": "Переглянути Галерею", "Movie.submitTooltip": "Надіслати свій фільм до галереї.", "Movie.submitMsg": "Завантажити в Галерею", "Movie.helpLayer": "Пересуньте фоновий круг вгору вашої програми. Тоді він опиниться позаду людини.", "Movie.helpText1": "Скористайтесь простими фігурами, щоб намалювати цього чоловічка.", "Movie.helpText2a": "Цей рівень є фільмом. Вам потрібно, щоб рука рухалась по екрану. Натисніть кнопку Старт, щоб переглянути.", "Movie.helpText2b": "При відтворенні фільму, значення блоку 'час' змінюється від 0 до 100. Оскільки потрібно, щоб координата 'y' руки також змінювалась від 0 до 100, то це має бути легко.", "Movie.helpText3": "Блок 'час' лічить від 0 до 100. Але тепер потрібно, щоб горизонтальне положення другої руки змінювалося від 100 до 0. Можете знайти просту математичну формулу, яка б розвернула напрямок?", "Movie.helpText4": "Використайте вивчене на попередньому рівні, щоб ноги перетнулись.", "Movie.helpText5": "Матема<PERSON><PERSON><PERSON>на формула для руки складна. Ось відповідь:", "Movie.helpText6": "Додай людині пару рук.", "Movie.helpText7": "Використовуйте блок \"якщо\", щоб зобразити невелику голову для першої частини фільму. Потім намалюйте велику голову для другої частини фільму.", "Movie.helpText8": "Надай ногам зворотного напрямку на середині фільму.", "Movie.helpText9": "Намалюй за людиною круг, який розширюється.", "Movie.helpText10": "Створіть фільм за власним бажанням. Ви отримали величезну кількість нових блоків, які варто дослідити. Щасти вам!", "Movie.helpText10Reddit": "Використовуйте кнопку \"Переглянути Галерею\", щоб переглянути фільми інших. Якщо створили щось цікаве - скористайтесь кнопкою \"Зберегти в Галереї\", щоб поділитись фільмом з іншими людьми.", "Music.playNoteTooltip": "Програє одну музичну ноту з заданою тривалістю і тоном.", "Music.playNote": "грати %1 ноту %2", "Music.restTooltip": "Пауза заданої тривалості", "Music.restWholeTooltip": "Пауза на одну цілу ноту.", "Music.rest": "пауза %1", "Music.setInstrumentTooltip": "Під час програвання наступних нот перемикає на заданий інструмент.", "Music.setInstrument": "увімкнути інструмент %1", "Music.startTooltip": "Виконує блоки всередині, коли клацнути кнопку 'Запустити програму'.", "Music.start": "коли натиснуто %1", "Music.pitchTooltip": "Одна нота (C4 це 7).", "Music.firstPart": "перша частина", "Music.piano": "фортепіано", "Music.trumpet": "труба", "Music.banjo": "банджо", "Music.violin": "скрипка", "Music.guitar": "гітара", "Music.flute": "флейта", "Music.drum": "бар<PERSON><PERSON><PERSON>н", "Music.choir": "хор", "Music.submitDisabled": "Запустіть програму і чекайте її зупинки. Потім ви зможете розмістити свою музику в галереї.", "Music.galleryTooltip": "Відкрити галерею музики.", "Music.galleryMsg": "Переглянути галерею", "Music.submitTooltip": "Завантажити вашу музику у галерею.", "Music.submitMsg": "Завантажити в Галерею", "Music.helpUseFunctions": "Ваш розв'язок працює, але ви можете зробити ліпше. Використайте функції для зменшення кількості повторень коду.", "Music.helpUseInstruments": "Музика звучатиме ліпше, якщо ви будете використовувати різні інструменти у кожному стартовому блоці.", "Music.helpText1": "Уведіть перші чотири ноти пісеньки «Брате Жаку».", "Music.helpText2a": "'Функція' дозволяє згрупувати блоки разом, а відтак запускати їх понад один раз.", "Music.helpText2b": "Створіть функцію, щоб зіграти перші чотири ноти пісні «Брате Жаку». Запустіть цю функцію двічі. Не додавайте нових нотних блоків.", "Music.helpText3": "Створіть іншу функцію для наступної частини пісні «Брате Жаку». Остання нота має бути довшою.", "Music.helpText4": "Створіть третю функцію для наступної частини пісні «Брате Жаку». Перші чотири ноти - коротші.", "Music.helpText5": "Завер<PERSON>іть мелодію пісні «Брате Жаку».", "Music.helpText6a": "Цей новий блок дозволяє вам використати інший музичний інструмент.", "Music.helpText6b": "Зіграйте свою мелодію на скрипці.", "Music.helpText7a": "Цей новий блок додає беззвучну затримку.", "Music.helpText7b": "Створіть другий стартовий блок з двома блоками затримки, після яких програється пісня «Брате Жаку».", "Music.helpText8": "Кожний стартовий блок повинен двічі грати пісню «Брате Жаку».", "Music.helpText9": "Створіть чотири стартові блоки, кожний з яких двічі виконує пісню «Брате Жаку». Додайте правильну кількість блоків затримки.", "Music.helpText10": "Створюйте все, що завгодно. Ви отримали величезну кількість нових блоків, які варто дослідити. Щасти вам!", "Music.helpText10Reddit": "Використовуйте кнопку \"Переглянути Галерею\", щоб прослухати мелодії інших людей. Якщо створили щось цікаве - скористайтесь кнопкою \"Зберегти в Галереї\", щоб поділитись музикою з іншими.", "Pond.scanTooltip": "Пошук супротивників. Вкажіть напрямок (0-360). Повертає відстань до найближчого супротивника в цьому напрямку. Повертає нескінченність, якщо супротивника не знайдено.", "Pond.cannonTooltip": "Постріл з гармати. Вкажіть напрямок (0-360) і відстань (0-70).", "Pond.swimTooltip": "Плисти вперед. Вкажіть напрямок (0-360).", "Pond.stopTooltip": "Припинити плавання. Гравець плавно зупиниться.", "Pond.healthTooltip": "Повертає поточне здоров'я гравця (0 - мертвий, 100 - здоровий).", "Pond.speedTooltip": "Повертає поточну швидкість гравця (0 - зупинився, 100 - повна швидкість).", "Pond.locXTooltip": "Повертає координату X гравця (0 - лівий край, 100 - правий край).", "Pond.locYTooltip": "Повертає координату Y гравця (0 - нижній край, 100 - верхній край).", "Pond.logTooltip": "Друкує число в консолі вашого вебоглядача.", "Pond.docsTooltip": "Показати документацію з мови.", "Pond.documentation": "Документація", "Pond.playerName": "Гра<PERSON><PERSON><PERSON>ь", "Pond.targetName": "Ціль", "Pond.pendulumName": "Маятник", "Pond.scaredName": "Бо<PERSON>г<PERSON>з", "Pond.helpUseScan": "Ваше рішення працює, але можна зробити краще. Скористайтесь \"scan\", щоб вказати відстань для стрільби гармати.", "Pond.helpText1": "Використовуйте команду 'cannon', щоб уразити ціль. Перший параметр - кут, другий параметр - відстань. Знайдіть їх правильне поєднання.", "Pond.helpText2": "Ця ціль повинна бути вражена декілька разів. Використовуйте 'while (true)' щоб робити щось невизначений час.", "Pond.helpText3a": "Цей противник рухається вперед-назад, через це у нього важко влучити. Вираз 'scan' повертає точну відстань до противника в зазначеному напрямку.", "Pond.helpText3b": "Відстань - саме те, що потрібно команді 'cannon' для влучної стрільби.", "Pond.helpText4": "Цей противник надто далеко, щоб використовувати гармату (яка має обмеження в 70 метрів). Замість цього, використовуйте  команду 'swim', щоб плисти у бік противника та врізатися в нього.", "Pond.helpText5": "Цей противник також надто далеко, щоб використовувати гармату. Але ви занадто слабкі, щоб вижити при зіткненні. Пливіть у бік противника, поки відстань до нього стане меншою, ніж 50. Потім \"stop\" і використовуйте гармату.", "Pond.helpText6": "Цей супротивник буде відходити, якщо у нього влучили. Пливіть вперед, якщо він перебуває за межами досяжності (70 метрів).", "Gallery": "Галерея"}