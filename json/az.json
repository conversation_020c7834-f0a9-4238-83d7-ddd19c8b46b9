{"@metadata": {"authors": ["AZISS", "<PERSON><PERSON>", "Cekli829", "Khan27", "Maqa001", "MuratTheTurkish", "<PERSON><PERSON><PERSON><PERSON>", "Wert<PERSON>se"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Tapmaca", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "Q<PERSON>ş", "Games.turtle": "Tısbağa", "Games.movie": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON>t", "Games.music": "<PERSON><PERSON><PERSON>", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "G<PERSON><PERSON>ə<PERSON><PERSON>", "Games.linesOfCode1": "Sən bu mərhələni  bir sətirlik JavaScript-də həll etdin:", "Games.linesOfCode2": "Sən bu mərhələni  %1 sətirlik JavaScript-də həll etdin:", "Games.nextLevel": "Sən  %1 mərhələyə hazırsan?", "Games.finalLevel": "Sən  növbəti sınağa hazırsan?", "Games.submitTitle": "Başlıq:", "Games.linkTooltip": "Bloklara istinadı göstərmək və yaddaşa saxlamaq.", "Games.runTooltip": "<PERSON><PERSON><PERSON> yaratdığın proqramı icrasına başla.", "Games.runProgram": "Proqramı İcra Et", "Games.resetTooltip": "Proqramı dayandır və mərhələni sıfırla.", "Games.resetProgram": "Sıfırla", "Games.help": "Kömək", "Games.catLogic": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLoops": "Dövrə", "Games.catMath": "<PERSON><PERSON><PERSON>", "Games.catText": "Mətn", "Games.catLists": "Siyahılar", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "D<PERSON><PERSON><PERSON>ənlər", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "<PERSON><PERSON><PERSON><PERSON> ilə əlaqəli problem var.", "Games.linkAlert": "Şəxsi bloklarını bu linklə bölüş:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, '%1' he<PERSON> bir qeyd olu<PERSON><PERSON>ş proqramla uyğun gəlmir.", "Games.xmlError": "Yadda saxladığınız sənəd yüklənə bilmədi. Bəlkə Blockly'nin fərqli bir versiyası ilə yaradıldı?", "Games.listVariable": "siyahı", "Games.textVariable": "mətn", "Games.blocks": "Bloklar", "Games.congratulations": "Təbriklər!", "Index.clear": "<PERSON><PERSON><PERSON><PERSON><PERSON> həllər si<PERSON>?", "Index.moreInfo": "M<PERSON><PERSON>lli<PERSON>lər üçün məlumat...", "Index.startOver": "Ye<PERSON><PERSON><PERSON>n baş<PERSON>aq istəyirsini?", "Index.clearData": "Məlumatları təmizlə", "Puzzle.animal1": "Ördək", "Puzzle.animal1Trait1": "Lələklər", "Puzzle.animal1Trait2": "<PERSON><PERSON>di<PERSON>", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Pişik", "Puzzle.animal2Trait1": "Bığcıq", "Puzzle.animal2Trait2": "Tük", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "Arı", "Puzzle.animal3Trait1": "<PERSON>l", "Puzzle.animal3Trait2": "İynə", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "İlbiz", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "Sümüksü", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "şəkil:", "Puzzle.legs": "aya<PERSON><PERSON>:", "Puzzle.legsChoose": "seç...", "Puzzle.traits": "xüsusiyyətlər:", "Puzzle.error0": "Mükəmməl!\n%1 blokun hamısı doğrudur.", "Puzzle.error1": "Demək olar ki! Bir blok səhvdir.", "Puzzle.error2": "%1 blokun yerləşməsi  səhfdir.", "Puzzle.tryAgain": "Vurğulanan blok səhvdir.\nÇalışmağa davam.", "Puzzle.checkAnswers": "Cavabları Yoxla", "Puzzle.helpText": "<PERSON>ər bir heyvan (yaşıl blok) üçün, ona aid rəsm<PERSON> seçərək, əya<PERSON><PERSON><PERSON>nın sayı və fərqli xüsusiyyətlərini qed et.", "Maze.moveForward": "irəli<PERSON>ə keçid", "Maze.turnLeft": "sola dö<PERSON>n", "Maze.turnRight": "<PERSON><PERSON><PERSON>", "Maze.doCode": "icra et", "Maze.pathAhead": "əgər yol qarşı<PERSON>sa", "Maze.pathLeft": "əgər yol soldadısa", "Maze.pathRight": "əgər yol <PERSON>", "Maze.repeatUntil": "təkrarla, ta ki", "Maze.moveForwardTooltip": "Oyunçunu bir xana irəli a<PERSON>ır.", "Maze.turnTooltip": "Yolçunu 90 dərəcə sola və ya sağa döndər.", "Maze.ifTooltip": "<PERSON>gər göstərilən istiqamətə yol varsa, hər-hansı bir iş gör.", "Maze.ifelseTooltip": "<PERSON>gər göstərilən istiqamətə yol varsa, birinci blokda göstərilən hərəkətləri icra et. <PERSON><PERSON> halda, ikinci blokda göstərilən hərəkətlərin icrasına başla.", "Maze.capacity0": "Sizin istifadəsiz %0 blokunuz qalmayıq.", "Maze.capacity1": "Sizin istifadəsiz %1 blokunuz qalıb.", "Maze.capacity2": "Sizin istifadəsiz %2 blokunuz qalıb.", "Maze.runTooltip": "<PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>n bütün gö<PERSON>əriş<PERSON>ərini icra edəcək.", "Maze.resetTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> labirintin b<PERSON>ı<PERSON><PERSON> qaytar.", "Maze.helpRun": "Nə baş verəcəyini görmək üçün proqramın içrasına başla.", "Bird.heading": "istiqamət", "Turtle.hideTurtle": "tısbağanı gizlət", "Turtle.showTurtle": "tısbağanı gö<PERSON>ər", "Turtle.print": "çap", "Turtle.fontHelpUrl": "https://az.wikipedia.org/wiki/Şrift", "Turtle.font": "şrift", "Turtle.fontSize": "<PERSON><PERSON><PERSON>", "Turtle.fontNormal": "normal", "Turtle.fontBold": "qalın", "Turtle.fontItalic": "kursiv", "Turtle.galleryMsg": "Q<PERSON><PERSON><PERSON> bax", "Turtle.submitMsg": "Qalereyaya göndər", "Turtle.helpText3b": "<PERSON>rı ulduz çə<PERSON>.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "başlanğıc x", "Movie.y1": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y", "Movie.x2": "son x", "Movie.y2": "son y", "Movie.radius": "radius", "Movie.width": "uzunluq", "Movie.height": "en", "Movie.circleDraw": "dairə", "Movie.rectDraw": "düzbucaqlı", "Movie.lineDraw": "ciz", "Movie.galleryMsg": "Q<PERSON><PERSON><PERSON> bax", "Music.piano": "<PERSON><PERSON><PERSON>", "Music.violin": "skripka", "Music.guitar": "gitara", "Music.flute": "fleyta", "Music.drum": "baraban", "Music.choir": "xor", "Music.galleryMsg": "Q<PERSON><PERSON><PERSON> bax", "Music.submitMsg": "Qalereyaya göndər", "Pond.docsTooltip": "Dil üzrə sənədləşməni gö<PERSON>ərir.", "Pond.documentation": "Sənədl<PERSON>şdirmə", "Pond.playerName": "Oyunçu", "Pond.targetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.pendulumName": "K<PERSON>f<PERSON>r", "Pond.scaredName": "Qorxmuş", "Gallery": "<PERSON><PERSON><PERSON><PERSON>"}