{"@metadata": {"authors": ["Ksh31", "<PERSON><PERSON><PERSON><PERSON>", "Nayvik", "<PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "ಬ್ಲಾಕ್ಲಿ ಆಟಗಳು", "Games.puzzle": "ಒಗಟು", "Games.maze": "ಸಿಕ್ಕು ದಾರಿ", "Games.bird": "ಪಕ್ಷಿ", "Games.turtle": "ಆಮೆ", "Games.movie": "ಚಲನಚಿತ್ರ", "Games.music": "ಸಂಗೀತ", "Games.pondTutor": "ಕೊಳದ ಬೋಧಕ", "Games.pond": "ಕೊಳ", "Games.linesOfCode1": "ಜಾವಾಸ್ಕ್ರಿಪ್ಟಿ‌ನ 1 ಸಾಲಿನೊಂದಿಗೆ ನೀವು ಈ ಮಟ್ಟವನ್ನು ಪರಿಹರಿಸಿದ್ದೀರಿ:", "Games.linesOfCode2": "ನೀವು ಈ ಹಂತವನ್ನು %1 ಸಾಲುಗಳ ಜಾವಾ ಸ್ಕ್ರಿಪ್ಟ್ ನ ಮುಖಾಂತರ ಬಗೆಹರಿಸಿದ್ದೀರಿ:", "Games.nextLevel": "ನೀವು %1 ನೇ ಹಂತಕ್ಕೆ ಹೋಗಲು ಸಿದ್ದರಿದ್ದೀರಾ?", "Games.finalLevel": "ನೀವು ಮುಂದಿನ ಸವಾಲಿಗೆ ಸಿದ್ಧರಿದ್ದೀರಾ?", "Games.submitTitle": "ಶೀರ್ಷಿಕೆ:", "Games.linkTooltip": "ಸಂಗ್ರಹಿಸಿ ಮತ್ತು ಬ್ಲಾಕ್ ಗಳಿಗೆ ಲಿಂಕ್ ಮಾಡಿ.", "Games.runTooltip": "ನೀವು ಬರೆದ ಪ್ರೋಗ್ರಾಂ ಅನ್ನು ಚಲಾಯಿಸಿ.", "Games.runProgram": "ಪ್ರೋಗ್ರಾಂ ಚಲಾಯಿಸಿ", "Games.resetTooltip": "ಪ್ರೋಗ್ರಾಮ್ ನಿಲ್ಲಿಸಿ ಮತ್ತು ಪ್ರಾರಂಭದ ಹಂತಕ್ಕೆ ಹಿಂತಿರುಗಿ.", "Games.resetProgram": "ಮರುಹೊಂದಿಸಿ", "Games.help": "ಸಹಾಯ", "Games.catLogic": "ತರ್ಕ", "Games.catLoops": "ಸುತ್ತುಗಳು", "Games.catMath": "ಗಣಿತ", "Games.catText": "ಪಠ್ಯ", "Games.catLists": "ಪಟ್ಟಿಗಳು", "Games.catColour": "ಬಣ್ಣ", "Games.catVariables": "ಚರಾಂಶಗಳು", "Games.catProcedures": "ಕಾರ್ಯಘಟಕಗಳು", "Games.httpRequestError": "ಕೋರಿಕೆಯಲ್ಲಿ ಒಂದು ಸಮಸ್ಯೆ ಇದೆ.", "Games.linkAlert": "ನಿಮ್ಮ ಬ್ಲಾಕ್ ಗಳನ್ನು ಈ ಸಂಪರ್ಕ ಕೊಂಡಿಯ ಮುಖಾಂತರ ಹಂಚಿಕೊಳ್ಳಿ:\n\n%1", "Games.hashError": "ಕ್ಷಮಿಸಿ,  '%1' ಸಂಗ್ರಹಿಸಲಾದ ಯಾವುದೇ ಪ್ರೋಗ್ರಾಮ್ ಗೆ ಸಂಬಂಧಿಸಿಲ್ಲ.", "Games.xmlError": "ನಿಮ್ಮ ಸಂಗ್ರಹಿಸಲಾದ ಕಡತವನ್ನು ಲೋಡ್ ಮಾಡಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ. ಬಹುಶಃ ಇದನ್ನು ಬ್ಲಾಕ್ಲಿಯ ವಿಭಿನ್ನ ಆವೃತ್ತಿಯೊಂದಿಗೆ ರಚಿಸಲಾಗಿದೆ?", "Games.submitted": "ಈ ಪ್ರೋಗ್ರಾಮ್ ಗಾಗಿ ಧನ್ಯವಾದಗಳು! ತರಬೇತಿ ಪಡೆದ ಕೋತಿಗಳ  ನಮ್ಮ ಸಿಬ್ಬಂದಿ ಇಷ್ಟಪಟ್ಟರೆ, ಅವರು ಅದನ್ನು ಒಂದೆರಡು ದಿನಗಳಲ್ಲಿ ಗ್ಯಾಲರಿಗೆ ಪ್ರಕಟಿಸುತ್ತಾರೆ.", "Games.listVariable": "ಪಟ್ಟಿ", "Games.textVariable": "ಪಠ್ಯ", "Games.breakLink": "ನೀವು ಜಾವಾಸ್ಕ್ರಿಪ್ಟ್ ಅನ್ನು ಸಂಪಾದಿಸಲು ಪ್ರಾರಂಭಿಸಿದ ಮೇಲೆ, ನೀವು ಬ್ಲಾಕ್ ಗಳನ್ನು ಸಂಪಾದಿಸಲು ಹಿಂತಿರುಗಲು ಸಾಧ್ಯವಿಲ್ಲ. ಇದು ಸರಿಯೇ?", "Games.blocks": "ಬ್ಲಾಕ್ ಗಳು", "Games.congratulations": "ಅಭಿನಂದನೆಗಳು!", "Games.helpAbort": "ಈ ಮಟ್ಟವು ಅತ್ಯಂತ ಕಷ್ಟಕರವಾಗಿದೆ. ನೀವು ಅದನ್ನು ಬಿಟ್ಟು ಮುಂದಿನ ಆಟಕ್ಕೆ ಹೋಗಲು ಬಯಸುವಿರಾ? ನಂತರ  ನೀವು ಯಾವಾಗಲಾದರೂ ಹಿಂತಿರುಗಬಹುದು.", "Index.clear": "ನಿಮ್ಮ ಎಲ್ಲಾ ಪರಿಹಾರಗಳನ್ನು ಅಳಿಸುವುದೇ?", "Index.subTitle": "ನಾಳಿನ ಪ್ರೋಗ್ರಾಮರ್ಗಳಿಗಾಗಿ ಇರುವ ಆಟಗಳು.", "Index.moreInfo": "ಶಿಕ್ಷಣ ತಜ್ಞರಿಗಾಗಿ ಮಾಹಿತಿ...", "Index.startOver": "ಪುನಃ ಪ್ರಾರಂಭಿಸಲು ಬಯಸುವಿರಾ?", "Index.clearData": "ಡೇಟಾವನ್ನು ತೆರವುಗೊಳಿಸಿ", "Puzzle.animal1": "ಬಾತುಕೋಳಿ", "Puzzle.animal1Trait1": "ಗರಿಗಳು", "Puzzle.animal1Trait2": "ಕೊಕ್ಕು", "Puzzle.animal1HelpUrl": "https://kn.wikipedia.org/wiki/ಬಾತುಕೋಳಿ", "Puzzle.animal2": "ಬೆಕ್ಕು", "Puzzle.animal2Trait1": "ಮೀಸೆ", "Puzzle.animal2Trait2": "ತುಪ್ಪಳ", "Puzzle.animal2HelpUrl": "https://kn.wikipedia.org/wiki/ಬೆಕ್ಕು", "Puzzle.animal3": "ಜೇನುನೊಣ", "Puzzle.animal3Trait1": "ಜೇನುತುಪ್ಪ", "Puzzle.animal3Trait2": "ಕುಟುಕುವ", "Puzzle.animal3HelpUrl": "https://kn.wikipedia.org/wiki/ಜೇನು_ಹುಳು", "Puzzle.animal4": "ಬಸವನ ಹುಳು", "Puzzle.animal4Trait1": "ಚಿಪ್ಪು", "Puzzle.animal4Trait2": "ಜಿಗುಟು ಮಣ್ಣು", "Puzzle.animal4HelpUrl": "https://kn.wikipedia.org/wiki/ಬಸವನ_ಹುಳು", "Puzzle.picture": "ಚಿತ್ರ:", "Puzzle.legs": "ಕಾಲುಗಳು:", "Puzzle.legsChoose": "ಆರಿಸಿ...", "Puzzle.traits": "ಲಕ್ಷಣಗಳು:", "Puzzle.error0": "ಪರಿಪೂರ್ಣ!\nಎಲ್ಲ %1 ಬ್ಲಾಕ್ ಗಳು ಸರಿಯಾಗಿವೆ.", "Puzzle.error1": "ಬಹುತೇಕ! ಒಂದು ಬ್ಲಾಕ್ ಸರಿ ಇಲ್ಲ.", "Puzzle.error2": "%1 ಬ್ಲಾಕ್ ಗಳು ಸರಿ ಇಲ್ಲ.", "Puzzle.tryAgain": "ಪ್ರಕಾಶಮಾನವಾಗಿರುವ ಬ್ಲಾಕ್ ಸರಿಯಾಗಿಲ್ಲ.\nಪ್ರಯತ್ನಿಸುತ್ತಲೇ ಇರಿ.", "Puzzle.checkAnswers": "ಉತ್ತರಗಳನ್ನು ಪರಿಶೀಲಿಸಿ", "Puzzle.helpText": "ಪ್ರತಿ ಪ್ರಾಣಿಗೆ (ಹಸಿರು), ಅದರ ಚಿತ್ರವನ್ನು ಲಗತ್ತಿಸಿ, ಅದರ ಕಾಲುಗಳ ಸಂಖ್ಯೆಯನ್ನು ಆರಿಸಿ ಮತ್ತು ಅದರ ಗುಣಲಕ್ಷಣಗಳ ಸಂಗ್ರಹವನ್ನು ಮಾಡಿ.", "Maze.moveForward": "ಮುಂದಕ್ಕೆ ಚಲಿಸಿ", "Maze.turnLeft": "ಎಡಕ್ಕೆ ತಿರುಗಿ", "Maze.turnRight": "ಬಲಕ್ಕೆ ತಿರುಗಿ", "Maze.doCode": "ಮಾಡು", "Maze.helpIfElse": "'ಆಗಿದ್ದರೆ-ಇಲ್ಲದಿದ್ದರೆ' ಬ್ಲಾಕ್ ಗಳು ಒಂದು ಅಥವಾ ಇನ್ನೊಂದನ್ನು ಮಾಡುತ್ತವೆ.", "Maze.pathAhead": "ಮುಂದೆ ದಾರಿ ಇದ್ದರೆ", "Maze.pathLeft": "ಎಡಕ್ಕೆ ದಾರಿ ಇದ್ದರೆ", "Maze.pathRight": "ಬಲಕ್ಕೆ ದಾರಿ ಇದ್ದರೆ", "Maze.repeatUntil": "ತನಕ ಪುನರಾವರ್ತಿಸಿ", "Maze.moveForwardTooltip": "ಆಟಗಾರನನ್ನು ಒಂದು ಜಾಗ ಮುಂದಕ್ಕೆ ಚಲಿಸುವಂತೆ ಮಾಡುತ್ತದೆ.", "Maze.turnTooltip": "ಆಟಗಾರನನ್ನು ಎಡ ಅಥವಾ ಬಲಕ್ಕೆ 90 ಡಿಗ್ರಿಗಳಷ್ಟು ತಿರುಗಿಸುತ್ತದೆ.", "Maze.ifTooltip": "ನಿಗದಿತ ದಿಕ್ಕಿನಲ್ಲಿ ಮಾರ್ಗವಿದ್ದರೆ, ಕೆಲವು ಕ್ರಮಗಳನ್ನು ಕೈಗೊಳ್ಳಿ.", "Maze.ifelseTooltip": "ನಿಗದಿತ ದಿಕ್ಕಿನಲ್ಲಿ ಮಾರ್ಗವಿದ್ದರೆ, ಮೊದಲೆನೆಯ ವಿಭಾಗದ ಕ್ರಮಗಳನ್ನು ಕೈಗೊಳ್ಳಿ. ಇಲ್ಲವಾದರೆ, ಎರಡನೇ ವಿಭಾಗದ ಕ್ರಮಗಳನ್ನು ಕೈಗೊಳ್ಳಿ.", "Maze.whileTooltip": "ಮುಕ್ತಾಯದ ಹಂತವನ್ನು ತಲುಪುವವರೆಗೆ ಸುತ್ತುವರಿದ ಕ್ರಿಯೆಗಳನ್ನು ಪುನರಾವರ್ತಿಸಿ.", "Maze.capacity0": "ನಿಮ್ಮಲ್ಲಿ %0 ಬ್ಲಾಕ್ ಗಳು ಉಳಿದುಕೊಂಡಿವೆ.", "Maze.capacity1": "ನಿಮ್ಮಲ್ಲಿ %1 ಬ್ಲಾಕ್ ಉಳಿದುಕೊಂಡಿದೆ.", "Maze.capacity2": "ನಿಮ್ಮಲ್ಲಿ %2 ಬ್ಲಾಕ್ ಗಳು ಉಳಿದುಕೊಂಡಿವೆ.", "Maze.runTooltip": "ಬ್ಲಾಕ್ ಗಳು ಹೇಳುವದನ್ನು ಆಟಗಾರನು ಮಾಡುವಂತೆ ಮಾಡುತ್ತದೆ.", "Maze.resetTooltip": "ಆಟಗಾರನನ್ನು ವಾಪಸ್ಸು  ಸಿಕ್ಕು ದಾರಿಯ ಪ್ರಾರಂಭದಲ್ಲಿ ಇರಿಸಿ.", "Maze.helpStack": "ಗುರಿಯನ್ನು ತಲುಪಲು ನನಗೆ ಸಹಾಯ ಮಾಡಲು ಒಂದೆರಡು 'ಮುಂದೆ ಸಾಗು' ಬ್ಲಾಕ್ ಗಳನ್ನು ಒಟ್ಟಿಗೆ ಜೋಡಿಸಿ.", "Maze.helpOneTopBlock": "ಈ ಮಟ್ಟದಲ್ಲಿ, ನೀವು ಬಿಳಿ ಬಣ್ಣದ ಕಾರ್ಯಕ್ಷೇತ್ರದಲ್ಲಿನ ಎಲ್ಲಾ ಬ್ಲಾಕ್‌ಗಳನ್ನು ಒಟ್ಟಿಗೆ ಜೋಡಿಸಬೇಕಾಗುತ್ತದೆ.", "Maze.helpRun": "ಏನಾಗುತ್ತದೆ ಎಂಬುದನ್ನು ನೋಡಲು ನಿಮ್ಮ ಪ್ರೋಗ್ರಾಂ ಅನ್ನು ಚಲಾಯಿಸಿ.", "Maze.helpReset": "ನಿಮ್ಮ ಪ್ರೋಗ್ರಾಂ ಸಿಕ್ಕುದಾರಿಯನ್ನು ಪರಿಹರಿಸಲಿಲ್ಲ. 'ಮರುಹೊಂದಿಸು' ಒತ್ತಿ ಮತ್ತು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ.", "Maze.helpRepeat": "ಕೇವಲ ಎರಡು ಬ್ಲಾಕ್ ಗಳನ್ನು ಬಳಸಿ ಈ ಮಾರ್ಗದ ಅಂತ್ಯವನ್ನು ತಲುಪಿ. ಒಂದಕ್ಕಿಂತ ಹೆಚ್ಚು ಬಾರಿ ಬ್ಲಾಕ್ ಅನ್ನು ಚಲಾಯಿಸಲು 'ಪುನರಾವರ್ತಿಸಿ' ಬಳಸಿ.", "Maze.helpCapacity": "ಈ ಮಟ್ಟಕ್ಕಾಗಿ ನೀವು ಎಲ್ಲಾ ಬ್ಲಾಕ್-ಗಳನ್ನು ಬಳಸಿದ್ದೀರಿ. ಹೊಸ ಬ್ಲಾಕ್ ರಚಿಸಲು, ನೀವು ಮೊದಲು ಅಸ್ತಿತ್ವದಲ್ಲಿರುವ ಬ್ಲಾಕ್ ಅನ್ನು ಅಳಿಸಬೇಕಾಗಿದೆ.", "Maze.helpRepeatMany": "'ಪುನರಾವರ್ತಿತ' ಬ್ಲಾಕ್ ಒಳಗೆ ನೀವು ಒಂದಕ್ಕಿಂತ ಹೆಚ್ಚು ಬ್ಲಾಕ್ ಗಳನ್ನು ಅಳವಡಿಸಬಹುದು.", "Maze.helpSkins": "ಈ ಮೆನುವಿನಿಂದ ನಿಮ್ಮ ನೆಚ್ಚಿನ ಆಟಗಾರನನ್ನು ಆರಿಸಿ.", "Maze.helpIf": "ಷರತ್ತು ನಿಜವಾಗಿದ್ದರೆ ಮಾತ್ರ 'ಆಗಿದ್ದರೆ' ಬ್ಲಾಕ್ ಏನನ್ನಾದರೂ ಮಾಡುತ್ತದೆ. ಎಡಕ್ಕೆ ಮಾರ್ಗವಿದ್ದರೆ ಎಡಕ್ಕೆ ತಿರುಗಲು ಪ್ರಯತ್ನಿಸಿ.", "Maze.helpMenu": "ಅದರ ಷರತ್ತನ್ನು ಬದಲಾಯಿಸಲು 'ಆಗಿದ್ದರೆ' ಬ್ಲಾಕ್‌ನಲ್ಲಿ %1  ಮೇಲೆ ಕ್ಲಿಕ್ ಮಾಡಿ.", "Maze.helpWallFollow": "ಈ ಸಂಕೀರ್ಣ ಸಿಕ್ಕುದಾರಿಯನ್ನು ನೀವು ಪರಿಹರಿಸಬಹುದೇ? ಎಡಗೈ ಗೋಡೆಯನ್ನು ಅನುಸರಿಸಲು ಪ್ರಯತ್ನಿಸಿ. ನುರಿತ ಪ್ರೋಗ್ರಾಮರ್ಗಳಿಗೆ ಮಾತ್ರ!", "Bird.noWorm": "ಹುಳುವನ್ನು ಹೊಂದಿಲ್ಲ", "Bird.heading": "ಕಡೆಗೆ", "Bird.noWormTooltip": "ಪಕ್ಷಿ ಹುಳುವನ್ನು ಪಡೆದಿಲ್ಲದ ಸ್ಥಿತಿ", "Bird.headingTooltip": "ಕೊಟ್ಟಿರುವ ಕೋನದ ದಿಕ್ಕಿನಲ್ಲಿ  ಚಲಿಸಿ: 0 ಬಲಕ್ಕೆ ಚಲಿಸಲು, 90 ನೇರವಾಗಿ ಮೇಲೆ ಚಲಿಸಲು, ಇತ್ಯಾದಿ.", "Bird.positionTooltip": "x ಮತ್ತು y ಪಕ್ಷಿಗಳ ಸ್ಥಾನವನ್ನು ಗುರುತಿಸುತ್ತವೆ.  X = 0 ಆಗಿದ್ದಾಗ, ಹಕ್ಕಿ ಎಡ ಅಂಚಿನ ಬಳಿ ಇರುತ್ತದೆ , x = 100  ಆಗಿದ್ದಾಗ, ಅದು ಬಲ ಅಂಚಿನ ಬಳಿ ಇರುತ್ತದೆ, Y = 0 ಆಗಿದ್ದಾಗ ಹಕ್ಕಿ ಕೆಳಭಾಗದಲ್ಲಿ ಇರುತ್ತದೆ,     y = 100 ಆಗಿದ್ದಾಗ,   ಅದು ಮೇಲ್ಭಾಗದಲ್ಲಿ ಇರುತ್ತದೆ.", "Bird.helpHeading": "ದಿಕ್ಕಿನ ಕೋನವನ್ನು ಬದಲಾಯಿಸುವುದರ ಮೂಲಕ, ಹಕ್ಕಿಯು ಹುಳುವನ್ನು ಹಿಡಿದು ಮತ್ತು ತನ್ನ ಗೂಡನ್ನು ಸೇರುವಂತೆ ಮಾಡಿ.", "Bird.helpHasWorm": "ನಿಮ್ಮ ಬಳಿ ಹುಳು ಇದ್ದರೆ ಒಂದು ದಿಕ್ಕಿನಲ್ಲಿ ಚಲಿಸಲು ಈ ಬ್ಲಾಕ್ ಅನ್ನು ಬಳಸಿ, ಅಥವಾ ನಿಮ್ಮ ಬಳಿ ಹುಳು ಇಲ್ಲದಿದ್ದರೆ ಬೇರೆ ದಿಕ್ಕಿನಲ್ಲಿ  ಚಲಿಸಿ.", "Bird.helpX": "'x' ಎಂಬುದು ನಿಮ್ಮ ಪ್ರಸ್ತುತ ಸಮತಲ ಸ್ಥಾನವಾಗಿದೆ. 'x' ಯಾವುದೇ ಒಂದು ಸಂಖ್ಯೆಗಿಂತ ಕಡಿಮೆಯಿದ್ದರೆ ಒಂದು ದಿಕ್ಕಿನಲ್ಲಿ ಚಲಿಸಿ, ಇಲ್ಲದಿದ್ದಲ್ಲಿ ಬೇರೆ ದಿಕ್ಕಿನಲ್ಲಿ ಚಲಿಸಿ", "Bird.helpElse": "'ಆಗಿದ್ದರೆ' ಬ್ಲಾಕ್ ಅನ್ನು ಮಾರ್ಪಡಿಸಲು ಐಕಾನ್ ಕ್ಲಿಕ್ ಮಾಡಿ.", "Bird.helpElseIf": "ಈ ಮಟ್ಟಕ್ಕೆ 'ಆಗಿರದಿದ್ದರೆ' ಮತ್ತು 'ಇಲ್ಲದಿದ್ದರೆ' ಎರಡೂ  ಬ್ಲಾಕ್ ಗಳ ಅಗತ್ಯವಿದೆ.", "Bird.helpAnd": "ಅದರ ಎರಡೂ ಒದಗಿಸುವ ಅಂಶಗಳು ನಿಜವಾಗಿದ್ದರೆ ಮಾತ್ರ 'ಮತ್ತು' ಬ್ಲಾಕ್ ನಿಜವಾಗುತ್ತದೆ.", "Bird.helpMutator": "'ಇಲ್ಲದಿದ್ದರೆ' ಬ್ಲಾಕ್ ಅನ್ನು 'ಆಗಿದ್ದರೆ' ಬ್ಲಾಕ್ ನ ಒಳಕ್ಕೆ ಎಳೆಯಿರಿ.", "Turtle.moveTooltip": "ಆಮೆಯನ್ನು ನಿಗದಿತ ಪ್ರಮಾಣದಲ್ಲಿ ಮುಂದಕ್ಕೆ ಅಥವಾ ಹಿಂದಕ್ಕೆ ಚಲಿಸುವಂತೆ ಮಾಡುತ್ತದೆ.", "Turtle.moveForward": "ಮುಂದಕ್ಕೆ ಚಲಿಸಿ ಈ ಪ್ರಮಾಣದಲ್ಲಿ", "Turtle.moveBackward": "ಹಿಂದಕ್ಕೆ ಚಲಿಸಿ ಈ ಪ್ರಮಾಣದಲ್ಲಿ", "Turtle.turnTooltip": "ನಿಗದಿತ ಸಂಖ್ಯೆಯ ಡಿಗ್ರಿಗಳಷ್ಟು, ಆಮೆ ಎಡ ಅಥವಾ ಬಲಕ್ಕೆ ತಿರುಗುವಂತೆ ಮಾಡುತ್ತದೆ.", "Turtle.turnRight": "ಬಲಕ್ಕೆ ತಿರುಗು ಈ ಪ್ರಮಾಣದಲ್ಲಿ", "Turtle.turnLeft": "ಎಡಕ್ಕೆ ತಿರುಗು ಈ ಪ್ರಮಾಣದಲ್ಲಿ", "Turtle.widthTooltip": "ಲೇಖನಿಯ ಮೊನೆಯ ಅಗಲವನ್ನು ಬದಲಾಯಿಸುತ್ತದೆ.", "Turtle.setWidth": "ಅಗಲವನ್ನು ಗೊತ್ತುಪಡಿಸು", "Turtle.colourTooltip": "ಲೇಖನಿಯ ಬಣ್ಣವನ್ನು ಬದಲಾಯಿಸುವುದು.", "Turtle.setColour": "ಬಣ್ಣವನ್ನು ಬದಲಿಸು", "Turtle.penTooltip": "ರೇಖಾಚಿತ್ರವನ್ನು ನಿಲ್ಲಿಸಲು ಅಥವಾ ಪ್ರಾರಂಭಿಸಲು,  ಲೇಖನಿಯನ್ನು ಎತ್ತುತ್ತದೆ ಅಥವಾ ಇಳಿಸುತ್ತದೆ.", "Turtle.penUp": "ಲೇಖನಿಯನ್ನು ಮೇಲೆತ್ತು", "Turtle.penDown": "ಲೇಖನಿಯನ್ನು ಇಳಿಸು", "Turtle.turtleVisibilityTooltip": "ಆಮೆಯನ್ನು (ವೃತ್ತ ಮತ್ತು ಬಾಣ) ಗೋಚರ  ಅಥವಾ ಅಗೋಚರವಾಗಿ ಮಾಡುತ್ತದೆ.", "Turtle.hideTurtle": "ಆಮೆಯನ್ನು ಬಚ್ಚಿಡು", "Turtle.showTurtle": "ಆಮೆಯನ್ನು ತೋರಿಸು", "Turtle.printTooltip": "ಆಮೆಯ ದಿಕ್ಕಿನಲ್ಲಿ ಪಠ್ಯವನ್ನು ಅದರ ಸ್ಥಳದಲ್ಲೇ ರಚಿಸುವಂತೆ ಮಾಡುತ್ತದೆ.", "Turtle.print": "ಮುದ್ರಿಸು", "Turtle.fontTooltip": "'ಮುದ್ರಿಸು' ಬ್ಲಾಕ್  ಬಳಸುವ ಅಕ್ಷರ ವಿನ್ಯಾಸವನ್ನು ಗೊತ್ತುಪಡಿಸುತ್ತದೆ.", "Turtle.font": "ಅಕ್ಷರ", "Turtle.fontSize": "ಅಕ್ಷರ ಗಾತ್ರ", "Turtle.fontNormal": "ಸಾಮಾನ್ಯ", "Turtle.fontBold": "ದಪ್ಪ", "Turtle.fontItalic": "ಓರೆಯಾದ", "Turtle.submitDisabled": "ನಿಮ್ಮ ಪ್ರೋಗ್ರಾಂ ನಿಲ್ಲುವವರೆಗೂ ಅದನ್ನು ಚಲಾಯಿಸಿ. ನಂತರ ನೀವು ನಿಮ್ಮ ರೇಖಾಚಿತ್ರವನ್ನು ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸಬಹುದು.", "Turtle.galleryTooltip": "ರೇಖಾಚಿತ್ರಗಳ ಚಿತ್ರಶಾಲೆಯನ್ನು ತೆರೆಯಿರಿ.", "Turtle.galleryMsg": "ಚಿತ್ರಶಾಲೆಯನ್ನು ನೋಡು", "Turtle.submitTooltip": "ಚಿತ್ರಶಾಲೆಗೆ ನಿಮ್ಮ ರೇಖಾಚಿತ್ರವನ್ನು ಸಲ್ಲಿಸಿ.", "Turtle.submitMsg": "ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸಿ.", "Turtle.helpUseLoop": "ನಿಮ್ಮ ಪರಿಹಾರ ಕೆಲಸಮಾಡುತ್ತದೆ, ಆದರೆ ನೀವು ಇನ್ನೂ ಉತ್ತಮವಾಗಿ ಮಾಡಬಹುದು.", "Turtle.helpUseLoop3": "ಕೇವಲ ಮೂರು ಬ್ಲಾಕ್‌ಗಳೊಂದಿಗೆ ಆಕಾರವನ್ನು ರಚಿಸಿ.", "Turtle.helpUseLoop4": "ಕೇವಲ ನಾಲ್ಕು ಬ್ಲಾಕ್ಗಳೊಂದಿಗೆ ನಕ್ಷತ್ರವನ್ನು ರಚಿಸಿ.", "Turtle.helpText1": "ಒಂದು ಚೌಕವನ್ನು ಬಿಡಿಸುವ ಪ್ರೋಗ್ರಾಂ ಅನ್ನು ರಚಿಸಿ.", "Turtle.helpText2": "ಚೌಕದ ಬದಲಾಗಿ ಪಂಚಭುಜಾಕ್ರತಿ ಬಿಡಿಸಲು, ನಿಮ್ಮ ಪ್ರೋಗ್ರಾಂ ಅನ್ನು ಬದಲಾಯಿಸಿ.", "Turtle.helpText3a": "ಬಣ್ಣವನ್ನು ಬದಲಾಯಿಸಲು ನಿಮಗೆ ಅನುಮತಿಸುವ ಹೊಸ ಬ್ಲಾಕ್ ಇದೆ:", "Turtle.helpText3b": "ಹಳದಿ ನಕ್ಷತ್ರವನ್ನು ರಚಿಸಿ.", "Turtle.helpText4a": "ನೀವು ಚಲಿಸಿದಾಗ ನಿಮ್ಮ ಲೇಖನಿಯನ್ನು ಕಾಗದದಿಂದ ಮೇಲೆತ್ತುವಂತೆ ಸಹಾಯಮಾಡುವ ಹೊಸ ಬ್ಲಾಕ್ ಇದೆ:", "Turtle.helpText4b": "ಸಣ್ಣ ಹಳದಿ ನಕ್ಷತ್ರವನ್ನು ರಚಿಸಿ, ನಂತರ ಅದರ ಮೇಲೆ ಒಂದು ರೇಖೆಯನ್ನು ಎಳೆಯಿರಿ.", "Turtle.helpText5": "ಒಂದು ನಕ್ಷತ್ರದ ಬದಲು, ಚೌಕದಲ್ಲಿ ಜೋಡಿಸಲಾದ ನಾಲ್ಕು ನಕ್ಷತ್ರಗಳನ್ನು ನೀವು ರಚಿಸಬಹುದೇ?", "Turtle.helpText6": "ಮೂರು ಹಳದಿ ನಕ್ಷತ್ರಗಳು ಮತ್ತು ಒಂದು ಬಿಳಿ ರೇಖೆಯನ್ನು ರಚಿಸಿ.", "Turtle.helpText7": "ನಕ್ಷತ್ರಗಳನ್ನು ರಚಿಸಿರಿ, ನಂತರ ನಾಲ್ಕು ಬಿಳಿ ರೇಖೆಗಳನ್ನು ಎಳೆಯಿರಿ.", "Turtle.helpText8": "360 ಬಿಳಿ ರೇಖೆಗಳನ್ನು ಎಳೆದರೆ ಚಿತ್ರವು ಹುಣ್ಣಿಮೆಯ ಚಂದ್ರನಂತೆ ಕಾಣುತ್ತದೆ.", "Turtle.helpText9": "ಚಂದ್ರನು ಅರ್ಧಚಂದ್ರಾಕಾರವಾಗಲು ನೀವು ಒಂದು ಕಪ್ಪು ವೃತ್ತವನ್ನು ಸೇರಿಸಬಹುದೇ?", "Turtle.helpText10": "ನಿಮಗೆ ಬೇಕಾದ್ದನ್ನು ರಚಿಸಿರಿ. ನಿಮಗೆ ಅನ್ವೇಷಿಸಲು ಹೊಸ ಬ್ಲಾಕ್ ಗಳ ದೊಡ್ಡ ಸಂಖ್ಯೆಯೇ ಇದೆ. ಆನಂದಿಸಿ!", "Turtle.helpText10Reddit": "ಇತರ ಜನರು ಬಿಡಿಸಿದ್ದನ್ನು ನೋಡಲು 'ಚಿತ್ರಶಾಲೆ ನೋಡು' ಗುಂಡಿಯನ್ನು ಬಳಸಿ. ನೀವು ಆಸಕ್ತಿದಾಯಕವಾದ  ಚಿತ್ರವನ್ನು ರಚಿಸಿದ್ದರೆ ಅದನ್ನು ಪ್ರಕಟಿಸಲು 'ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸು' ಗುಂಡಿಯನ್ನು ಬಳಸಿ.", "Turtle.helpToolbox": "ಬ್ಲಾಕ್ ಗಳನ್ನು ನೋಡಲು ವರ್ಗವನ್ನು ಆರಿಸಿ.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "ಪ್ರಾರಂಭದ x", "Movie.y1": "ಪ್ರಾರಂಭದ y", "Movie.x2": "ಕೊನೆಯ x", "Movie.y2": "ಕೊನೆಯ y", "Movie.radius": "ತ್ರಿಜ್ಯ", "Movie.width": "ಅಗಲ", "Movie.height": "ಎತ್ತರ", "Movie.circleTooltip": "ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ಸ್ಥಳದಲ್ಲಿ ಮತ್ತು ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ತ್ರಿಜ್ಯದೊಂದಿಗೆ ವೃತ್ತವನ್ನು ರಚಿಸುತ್ತದೆ", "Movie.circleDraw": "ವೃತ್ತ", "Movie.rectTooltip": "ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ಸ್ಥಳದಲ್ಲಿ ಮತ್ತು ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ಅಗಲ ಮತ್ತು ಎತ್ತರದೊಂದಿಗೆ ಆಯತವನ್ನು ರಚಿಸುತ್ತದೆ.", "Movie.rectDraw": "ಆಯತ", "Movie.lineTooltip": "ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ಅಗಲದೊಂದಿಗೆ ಒಂದು ಬಿಂದುವಿನಿಂದ ಇನ್ನೊಂದಕ್ಕೆ ರೇಖೆಯನ್ನು ಎಳೆಯುತ್ತದೆ.", "Movie.lineDraw": "ರೇಖೆ", "Movie.timeTooltip": "ಅನಿಮೇಷನ್‌ನಲ್ಲಿ ಪ್ರಸ್ತುತ ಸಮಯವನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ(0-100).", "Movie.colourTooltip": "ಲೇಖನಿಯ ಬಣ್ಣವನ್ನು ಬದಲಾಯಿಸುವುದು.", "Movie.setColour": "ಬಣ್ಣವನ್ನು ಬದಲಿಸು", "Movie.submitDisabled": "ನಿಮ್ಮ ಚಲನಚಿತ್ರವು ಚಲಿಸುವುದಿಲ್ಲ. ಆಸಕ್ತಿದಾಯಕವಾದದ್ದನ್ನು ಮಾಡಲು ಬ್ಲಾಕ್ಗಳನ್ನು ಬಳಸಿ. ನಂತರ ನೀವು ನಿಮ್ಮ ಚಲನಚಿತ್ರವನ್ನು ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸಬಹುದು.", "Movie.galleryTooltip": "ಚಲನಚಿತ್ರಗಳ ಚಿತ್ರಶಾಲೆಯನ್ನು ತೆರೆಯಿರಿ.", "Movie.galleryMsg": "ಚಿತ್ರಶಾಲೆಯನ್ನು ನೋಡು", "Movie.submitTooltip": "ನಿಮ್ಮ ಚಲನಚಿತ್ರವನ್ನು ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸಿ.", "Movie.submitMsg": "ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸು", "Movie.helpLayer": "ಹಿನ್ನೆಲೆಯ ವೃತ್ತವನ್ನು ನಿಮ್ಮ ಪ್ರೋಗ್ರಾಂನ ಮೇಲ್ಭಾಗಕ್ಕೆ ಸರಿಸಿ. ಆಗ ಅದು ವ್ಯಕ್ತಿಯ ಹಿಂದೆ ಕಾಣಿಸುತ್ತದೆ.", "Movie.helpText1": "ಈ ವ್ಯಕ್ತಿಯ ಚಿತ್ರವನ್ನು ಬಿಡಿಸಲು ಸರಳ ಆಕಾರಗಳನ್ನು ಬಳಸಿ.", "Movie.helpText2a": "ಈ ಹಂತವು ಒಂದು ಚಲನಚಿತ್ರವಾಗಿದೆ. ವ್ಯಕ್ತಿಯ ತೋಳು ಪರದೆಯಾದ್ಯಂತ ಚಲಿಸಬೇಕೆಂದು ನೀವು ಬಯಸುತ್ತೀರಿ. ಮುನ್ನೋಟವನ್ನು ನೋಡಲು ಪ್ಲೇ ಗುಂಡಿಯನ್ನು ಒತ್ತಿರಿ.", "Movie.helpText2b": "ಚಲನಚಿತ್ರ ಚಾಲನೆಯಲ್ಲಿರುವಾಗ,'time' ಬ್ಲಾಕ್ ನ ಮೌಲ್ಯವು 0 ರಿಂದ 100 ರವರೆಗೆ ಇರುತ್ತದೆ ತೋಳಿನ 'y' ಸ್ಥಾನವು 0 ರಿಂದ ಪ್ರಾರಂಭವಾಗಿ 100 ಕ್ಕೆ ಹೋಗಬೇಕೆಂದು ನೀವು ಬಯಸುವುದರಿಂದ ಇದು ಸುಲಭವಾಗಬೇಕು.", "Movie.helpText3": "'time' ಬ್ಲಾಕ್ 0 ರಿಂದ 100 ರವರೆಗೆ ಎಣಿಸುತ್ತದೆ. ಆದರೆ ಈಗ ನೀವು ಇನ್ನೊಂದು ತೋಳಿನ 'y' ಸ್ಥಾನವು 100 ರಿಂದ ಪ್ರಾರಂಭವಾಗಿ 0 ಕ್ಕೆ ಹೋಗಬೇಕೆಂದು ಬಯಸುತ್ತೀರಿ. ದಿಕ್ಕನ್ನು ತಿರುಗಿಸುವ ಸರಳ ಗಣಿತದ ಸೂತ್ರವನ್ನು ನೀವು ಕಂಡುಹಿಡಿಯಬಹುದೇ?", "Movie.helpText4": "ಕಾಲುಗಳನ್ನು ಅಡ್ಡ ಹಾಯಿಸಲು ಹಿಂದಿನ ಹಂತದಲ್ಲಿ ನೀವು ಕಲಿತದ್ದನ್ನು ಬಳಸಿ.", "Movie.helpText5": "ತೋಳಿನ ಗಣಿತದ ಸೂತ್ರವು ಸಂಕೀರ್ಣವಾಗಿದೆ. ಉತ್ತರ ಇಲ್ಲಿದೆ:", "Movie.helpText6": "ವ್ಯಕ್ತಿಗೆ ಒಂದೆರಡು ಕೈಗಳನ್ನು ನೀಡಿ.", "Movie.helpText7": "ಚಲನಚಿತ್ರದ ಮೊದಲಾರ್ಧದಲ್ಲಿ ಸಣ್ಣ ತಲೆ ರಚಿಸಲು 'ಆಗಿದ್ದರೆ' ಬ್ಲಾಕ್ ಬಳಸಿ. ನಂತರ ಚಲನಚಿತ್ರದ ದ್ವಿತೀಯಾರ್ಧಕ್ಕೆ ದೊಡ್ಡ ತಲೆ ರಚಿಸಿರಿ.", "Movie.helpText8": "ಚಲನಚಿತ್ರದ ಮಧ್ಯದಲ್ಲಿ ಕಾಲುಗಳನ್ನು ಹಿಮ್ಮುಖ ದಿಕ್ಕಿಗೆ ಮಾಡಿ.", "Movie.helpText9": "ವ್ಯಕ್ತಿಯ ಹಿಂದೆ ವಿಸ್ತರಿಸುತ್ತಿರುವ ವೃತ್ತವನ್ನು ರಚಿಸಿರಿ.", "Movie.helpText10": "ನಿಮಗೆ ಬೇಕಾದುದರ ಚಲನಚಿತ್ರ ಮಾಡಿ. ನಿಮಗೆ ಅನ್ವೇಷಿಸಲು ಹೊಸ ಬ್ಲಾಕ್ ಗಳ ದೊಡ್ಡ ಸಂಖ್ಯೆಯೇ ಇದೆ. ಆನಂದಿಸಿ!", "Movie.helpText10Reddit": "ಇತರ ಜನರು ಬಿಡಿಸಿದ್ದನ್ನು ನೋಡಲು 'ಚಿತ್ರಶಾಲೆ ನೋಡು' ಗುಂಡಿಯನ್ನು ಬಳಸಿ. ನೀವು ಆಸಕ್ತಿದಾಯಕವಾದ  ಚಿತ್ರವನ್ನು ಬಿಡಿಸಿದ್ದರೆ ಅದನ್ನು ಪ್ರಕಟಿಸಲು 'ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸು' ಗುಂಡಿಯನ್ನು ಬಳಸಿ.", "Music.playNoteTooltip": "ನಿಗದಿತ ಅವಧಿ ಮತ್ತು ತೀವ್ರತೆಯ ಒಂದು ಸಂಗೀತದ ನೋಟ್ ಅನ್ನು ನುಡಿಸುತ್ತದೆ.", "Music.playNote": "ನುಡಿಸು %1 ನೋಟ್ %2", "Music.restTooltip": "ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ಅವಧಿಯವರೆಗೆ ಕಾಯುತ್ತದೆ.", "Music.restWholeTooltip": "ಒಂದು ಸಂಪೂರ್ಣ ನೋಟ್-ಗಾಗಿ ಕಾಯುತ್ತದೆ.", "Music.rest": "ವಿರಾಮ %1", "Music.setInstrumentTooltip": "ತದ ನಂತರದ ಸಂಗೀತ ನೋಟ್ ಗಳನ್ನು ನುಡಿಸುವಾಗ ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ಸಾಧನಕ್ಕೆ ಬದಲಾಯಿಸುತ್ತದೆ.", "Music.setInstrument": "ವಾದ್ಯ %1 ಗೊತ್ತುಪಡಿಸು", "Music.startTooltip": "'ಪ್ರೋಗ್ರಾಂ ರನ್ ಮಾಡಿ' ಗುಂಡಿಯನ್ನು ಒತ್ತಿದಾಗ ಒಳಗಿರುವ ಬ್ಲಾಕ್ ಗಳನ್ನು ಕಾರ್ಯಗತಗೊಳಿಸುತ್ತದೆ.", "Music.start": "%1 ಅನ್ನು ಕ್ಲಿಕ್ ಮಾಡಿದಾಗ", "Music.pitchTooltip": "ಒಂದು ನೋಟ್(C4 ಎಂದರೆ 7).", "Music.firstPart": "ಮೊದಲ ಭಾಗ", "Music.piano": "ಪಿಯಾನೋ", "Music.trumpet": "ತುತ್ತೂರಿ", "Music.banjo": "ಕೈ ವೀಣೆ", "Music.violin": "ಪಿಟೀಲು", "Music.guitar": "ಗಿಟಾರ್", "Music.flute": "ಕೊಳಲು", "Music.drum": "ಡ್ರಮ್", "Music.choir": "ಗಾಯಕ", "Music.submitDisabled": "ನಿಮ್ಮ ಪ್ರೋಗ್ರಾಂ ನಿಲ್ಲುವವರೆಗೂ ಅದನ್ನು ಚಲಾಯಿಸಿ. ನಂತರ ನೀವು ನಿಮ್ಮ ರೇಖಾಚಿತ್ರವನ್ನು ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸಬಹುದು.", "Music.galleryTooltip": "ಸಂಗೀತದ ಚಿತ್ರಶಾಲೆಯನ್ನು ತೆರೆಯಿರಿ.", "Music.galleryMsg": "ಚಿತ್ರಶಾಲೆಯನ್ನು ನೋಡು", "Music.submitTooltip": "ನಿಮ್ಮ ಸಂಗೀತವನ್ನು ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸಿ.", "Music.submitMsg": "ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸು", "Music.helpUseFunctions": "ನಿಮ್ಮ ಪರಿಹಾರವು ಕಾರ್ಯನಿರ್ವಹಿಸುತ್ತದೆ, ಆದರೆ ನೀವು ಉತ್ತಮವಾಗಿ ಮಾಡಬಹುದು.ಪುನರಾವರ್ತಿತ ಕೋಡ್ ಪ್ರಮಾಣವನ್ನು ಕಡಿಮೆ ಮಾಡಲು ಕಾರ್ಯಘಟಕಗಳನ್ನು ಬಳಸಿ.", "Music.helpUseInstruments": "ಪ್ರತಿ ಸ್ಟಾರ್ಟ್ ಬ್ಲಾಕ್‌ನಲ್ಲಿ ನೀವು ಬೇರೆ ವಾದ್ಯವನ್ನು ಬಳಸಿದರೆ ಸಂಗೀತವು ಉತ್ತಮವಾಗಿ ಧ್ವನಿಸುತ್ತದೆ.", "Music.helpText1": "'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ನ ಮೊದಲ ನಾಲ್ಕು ನೋಟ್ ಗಳನ್ನು ರಚಿಸಿ.", "Music.helpText2a": "'ಕಾರ್ಯಘಟಕ' ನಿಮಗೆ ಬ್ಲಾಕ್ಗಳನ್ನು ಒಟ್ಟಿಗೆ ಗುಂಪು ಮಾಡಲು ಅನುಮತಿಸುತ್ತದೆ, ನಂತರ ಅವುಗಳನ್ನು ಒಂದಕ್ಕಿಂತ ಹೆಚ್ಚು ಬಾರಿ ಚಲಾಯಿಸಬಹುದು.", "Music.helpText2b": "'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ನ ಮೊದಲ ನಾಲ್ಕು ನೋಟ್ ಗಳನ್ನು ನುಡಿಸಲು ಒಂದು ಕಾರ್ಯಘಟಕವನ್ನು ರಚಿಸಿ. ಆ ಕಾರ್ಯಘಟಕವನ್ನು ಎರಡು ಬಾರಿ ಚಲಾಯಿಸಿ. ಯಾವುದೇ ಹೊಸ ನೋಟ್ ಬ್ಲಾಕ್ ಗಳನ್ನು ಸೇರಿಸಬೇಡಿ.", "Music.helpText3": "'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ನ ಮುಂದಿನ ಭಾಗಕ್ಕೆ ಎರಡನೇ ಕಾರ್ಯ  ಘಟಕವನ್ನು ರಚಿಸಿ. ಕೊನೆಯ ನೋಟ್  ದೀರ್ಘವಾಗಿದೆ.", "Music.helpText4": "'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ನ ಮುಂದಿನ ಭಾಗಕ್ಕೆ ಮೂರನೇ ಕಾರ್ಯಘಟಕವನ್ನು ರಚಿಸಿ. ಮೊದಲ ನಾಲ್ಕು ನೋಟ್ ಗಳು  ಲಘುವಾಗಿರುತ್ತವೆ.", "Music.helpText5": "'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ನ ಸಂಪೂರ್ಣ ರಾಗವನ್ನು ಪೂರ್ಣಗೊಳಿಸಿ.", "Music.helpText6a": "ಈ ಹೊಸ ಬ್ಲಾಕ್ ನಿಮಗೆ ಮತ್ತೊಂದು ವಾದ್ಯಕ್ಕೆ ಬದಲಾಯಿಸಲು ಅನುವು ಮಾಡಿಕೊಡುತ್ತದೆ.", "Music.helpText6b": "ಪಿಟೀಲಿನ ಮೂಲಕ ನಿಮ್ಮ ರಾಗವನ್ನು ನುಡಿಸಿ.", "Music.helpText7a": "ಈ ಹೊಸ ಬ್ಲಾಕ್ ನಿಶ್ಯಬ್ಧವಾದ ವಿಳಂಬವನ್ನು ಸೇರಿಸುತ್ತದೆ.", "Music.helpText7b": "ಎರಡು ವಿಳಂಬ ಬ್ಲಾಕ್ಗಳನ್ನು ಹೊಂದಿರುವ ಎರಡನೇ ಸ್ಟಾರ್ಟ್ ಬ್ಲಾಕ್ ಅನ್ನು ರಚಿಸಿ, ಆ ನಂತರವೂ 'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ಅನ್ನು ನುಡಿಸುತ್ತದೆ", "Music.helpText8": "ಪ್ರತಿ ಸ್ಟಾರ್ಟ್ ಬ್ಲಾಕ್ ಎರಡು ಬಾರಿ 'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ಅನ್ನು ನುಡಿಸಬೇಕು.", "Music.helpText9": "ಪ್ರತಿಯೊಂದೂ ಎರಡು ಬಾರಿ 'ಫ್ರೆರೆ ಜಾಕ್ವೆಸ್' ಆಡುವ ನಾಲ್ಕು ಸ್ಟಾರ್ಟ್ ಬ್ಲಾಕ್‌ಗಳನ್ನು ರಚಿಸಿ. ಸರಿಯಾದ ಸಂಖ್ಯೆಯ ವಿಳಂಬ ಬ್ಲಾಕ್‌ಗಳನ್ನು ಸೇರಿಸಿ.", "Music.helpText10": "ನಿಮಗೆ ಬೇಕಾದ್ದನ್ನು ರಚಿಸಿರಿ. ನಿಮಗೆ ಅನ್ವೇಷಿಸಲು ಹೊಸ ಬ್ಲಾಕ್ ಗಳ ದೊಡ್ಡ ಸಂಖ್ಯೆಯೇ ಇದೆ. ಆನಂದಿಸಿ!", "Music.helpText10Reddit": "ಇತರ ಜನರು ರಚಿಸಿದ್ದನ್ನು ನೋಡಲು 'ಚಿತ್ರಶಾಲೆ ನೋಡು' ಗುಂಡಿಯನ್ನು ಬಳಸಿ. ನೀವು ಆಸಕ್ತಿದಾಯಕವಾದ  ಚಿತ್ರವನ್ನು ರಚಿಸಿದ್ದರೆ ಅದನ್ನು ಪ್ರಕಟಿಸಲು 'ಚಿತ್ರಶಾಲೆಗೆ ಸಲ್ಲಿಸು' ಗುಂಡಿಯನ್ನು ಬಳಸಿ.", "Pond.scanTooltip": "ಶತ್ರುಗಳಿಗಾಗಿ ಕಣ್ಣುಹಾಯಿಸಿ. ದಿಕ್ಕನ್ನು ನಿರ್ದಿಷ್ಟಪಡಿಸಿ (0-360). ಆ ದಿಕ್ಕಿನಲ್ಲಿರುವ ಹತ್ತಿರದ ಶತ್ರುವಿನ ದೂರವನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ. ಯಾವುದೇ ಶತ್ರು ಕಂಡುಬಂದಿಲ್ಲದಿದ್ದರೆ ಅನಂತವನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ.", "Pond.cannonTooltip": "ಫಿರಂಗಿಯನ್ನು ಹಾರಿಸಿ. ದಿಕ್ಕು(0-360) ಮತ್ತು ವ್ಯಾಪ್ತಿ(0-70) ಯನ್ನು ನಿರ್ದಿಷ್ಟಪಡಿಸಿ.", "Pond.swimTooltip": "ಮುಂದಕ್ಕೆ ಈಜಿ. ದಿಕ್ಕನ್ನು ನಿರ್ದಿಷ್ಟಪಡಿಸಿ (0-360).", "Pond.stopTooltip": "ಈಜುವುದನ್ನು ನಿಲ್ಲಿಸಿ. ಆಟಗಾರನು ನಿಧಾನವಾಗಿ  ನಿಲ್ಲುತ್ತಾನೆ.", "Pond.healthTooltip": "ಆಟಗಾರನ ಪ್ರಸ್ತುತ ಆರೋಗ್ಯ ಸ್ಥಿತಿಯನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ (0 ಸತ್ತಿದೆ, 100 ಆರೋಗ್ಯಕರವಾಗಿದೆ).", "Pond.speedTooltip": "ಆಟಗಾರನ ಪ್ರಸ್ತುತ ವೇಗವನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ (0 ನಿಲ್ಲಿಸಲಾಗಿದೆ, 100 ಪೂರ್ಣ ವೇಗವಾಗಿದೆ).", "Pond.locXTooltip": "ಆಟಗಾರನ X ನಿರ್ದೇಶಾಂಕವನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ (0 ಎಡ ಅಂಚು, 100 ಬಲ ಅಂಚು).", "Pond.locYTooltip": "ಆಟಗಾರನ Y ನಿರ್ದೇಶಾಂಕವನ್ನು ಹಿಂತಿರುಗಿಸುತ್ತದೆ (0 ಕೆಳಗಿನ ಅಂಚು, 100 ಮೇಲಿನ ಅಂಚು).", "Pond.logTooltip": "ನಿಮ್ಮ ಬ್ರೌಸರ್‌ನ ಕನ್ಸೋಲ್‌ಗೆ ಸಂಖ್ಯೆಯನ್ನು ಮುದ್ರಿಸುತ್ತದೆ.", "Pond.docsTooltip": "ಭಾಷಾ ದಸ್ತಾವೇಜನ್ನು ಪ್ರದರ್ಶಿಸಿ.", "Pond.documentation": "ದಾಖಲೀಕರಣ", "Pond.playerName": "ಆಟಗಾರ", "Pond.targetName": "ಗುರಿ", "Pond.pendulumName": "ಲೋಲಕ", "Pond.scaredName": "ಹೆದರಿದ", "Pond.helpUseScan": "ನಿಮ್ಮ ಪರಿಹಾರವು ಕಾರ್ಯನಿರ್ವಹಿಸುತ್ತದೆ, ಆದರೆ ನೀವು ಉತ್ತಮವಾಗಿ ಮಾಡಬಹುದು. ಫಿರಂಗಿಗೆ ಎಷ್ಟು ದೂರ ಶೂಟ್ ಮಾಡಬೇಕೆಂದು ಹೇಳಲು 'scan' ಬಳಸಿ.", "Pond.helpText1": "ಗುರಿಯನ್ನು ಹೊಡೆಯಲು 'cannon' ಆದೇಶವನ್ನು ಬಳಸಿ. ಮೊದಲ ನಿಯತಾಂಕವು ಕೋನ, ಎರಡನೆಯ ನಿಯತಾಂಕವು ವ್ಯಾಪ್ತಿ. ಸರಿಯಾದ ಸಂಯೋಜನೆಯನ್ನು ಕಂಡುಕೊಳ್ಳಿ.", "Pond.helpText2": "ಈ ಗುರಿಯನ್ನು ಹಲವು ಬಾರಿ ಹೊಡೆಯಬೇಕಾಗಿದೆ. ಏನನ್ನಾದರೂ ಅನಿರ್ದಿಷ್ಟವಾಗಿ ಮಾಡಲು 'while (true)' ಲೂಪ್ ಅನ್ನು ಬಳಸಿ.", "Pond.helpText3a": "ಈ ಎದುರಾಳಿಯು ಹಿಂದಕ್ಕೆ ಮತ್ತು ಮುಂದಕ್ಕೆ ಚಲಿಸುವುದರಿಂದ, ಹೊಡೆಯಲು ಕಷ್ಟವಾಗುತ್ತದೆ. 'scan' ಆದೇಶವು ನಿರ್ದಿಷ್ಟಪಡಿಸಿದ ದಿಕ್ಕಿನಲ್ಲಿ ಎದುರಾಳಿಯ ನಿಖರವಾದ ವ್ಯಾಪ್ತಿಯನ್ನು ನೀಡುತ್ತದೆ.", "Pond.helpText3b": "'cannon' ಆದೇಶವು ಕರಾರುವಾಕ್ಕಾಗಿ ಹಾರಿಸಲು ಬೇಕಾದ ನಿಖರವಾದ ವ್ಯಾಪ್ತಿ ಇದಾಗಿದೆ.", "Pond.helpText4": "ಈ ಎದುರಾಳಿಯು ಫಿರಂಗಿಯನ್ನು ಬಳಸಲು ತುಂಬಾ ದೂರದಲ್ಲಿದ್ದಾರೆ(ಫಿರಂಗಿ 70 ಮೀಟರ್ ವ್ಯಾಪ್ತಿ ಹೊಂದಿದೆ). ಬದಲಾಗಿ,  'swim' ಆದೇಶವನ್ನು ಬಳಸಿ ಎದುರಾಳಿಯ ಕಡೆಗೆ ಈಜಲು ಪ್ರಾರಂಭಿಸಿ ಮತ್ತು ಎದುರಾಳಿಗೆ ಅಪ್ಪಳಿಸಿ.", "Pond.helpText5": "ಈ ಎದುರಾಳಿ ಕೂಡ ಫಿರಂಗಿಯನ್ನು ಬಳಸಲು ತುಂಬಾ ದೂರದಲ್ಲಿದ್ದಾರೆ. ಆದರೆ ಘರ್ಷಣೆಯಿಂದ ಬದುಕುಳಿಯಲು ನೀವು ತುಂಬಾ ದುರ್ಬಲರಾಗಿದ್ದೀರಿ. ನಿಮ್ಮ ಸಮತಲ ಸ್ಥಾನವು 50ಕ್ಕಿಂತ ಕಡಿಮೆಯಿದ್ದರೆ ಎದುರಾಳಿಯ ಕಡೆಗೆ ಈಜಿಕೊಳ್ಳಿ. ನಂತರ 'stop' ಮತ್ತು ಫಿರಂಗಿಯನ್ನು ಬಳಸಿ.", "Pond.helpText6": "ಈ ಎದುರಾಳಿ ಹೊಡೆದಾಗ ಅದು ದೂರ ಸರಿಯುತ್ತದೆ. ಅದು ವ್ಯಾಪ್ತಿಯಿಂದ ಹೊರಗಿದ್ದರೆ (70 ಮೀಟರ್) ಅದರ ಕಡೆಗೆ ಈಜಿಕೊಳ್ಳಿ.", "Gallery": "ಚಿತ್ರಶಾಲೆ"}