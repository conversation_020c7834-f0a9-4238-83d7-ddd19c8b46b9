{"@metadata": {"authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mguix", "Micobu"]}, "Games.name": "Blockly Games", "Games.puzzle": "Trencaclosques", "Games.maze": "Laberint", "Games.bird": "Ocell", "Games.turtle": "Tortuga", "Games.movie": "Pel·lícula", "Games.music": "Música", "Games.pondTutor": "<PERSON><PERSON>", "Games.pond": "La Bassa", "Games.linesOfCode1": "Has solucionat aquest nivell amb 1 línia de JavaScript:", "Games.linesOfCode2": "Has solucionat aquest nivell amb %1 línies de JavaScript:", "Games.nextLevel": "Estàs preparat pel nivell %1?", "Games.finalLevel": "Estàs preparat pel següent repte?", "Games.submitTitle": "Títol:", "Games.linkTooltip": "Desa i enllaça als blocs.", "Games.runTooltip": "Executa el programa que has escrit", "Games.runProgram": "Executa el programa", "Games.resetTooltip": "Atura el programa i reinicia el nivell.", "Games.resetProgram": "Reinicialitza", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Lògica", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matemàtiques", "Games.catText": "Text", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "Color", "Games.catVariables": "Variables", "Games.catProcedures": "Procediments", "Games.httpRequestError": "Hi ha hagut un problema amb la sol·licitud.", "Games.linkAlert": "Comparteix els teus blocs amb aquest enllaç: %1", "Games.hashError": "<PERSON> sentim, '%1' no es correspon amb cap fitxer desat de Blockly.", "Games.xmlError": "No s'ha pogut carregar el teu fitxer desat.  Potser va ser creat amb una versió diferent de <PERSON>ly?", "Games.submitted": "Gràcies per aquest programa! Si agrada al nostre equip de micos ensinistrats te'l publicaran a la galeria en un parell de dies.", "Games.listVariable": "llista", "Games.textVariable": "text", "Games.breakLink": "Un cop comencis a editar el JavaScript no podràs tornar a editar blocs. Et sembla bé?", "Games.blocks": "Blocs", "Games.congratulations": "Felicitats!", "Games.helpAbort": "Aquest nivell és extremadament difícil. El vols deixar i anar al següent joc? Sempre podràs tornar-hi més tard.", "Index.clear": "Eliminar totes les solucions?", "Index.subTitle": "Jocs pels programadors del futur.", "Index.moreInfo": "Info per als educadors...", "Index.startOver": "Vols tornar a començar?", "Index.clearData": "Es<PERSON><PERSON> totes les dades", "Puzzle.animal1": "Ànec", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Bec", "Puzzle.animal1HelpUrl": "https://ca.wikipedia.org/wiki/Ànecs", "Puzzle.animal2": "Gat", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://ca.wikipedia.org/wiki/Gat", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "mel", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://ca.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_la_mel", "Puzzle.animal4": "cargol", "Puzzle.animal4Trait1": "Closca", "Puzzle.animal4Trait2": "<PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://ca.wikipedia.org/wiki/Caragol_de_terra", "Puzzle.picture": "foto:", "Puzzle.legs": "potes:", "Puzzle.legsChoose": "triar...", "Puzzle.traits": "trets:", "Puzzle.error0": "Perfecte!\nTots els blocs de %1 són correctes.", "Puzzle.error1": "Gairebé! Una bloc és incorrecte.", "Puzzle.error2": "%1 blocs són errònies.", "Puzzle.tryAgain": "El bloc de subratllat no és correcta.\nContinuï provant.", "Puzzle.checkAnswers": "Comprovar les respostes", "Puzzle.helpText": "Per a cada animal (verd), adjunta-hi la seva fotografia, tria quantes potes té i llista'n els seus trets.", "Maze.moveForward": "mou <PERSON><PERSON>t", "Maze.turnLeft": "gira esquerra", "Maze.turnRight": "gira dreta", "Maze.doCode": "fes", "Maze.helpIfElse": "Les declaracions 'si-si no' faran fer una cosa o una altra.", "Maze.pathAhead": "al davant", "Maze.pathLeft": "si camí a l'esquerra", "Maze.pathRight": "si camí a la dreta", "Maze.repeatUntil": "repeteix fins al", "Maze.moveForwardTooltip": "<PERSON>u el Pegman endavant un espai.", "Maze.turnTooltip": "Gira el Pegman a l'esquerra o a la dreta 90 graus.", "Maze.ifTooltip": "Si hi ha un camí en la direcció especificada, llavors fes algunes accions.", "Maze.ifelseTooltip": "Si hi ha un camí en la direcció especificada, llavors fes el primer bloc d'accions. Si no, fes el segon bloc d'accions.", "Maze.whileTooltip": "Repeteix les accions de dins del bloc fins arribar al punt final.", "Maze.capacity0": "Et resten %0 blocs.", "Maze.capacity1": "Et resten %1 blocs.", "Maze.capacity2": "Et resten %2 blocs.", "Maze.runTooltip": "Fa que el jugador faci el que diuen els blocs.", "Maze.resetTooltip": "Col·loca el jugador a l'inici del laberint.", "Maze.helpStack": "Apila un parell de blocs 'mou endavant' junts per ajudar-me a arribar a la meta.", "Maze.helpOneTopBlock": "En aquest nivell, has d'apilar junts tots els blocs a l'espai de treball blanc.", "Maze.helpRun": "Executeu el vostre programa per veure què passa.", "Maze.helpReset": "El vostre programa no ha resolt el laberint. Cliqueu 'Reinicialitza' i torneu a intentar-ho.", "Maze.helpRepeat": "Els ordinadors tenen una memòria limitada. Arriba al final d'aquest camí utilitzant només dos blocs. Utilitza 'repeteix' per a executar un bloc més d'una vegada.", "Maze.helpCapacity": "Heu usat tots els blocs per aquest nivell. Per crear un nou bloc, primer heu d'esborrar un bloc existent.", "Maze.helpRepeatMany": "Podeu encabit més d'un bloc dins d'un bloc 'repeteix'.", "Maze.helpSkins": "Escolliu el vostre jugador favorit en aquest menú.", "Maze.helpIf": "Una condició 'si' farà fer alguna cosa només si la condició és certa. Prova de girar a l'esquerra si hi ha un camí a l'esquerra.", "Maze.helpMenu": "Feu clic a %1 en el bloc 'si' per canviar-ne la condició.", "Maze.helpWallFollow": "Pots resoldre aquest complicat laberint? Prova de seguir la paret de l'esquerra. Només programadors avançats!", "Bird.noWorm": "no té cuc", "Bird.heading": "dire<PERSON>ó", "Bird.noWormTooltip": "La condició quan l'ocell no ha agafat el cuc.", "Bird.headingTooltip": "Fes que es mogui en la direcció de l'angle: 0 és a la dreta, 90 és amunt, etc.", "Bird.positionTooltip": "x i y marquen la posició de l'ocell. Quan x=0 l'ocell és vora el marge esquerre. Quan x=100 és vora el marge dret. Quan y=0 l'ocell és a sota de tot i quan y=100 és a dalt de tot.", "Bird.helpHeading": "Canvia l'angle de la direcció per fer que l'ocell agafi el cuc i aterri al seu niu.", "Bird.helpHasWorm": "Fes servir aquest bloc per anar en una direcció si té el cuc, o en una altra de diferent si no té el cuc.", "Bird.helpX": "'x' és la teva posició horitzontal actual. Fes servir aquest bloc per anar en una direcció si 'x' és més petita que un número i, si no, cap a una altra direcció.", "Bird.helpElse": "Clica la icona per modificar el bloc 'si'.", "Bird.helpElseIf": "Aquest nivell vol tant un bloc 'si no, si' com un bloc 'i si no'.", "Bird.helpAnd": "El bloc 'i' és cert només si totes dues entrades són certes.", "Bird.helpMutator": "Arrossega un bloc 'si no' dins del bloc 'si'.", "Turtle.moveTooltip": "Mou la tortuga endavant o enrere segons la quantitat especificada.", "Turtle.moveForward": "moure endavant", "Turtle.moveBackward": "moure enrere", "Turtle.turnTooltip": "Gira la tortuga cap a l'esquerra o cap a la dreta el nombre especificat de graus.", "Turtle.turnRight": "gira cap a la dreta", "Turtle.turnLeft": "gira cap a l'esquerra", "Turtle.widthTooltip": "Canvia el gruix de la ploma.", "Turtle.setWidth": "canvia el gruix a", "Turtle.colourTooltip": "Canvia el color de la ploma.", "Turtle.setColour": "canvia el color a", "Turtle.penTooltip": "Aixeca o abaixa la ploma, per acabar o començar a dibuixar.", "Turtle.penUp": "ploma amunt", "Turtle.penDown": "ploma avall", "Turtle.turtleVisibilityTooltip": "Fa que la tortuga (cercle i fletxa) sigui visible o invisible.", "Turtle.hideTurtle": "amaga tortuga", "Turtle.showTurtle": "mostra tortuga", "Turtle.printHelpUrl": "https://ca.wikipedia.org/wiki/Impressió", "Turtle.printTooltip": "Dibuixa text en la direcció de la tortuga a partir de la seva posició.", "Turtle.print": "imprimir", "Turtle.fontHelpUrl": "https://ca.wikipedia.org/wiki/Font_(tipografia)", "Turtle.fontTooltip": "Canvia la font usada pel bloc d'impressió.", "Turtle.font": "font", "Turtle.fontSize": "mida de lletra", "Turtle.fontNormal": "normal", "Turtle.fontBold": "negreta", "Turtle.fontItalic": "cursiva", "Turtle.submitDisabled": "Fes córrer el programa fins que s'aturi. Llavors pots enviar el teu dibuix a la galeria.", "Turtle.galleryTooltip": "Obrir la galeria de dibuixos.", "Turtle.galleryMsg": "Mirar <PERSON>", "Turtle.submitTooltip": "Envia aquest dibuix a la galeria.", "Turtle.submitMsg": "Enviar a la Galeria", "Turtle.helpUseLoop": "La teva solució funciona però encara ho pots fer més bé.", "Turtle.helpUseLoop3": "Dibuixa la forma només amb tres blocs.", "Turtle.helpUseLoop4": "Dibuixa l'estrella només amb quatre blocs.", "Turtle.helpText1": "Crea un programa que dibuixi un quadrat.", "Turtle.helpText2": "Canvia el programa per dibuixar un pentàgon en comptes d'un quadrat.", "Turtle.helpText3a": "Hi ha un bloc nou que et permetrà de canviar el color:", "Turtle.helpText3b": "Dibuixa una estrella groga.", "Turtle.helpText4a": "Hi ha un bloc nou que et permetrà aixecar el llapis del paper quan et moguis:", "Turtle.helpText4b": "Dibuixa una estrella groga petita i després dibuixa una lína al cim seu.", "Turtle.helpText5": "En comptes d'una estrella, podries dibuixar quatre estrelles arrenglerades en forma de quadrat?", "Turtle.helpText6": "Dibuixa tres estrelles grogues i una línia blanca.", "Turtle.helpText7": "Dibuixa les estrelles i després dibuixa quatre línies blanques.", "Turtle.helpText8": "Dibuixant 360 línies blanques semblarà la lluna plena.", "Turtle.helpText9": "Pots afegir-hi un cercle negre de manera que la lluna es torni quart creixent?", "Turtle.helpText10": "Dibuixa el que et sembli. Ara tens molts de blocs nous per explorar. Passa't-ho bé!", "Turtle.helpText10Reddit": "Fes servir el botó \"Mirar Galeria\" per veure què ha dibuixat l'altra gent. Si dibuixes alguna cosa interessant fes servir el botó 'Enviar a la Galeria' per publicar-la.", "Turtle.helpToolbox": "Tria una categoria per veure'n els blocs.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x inicial", "Movie.y1": "y inicial", "Movie.x2": "x final", "Movie.y2": "y final", "Movie.radius": "radi", "Movie.width": "amplada", "Movie.height": "alçada", "Movie.circleTooltip": "Dibuixa un cercle a la posició indicada amb el radi indicat.", "Movie.circleDraw": "cercle", "Movie.rectTooltip": "Dibuixa un rectangle a la posició indicada i amb l'amplada i l'alçada especificades.", "Movie.rectDraw": "rectangle", "Movie.lineTooltip": "Dibuixa una línia d'un punt a l'altre amb el gruix especificat.", "Movie.lineDraw": "línia", "Movie.timeTooltip": "Retorna el moment actual de l'animació (0-100).", "Movie.colourTooltip": "Canvia el color del llàpis.", "Movie.setColour": "estableix color", "Movie.submitDisabled": "La teva pel·lícula no es mou. Fes servir els blocs per fer alguna cosa interessant. Llavors pots enviar la pel·lícula a la galeria.", "Movie.galleryTooltip": "Obre la galeria de pel·lícules.", "Movie.galleryMsg": "Mirar <PERSON>", "Movie.submitTooltip": "Envia la pel·licula a la galeria.", "Movie.submitMsg": "Enviar a Galeria", "Movie.helpLayer": "Mou el cercle de fons a l'inici del programa. Llavors apareixerà darrere de la persona.", "Movie.helpText1": "Fes servir formes simples per dibuixar la persona.", "Movie.helpText2a": "Aquest nivell és una pel·lícula. Cal que el braç de la persona es mogui per la pantalla. Prem el botó Reproduir per previsualitzar-ho.", "Movie.helpText2b": "Mentre la pel·lícula es reprodueix el valor del bloc 'temps' compta de 0 a 100. Com que vols que la posició 'y' del braç comenci a 0 i acabi a 100 això hauria de ser fàcil.", "Movie.helpText3": "El bloc 'temps' compta de 0 a 100. Però ara cal que la posició 'y' de l'altre braç comenci a 100 i vagi fins a 0. Podries dir una fórmula matemàtica simple que ho giri al revés?", "Movie.helpText4": "Fes servir el que hagis après al nivell anterior per fer cames que es creuin.", "Movie.helpText5": "La fórmula matemàtica pel braç és complicada. Aquí en tens la resposta:", "Movie.helpText6": "Afegeix un parell de mans a la persona.", "Movie.helpText7": "Fes servir el bloc 'si' per dibuixar-li un cap petit per la primera meitat de la pel·lícula. Llavors dibuixa-li un cap gros per la segona meitat de la pel·lícula.", "Movie.helpText8": "Fes que les cames canviïn de direcció a la meitat de la pel·lícula.", "Movie.helpText9": "Dibuixa una rodona que es vagi engrandint darrere de la persona.", "Movie.helpText10": "Fes una pel·lícula del que més t'agradi. Ara tens molts blocs nous per explorar. Passa't-ho bé!", "Movie.helpText10Reddit": "Fes servir el botó 'Veure Galeria' per veure pel·lícules que ha fet altra gent. Si has fet una pel·lícula interessant fes servir el botó 'Envia a Galeria' per publicar-la.", "Music.playNoteTooltip": "Toca una nota musical de la durada i to especificats.", "Music.playNote": "toca %1 nota %2", "Music.restTooltip": "Espera la durada especificada.", "Music.restWholeTooltip": "Espera la durada d'una nota rodona.", "Music.rest": "espera %1", "Music.setInstrumentTooltip": "Commuta a l'instrument especificat per tocar les notes musicals següents.", "Music.setInstrument": "estableix instrument %1", "Music.startTooltip": "Executa els blocs de dins quan es cliqui el botó 'Executar Programa'.", "Music.start": "quan es cliqui %1", "Music.pitchTooltip": "Nota (el Do és el 7).", "Music.firstPart": "primera part", "Music.piano": "piano", "Music.trumpet": "trompeta", "Music.banjo": "banjo", "Music.violin": "violí", "Music.guitar": "guitarra", "Music.flute": "flauta", "Music.drum": "tambor", "Music.choir": "cor", "Music.submitDisabled": "Executa el programa fins que que s'aturi. En acabat pots enviar la música a la galeria.", "Music.galleryTooltip": "Obre la galeria de música.", "Music.galleryMsg": "Mirar <PERSON>", "Music.submitTooltip": "Envia la música a la galeria.", "Music.submitMsg": "Enviar a Galeria", "Music.helpUseFunctions": "Aquesta solució funciona però ho podries millorar. Fes servir funcions per estalviar la repetició de codi.", "Music.helpUseInstruments": "La música sonarà més bé si fas servir un instrument diferent a cada inici de bloc.", "Music.helpText1": "Compon les primeres quatre notes de '<PERSON><PERSON>'.", "Music.helpText2a": "Una 'funció' permet d'agrupar blocs i en acabat executar-los més d'un cop.", "Music.helpText2b": "Crea una funció per tocar les primeres quatre notes de 'Fr<PERSON> Jacques'. Executa-la dos cops sense afegir cap altre bloc amb notes.", "Music.helpText3": "Crea una segona funció pel segon tros de 'Fr<PERSON> Jacques'. L'última nota és més llarga.", "Music.helpText4": "Crea una tercera funció per al següent tros de 'Frère Jacques'. Les primeres quatre notes són més curtes.", "Music.helpText5": "Acaba de fer la tonada sencera de '<PERSON><PERSON>'.", "Music.helpText6a": "Aquest bloc et permet de canviar d'instrument.", "Music.helpText6b": "Toca la cançó amb un violí.", "Music.helpText7a": "Aquest bloc nou afegeixes una pausa en silenci.", "Music.helpText7b": "Crea un segon bloc d'inici que tingui dos blocs de retard i després també toqui 'Fr<PERSON> Jacques'.", "Music.helpText8": "Cada bloc d'inici hauria de tocar '<PERSON><PERSON>' dos cops.", "Music.helpText9": "Crea quatre blocs d'inici de manera que cadascun toqui '<PERSON><PERSON>' dos cops. Afegeix el nombre correcte de blocs de retard.", "Music.helpText10": "Compon el que et sembli. Tens una bona colla de blocs nous per explorar. Passa't-ho bé!", "Music.helpText10Reddit": "Fes servir el botó 'Mirar Galeria' per veure què ha compost l'altra gent. Si compons res d'interessant fes servir el botó 'Enviar a la Galeria' per publicar-ho.", "Pond.scanTooltip": "Busca els enemics. Especifica-li una direcció (0-360). Torna la distància a l'enemic més proper en aquella direcció. Torna Infinit si no troba cap enemic.", "Pond.cannonTooltip": "Dispara el canó. Especifica-li la direcció (0-360) i l'abast (0-70).", "Pond.swimTooltip": "Neda endavant. Especifica-li una direcció (0-360).", "Pond.stopTooltip": "Para de nedar. El ninot frenarà fins a aturar-se.", "Pond.healthTooltip": "Torna la salut actual del ninot (0 si és mort, 100 si és ple de salut).", "Pond.speedTooltip": "Torna la velocitat actual del ninot (0 si és aturat, 100 si és amb velocitat màxima)", "Pond.locXTooltip": "Torna al coordenada X del ninot (0 és la banda esquerra i 100 és la banda dreta).", "Pond.locYTooltip": "Torna la coordenada Y del ninot (0 és la banda de sota i 100 és la de sobre).", "Pond.logTooltip": "Imprimeix un nombre a la consola del navegador.", "Pond.docsTooltip": "Mostra la documentació del llenguatge.", "Pond.documentation": "Documentació", "Pond.playerName": "<PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Pèndol", "Pond.scaredName": "Atemorit", "Pond.helpUseScan": "Aquesta solució funciona però ho pots millorar. Fes servir 'buscar' per dir al canó amb quin abast ha de tirar.", "Pond.helpText1": "Fes servir l'ordre 'canó' per tocar l'objectiu. El primer paràmetre és l'angle i el segon l'abast. Troba-li la combinació adient.", "Pond.helpText2": "Aquest objectiu s'ha de tocar molts de cops. Fes servir un bucle 'mentre (cert)' per fer una cosa indefinidament.", "Pond.helpText3a": "Aquest oponent es mou endavant i enrere, fent que sigui difícil d'encertar. L'expressió 'buscar' torna la distància exacta de l'oponent en la direcció especificada.", "Pond.helpText3b": "Aquesta distància és justament l'abast que cal a la comanda 'canó' per disparar amb precisió.", "Pond.helpText4": "Aquest oponent és massa lluny per fer servir el canó (limitat a 70 metres). Fes servir, en canvi, la comanda 'nedar' per nedar cap a l'oponent i xocar-hi.", "Pond.helpText5": "Aquest oponent és massa lluny per fer servir el canó. Però ets massa feble per sobreviure a un xoc. Neda cap a ell mentre la teva posició sigui menys de 50. Llavors atura't amb 'Aturar' i fes servir el canó.", "Pond.helpText6": "Aquest oponent se n'anirà quan el toquis. Neda cap a ell si és fora d'abast (70 metres).", "Gallery": "Galeria"}