{"@metadata": {"authors": ["Csega", "Dj", "<PERSON><PERSON>", "Horvolgyinoemi", "Kiscsillag", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Urbalazs", "ViDam", "아라"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "<PERSON><PERSON><PERSON>", "Games.maze": "Lab<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON>", "Games.turtle": "Teknőc", "Games.movie": "Film", "Games.music": "<PERSON><PERSON>", "Games.pondTutor": "<PERSON><PERSON> g<PERSON>", "Games.pond": "<PERSON><PERSON>", "Games.linesOfCode1": "Egyetlen sor JavaScript kóddal megoldottad a feladatot:", "Games.linesOfCode2": "%1 sor JavaScript kóddal oldottad meg a feladatot.", "Games.nextLevel": "Készen állsz a(z) %1. feladatra?", "Games.finalLevel": "Készen állsz a következő kihívásra?", "Games.submitTitle": "Cím:", "Games.linkTooltip": "Mentés és hivatkozás a blokkokra.", "Games.runTooltip": "Futtasd a programod!", "Games.runProgram": "Program futtatása", "Games.resetTooltip": "Program leállítása, és a szint visszaállítása.", "Games.resetProgram": "Visszaállítás", "Games.help": "S<PERSON>gó", "Games.catLogic": "Logika", "Games.catLoops": "<PERSON><PERSON><PERSON><PERSON>", "Games.catMath": "<PERSON><PERSON>", "Games.catText": "Szövegkezelés", "Games.catLists": "Listakezelés", "Games.catColour": "<PERSON><PERSON><PERSON><PERSON>", "Games.catVariables": "Változók", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "A kéréssel kapcsolatban probléma merült fel.", "Games.linkAlert": "Ezzel a hivatkozással tudod megosztani a programodat:\n\n%1", "Games.hashError": "Sajnos a '%1' hivatkozás nem tartozik egyetlen programhoz sem.", "Games.xmlError": "A programodat nem lehet betölteni.  Elképzelhető, hogy a Blockly egy másik verziójában készült?", "Games.submitted": "Köszönjük a programodat! Ha a kiképzett majmokból álló c<PERSON>patunknak is tetszik, akkor pár napon belül közzéteszik a galériánkban.", "Games.listVariable": "lista", "Games.textVariable": "szöveg", "Games.breakLink": "Ha elkezded a JavaScriptet szerkeszteni akkor később nem léphetsz vissza a blokkok szerkesztésébe. Ezt akarod?", "Games.blocks": "Blokkok", "Games.congratulations": "Gratulálok!", "Games.helpAbort": "Ez a szint különösen nehéz. Szeretnél a következő játékra ugrani? Bármikor megpróbálhatod újból!", "Index.clear": "Biztos hogy törölni szeretnéd az összes megoldásod?", "Index.subTitle": "A jövő programozóinak játékai.", "Index.moreInfo": "Információk oktatóknak...", "Index.startOver": "Szeretnéd az elejéről kezdeni?", "Index.clearData": "Adatok törlése", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "Tollak", "Puzzle.animal1Trait2": "Csőr", "Puzzle.animal1HelpUrl": "http://hu.wikipedia.org/wiki/T%C5%91k%C3%A9s_r%C3%A9ce", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "Bajusz", "Puzzle.animal2Trait2": "Szőrme", "Puzzle.animal2HelpUrl": "https://hu.wikipedia.org/wiki/Macska", "Puzzle.animal3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "M<PERSON>z", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "http://hu.wikipedia.org/wiki/H%C3%A1zim%C3%A9h", "Puzzle.animal4": "Csiga", "Puzzle.animal4Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON>lka", "Puzzle.animal4HelpUrl": "http://hu.wikipedia.org/wiki/Csig%C3%A1k", "Puzzle.picture": "kép:", "Puzzle.legs": "lábak:", "Puzzle.legsChoose": "v<PERSON><PERSON><PERSON>…", "Puzzle.traits": "jellemzők:", "Puzzle.error0": "Tökéletes!\nMind a %1 blokk a helyén van.", "Puzzle.error1": "Majdnem si<PERSON>ült! Egy blokk rossz helyre került.", "Puzzle.error2": "%1 blokk rossz helyre került.", "Puzzle.tryAgain": "A kiemelt blokk nincs a helyén.\nPróbáld újraǃ", "Puzzle.checkAnswers": "Válaszok ellenőrzése", "Puzzle.helpText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>), ka<PERSON><PERSON><PERSON> a <PERSON><PERSON><PERSON><PERSON>, á<PERSON><PERSON><PERSON><PERSON> be a lábainak számát, , <PERSON><PERSON> egy<PERSON><PERSON>.", "Maze.moveForward": "<PERSON><PERSON>", "Maze.turnLeft": "<PERSON><PERSON><PERSON> balra", "Maze.turnRight": "<PERSON><PERSON><PERSON>", "Maze.doCode": "végrehajt", "Maze.helpIfElse": "A Ha-egyébként feltétel esetén vagy az egyik, vagy a másik utasítás-csoport kerül végrehajtásra.", "Maze.pathAhead": "Ha szabad az út előtted", "Maze.pathLeft": "Ha szabad az út balra", "Maze.pathRight": "Ha szabad az út jobbra", "Maze.repeatUntil": "<PERSON><PERSON><PERSON><PERSON> amíg el<PERSON>", "Maze.moveForwardTooltip": "A figura előre lép egy me<PERSON>őt.", "Maze.turnTooltip": "A figura 90 fokot fordul balra, vagy jobbra.", "Maze.ifTooltip": "Ha szabad az út a megadott irányban, akkor végrehajtja az utasításokat.", "Maze.ifelseTooltip": "Ha szabad az út a megadott irányban, akkor végrehajtja az első blokkban megadott utasításokat. Egyébként a második blokkban szereplő utasításokat hajtja végre.", "Maze.whileTooltip": "A beágyazott utasításokat hajtja végre a cél eléréséig.", "Maze.capacity0": "<PERSON><PERSON>sz fel több blokkot.", "Maze.capacity1": "Még %1 blokkot használhatsz fel.", "Maze.capacity2": "Még %2 blokkot használhatsz fel.", "Maze.runTooltip": "A figura végrehajtja a blokkokkal megadott programot.", "Maze.resetTooltip": "A labirintus kezdő pozíciójába állítja a figurát.", "Maze.helpStack": "Seg<PERSON>ts eljuttatni a figur<PERSON>t a c<PERSON>, kapcsolj össze néhány '<PERSON>j <PERSON>' blokkot!", "Maze.helpOneTopBlock": "Ezen a szinten össze kell kapcsolnod minden blokkot a munkaterületen.", "Maze.helpRun": "Futtasd a programot, nézük meg mi történik.", "Maze.helpReset": "A programmal nem sikerült a figurát a célba juttatni. Kezdd elölr<PERSON>l.", "Maze.helpRepeat": "Juttasd el a figurát a célig mindössze 2 blokk felhasználásával. <PERSON>z<PERSON>ld az 'Ismételd a cél eléréséig' blokkot egy másik blokk ismételt végrehajtásához.", "Maze.helpCapacity": "Minden rendelkezésedre álló blokkot felhasználtál. Új blokk kirakásához egy létezőt törölnöd kell.", "Maze.helpRepeatMany": "Több blokkot is használhatsz az Ismétlésen belül.", "Maze.helpSkins": "Válaszd ki kedvenc figurádat a menüből.", "Maze.helpIf": "Egy 'Ha' esetén az utasítások csak akkor kerülnek végrehajtásra, ha a feltétel igaz. Próbáld a figurát balra fordítani, ha van út balra.", "Maze.helpMenu": "Kattints a(z) %1 elemre és változtass a 'ha' blokk feltételén.", "Maze.helpWallFollow": "Végig tudod vezetni a figurát ezen a bonyolult labirintuson? Próbáld a bal oldali falat követni. Haladó programozóknak!", "Bird.noWorm": "nincs n<PERSON><PERSON> k<PERSON>c", "Bird.heading": "<PERSON><PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "<PERSON><PERSON>, amikor nincs a madárnál a kukac.", "Bird.headingTooltip": "A szöggel megadott irányba mozdul: a 0 fok jobbra, a 90 fok fölfele, a 180 fok balra és a 270 fok lefele irányt jelent.", "Bird.positionTooltip": "x és y a madár <PERSON>. Ha x=0, a madár a bal sz<PERSON>len va. Ha x=100 akkor a jobb szélen. Ha y=0 akkor a madár az al<PERSON>ó s<PERSON> van, ha y=100 akkor a tetején.", "Bird.helpHeading": "Irányítsd a madár repülésének <PERSON>, hogy felszedje a kuka<PERSON>, és leszálljon a fészkére.", "Bird.helpHasWorm": "Ezzel a blokkal különböző irányokba irányíthatod a madarat attól függően hogy nála van-e a kukac, vagy még nincs.", "Bird.helpX": "'x' a madár v<PERSON><PERSON> pozíciója, azt mondja meg, milyen messze van a kép bal sz<PERSON>létől. <PERSON><PERSON><PERSON><PERSON> ezt a blokkot hogy más irányban repülj<PERSON>n a madár, ha a megadott távolságnál k<PERSON> van a bal sz<PERSON><PERSON><PERSON>, és egy más<PERSON>, ha messzebb van.", "Bird.helpElse": "Kattints az ikonra, hogy módosítsd a 'Ha' blokk típusát.", "Bird.helpElseIf": "Ennek a szintnek a megoldásához egy 'Egyébként Ha' és egy 'Egyébként' blokk is kelleni fog.", "Bird.helpAnd": "Az 'És' blokk értéke akkor 'igaz', ha <PERSON>két bemenetének 'igaz' az éré<PERSON>ke.", "Bird.helpMutator": "Húzz egy 'Egyébként' blokkot a 'Ha' blokkra.", "Turtle.moveTooltip": "A teknőcöt mozgatja előre, vagy h<PERSON>tra a pixelben megadott értékkel.", "Turtle.moveForward": "<PERSON><PERSON> (pixel)", "Turtle.moveBackward": "<PERSON><PERSON> (pixel)", "Turtle.turnTooltip": "A teknőcöt <PERSON>, vagy balra fordítja a fokban megadott érté<PERSON>.", "Turtle.turnRight": "<PERSON><PERSON><PERSON> (fok)", "Turtle.turnLeft": "<PERSON><PERSON><PERSON> balra (fok)", "Turtle.widthTooltip": "Vonalvastagság változtatása.", "Turtle.setWidth": "Vonalvastagság", "Turtle.colourTooltip": "A rajzolás színének változtatása.", "Turtle.setColour": "Raj<PERSON>lás színe", "Turtle.penTooltip": "Toll felemel<PERSON>e <PERSON>, att<PERSON><PERSON> fü<PERSON>, hogy elkezdjük vagy befejezzük a rajzolást.", "Turtle.penUp": "<PERSON><PERSON>t fel", "Turtle.penDown": "<PERSON><PERSON><PERSON> le", "Turtle.turtleVisibilityTooltip": "A teknőc (kör és nyíl) láthatóvá tétele, vagy <PERSON>.", "Turtle.hideTurtle": "Teknőc elrejtése", "Turtle.showTurtle": "Teknőc felfedése", "Turtle.printHelpUrl": "https://hu.wikipedia.org/wiki/Nyomtat%C3%A1s_(nyomd%C3%A1szat)", "Turtle.printTooltip": "Üzenetet ír a teknőc irányában a helyétől.", "Turtle.print": "ki<PERSON>r", "Turtle.fontHelpUrl": "https://hu.wikipedia.org/wiki/Bet%C5%B1k%C3%A9p", "Turtle.fontTooltip": "Beállítja az üzenet betűtípusát.", "Turtle.font": "Betűtípus", "Turtle.fontSize": "Bet<PERSON><PERSON><PERSON>", "Turtle.fontNormal": "<PERSON><PERSON><PERSON>", "Turtle.fontBold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.fontItalic": "<PERSON><PERSON><PERSON>", "Turtle.submitDisabled": "Futtasd a programot amíg le nem áll. Az eredményt megmutathatod mindenkinek a galériában!", "Turtle.galleryTooltip": "Rajzok galéria me<PERSON>.", "Turtle.galleryMsg": "Galéria", "Turtle.submitTooltip": "Küldje el a rajzát a galériába.", "Turtle.submitMsg": "Kép elküldése a Galériába", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON><PERSON> a felada<PERSON>t, de tudsz enn<PERSON>l jobbat is!", "Turtle.helpUseLoop3": "<PERSON><PERSON><PERSON> meg a form<PERSON>t h<PERSON>rom blo<PERSON>l!", "Turtle.helpUseLoop4": "<PERSON><PERSON><PERSON> meg a csillagot mindössze négy blokkal!", "Turtle.helpText1": "Készíts programot, ami négyzetet rajzol!", "Turtle.helpText2": "K<PERSON><PERSON><PERSON><PERSON> programot, ami egy ötszöget rajzol a négyzet helyett!", "Turtle.helpText3a": "Az új blokkal változtathatod a húzott vonal színét:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON>j s<PERSON>rga csillagot!", "Turtle.helpText4a": "<PERSON>z új blok segítségével felemelheted a  ceruzád:", "Turtle.helpText4b": "<PERSON><PERSON><PERSON><PERSON> egy kis s<PERSON> c<PERSON>, majd egy von<PERSON> fö<PERSON>!", "Turtle.helpText5": "Ha tudsz egy csi<PERSON><PERSON>, tudsz négyet is! <PERSON><PERSON><PERSON>d el <PERSON>ket egy négyzet csúcsaira.", "Turtle.helpText6": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> s<PERSON> c<PERSON>, és egy fehé<PERSON>!", "Turtle.helpText7": "<PERSON><PERSON><PERSON> meg a csillagokat, majd négy fehé<PERSON>!", "Turtle.helpText8": "360, a középppontból induló fehér vonal pont úgy fog kinézni, mint a telihold.", "Turtle.helpText9": "Tuds<PERSON> a hold elé egy fekete kört rajzolni, hogy fogy<PERSON> hold legyen?", "Turtle.helpText10": "Kaptál jópár extra blokkot. Próbáld ki ő<PERSON>, raj<PERSON><PERSON>j s<PERSON>badon!", "Turtle.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> a \"Galéria\" gombot, ho<PERSON> l<PERSON>, min dolgoztak a többiek. Ha saját munkád szeretnéd megosztani mindenkivel, katti<PERSON> a \"Galériába küldés\" gombra!", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>, hogy megnézhesd a blokkokat!", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "eleje x", "Movie.y1": "eleje y", "Movie.x2": "vége x", "Movie.y2": "vége y", "Movie.radius": "<PERSON><PERSON><PERSON><PERSON>", "Movie.width": "szélesség", "Movie.height": "ma<PERSON>sá<PERSON>", "Movie.circleTooltip": "<PERSON><PERSON><PERSON> rajzol a megadott helyre, a megadott sugárral.", "Movie.circleDraw": "<PERSON><PERSON><PERSON>", "Movie.rectTooltip": "Téglalapot rajzol a megadott helyre, a megadott szélességgel és magassággal.", "Movie.rectDraw": "téglalap", "Movie.lineTooltip": "Megadott vastagságú von<PERSON>t húz egyik pontból a másikba.", "Movie.lineDraw": "<PERSON><PERSON>", "Movie.timeTooltip": "Visszaadja az eltelt időt (0-100).", "Movie.colourTooltip": "Változtatja a rajzolás színét.", "Movie.setColour": "szín", "Movie.submitDisabled": "A filmed nem mozog. <PERSON><PERSON><PERSON><PERSON> a blo<PERSON><PERSON>kat, hogy valami érdekeset készíts, tö<PERSON>d fel az eredményt a galériába!", "Movie.galleryTooltip": "Film-gal<PERSON><PERSON>.", "Movie.galleryMsg": "Galéria", "Movie.submitTooltip": "Filmjének feltöltése a galériába.", "Movie.submitMsg": "Feltöltés a Galériába.", "Movie.helpText1": "Rajzolj emberkét egyszerű formákból!", "Movie.helpText2a": "Most egy filmet fogsz készíteni. Egy ember karj<PERSON>t kell végigmozgatnod a képernyőn. Nyomd meg a Lej<PERSON><PERSON><PERSON><PERSON> gombot, hogy megnézd, mit alkottál!", "Movie.helpText2b": "Ahogy lejátszod a filmet, az „idő” blokk értéke 0-tól 100-ig emelkedik. <PERSON>vel azt szeretnéd, hogy a kar „y” pozíciója is 0-tól 100-ig v<PERSON>, nem lesz nehéz dolgod.", "Movie.helpText3": "Az „id<PERSON>” blokk értéke továbbra is 0-tól 100-ig emelkedik. Most viszont azt szeretnéd, hogy a másik kar „y” pozíciója 100-ról 0-ra csökkenjen. Meg tudod cser<PERSON>lni a számlálás irányát valamilyen matematikai formulával?", "Movie.helpText4": "<PERSON><PERSON><PERSON><PERSON> az eddig <PERSON>, hogy keresztezd a lábakat.", "Movie.helpText5": "A kar mozgatásához szükséges matematikai képlet bonyolult.\nItt a válasz:", "Movie.helpText6": "Rajzolj az embernek kezeket.", "Movie.helpText7": "Az eső labda matematikai képlete bonyolult. Íme a megoldás:", "Movie.helpText8": "<PERSON><PERSON><PERSON><PERSON> a '<PERSON>' blo<PERSON>, hogy a film első felében piros és kék, a második felében zold labdákat rajzolhass.", "Movie.helpText9": "Ha a labdával végig tudsz menni a megraj<PERSON><PERSON> dr<PERSON><PERSON>, több<PERSON> nincs s<PERSON><PERSON><PERSON> lehetetlen feladat!", "Movie.helpText10": "Készíts olyan filmet, amilyet csak szeretnél! Közben kipróbálhatod az új blokkokat is. Engedd szabadon a fantáziád!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> a '<PERSON><PERSON><PERSON><PERSON>' go<PERSON><PERSON>, ho<PERSON> me<PERSON>, mások milyen filmeket készítettek. Ha meg szeretnéd mutatni a munkád másoknak, te is feltölthetsz filmeket a 'Feltöltés a Galériába' gomb segí<PERSON>.", "Music.playNoteTooltip": "<PERSON>gy adott ma<PERSON>ságú és hosszúságú hangot játszik le.", "Music.setInstrument": "kiv<PERSON><PERSON><PERSON><PERSON><PERSON>: %1", "Music.firstPart": "<PERSON><PERSON><PERSON> rész", "Music.piano": "zongora", "Music.trumpet": "trombita", "Music.banjo": "<PERSON><PERSON><PERSON><PERSON>", "Music.violin": "heged<PERSON>", "Music.guitar": "<PERSON><PERSON><PERSON><PERSON>", "Music.flute": "furulya", "Music.drum": "dob", "Music.choir": "kórus", "Pond.scanTooltip": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>. <PERSON><PERSON> meg e<PERSON> (0-360), <PERSON><PERSON> <PERSON><PERSON>, mi<PERSON><PERSON> messze van az adott ir<PERSON> a legközelebbi ellenfél. Ha nincs arra senki, végtelent ad vissza.", "Pond.cannonTooltip": "Tü<PERSON>j a<PERSON> ágyuval! Adj meg egy i<PERSON> (0-360) és egy távolságot (0-70).", "Pond.swimTooltip": "Kezdj el úszni a megadott irányba (0-360).", "Pond.stopTooltip": "Állj meg!", "Pond.healthTooltip": "Visszaadja a játékos életét (0: ha<PERSON>t, 100: tel<PERSON><PERSON> e<PERSON>szs<PERSON>).", "Pond.speedTooltip": "Visszaadja a játékos aktuális se<PERSON> (0: áll, 100: maxim<PERSON><PERSON> sebes<PERSON><PERSON><PERSON><PERSON> halad)", "Pond.locXTooltip": "Visszaadja a játékos x koordinátáj<PERSON>t (0: bal sz<PERSON><PERSON>, 100: <PERSON><PERSON> s<PERSON>l)", "Pond.locYTooltip": "Visszaadja a játékos x koordinátáj<PERSON>t (0: lent, 100: fent)", "Pond.docsTooltip": "Programozási nyelv dokumentációja.", "Pond.documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.pendulumName": "Billegő", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON> megoldanod a feladatot, de tudsz te jobbat is. <PERSON><PERSON><PERSON><PERSON> a 'scan' blokkot, hogy pontosabban tudj lőni a<PERSON> ágyuval.", "Pond.helpText1": "Hasz<PERSON>ld a 'cannon' utasítást hogy eltaláld a célpontot. Az első paraméter a lövés iránya, a második a távolság. Számítsd ki a megfelelő értékeket!", "Pond.helpText2": "<PERSON>em el<PERSON>g e<PERSON> el<PERSON>od a célpontot. Használj 'while (true)' ciklust hogy többször l<PERSON>.", "Pond.helpText3a": "<PERSON>z az ellenfél oda-v<PERSON><PERSON>, e<PERSON><PERSON><PERSON> nem könnyű eltalálni. <PERSON><PERSON><PERSON><PERSON> a 'scan' <PERSON><PERSON><PERSON>, hogy pontosan meghatározd az ellenség távolságát!", "Pond.helpText3b": "Ezt a távo<PERSON><PERSON><PERSON> has<PERSON>, hogy a 'cannon' parancs pontos legyen!", "Pond.helpText4": "<PERSON><PERSON> el<PERSON><PERSON>l túl messze van ahhoz hogy innen eltaláld. <PERSON>z<PERSON><PERSON> az 'swim' paranc<PERSON><PERSON>, hogy közeleb<PERSON> kerülj hozz<PERSON>, és nekiütközz!", "Pond.helpText5": "Ez az ellenfél is lőtávolságon kívül van. Viszont egy ütközést nem élnél túl. Ússz az ellenfél felé, míg lőtávolságba nem kerül, azt<PERSON> 'stop' meg. Mondjuk ha elérted vízszintesen az 50-es osztást.", "Pond.helpText6": "<PERSON>z az ellenfél menekülni fog, ha eltalálod. Ha hatótávolságon kívül került (70 méter), ússz közelebb hozzá!"}