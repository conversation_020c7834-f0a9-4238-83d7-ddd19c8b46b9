{"@metadata": {"authors": ["Acamicamacaraca", "BadDog", "<PERSON><PERSON><PERSON>", "Milicevic01", "<PERSON>", "<PERSON><PERSON><PERSON>", "Perevod16", "Rancher", "<PERSON><PERSON><PERSON>", "Zoranzoki21", "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Блокли игре", "Games.puzzle": "Слагалица", "Games.maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.bird": "Птица", "Games.turtle": "Корњача", "Games.movie": "<PERSON>ил<PERSON>", "Games.music": "Музика", "Games.pondTutor": "Водич за Рибњак", "Games.pond": "Рибњак", "Games.linesOfCode1": "Решили сте овај ниво једним редом JavaScript-а:", "Games.linesOfCode2": "Решили сте овај ниво са %1 реда JavaScript-а:", "Games.nextLevel": "Јесте ли спремни за ниво %1?", "Games.finalLevel": "Јесте ли спремни за следећи изазов?", "Games.submitTitle": "Наслов:", "Games.linkTooltip": "Сачувајте и повежите са блоковима.", "Games.runTooltip": "Покрените програм који сте написали.", "Games.runProgram": "Покрени програм", "Games.resetTooltip": "Зауставите програм и ресетујте ниво.", "Games.resetProgram": "Поново постави", "Games.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Логика", "Games.catLoops": "Петље", "Games.catMath": "Математика", "Games.catText": "Текст", "Games.catLists": "Спискови", "Games.catColour": "Боја", "Games.catVariables": "Променљиве", "Games.catProcedures": "Функције", "Games.httpRequestError": "Дошло је до проблема са захтевом.", "Games.linkAlert": "Делите своје блокове овом везом:\n\n%1", "Games.hashError": "Нажалост, „%1” не одговара ниједном сачуваном програму.", "Games.xmlError": "Није могуће учитати вашу сачувану датотеку. Можда је направљена другом верзијом Блоклија.", "Games.submitted": "Хвала вам на овом програму! Ако се нашем особљу обучених мајмуна допадне, објавиће га у галерији у року од неколико дана.", "Games.listVariable": "списак", "Games.textVariable": "текст", "Games.breakLink": "Једном када почнете са уређивањем JavaScript-а, не можете се вратити на уређивање блокова. Да ли је то у реду?", "Games.blocks": "Блокови", "Games.congratulations": "Честитамо!", "Games.helpAbort": "Овај ниво је веома тежак. Желите ли да га прескочите и идите на следећу игру? Увек можете да се вратите назад касније.", "Index.clear": "Уклањате сва ваша решења?", "Index.subTitle": "Игре за будуће програмере.", "Index.moreInfo": "Информације за едукаторе...", "Index.startOver": "Желите да почнете испочетка?", "Index.clearData": "Обриши податке", "Puzzle.animal1": "Патка", "Puzzle.animal1Trait1": "<PERSON>ер<PERSON>е", "Puzzle.animal1Trait2": "Кљун", "Puzzle.animal1HelpUrl": "https://sr.wikipedia.org/sr-ec/Patka", "Puzzle.animal2": "Мачка", "Puzzle.animal2Trait1": "Бркови", "Puzzle.animal2Trait2": "Крзно", "Puzzle.animal2HelpUrl": "https://sr.wikipedia.org/wiki/Мачка", "Puzzle.animal3": "Пчела", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "Жаока", "Puzzle.animal3HelpUrl": "https://sr.wikipedia.org/wiki/Пчела", "Puzzle.animal4": "Пуж", "Puzzle.animal4Trait1": "Шкољка", "Puzzle.animal4Trait2": "Слуз", "Puzzle.animal4HelpUrl": "https://sr.wikipedia.org/wiki/Пуж", "Puzzle.picture": "слика:", "Puzzle.legs": "ноге:", "Puzzle.legsChoose": "одаберите…", "Puzzle.traits": "особине:", "Puzzle.error0": "Савршено!\nСвих %1 блокова је исправно постављено.", "Puzzle.error1": "Замало! Један блок је неисправно постављен.", "Puzzle.error2": "%1 блокова је неисправно постављено.", "Puzzle.tryAgain": "Означени блок није исправан.\nНастави да покушаваш.", "Puzzle.checkAnswers": "Провери одговоре", "Puzzle.helpText": "За сваку животињу (зелен<PERSON>), приложи њену слику, изабери њен број ногу, и наслажи њене особине.", "Maze.moveForward": "иди напред", "Maze.turnLeft": "окрени се лево", "Maze.turnRight": "скрени десно", "Maze.doCode": "изврши", "Maze.helpIfElse": "Блокови „ако-иначе“ извршиће или једно или друго.", "Maze.pathAhead": "ако постоји пут напред", "Maze.pathLeft": "ако постоји пут лево", "Maze.pathRight": "ако постоји пут десно", "Maze.repeatUntil": "понављај до", "Maze.moveForwardTooltip": "Помера играча напред за једно место.", "Maze.turnTooltip": "Окреће играча улево или удесно за 90 степени.", "Maze.ifTooltip": "Ако постоји пут у датом правцу, онда изврши неке радње.", "Maze.ifelseTooltip": "Ако постоји пут у датом правцу, онда изврши први блок радњи. У супротном, изврши други.", "Maze.whileTooltip": "Понавља задате радње док не дође до краја.", "Maze.capacity0": "Преостало ти је %0 блокова.", "Maze.capacity1": "Преостао ти је %1 блок.", "Maze.capacity2": "Преостало ти је %2 блокова.", "Maze.runTooltip": "Одређује шта ће играч урадити на основу блокова.", "Maze.resetTooltip": "Вратите играча на почетак лавиринта.", "Maze.helpStack": "Наређајте два блока „иди напред“ да бисте ми помогли да дођем до циља.", "Maze.helpOneTopBlock": "У овом нивоу треба да наређате све блокове у бели радни простор.", "Maze.helpRun": "Покрените програм и видите шта се дешава.", "Maze.helpReset": "Програм није решио лавиринт. Кликните на „Поново“ да бисте поново покушали.", "Maze.helpRepeat": "Дођите до краја пута употребивши само два блока. Користите „понови“ да бисте извршили блок више пута.", "Maze.helpCapacity": "Сте користили све блокове на овом нивоу. Да направите нови блок, потребно је да прво уклоните постојећи блок.", "Maze.helpRepeatMany": "Достигни циљ употребом само пет блокова.", "Maze.helpSkins": "Изаберите Свој омиљени играч из овог менија.", "Maze.helpIf": "'ако' блок ће урадити нешто једино ако је услов испуњен. Покушај окрет у лево ако постоји пут са леве стране.", "Maze.helpMenu": "Кликните на %1 'ако' блок, да промени свој статус.", "Maze.helpWallFollow": "Можеш ли да решиш овај сложени лавиринт? Покшај да се крећеш уз леви зид.", "Bird.noWorm": "нема црва", "Bird.heading": "правац", "Bird.noWormTooltip": "Стање у ком птица није добила црва.", "Bird.headingTooltip": "Крећу у правцу којим углом: 0 је на десној страни, 90 је усправно, итд", "Bird.positionTooltip": "X и Y марка птица позиције. Када је X = 0 птица је од леве ивице, ако је X = 100 је у близини десне ивице. Када је Y = 0 ова птица је на дну, када је Y = 100 је на самом врху.", "Bird.helpHeading": "Промените категорију под таквим углом да птица је била на црва и земљу у своје гнездо.", "Bird.helpHasWorm": "Користите овај блок у једној категорији, ако имате црв, или друге Наслов, ако немате црва.", "Bird.helpX": "'X' - то је ваш тренутни водоравни положај. Користите овај блок у једној категорији, ако је 'X'  је мањи од броја, или други Наслов у супротном.", "Bird.helpElse": "Кликните на икону да промените блок 'if'.", "Bird.helpElseIf": "Овај ниво потреба као 'else if' и 'else' блок.", "Bird.helpAnd": "У 'И' блока је Истина, само ако су оба скупа улазних података истините.", "Bird.helpMutator": "Превуците 'else' јединицу 'if' блок.", "Turtle.moveTooltip": "Помера черепашку напред или назад на поменути број.", "Turtle.moveForward": "иди напред", "Turtle.moveBackward": "да се креће назад", "Turtle.turnTooltip": "Корњача се окреће лево или десно на одређено броја степени.", "Turtle.turnRight": "скрените десно на", "Turtle.turnLeft": "скрените лево на", "Turtle.widthTooltip": "Changes the width of the pen.", "Turtle.setWidth": "подесите ширину на", "Turtle.colourTooltip": "Мења боју оловке.", "Turtle.setColour": "подесите боју за", "Turtle.penTooltip": "Подиже или изоставља оловку, да се заустави или почне да се скрене.", "Turtle.penUp": "дигни оловку", "Turtle.penDown": "спусти оловку", "Turtle.turtleVisibilityTooltip": "Чини корњачу (круг и стрелица) видљивом или невидљивом.", "Turtle.hideTurtle": "сак<PERSON>иј корњачу", "Turtle.showTurtle": "прикажи корњачу", "Turtle.printHelpUrl": "https://sr.wikipedia.org/wiki/Штампарство", "Turtle.printTooltip": "Исцртава текст у правцу корњаче на њеном месту.", "Turtle.print": "напиши", "Turtle.fontTooltip": "Поставља фонт који користи блок за писање.", "Turtle.font": "фонт", "Turtle.fontSize": "величина фонта", "Turtle.fontNormal": "нормалан", "Turtle.fontBold": "подебљани", "Turtle.fontItalic": "искошени", "Turtle.submitDisabled": "Пратите програм до краjа. Онда можете да пошаљете свој цртеж у галерији.", "Turtle.galleryTooltip": "Отворите галерију цртежа.", "Turtle.galleryMsg": "Прикажи галерију", "Turtle.submitTooltip": "Пошаљите свој цртеж у галерију.", "Turtle.submitMsg": "Пошаљи у галерију", "Turtle.helpUseLoop": "Твоје решење ради, али ти то можеш још боље.", "Turtle.helpUseLoop3": "Нацртај овај облик само са три блока.", "Turtle.helpUseLoop4": "Нацртај звезду само са четири блока.", "Turtle.helpText1": "Направи програм који црта квадрат.", "Turtle.helpText2": "Промени твој програм тако да црта петоугао уместо квадрата.", "Turtle.helpText3a": "Ево новог блока који ти омогућава да промениш боју:", "Turtle.helpText3b": "Нацртај жуту звезду.", "Turtle.helpText4a": "Ево новог блока који ти омогућава да подигнеш оловку са папира кад се крећеш:", "Turtle.helpText4b": "Нацртај малу жуту звезду, потом нацртај линију изнад ње.", "Turtle.helpText5": "Уместо једне звезде, можеш ли нацртати четири звезде распоређене у углове квадрата?", "Turtle.helpText6": "Нацртај три жуте звезде, и једну белу линију.", "Turtle.helpText7": "Нацртај звезде, потом нацртај четири беле линије.", "Turtle.helpText8": "Цртање 360 белих линија изгледаће као пун месец.", "Turtle.helpText9": "Можеш ли додати црни круг тако да ти месец постане млади месец?", "Turtle.helpText10": "Цртај шта желиш. Им<PERSON>ш огроман број нових блокова које можеш да истражујеш. Забављај се!", "Turtle.helpText10Reddit": "Користите дугме \"Погледај Галерију\" да бисте видели шта су други нацртали. Ако сте нацртали нешто занимљиво, користите дугме \"Постави у Галерију\" да бисте то објавили.", "Turtle.helpToolbox": "Одаберите категорију да бидите блокове.", "Movie.x": "X", "Movie.y": "Y", "Movie.x1": "почетак x", "Movie.y1": "почетак y", "Movie.x2": "крај x", "Movie.y2": "крај y", "Movie.radius": "полупречник", "Movie.width": "ши<PERSON><PERSON><PERSON>", "Movie.height": "висина", "Movie.circleTooltip": "Црта круг у том месту и са одређеном радијусу.", "Movie.circleDraw": "круг", "Movie.rectTooltip": "Црта правоугаоник на том месту и са датом ширине и висине.", "Movie.rectDraw": "правоугаоник", "Movie.lineTooltip": "Цртање линије од једне тачке до друге са одређеном ширином.", "Movie.lineDraw": "линија", "Movie.timeTooltip": "Враћа тренутно време у филму (0–100).", "Movie.colourTooltip": "Мења боју оловке.", "Movie.setColour": "промени боју у", "Movie.submitDisabled": "Ваш филм није покретан. Користите блокове да направите нешто занимљиво. Потом можете свој филм објавити у галерији.", "Movie.galleryTooltip": "Отворите галерију филмова.", "Movie.galleryMsg": "Погледај Галерију", "Movie.submitTooltip": "Пошаљите свој филм у галерију.", "Movie.submitMsg": "Постави у Галерију", "Movie.helpLayer": "Помери круг на врх свог програма. Потом ће се појавити иза особе.", "Movie.helpText1": "Користите једноставне облике да бисте нацртали ову особу.", "Movie.helpText2a": "Овај ниво је филм. Желите да се рука особе помера преко екрана. Притисните дугме за репродукцију да бисте видели преглед.", "Movie.helpText2b": "Како се филм репродуктује, вредност блока „time” броји од 0 до 100. Пошто желите да позиција „y” руке почне на 0 и иде до 100, ово треба да буде лако.", "Movie.helpText3": "Блок „time” броји од 0 до 100. Али сада желите да позиција „y” друге руке почне на 100 и иде до 0. Можете ли схватити једноставну математичку формулу која обрће смер?", "Movie.helpText4": "Користите шта сте научили на претходном нивоу да бисте направили ноге које прелазе.", "Movie.helpText5": "Математичка формула за руку је компликована. Ево одговора:", "Movie.helpText6": "Дајте особи пар руку.", "Movie.helpText7": "Користите блок „if” да бисте нацртали малу главу за прву половину филма. Онда нацртајте велику главу за другу половину филма.", "Movie.helpText8": "Направите да ноге обрну смер на пола пута кроз филм.", "Movie.helpText9": "Нацртајте круг који се шири иза особе.", "Movie.helpText10": "Направите филм за шта год желите. Имате огроман број нових блокова које можете да истражујете. Забавите се!", "Movie.helpText10Reddit": "Користите дугме „Прикажи галерију” да бисте погледали филмове које су други направили. Ако направите интересантан филм, користите дугме „Пошаљи у галерију” да бисте га објавили.", "Music.playNoteTooltip": "Свира једну ноту специфичне дужине и висине.", "Music.playNote": "репродукуј %1 ноту %2", "Music.restTooltip": "Чека одређено време.", "Music.restWholeTooltip": "Чека целу ноту.", "Music.rest": "целих %1", "Music.setInstrumentTooltip": "Пребацује се на специфични инструмент када се свирају наредне ноте.", "Music.setInstrument": "постави инструмент на %1", "Music.startTooltip": "Извршава унутрашње блокове када се кликне на дугме ’Покрени програм’.", "Music.start": "када је %1 кликнут", "Music.pitchTooltip": "Једна нота (Це4 је 7).", "Music.firstPart": "први део", "Music.piano": "клавир", "Music.trumpet": "труба", "Music.banjo": "банџо", "Music.violin": "виолина", "Music.guitar": "гитара", "Music.flute": "флаута", "Music.drum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Music.choir": "хор", "Music.submitDisabled": "Покрените програм док се не заустави. Потом можете послати вашу музику у галерију.", "Music.galleryTooltip": "Отворите галерију музике.", "Music.galleryMsg": "Прикажи галерију", "Music.submitTooltip": "Пошаљите своју музику у галерију.", "Music.submitMsg": "Пошаљи у галерију", "Music.helpUseFunctions": "Ваше решење ради, али можете боље. Користите функције да бисте смањили количину поновљеног кода.", "Music.helpUseInstruments": "Музика ће звучати боље ако користите други инструмент у сваком почетном блоку.", "Music.helpText1": "Компонујте прве четири ноте мелодије „Фратар Жак”.", "Music.helpText2a": "’Функција’ вам омогућава да групишете блокове, а потом их покренете више пута.", "Music.helpText2b": "Направите функцију да бисте покренули прве четири ноте мелодије „Фратар Жак”. Покрените ту функцију два пута. Немојте додавати нове нотне блокове.", "Music.helpText3": "Направите другу функцију за следећи део мелодије „Фратар Жак”. Последња нота је дужа.", "Music.helpText4": "Направите трећу функцију за следећи део мелодије „Фратар Жак”. Прве четири ноте су краће.", "Music.helpText5": "Завршите читаву мелодију „Фратар Жак”.", "Music.helpText6a": "Овај нови блок вам омогућава да пређете на други инструмент.", "Music.helpText6b": "Свирајте вашу мелодију виолином.", "Music.helpText7a": "Овај нови блок додаје тихо кашњење.", "Music.helpText8": "Сваки почетни блок би требало да свира мелодију „Фратар Жак” два пута.", "Music.helpText9": "Направите четири почетна блока где сваки свира мелодију „Фратар Жак” два пута. Додајте тачан број блокова кашњења.", "Music.helpText10": "Компонујте све шта желите. Имате велик број нових блокова које можете да истражите. Забавите се!", "Music.helpText10Reddit": "Користите дугме ’Види галерију’ да бисте видели шта су други компоновали. Ако компонујете нешто интересантно, користите дугме ’Пошаљи у галерију’ да то објавите.", "Pond.scanTooltip": "Претрага непријатеља. Одређивање смера (0-360). Враћање удаљености најближе мете у том смеру. Враћање бесконачности ако ниједна мета није пронађена.", "Pond.cannonTooltip": "Испаљивање из топа. Одређивање смера (0-360) и домета (0-70).", "Pond.swimTooltip": "Пливање напред. Одређивање смера (0-360).", "Pond.stopTooltip": "Престанак пливања. Играч ће успоравати док се не заустави.", "Pond.healthTooltip": "Враћа играчево тренутно здравље (0 је мртав, 100 је здрав).", "Pond.speedTooltip": "Враћа тренутну брзину играча (0 је заустављен, 100 је максимална брзина).", "Pond.locXTooltip": "Враћа X координату играча (0 је лева ивица, 100 је десна ивица).", "Pond.locYTooltip": "Враћа Y координату играча (0 је доња ивица, 100 је горња ивица).", "Pond.logTooltip": "Приказује број у конзоли вашег претраживача.", "Pond.docsTooltip": "Приказује језик документације.", "Pond.documentation": "Документација", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Корисник", "Pond.pendulumName": "Клатно", "Pond.scaredName": "Уплашени", "Pond.helpUseScan": "Ваша решења раде, али можете и боље. Користите 'scan' да бисте видели колико далеко пуцати из топа.", "Pond.helpText1": "Користите команду 'cannon' да бисте погодили мету. Први параметар је угао, други параметар је домет. Откријте праву комбинацију.", "Pond.helpText2": "Ова мета мора да се погоди више пута. Користите петљу 'while (true)' да бисте нешто радили бесконачно.", "Pond.helpText3a": "Противник се креће напред и назад, због чега га је тешко погодити. Израз 'scan' враћа тачан домет до противника у одређеном смеру.", "Pond.helpText3b": "Домет је тачно оно што команда 'cannon' треба да би се пуцало прецизно.", "Pond.helpText4": "Овај противник је предалеко да би се користио топ (који има домет од 70 метара). Уместо тога, користите команду 'swim' да бисте почели пливати према противнику и забили се у њега.", "Pond.helpText5": "Овај противник је такође предалеко да би се користио топ. Али Ви сте преслаби да преживите судар. Пливајте према противнику док Вам је хоризонтална локација мања од 50. Потом користите 'stop' и употребите топ.", "Pond.helpText6": "Овај противник ће се удаљити када је погођен. Пливајте према њему ако је ван домета (70 метара).", "Gallery": "Гал<PERSON><PERSON><PERSON><PERSON>а"}