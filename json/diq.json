{"@metadata": {"authors": ["1917 <PERSON><PERSON>", "Gorizon", "Kumkumuk", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON><PERSON>", "Games.turtle": "Ke<PERSON>", "Games.movie": "<PERSON><PERSON>", "Games.music": "Muzik", "Games.pondTutor": "<PERSON><PERSON><PERSON> m<PERSON>i", "Games.pond": "Golık", "Games.linesOfCode1": "Şıma ena sewiya be JavaScript ra 1 xete hal kerde:", "Games.linesOfCode2": "Şıma ena sewiya be JavaScript ra %1 xete hal kerde:", "Games.nextLevel": "Seba sewiya %1 rê şıma hazırê?", "Games.finalLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bahdoyêne rê şıma hazırê?", "Games.submitTitle": "Sername:", "Games.linkTooltip": "Qeyd ke û be blokan ra gıre de.", "Games.runTooltip": "Proğramo ke to nusnayo bıxebetne", "Games.runProgram": "Programi Akar fi", "Games.resetTooltip": "Proğrami vındarnı u sewiyay reset ke", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Mentıq", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematik", "Games.catText": "<PERSON><PERSON><PERSON>", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Vırneyeni", "Games.catProcedures": "Fonksiyoni", "Games.httpRequestError": "waştışi deyne zew problem esto", "Games.linkAlert": "Blokan na linkera bıhesrne\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON>, '%1' be qet yew programi ra yewbini nê<PERSON>.", "Games.xmlError": "Dosyaya şımaya qeydbiyayiye bar nêbena. Beno ke be versiyonê Blocklyê bini ra vıraziya?", "Games.listVariable": "liste", "Games.textVariable": "nuşte", "Games.blocks": "Kılitkerdışi", "Games.congratulations": "Tebrik kem!", "Index.clear": "A<PERSON><PERSON> pêro wa bıesteri yê?", "Index.subTitle": "<PERSON><PERSON><PERSON><PERSON> mesti rê kayi", "Index.moreInfo": "Mısnayoğan rê melumat...", "Index.startOver": "Qayılê ke fına suru bıkerê?", "Index.clearData": "<PERSON><PERSON><PERSON>ere", "Puzzle.animal1": "Ordege", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Nıkıle", "Puzzle.animal1HelpUrl": "https://diq.wikipedia.org/wiki/Ordege", "Puzzle.animal2": "P<PERSON><PERSON><PERSON>nge", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Çerme", "Puzzle.animal2HelpUrl": "https://diq.wikipedia.org/wiki/Pısınge", "Puzzle.animal3": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "Tire", "Puzzle.animal3HelpUrl": "https://diq.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "Puzzle.animal4": "Qarvelık", "Puzzle.animal4Trait1": "Qafık", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://diq.wikipedia.org/wiki/Qarvelık", "Puzzle.picture": "resım:", "Puzzle.legs": "şeqi:", "Puzzle.legsChoose": "we<PERSON>ine...", "Puzzle.traits": "<PERSON>ısusiyeti:", "Puzzle.error0": "Muhteşem!\nBloga %1 pêro raşta.", "Puzzle.error1": "Tay mend bı! Ju bloqe ç<PERSON>ta", "Puzzle.error2": "%1 bloqi çewti", "Puzzle.tryAgain": "Pronyaye bloq çewta. Cerbnayışi rê dewam kerê", "Puzzle.checkAnswers": "Netican kontrol ke", "Maze.moveForward": "raver <PERSON>o", "Maze.turnLeft": "<PERSON>o het<PERSON>", "Maze.turnRight": "<PERSON>o het<PERSON> ra<PERSON>ti", "Maze.doCode": "bıke", "Maze.pathAhead": "Eger ke verdı ray esta se", "Maze.pathLeft": "Eger ke çepdı ray esta se", "Maze.pathRight": "Eger taşt dı ray esta se", "Maze.repeatUntil": "hend tekrar ke", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> yu vengeg beno revey.", "Maze.turnTooltip": "<PERSON><PERSON><PERSON> çerğê raşti yana çepi keno", "Maze.capacity0": "%0 bloqê <PERSON><PERSON><PERSON> mendê", "Maze.capacity1": "%1 bloqê <PERSON><PERSON>ma mendê", "Maze.capacity2": "%2 bloqê <PERSON><PERSON><PERSON> mendê", "Maze.resetTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>i seraniya labirenti de rone", "Maze.helpSkins": "Nê menu ra kaykerê xoyê vicnayeyi weçine.", "Bird.noWorm": "<PERSON><PERSON><PERSON><PERSON>", "Bird.heading": "sername", "Bird.helpElse": "Bloqê 'if' vurnayışi rê ikoni bıploğnê", "Bird.helpElseIf": "Na sewiya hem 'else if' hem zi bloqê 'else' lazımo", "Bird.helpMutator": "<PERSON>lo<PERSON><PERSON> 'else' ka<PERSON>ê bloqe 'if' kerê.", "Turtle.moveForward": "honde raver şo", "Turtle.moveBackward": "honde peyser şo", "Turtle.turnRight": "hende <PERSON>o het<PERSON> r<PERSON>", "Turtle.turnLeft": "hende <PERSON>o het<PERSON>", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON> q<PERSON> vurn<PERSON>.", "Turtle.setWidth": "<PERSON><PERSON><PERSON><PERSON> eyar kerê", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON> qeleme vurn<PERSON>.", "Turtle.setColour": "rengi eyar kerê", "Turtle.penUp": "qeleme berz ke", "Turtle.penDown": "qeleme rone", "Turtle.hideTurtle": "kesa bın<PERSON><PERSON>ne", "Turtle.showTurtle": "kesa b<PERSON><PERSON><PERSON>ne", "Turtle.print": "çap ke", "Turtle.font": "font", "Turtle.fontSize": "çapa fonti", "Turtle.fontNormal": "normal", "Turtle.fontBold": "qalınd", "Turtle.fontItalic": "İtalik", "Turtle.galleryTooltip": "Galeriya nusan a<PERSON>", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON> bı<PERSON>", "Turtle.submitTooltip": "<PERSON><PERSON><PERSON><PERSON> ho bırı<PERSON><PERSON> galeriy", "Turtle.submitMsg": "Gale<PERSON>ye rê bırı<PERSON>e", "Turtle.helpUseLoop3": "Şekıli teyna pê hirê blokana bınus<PERSON>", "Turtle.helpUseLoop4": "Estarey teyna pê çehar blokana bınus<PERSON>", "Turtle.helpText1": "Çerdağ nusen yew program vıraze", "Turtle.helpText2": "Heruna çerdağ seba phanckışt musayışi programi bıvurnê", "Turtle.helpText3a": "Reng vurn<PERSON>ışi rê mısade dayen yew bloko newe esto:", "Turtle.helpText3b": "<PERSON><PERSON> astareyo zerd vıraze.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "ver<PERSON><PERSON><PERSON> x", "Movie.y1": "ver<PERSON><PERSON><PERSON> y", "Movie.x2": "peyni<PERSON> x", "Movie.y2": "peyniye y", "Movie.radius": "çapane", "Movie.width": "<PERSON><PERSON><PERSON><PERSON>", "Movie.height": "<PERSON><PERSON><PERSON><PERSON>", "Movie.circleDraw": "çembere", "Movie.rectDraw": "çargoşeyo tikın", "Movie.lineDraw": "xete", "Movie.colourTooltip": "<PERSON><PERSON><PERSON> qeleme vurn<PERSON>.", "Movie.setColour": "rengi eyar kerê", "Movie.galleryTooltip": "<PERSON><PERSON> filman a<PERSON>", "Movie.galleryMsg": "<PERSON><PERSON><PERSON> bı<PERSON>", "Movie.submitTooltip": "Filmi bırış<PERSON> galeriy", "Movie.submitMsg": "Gale<PERSON>ye rê bırı<PERSON>e", "Movie.helpText6": "<PERSON><PERSON><PERSON> destan bıd<PERSON>n kesi.", "Music.playNote": "%1 dem de %2 bılewne", "Music.restTooltip": "<PERSON><PERSON><PERSON> muhlet<PERSON> paweno", "Music.restWholeTooltip": "Hend<PERSON> noti pêro paweno", "Music.rest": "wexto mende %1", "Music.start": "şıma ke nişanê %1 tıkna", "Music.pitchTooltip": "Yû note (C4 7 o).", "Music.firstPart": "pareyo verên", "Music.piano": "piyano", "Music.trumpet": "trompet", "Music.banjo": "banco", "Music.violin": "kemane", "Music.guitar": "gitar", "Music.flute": "flute", "Music.drum": "da<PERSON>l", "Music.choir": "koro", "Music.galleryTooltip": "Galeriya muzıki a<PERSON>ê", "Music.galleryMsg": "<PERSON><PERSON><PERSON> bı<PERSON>", "Music.submitTooltip": "Muzık<PERSON> ho bırışê galeri", "Music.submitMsg": "Gale<PERSON>ye rê bırı<PERSON>e", "Music.helpText1": "S<PERSON>ft<PERSON>ên çehar notanê '<PERSON><PERSON>'i bınus<PERSON>", "Music.helpText5": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>' pêroy temam kerê", "Music.helpText6a": "No bloqo newe, yewna enst<PERSON><PERSON> rav<PERSON>no", "Music.helpText6b": "Melodiya ho pê kemaniya  bıcınê", "Music.helpText7a": "No bloko newe bêveng yew peyra manayış keno cı", "Pond.swimTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bı<PERSON>. <PERSON>w het nışan ke (0-360)", "Pond.stopTooltip": "<PERSON><PERSON><PERSON> verad. <PERSON><PERSON> hêd<PERSON> vındeno", "Pond.logTooltip": "<PERSON><PERSON><PERSON><PERSON> browserê şıma rê yew numre çap keno", "Pond.docsTooltip": "Dokumanan<PERSON> zıwan<PERSON> bı<PERSON>ne", "Pond.documentation": "Dokumentasyon", "Pond.playerName": "<PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON>", "Gallery": "Gale<PERSON><PERSON>"}