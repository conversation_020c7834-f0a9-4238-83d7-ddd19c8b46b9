{"@metadata": {"authors": ["Gt", "<PERSON><PERSON><PERSON>"]}, "Games.name": "Blokkaspæl", "Games.puzzle": "Putlispæl", "Games.maze": "Villiniborg", "Games.bird": "Fuglur", "Games.turtle": "Skjaldbøka", "Games.movie": "<PERSON><PERSON>", "Games.music": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON>", "Games.pond": "<PERSON><PERSON><PERSON><PERSON>", "Games.linesOfCode1": "Tú loysti hetta stigið við einari linju av JavaScript:", "Games.linesOfCode2": "Tú loysti hetta stigið við %1 linjum av JavaScript:", "Games.nextLevel": "Er tú klár/ur til %1. stig?", "Games.finalLevel": "Er tú klár/ur til næstu avbjóðing?", "Games.submitTitle": "Heiti:", "Games.linkTooltip": "Goym og leinkja til blokkar.", "Games.runTooltip": "<PERSON><PERSON> forritið, sum tú skrivaði.", "Games.runProgram": "Koyr forritið", "Games.resetTooltip": "Steðga forritinum og nullstilla stigið.", "Games.resetProgram": "Nullstilla", "Games.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Logikkur", "Games.catLoops": "Lykkjur", "Games.catMath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catText": "Tekstur", "Games.catLists": "Listar", "Games.catColour": "Litur", "Games.catVariables": "Variablar", "Games.catProcedures": "Funktiónir", "Games.httpRequestError": "<PERSON>r var ein trupulleiki við umbønini.", "Games.linkAlert": "<PERSON><PERSON> b<PERSON>r hjá tær við hesi le<PERSON>:\n\n%1", "Games.hashError": "Orsaka '%1' samsvarar ikki við nakað goymt forrit.", "Games.xmlError": "Fái ikki innlisið tína goymdu fílu. Møguliga var hon gjørd við eini aðrari útgávu av Blockly?", "Games.submitted": "Takk fyri hetta forritið! Um okkara vælvandu apur dámar tað, so fara tey at útgeva tað í savninum um nakrar dagar.", "Games.listVariable": "listi", "Games.textVariable": "tekstur", "Games.breakLink": "Tá tú byrjar at rætta í forritinum við JavaScript, so kanst tú ikki longur rætta við blokkum. Er hetta í lagi?", "Games.blocks": "Blokkar", "Games.congratulations": "Til lukku!", "Games.helpAbort": "<PERSON>tta stigið er ófatiliga torført. Kundi tú hugsað tær at lopið tað um og farið til næsta spæl? Tú kanst altíð koma aftur seinni.", "Index.clear": "Strika allar tínar loys<PERSON>", "Index.subTitle": "<PERSON><PERSON><PERSON><PERSON> fyri framtíðar forritarar.", "Index.moreInfo": "Upplýsingar til lærarar...", "Index.startOver": "<PERSON><PERSON><PERSON>?", "Index.clearData": "<PERSON><PERSON><PERSON>", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON>ev", "Puzzle.animal1HelpUrl": "https://fo.wikipedia.org/wiki/Villdunna", "Puzzle.animal2": "Ketta", "Puzzle.animal2Trait1": "Kjálkaskegg", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://fo.wikipedia.org/wiki/Kettur", "Puzzle.animal3": "Býfluga", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://fo.wikipedia.org/wiki/B%C3%BD", "Puzzle.animal4": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "Skel", "Puzzle.animal4Trait2": "Slím", "Puzzle.animal4HelpUrl": "https://fo.wikipedia.org/wiki/Sniglar", "Puzzle.picture": "mynd:", "Puzzle.legs": "bein:", "Puzzle.legsChoose": "vel...", "Puzzle.traits": "<PERSON><PERSON>ð<PERSON>ni:", "Puzzle.error0": "Einastandandi!\nAllir %1 blokkar eru rættir.", "Puzzle.error1": "Næstan! Ein blokkur er ikki rættur.", "Puzzle.error2": "%1 blokkar eru ikki rættir.", "Puzzle.tryAgain": "<PERSON>n upplýsti blokkurin er ikki rættur.\nRoyn aftur.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON> eftir svarunum", "Puzzle.helpText": "<PERSON><PERSON><PERSON> hvørt dj<PERSON> (<PERSON><PERSON><PERSON><PERSON>), <PERSON><PERSON><PERSON> <PERSON> myndina, vel tal av beinum og ger ein stakk av eyðkennum.", "Maze.moveForward": "flyt fram", "Maze.turnLeft": "vend til vinstru", "Maze.turnRight": "vend til høgru", "Maze.doCode": "ger", "Maze.helpIfElse": "If-else blokkar gera eitt ella eitt annað", "Maze.pathAhead": "um rás er fyri framman", "Maze.pathLeft": "um rás er til vinstru", "Maze.pathRight": "um rás er til høgru", "Maze.repeatUntil": "end<PERSON><PERSON> inntil", "Maze.moveForwardTooltip": "Flyt leikaran eitt stig fram.", "Maze.turnTooltip": "<PERSON><PERSON>ir le<PERSON> 90 gradir til vinstru ella høgru.", "Maze.ifTooltip": "Um tað er ein rás í ásettu k<PERSON>, so skal okkurt gerast.", "Maze.ifelseTooltip": "Um ein rás er í á<PERSON> k<PERSON>, so skal fyrsti blokkurin av gerðum gerast. <PERSON><PERSON>, ger seinna blokkin av gerðum.", "Maze.whileTooltip": "Endurtak ásettu gerðirnar inntil málið er nátt.", "Maze.capacity0": "Tú hevur %0 blokkar eftir.", "Maze.capacity1": "Tú hevur %1 blokk eftir.", "Maze.capacity2": "Tú hevur %2 blokkar eftir.", "Maze.runTooltip": "<PERSON><PERSON>, at spælarin ger tað, sum blokkurin sigur.", "Maze.resetTooltip": "Set spælaran aftur til byrjanina av villiniborgini.", "Maze.helpStack": "<PERSON><PERSON><PERSON><PERSON> nakrar 'flyt fram' blokkar saman fyri at hjálpa mær at røkka málinum.", "Maze.helpOneTopBlock": "Á hesum stiginum mást tú stápla saman allar blokkarnar í tí hvíta arbeiðsøkinum.", "Maze.helpRun": "<PERSON><PERSON> forritið fyri at síggja, hvat hendir.", "Maze.helpReset": "Forrit<PERSON>ð hjá tær loysti ikki avbjóðingina. Trýst á 'Nullstilla' og royn aftur.", "Maze.helpRepeat": "Kom til endan av hesi rásini við einans at brúka tveir blokkar. Brúka 'endurt<PERSON>' fyri at koyra ein blokk meira enn eina ferð.", "Maze.helpCapacity": "Tú hevur brúkt allar blokkarnar til hetta stigið. Fyri at gera ein nýggjan blokk, er neyðugt at tú fyrst strikar ein verandi blokk.", "Maze.helpRepeatMany": "<PERSON>ra enn ein blokkur rúmast inni í einum 'endurtak' blokki.", "Maze.helpSkins": "Vel tín yndisleikara frá hesi valmy<PERSON>.", "Maze.helpIf": "Ein 'um' blokkur ger einans nakað, um treytin er sonn. <PERSON><PERSON> at venda til vinstru um ein leið er til vinstru.", "Maze.helpMenu": "Klikka á %1 í 'um' blokkinum fyri at broyta treytina.", "Maze.helpWallFollow": "<PERSON><PERSON><PERSON><PERSON> tú at loysa hesa avbjóðandi villiniborgina? <PERSON><PERSON> at fylgja vegginum á vinstru hond. Einans fyri framkomnar forritarar!", "Bird.noWorm": "hevur ikki maðk", "Bird.heading": "set kós", "Bird.noWormTooltip": "<PERSON><PERSON> tá fuglurin ikki hevur fingið maðkin.", "Bird.headingTooltip": "Flyt samsvarandi vinklinum: 0 er til høgru, 90 er beint upp, o.s.fr.", "Bird.positionTooltip": "x og y vísa hvar fulgurin er staddur. Tá x = 0 er fuglurin á vinstra kanti, tá x = 100 er hann á høgra kanti. Tá y = 0 er fuglurin á niðasta kanti, tá y = 100 er hann á ovara kanti.", "Bird.helpHeading": "<PERSON><PERSON><PERSON> vinkulin fyri at fáa fuglin at fanga maðkin og lenda í reiðrinum.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON><PERSON> henda blokkin fyri at fara í ein rætning, um tú hevur maðkin, el<PERSON> í ein annan rætning, um tú ikki hevur maðkin.", "Bird.helpX": "'x' er tín núverandi vatnrætta støða. <PERSON><PERSON><PERSON><PERSON> henda blokkin fyri at fara í ein rætning um 'x' er minni enn eitt tal, og far annars í ein annan rættning.", "Bird.helpElse": "Klikka á ikonina fyri at broyta 'um' blokkin.", "Bird.helpElseIf": "<PERSON><PERSON> stigið hevur brúk fyri bæði ein 'annars um' og ein 'annars' blokk.", "Bird.helpAnd": "'Og' blokkurin er einans sannur um báðir partar av blokkinum eru sannir.", "Bird.helpMutator": "<PERSON><PERSON><PERSON> ein 'annars' blokk inn í 'um' blokkin.", "Turtle.moveTooltip": "Flyt skjaldbøkun fram ella aftur við einum ásettum tali.", "Turtle.moveForward": "flyt fram við", "Turtle.moveBackward": "flyt aftur við", "Turtle.turnTooltip": "Vend skjaldbøkuna til vinstru ella høgru eftir einum ásettum vinkli.", "Turtle.turnRight": "vend til høgru við", "Turtle.turnLeft": "vend til vinstru við", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON><PERSON> breiddina á penninum.", "Turtle.setWidth": "set breidd til", "Turtle.colourTooltip": "Broytir litin á penninum.", "Turtle.setColour": "set lit til", "Turtle.penTooltip": "Lyftir ella setir pennin niður, fyri at steðga ella byrja at tekna.", "Turtle.penUp": "pennur upp", "Turtle.penDown": "pennur niður", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON> <PERSON> at skjaldbøkan (sirkul og pílur) er sjónlig ella ó<PERSON>jónlig.", "Turtle.hideTurtle": "fjal sk<PERSON><PERSON>", "Turtle.showTurtle": "vís skja<PERSON>øku", "Turtle.printTooltip": "<PERSON><PERSON><PERSON> tekst tann vegin, sum sk<PERSON><PERSON><PERSON><PERSON><PERSON> vendir, har sum hon er.", "Turtle.print": "prenta", "Turtle.fontTooltip": "<PERSON><PERSON><PERSON>, sum verður brúktur av prentblokkinum.", "Turtle.font": "<PERSON><PERSON>", "Turtle.fontSize": "skriftstødd", "Turtle.fontNormal": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontBold": "feitur", "Turtle.fontItalic": "skákskrift", "Turtle.submitDisabled": "<PERSON><PERSON> forritið inntil tað steðgar. <PERSON><PERSON>ðani kanst tú senda tekningarnar hjá tær til myndasavnið.", "Turtle.galleryTooltip": "Lat myndasavnið upp.", "Turtle.galleryMsg": "<PERSON><PERSON>", "Turtle.submitTooltip": "Send tínar tekningar til myndasavnið.", "Turtle.submitMsg": "Send til Myndasavnið", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON> hjá tær riggar, men tú kanst gera tað enn betri.", "Turtle.helpUseLoop3": "Tekna skapið við einans trimum blokkum.", "Turtle.helpUseLoop4": "Tekna stjørnuna við einans fýra blokkum.", "Turtle.helpText1": "<PERSON>er eitt forrit, sum teknar ein ferning.", "Turtle.helpText2": "<PERSON><PERSON><PERSON> for<PERSON>, soleið<PERSON> at tað teknar ein fimmkant í staðin fyri ein ferning.", "Turtle.helpText3a": "Nú er ein nýgg<PERSON> blo<PERSON>r, sum letur teg broyta litin:", "Turtle.helpText3b": "<PERSON><PERSON>na eina gula stjørnu.", "Turtle.helpText4a": "Nú er ein ný<PERSON> b<PERSON>, sum letur teg lyfta pennin upp av pappírinum, tá tú flytir hann:", "Turtle.helpText4b": "Tekna eina lítla gula stjørnu, og tekna síðani eina linju omanfyri hana.", "Turtle.helpText5": "Í staðin fyri eina stjørnu, kanst tú tekna fýra stjørnur í einum ferningi?", "Turtle.helpText6": "Tekna tríggjar gular stjørnur og eina hvíta linju.", "Turtle.helpText7": "Tekna stjørnurnar og tekna síðani fýra hvítar linjur.", "Turtle.helpText8": "Tá tú teknar 360 hvítar linjur, so sær tað út sum ein fullmáni.", "Turtle.helpText9": "<PERSON>r til hjá tær at tekna ein svartan sirkul, soleiðis at mánin ikki longur er fullur?", "Turtle.helpText10": "Tekna hvat tú vilt. Tú hevur eina rúgvu av nýggjum blokkum, sum tú kanst kanna. Hav tað stuttligt!", "Turtle.helpText10Reddit": "Brúka '<PERSON><PERSON>' knøttin fyri at síggja hvat onnur hava teknað. Teknar tú okkurt áhugavert, so kanst tú brúka 'Send til Myndasavn' knøttin at útgeva tað.", "Turtle.helpToolbox": "<PERSON><PERSON> ein bólk fyri at síggja blokkarnar.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "<PERSON><PERSON><PERSON><PERSON> x", "Movie.y1": "<PERSON><PERSON><PERSON><PERSON> y", "Movie.x2": "enda x", "Movie.y2": "enda y", "Movie.radius": "radius", "Movie.width": "brei<PERSON>", "Movie.height": "<PERSON>æ<PERSON>", "Movie.circleTooltip": "<PERSON><PERSON>nar ein sirkul á ásetta staðnum við ásetta radiusinum.", "Movie.circleDraw": "<PERSON><PERSON><PERSON>", "Movie.rectTooltip": "<PERSON><PERSON><PERSON> eitt rættvinklaðan fýrakant á ásetta staðnum við ásettu breiddini og hæddini.", "Movie.rectDraw": "rættvinklaður fýrak<PERSON>ur", "Movie.lineTooltip": "<PERSON><PERSON><PERSON> eina linju frá einum punkti til eitt annað við á<PERSON> brei<PERSON>.", "Movie.lineDraw": "linja", "Movie.timeTooltip": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> tí<PERSON> (0-100).", "Movie.colourTooltip": "Broytir litin á penninum.", "Movie.setColour": "set lit til", "Movie.submitDisabled": "Filmurin hjá tær flytir seg ikki. Brúka blokkar fyri at gera okkurt áhugavert. Síðani kanst tú senda filmin til myndasavnið.", "Movie.galleryTooltip": "Lat filmssavnið upp.", "Movie.galleryMsg": "Sí filmssavnið", "Movie.submitTooltip": "Send filmin hjá tær til filmssavnið.", "Movie.submitMsg": "Send til films<PERSON>vnið", "Movie.helpLayer": "<PERSON>t bakgrundssirkulin ovast í forritið hjá tær. So fer hann at síggjast aftan fyri pers<PERSON>in.", "Movie.helpText1": "Brú<PERSON> einføld skap til at tekna hendan persónin.", "Movie.helpText2a": "<PERSON>tta stigið er ein filmur. Tú vilt fáa armin hjá persóninum at flyta seg tvørtur um skíggjan. Trýst á spæliknøttin fyri at síggja eina undansýning.", "Movie.helpText2b": "Tá filmurin spælir veksur virðið á tíðarblokkinum frá 0 til 100. <PERSON><PERSON> eigur at vera lætt, eftirsum at tú vilt hava y positiónina av arminum at byrja við 0 og enda við 100.", "Movie.helpText3": "Tíðarblokkurin telur frá 0 til 100. Nú skal y positiónin av hinum arminum byrja við 100 og enda við 0. <PERSON>ur tú ein einfaldan frymil, sum roknar tað út?", "Movie.helpText4": "<PERSON><PERSON><PERSON><PERSON> tað, ið tú lærdi í undanfarna stiginum, at gera bein, sum krossast.", "Movie.helpText5": "<PERSON><PERSON><PERSON> fyri armin er fløktur. Her er svarið:", "Movie.helpText6": "<PERSON><PERSON> pers<PERSON>um tvær hendur.", "Movie.helpText7": "<PERSON><PERSON><PERSON><PERSON> 'um' blokkin til at tekna eitt lítið høvd í fyrru helvtini av filminum. Síðani skal tú tekna eitt stórt høvd til seinnu helvtina av filminum.", "Movie.helpText8": "<PERSON><PERSON>, at beinini fara umvendan veg, tá filmurin er hál<PERSON>run<PERSON>.", "Movie.helpText9": "Tekna ein vaksandi sirkul aftan fyri pers<PERSON>in.", "Movie.helpText10": "Ger ein film av tí sum tær lystir. Tú hevur eina rúgvu av nýggjum blokkum, sum tú kanst kanna. Hav tað stuttligt!", "Movie.helpText10Reddit": "Brúka 'S<PERSON> filmsavn' knøttin fyri at síggja filmar, ið onnur hava gjørt. Skapar tú okkurt áhugavert, so kanst tú brúka 'Send til filmsavn' knøttin at útgeva tað.", "Music.playNoteTooltip": "<PERSON><PERSON><PERSON><PERSON>r ein tóna við ásettu longd og tónahædd.", "Music.playNote": "spæl %1 tóna %2", "Music.restTooltip": "<PERSON><PERSON><PERSON><PERSON> tíð<PERSON>.", "Music.restWholeTooltip": "<PERSON><PERSON><PERSON><PERSON> ein heilan tóna.", "Music.rest": "hvíl %1", "Music.setInstrumentTooltip": "Skiftur til tað ásetta ljóðførið, sum skal spæla næstu tónarnar.", "Music.setInstrument": "set lj<PERSON><PERSON><PERSON><PERSON><PERSON> til %1", "Music.startTooltip": "<PERSON>j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> blokkarnar inni í tá ið klikt verður á 'Koyr forrit' knappin.", "Music.start": "tá %1 verður klikt", "Music.pitchTooltip": "<PERSON> tóni (C4 er 7)", "Music.firstPart": "fyrsti partur", "Music.piano": "klaver", "Music.trumpet": "trompet", "Music.banjo": "banjo", "Music.violin": "violin", "Music.guitar": "gittari", "Music.flute": "floyta", "Music.drum": "trumma", "Music.choir": "kór", "Music.submitDisabled": "<PERSON><PERSON> forritið til tað steðgar. Síðani kanst tú senda tónleikin til savnið.", "Music.galleryTooltip": "Lat tónleikasavnið upp.", "Music.galleryMsg": "Sí tónleikasavnið.", "Music.submitTooltip": "Send tín tónleik til tónleikasavnið.", "Music.submitMsg": "Send til almenna listasavnið", "Music.helpUseFunctions": "<PERSON><PERSON><PERSON> hjá tær riggar, men tú klárar tað betri. Brúka funktiónir fyri at minka um nøgdina av endurtakandi kodu.", "Music.helpUseInstruments": "Tónleikurin ljóðar betri, um tú brúkar eitt annað ljóðfø<PERSON> í hvørjum startblokki.", "Music.helpText1": "Set saman fyrstu fýra tónarnar av '<PERSON><PERSON><PERSON>'.", "Music.helpText2a": "Ein 'funktión' loyvir tær at bólka blokkar, sum tú síðani kanst koyra meira enn eina ferð.", "Music.helpText2b": "<PERSON><PERSON> <PERSON><PERSON>, sum spæla fyrstu fýra tónarnar av 'Dovni Jákup'. <PERSON>yr ta funktiónina tvær ferðir. Lat vera við at leggja nakrar nýggjar blokkar afturat.", "Music.helpText3": "Ger næstu funktión<PERSON> fyri seinna partin av '<PERSON>vni Ják<PERSON>'. Síðsti tónin er longri.", "Music.helpText4": "Ger eina triðju funktión fyri triðja partin av '<PERSON>vni Jákup'. <PERSON><PERSON> fýra fyrstu tónarnir eru styttri.", "Music.helpText5": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' lagið liðugt.", "Music.helpText6a": "<PERSON><PERSON> nýggi blokkurin letur teg skifta til eitt annað lj<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Music.helpText6b": "<PERSON><PERSON><PERSON><PERSON> lagið hjá tær við eini violin.", "Music.helpText7a": "<PERSON><PERSON> n<PERSON>ggi blokkurin leggur eina ljó<PERSON> seinking inn.", "Music.helpText7b": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, sum hevur tveir seinkingarblokkar, og síðani eisini spælir '<PERSON>v<PERSON> Jákup'.", "Music.helpText8": "<PERSON><PERSON><PERSON><PERSON> startblokkur skal spæla 'Dovni Ják<PERSON>' tvær ferðir.", "Music.helpText9": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, sum hvør spæla '<PERSON>v<PERSON> Jákup' tvær ferðir. Legg rætta talið av seinkingarblokkum afturat.", "Music.helpText10": "Komponera tað tú vilt. Tú hevur eina rúgvu av nýggjum blokkum, sum tú kanst kanna. Hav tað stuttligt!", "Music.helpText10Reddit": "Brúka '<PERSON><PERSON> tónasavn' knøttin fyri at síggja, hvat onnur hava skapað. Komponerar tú okkurt áhugavert, so kanst tú brúka 'Send til tónasavn' knøttin at útgeva tað.", "Pond.scanTooltip": "<PERSON><PERSON><PERSON> eftir fíggindum. <PERSON><PERSON> e<PERSON> k<PERSON> (0-360). <PERSON><PERSON>ur frástøðuna til nærmasta fíggindan í ásettu kósini. <PERSON><PERSON><PERSON>, um eingin fíggindi verður funnin.", "Pond.cannonTooltip": "Skj<PERSON><PERSON> við kan<PERSON>. <PERSON><PERSON> e<PERSON> k<PERSON> (0-360) og eina frás<PERSON>ø<PERSON> (0-70).", "Pond.swimTooltip": "Svim frameftir. <PERSON><PERSON> (0-360).", "Pond.stopTooltip": "Gevst at svimja. Sp<PERSON><PERSON>in minkar um ferðina til hann steðgar.", "Pond.healthTooltip": "Gevur núverandi heilsuna hjá spælaranum (0 er deyður, 100 er frískur).", "Pond.speedTooltip": "<PERSON><PERSON>ur núverandi ferðina hjá spælaranum (0 er steðgaður, 100 er full ferð).", "Pond.locXTooltip": "Gevur X krosstalið hjá spælaranum (0 er vinstri kantur, 100 er høgri kantur).", "Pond.locYTooltip": "Gevur Y krosstalið hjá spælaranum (0 er niðari kantur, 100 er ovari kantur).", "Pond.docsTooltip": "<PERSON><PERSON> m<PERSON> s<PERSON>.", "Pond.documentation": "Sk<PERSON><PERSON>esting", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "Ó<PERSON>full/ur", "Pond.helpUseScan": "<PERSON><PERSON><PERSON> hjá tær riggar, men tú kanst gera tað betri. Brúka 'scan' fyri at fortelja kanónini, hvussu langt hon skal skjóta.", "Pond.helpText1": "<PERSON><PERSON><PERSON><PERSON> 'cannon' b<PERSON><PERSON><PERSON> fyri at raka málið. Fyrsti parameturin er vinkulin og annar parameturin er hvussu langt. Finn tað røttu sa<PERSON>.", "Pond.helpText2": "<PERSON><PERSON><PERSON><PERSON><PERSON> er at raka hetta málið fleiri ferðir. <PERSON><PERSON><PERSON><PERSON> e<PERSON> 'while (true)' lyk<PERSON><PERSON> fyri at gera okkurt óendaliga ofta.", "Pond.helpText3a": "<PERSON><PERSON> mó<PERSON>tøðuf<PERSON>lkið flytir seg aftur og fram, og tað ger tað ringt at raka. 'scan' gevur nágreiniligu frástøðuna til mótstøðufólkið í ásetta rætningin.", "Pond.helpText3b": "<PERSON><PERSON><PERSON> frástøðan er akkur<PERSON>t tað, sum 'cannon' boðini hava tørv á fyri at raka beint.", "Pond.helpText4": "<PERSON><PERSON> mótstøðufólkið er ov langt burturi fyri kanónina (sum hevur eina avmarking uppá 70 metrar). Ístaðin mást tú brúka 'swim' boð<PERSON> fyri at byr<PERSON> at svimja yvir til mótstøðufólkið og krassja í tað.", "Pond.helpText5": "<PERSON><PERSON> mótstøðufólkið er eisini ov langt burturi fyri at kanónin kann brúkast. Men tú ert ov veikur at yvirliva ein samanstoyt. Svim yvir móti mótstøðufólkinum meðan horisontala positiónin hjá tær er minni enn 50. 'stop' s<PERSON>ðani og brúka kanónina.", "Pond.helpText6": "<PERSON><PERSON> m<PERSON>ø<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> flytir seg burtur, tá tað verður rakt. <PERSON><PERSON><PERSON> móti tí, um tað er ov langt vekk (70 metrar).", "Gallery": "Savn"}