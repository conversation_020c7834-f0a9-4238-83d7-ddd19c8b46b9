{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Costin PIETR<PERSON>U", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Minisarm", "<PERSON><PERSON>"]}, "Games.name": "Blockly Games", "Games.puzzle": "Puzzle", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.turtle": "Țestoasă", "Games.movie": "Film", "Games.music": "Muzică", "Games.pondTutor": "Pond Tutor", "Games.pond": "Pond", "Games.linesOfCode1": "Ai rezolvat acest nivel cu o linie de JavaScript:", "Games.linesOfCode2": "Ați rezolvat acest nivel cu %1 linii de JavaScript:", "Games.nextLevel": "Ești pregatit pentru nivelul %1?", "Games.finalLevel": "Sunteți pregătiți pentru următoarea provocare?", "Games.submitTitle": "Titlu:", "Games.linkTooltip": "Salvează și adaugă la blocuri.", "Games.runTooltip": "Rulați programul pe care l-ați scris.", "Games.runProgram": "Rulează programul", "Games.resetTooltip": "Opriți programul și resetați nivelul.", "Games.resetProgram": "Resetează", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logic", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematică", "Games.catText": "Text", "Games.catLists": "Liste", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variabile", "Games.catProcedures": "Funcții", "Games.httpRequestError": "A apărut o problemă la solicitare.", "Games.linkAlert": "Distribuie-ți blocurile folosind această legătură:\n\n%1", "Games.hashError": "<PERSON><PERSON>, „%1” nu corespunde nici unui program salvat.", "Games.xmlError": "Sistemul nu a putut încărca fișierul salvat. Poate că a fost creat cu o altă versiune de Blockly?", "Games.submitted": "Vă mulțumim pentru acest program! Dacă personalul nostru de maimuțe instruite îl plac, îl vor publica în galerie în câteva zile.", "Games.listVariable": "listă", "Games.textVariable": "text", "Games.breakLink": "După ce începeți să editați JavaScript, nu vă puteți întoarce la blocurile de editare. E bine așa?", "Games.blocks": "<PERSON><PERSON>", "Games.congratulations": "Felicitări!", "Games.helpAbort": "Acest nivel este extrem de dificil. Doriți să îl săriți și să mergeți la următorul joc? Vă puteți întoarce întotdeauna mai târziu.", "Index.clear": "Ștergeți toate soluțiile?", "Index.subTitle": "Jocuri pentru programatorii de mâine.", "Index.moreInfo": "Mai multe informații", "Index.startOver": "<PERSON><PERSON>i s<PERSON>?", "Index.clearData": "Ștergeț<PERSON> datele", "Puzzle.animal1": "Ra<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "Cioc", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Pisică", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON>ă", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "Albină", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "Ac", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "Mel<PERSON>", "Puzzle.animal4Trait1": "Co<PERSON>lie", "Puzzle.animal4Trait2": "Slime", "Puzzle.animal4HelpUrl": "https://ro.wikipedia.org/wiki/Melc", "Puzzle.picture": "imagine:", "Puzzle.legs": "picioare:", "Puzzle.legsChoose": "alege...", "Puzzle.traits": "calități:", "Puzzle.error0": "Perfect!\nToate cele %1 (de) blocuri sunt corecte.", "Puzzle.error1": "Aproape! Un bloc este incorect.", "Puzzle.error2": "%1 (de) blocuri sunt incorecte.", "Puzzle.tryAgain": "Blocul evidențiat nu este corect.\nMai încearcă.", "Puzzle.checkAnswers": "Verifică răspunsurile", "Puzzle.helpText": "<PERSON><PERSON>u fiecare animal (verde), at<PERSON><PERSON><PERSON><PERSON><PERSON> imaginea, alege<PERSON><PERSON> numărul de picioare și faceți o serie de trăsături.", "Maze.moveForward": "merge înainte", "Maze.turnLeft": "întoarce la stânga", "Maze.turnRight": "înto<PERSON>ce la dreapta", "Maze.doCode": "execută", "Maze.helpIfElse": "<PERSON><PERSON><PERSON> „dacă-altfel” vor efectua fie o anumită acțiune, fie pe cealaltă.", "Maze.pathAhead": "dacă o cale î<PERSON>nte", "Maze.pathLeft": "dacă o cale spre stânga", "Maze.pathRight": "dacă o cale spre dreapta", "Maze.repeatUntil": "repetă până când", "Maze.moveForwardTooltip": "Mută jucătorul înainte cu un singur spațiu.", "Maze.turnTooltip": "Rotește personajul la stânga sau la dreapta cu 90 de grade.", "Maze.ifTooltip": "Dacă există o cale liberă în direcția aleasă, atunci execută câteva acțiuni.", "Maze.ifelseTooltip": "Dacă există o cale în direcția specificată, atunci execută primul bloc de acțiuni. În caz contrar, execută al doilea bloc de acțiuni.", "Maze.whileTooltip": "Repetă comenzile incluse până când atinge punctul de final.", "Maze.capacity0": "Mai ai %0 blocuri r<PERSON>.", "Maze.capacity1": "Mai ai %1 bloc rămas.", "Maze.capacity2": "Mai ai %2 (de) blocuri r<PERSON>.", "Maze.runTooltip": "Face ca <PERSON><PERSON>l s<PERSON> execute comenzile bloc<PERSON>lor.", "Maze.resetTooltip": "Trimite personajul la punctul de început al labirintului.", "Maze.helpStack": "Alipește mai multe blocuri de „merge înainte” pentru a mă ajuta să-mi ating obiectivul.", "Maze.helpOneTopBlock": "La acest nivel trebuie să alipești toate blocurile în spațiul de lucru alb.", "Maze.helpRun": "Rulează-ți programul pentru a vedea ce se întâmplă.", "Maze.helpReset": "Programul tău nu a rezolvat labirintul. Apasă „Resetează” și mai încearcă o dată.", "Maze.helpRepeat": "<PERSON><PERSON>i până la sfârșitul acestui drum, utilizând doar două blocuri. Pentru a rula un bloc de mai multe ori folosește comanda „repetă”.", "Maze.helpCapacity": "<PERSON> folosit toate blocurile pentru acest nivel. Pentru a crea un nou bloc, trebuie mai întâi să ștergi un bloc existent.", "Maze.helpRepeatMany": "Poți include mai mult de un bloc în interiorul unui bloc „repetă până când”.", "Maze.helpSkins": "Alege-ți personajul favorit din acest meniu.", "Maze.helpIf": "Un bloc 'dacă' face ceva numai dacă condiția este adevărată. Încearcă să cotești la stânga dacă există o cale la stânga.", "Maze.helpMenu": "Dă click pe %1 în blocul 'dacă' pentru a-i schimba condiția.", "Maze.helpWallFollow": "Poți să rezolvi labirintul acesta complicat? Încearcă să urmărești zidul dinspre mâna stângă. Numai pentru programatori avansați!", "Bird.noWorm": "nu are vierme", "Bird.heading": "titlu", "Bird.noWormTooltip": "Situația când pasărea nu a dobândit viermele.", "Bird.headingTooltip": "Deplasați-vă în direcția unghiului dat: 0 este în dreapta, 90 este în sus, etc.", "Bird.positionTooltip": "x și y marchează poziția păsării. Când x = 0 pasărea se află lângă marginea din stânga, atunci când x = 100 se află lângă marginea dreaptă. Când y = 0 pasărea este în partea de jos, când y = 100 este în partea de sus.", "Bird.helpHeading": "Modificați unghiul de înclinare pentru a face pasărea să primească viermele și ateriza în cuibul ei.", "Bird.helpHasWorm": "Utilizați acest bloc pentru a merge într-o singură poziție dacă aveți viermele sau o altă rubrică dacă nu aveți viermele.", "Bird.helpX": "\"x\" reprezintă poziția orizontală actuală. Utilizați acest bloc pentru a merge într-o singură poziție dacă 'x' este mai mic decât un număr sau alt titlu.", "Bird.helpElse": "<PERSON><PERSON><PERSON> clic pe pictograma pentru a modifica blocul \"if\".", "Bird.helpElseIf": "Acest nivel necesită atât un bloc 'else if', cât și un 'else'.", "Bird.helpAnd": "Blocul 'and' este valabil numai dacă ambele intrări sunt adev<PERSON>rate.", "Bird.helpMutator": "Trageți un bloc 'else' în blocul 'if'.", "Turtle.moveTooltip": "Deplasează țestoasa înainte sau înapoi cu valoarea specificată.", "Turtle.moveForward": "deplasează înainte cu", "Turtle.moveBackward": "deplasează înapoi cu", "Turtle.turnTooltip": "Întoarce <PERSON>sa la stânga sau la dreapta cu numărul de grade specificat.", "Turtle.turnRight": "întoarce la dreapta cu", "Turtle.turnLeft": "întoarce la stânga cu", "Turtle.widthTooltip": "Modifică lățimea stiloului.", "Turtle.setWidth": "setează lățimea la", "Turtle.colourTooltip": "Schimbă culoarea stiloului.", "Turtle.setColour": "setează culoarea la", "Turtle.penTooltip": "Ridică sau coboară stiloul pentru a opri sau începe desenarea.", "Turtle.penUp": "ridică stiloul", "Turtle.penDown": "coboar<PERSON> stiloul", "Turtle.turtleVisibilityTooltip": "Face țestoasa (cercul și săgeata) vizibilă sau invizibilă.", "Turtle.hideTurtle": "ascunde <PERSON>est<PERSON>", "Turtle.showTurtle": "<PERSON><PERSON><PERSON>", "Turtle.printHelpUrl": "https://ro.wikipedia.org/wiki/Tip<PERSON><PERSON>re", "Turtle.printTooltip": "Desenează textul pe direcția țestoasei și în poziția acesteia.", "Turtle.print": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.fontHelpUrl": "https://ro.wikipedia.org/wiki/Font", "Turtle.fontTooltip": "Setează fontul utilizat de blocul de afișare.", "Turtle.font": "font", "Turtle.fontSize": "dimensiunea fontului", "Turtle.fontNormal": "normală", "Turtle.fontBold": "al<PERSON>", "Turtle.fontItalic": "cursiv", "Turtle.submitDisabled": "Rulați programul până se oprește. Apoi puteți trimite desenul la galerie.", "Turtle.galleryTooltip": "Deschideți galeria desenelor.", "Turtle.galleryMsg": "Vedeți Galerie", "Turtle.submitTooltip": "Trimiteți desenul spre galerie.", "Turtle.submitMsg": "Trimiteți la Galerie", "Turtle.helpUseLoop": "Soluția ta funcționează, dar po<PERSON><PERSON> face mai bine.", "Turtle.helpUseLoop3": "Desenați forma cu doar trei blocuri.", "Turtle.helpUseLoop4": "Desenați steaua cu doar patru blocuri.", "Turtle.helpText1": "Creați un program care desenează un pătrat.", "Turtle.helpText2": "Modificați programul pentru a desena un pentagon în locul unui pătrat.", "Turtle.helpText3a": "Există un nou bloc care vă permite să modificați culoarea:", "Turtle.helpText3b": "Desenați o stea galbenă.", "Turtle.helpText4a": "Există un nou bloc care vă permite să ridicați creionul de pe hârtie când vă mutați:", "Turtle.helpText4b": "Desenați o mică stea galbenă, apoi trageți o linie deasupra ei.", "Turtle.helpText5": "În loc de o stea, poți desena patru stele aranjate într-un pătrat?", "Turtle.helpText6": "Desenați trei stele galbene și o linie albă.", "Turtle.helpText7": "Desena<PERSON>i stelele, apoi desenați patru linii albe.", "Turtle.helpText8": "Desenarea a 360 de linii albe vor arata ca luna plină.", "Turtle.helpText9": "Puteți adăuga un cerc negru astfel încât luna să devină o semilună?", "Turtle.helpText10": "Desenați tot ce doriți. Ai un număr mare de blocuri noi pe care le poți explora. A se distra!", "Turtle.helpText10Reddit": "Utilizați opțiunea \"Vezi galerie\" pentru a vedea ce au desenat alte persoane. Dacă desenați ceva interesant, utilizați funcția \"Trimiteți la Galerie\" buton pentru a publica.", "Turtle.helpToolbox": "Alegeți o categorie pentru a vedea blocurile.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "începe x", "Movie.y1": "start y", "Movie.x2": "sfâr<PERSON><PERSON> x", "Movie.y2": "s<PERSON><PERSON><PERSON><PERSON><PERSON> y", "Movie.radius": "rază", "Movie.width": "lățime", "Movie.height": "înălțime", "Movie.circleTooltip": "Desenează un cerc la locația specificată și cu raza specificată.", "Movie.circleDraw": "cerc", "Movie.rectTooltip": "Desenează un dreptunghi în locația specificată și cu lățimea și înălțimea specificate.", "Movie.rectDraw": "dre<PERSON><PERSON><PERSON>", "Movie.lineTooltip": "Desenează o linie dintr-un punct în altul cu lățimea specificată.", "Movie.lineDraw": "linie", "Movie.timeTooltip": "Returnează ora curentă în animație (0-100).", "Movie.colourTooltip": "Modifică culoarea stiloului.", "Movie.setColour": "setează culoarea la", "Movie.submitDisabled": "Filmul tău nu se mișcă. Utilizați blocuri pentru a face ceva interesant. Apoi, puteți trimite filmul în galerie.", "Movie.galleryTooltip": "Deschideți galeria de filme.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON><PERSON> Gale<PERSON>", "Movie.submitTooltip": "Trimiteți filmul spre galerie.", "Movie.submitMsg": "Trimiteți la Galerie", "Movie.helpLayer": "Deplasați cercul de fond în partea de sus a programului. Apoi, va apărea în spatele persoanei.", "Movie.helpText1": "Utilizați forme simple pentru a desena această persoană.", "Movie.helpText2a": "Acest nivel este un film. Vrei ca brațul persoanei să se deplaseze pe ecran. Apăsați butonul de redare pentru a vedea o previzualizare.", "Movie.helpText2b": "Pe măsură ce filmul rulează, valoarea blocului 'time' se calculează de la 0 la 100. Deoarece doriți ca poziția 'y' a brațului să înceapă la 0 și să meargă la 100, acest lucru ar trebui să fie ușor.", "Movie.helpText3": "Blocul 'time' numără de la 0 la 100. Dar acum doriți ca poziția 'y' a celuilalt braț să înceapă la 100 și să meargă la 0. Îți poți da seama de o formulă matematică simplă care învinge direcția?", "Movie.helpText4": "Utilizați ceea ce ați învățat la nivelul anterior pentru a face picioare care se încrucișează.", "Movie.helpText5": "Formula matematică a brațului este complicată. Iată răspunsul:", "Movie.helpText6": "Dă-i o pereche de mâini.", "Movie.helpText7": "Utilizați blocul 'if' pentru a desena un cap mic pentru prima jumătate a filmului. Apoi desenează un cap mare pentru a doua jumătate a filmului.", "Movie.helpText8": "Faceți picioarele să meargă în direcția inversă la jumătatea filmului.", "Movie.helpText9": "Desenați un cerc în spatele persoanei.", "Movie.helpText10": "Faceți un film cu tot ce doriți. Ai un număr mare de blocuri noi pe care le poți explora. Distrează-te!", "Movie.helpText10Reddit": "Utilizați opțiunea \"Vezi galerie\"  pentru a vedea filmele pe care alte persoane le-au făcut. Dacă faceți un film interesant, utilizați funcția \"Trimiteți la Galerie\" buton pentru a publica.", "Music.playNoteTooltip": "Redă o notă muzicală a duratei și pasului specificat.", "Music.playNote": "redă %1 notă %2", "Music.restTooltip": "Se așteaptă durata specificată.", "Music.restWholeTooltip": "Se așteaptă o notă întreagă.", "Music.rest": "restul %1", "Music.setInstrumentTooltip": "Comută la instrumentul specificat când redați note muzicale ulterioare.", "Music.setInstrument": "setați instrumentul la %1", "Music.startTooltip": "Execută blocurile din interior atunci când se face clic pe butonul 'Run Program'.", "Music.start": "când %1 a făcut clic", "Music.pitchTooltip": "O notă (C4 este 7).", "Music.firstPart": "Prima parte", "Music.piano": "pian", "Music.trumpet": "trompetă", "Music.banjo": "banjo", "Music.violin": "vioară", "Music.guitar": "chitară", "Music.flute": "flaut", "Music.drum": "<PERSON><PERSON><PERSON>", "Music.choir": "cor", "Music.submitDisabled": "Rulați programul până se oprește. Apoi, puteți trimite muzica la galerie.", "Music.galleryTooltip": "Deschideți galeria de filme.", "Music.galleryMsg": "Vedeți Galerie", "Music.submitTooltip": "Trimiteți-vă muzica la galerie.", "Music.submitMsg": "Trimiteți la Galerie", "Music.helpUseFunctions": "Soluția ta funcționează, dar po<PERSON><PERSON> face mai bine. Utilizați funcțiile pentru a reduce cantitatea de cod repetat.", "Music.helpUseInstruments": "Muzica va suna mai bine dacă folosiți un instrument diferit în fiecare bloc de pornire.", "Music.helpText1": "Scrieți primele patru note ale lui '<PERSON><PERSON>'.", "Music.helpText2a": "O 'function' care vă permite să grupați blocurile împreună, apoi să executați mai multe de o dată.", "Music.helpText2b": "Creați o funcție pentru a reda primele patru note ale 'Frère <PERSON>'. Rulați această funcție de două ori. Nu adăugați blocuri noi.", "Music.helpText3": "Creați a doua funcție pentru următoarea parte a 'Frère Jacques'. Ultima notă este mai lungă.", "Music.helpText4": "Creați a treia funcție pentru următoarea parte a 'Frère Jacques'. Primele patru note sunt mai scurte.", "Music.helpText5": "Finalizați melodia completă a lui 'Frère Jacques'.", "Music.helpText6a": "Acest nou bloc vă permite să treceți la un alt instrument.", "Music.helpText6b": "Redați melodia dvs. cu o vioară.", "Music.helpText7a": "Acest nou bloc adaugă o întârziere tacită.", "Music.helpText7b": "Creați un al doilea bloc de pornire care are două blocuri de întârziere, apoi joacă și '<PERSON><PERSON> Jacques'.", "Music.helpText8": "Fiecare bloc de pornire trebuie să joace de două ori '<PERSON><PERSON>'.", "Music.helpText9": "Creați patru blocuri de start care fiecare joac<PERSON> '<PERSON><PERSON> Jacques' de două ori. Adăugați numărul corect de blocuri de întârziere.", "Music.helpText10": "Compune orice vrei. Ai un număr mare de blocuri noi pe care le poți explora. Distrează-te!", "Music.helpText10Reddit": "Utilizați opțiunea \"Vezi galerie\" pentru a vedea ce alte persoane au compus. Dacă compuneți ceva interesant, utilizați butonul \"Trimiteți la Galerie\" pentru a publica.", "Pond.scanTooltip": "Căutați inamici. Specificați o direcție (0-360). Returnează distanța până la cel mai apropiat dușman în acea direcție. Returnează Infinit dacă nu s-a găsit niciun inamic.", "Pond.cannonTooltip": "Focul de tun. Specificați o direcție (0-360) și un interval (0-70).", "Pond.swimTooltip": "Înoată înainte. Specificați o direcție (0-360).", "Pond.stopTooltip": "Opriți înotul. Jucătorul va înceta să se oprească.", "Pond.healthTooltip": "Returnează sănătatea actuală a jucătorului (0 este mort, 100 este sănătos).", "Pond.speedTooltip": "Returnează viteza curentă a jucătorului (0 este oprit, 100 este viteză maximă).", "Pond.locXTooltip": "Returnează coordonatele X ale jucătorului (0 este marginea din stânga, 100 este marginea dreaptă).", "Pond.locYTooltip": "Returnează coordonatele Y ale playerului (0 este marginea de jos, 100 este marginea de sus).", "Pond.docsTooltip": "Afișați documentația lingvistică.", "Pond.documentation": "Documentație", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Țintă", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "Înspăimântat", "Pond.helpUseScan": "Soluția ta funcționează, dar <PERSON><PERSON><PERSON><PERSON> face mai bine. Utilizați 'scanare' pentru a spune tunului cât de departe să tragă.", "Pond.helpText1": "Utilizați comanda 'tun' pentru a atinge ținta. Primul parametru este unghiul, al doilea parametru este intervalul. Gasește combinația corectă.", "Pond.helpText2": "Acest obiectiv trebuie să fie lovit de mai multe ori. Utilizați o buclă 'while (true)' pentru a face ceva pe termen nelimitat.", "Pond.helpText3a": "Acest adversar se miș<PERSON><PERSON> înainte și înapoi, ceea ce face dificilă lovirea. Expresia 'scanare' returnează intervalul exact la adversar în direcția specificată.", "Pond.helpText3b": "Această gamă este exact ceea ce comanda 'tunului'  trebuie să declanșeze cu exactitate.", "Pond.helpText4": "Acest adversar este prea departe pentru a folosi tunul (care are o limită de 70 de metri). În schimb, utilizați comanda 'înotați' pentru a începe să înotați spre adversar și să se prăbușească în el.", "Pond.helpText5": "Acest adversar este, de asem<PERSON><PERSON>, prea departe pentru a folosi tunul. Dar ești prea slab pentru a supraviețui unei coliziuni. Învârtiți spre adversar în timp ce poziția dvs. orizontală este mai mică de 50. Apoi 'opriți' și utilizați tunul.", "Pond.helpText6": "Acest adversar se va îndepărta atunci când este lovit. Înotați spre el dacă este în afara razei de acțiune (70 de metri).", "Gallery": "Galerie"}