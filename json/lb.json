{"@metadata": {"authors": ["<PERSON>es", "<PERSON><PERSON>", "Soued031", "Volvox"]}, "Games.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.puzzle": "Puzzle", "Games.maze": "Labyrinth", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Muse<PERSON>", "Games.nextLevel": "Sidd Dir prett fir den Niveau %1?", "Games.finalLevel": "Sidd Dir prett fir déi nächst Erausfuerderung?", "Games.submitTitle": "Titel:", "Games.linkTooltip": "Sp<PERSON><PERSON>ren a mat de Bléck verlinken", "Games.runTooltip": "De Programm starten deen Dir geschriwwen hutt.", "Games.runProgram": "Programm ausféieren", "Games.resetTooltip": "De Programm stoppen an den Niveau zrécksetzen.", "Games.resetProgram": "Zrécksetzen", "Games.help": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Logik", "Games.catLoops": "Schleefen", "Games.catMath": "Mathematik", "Games.catText": "Text", "Games.catLists": "<PERSON><PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variabelen", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "Et gouf e Probleem mat der Ufro.", "Games.linkAlert": "<PERSON><PERSON> mat dësem Link:\n\n%1", "Games.hashError": "Leider entsprécht '%1' kengem vun de gespäicherte Programmer.", "Games.xmlError": "<PERSON>re gespäicherte Fichier konnt net geluede ginn. Vläicht hutt Dir e mat enger anerer Versioun vu <PERSON> gemaach?", "Games.listVariable": "<PERSON><PERSON><PERSON><PERSON>", "Games.textVariable": "Text", "Games.blocks": "<PERSON><PERSON><PERSON>", "Games.congratulations": "Gléckwonsch!", "Index.clear": "All Är Léisunge läschen?", "Index.subTitle": "Spiller fir d'Programméierer vu muer.", "Index.moreInfo": "Pedagogesch Informatiounen...", "Index.startOver": "Wëllt Dir vu vir ufänken?", "Index.clearData": "Date läschen", "Puzzle.animal1": "Int", "Puzzle.animal1Trait1": "<PERSON><PERSON>ren", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait2": "Pelz", "Puzzle.animal2HelpUrl": "https://lb.wikipedia.org/wiki/<PERSON><PERSON>az", "Puzzle.animal3": "Bei", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal4": "Schleek", "Puzzle.animal4Trait1": "Haischen", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.picture": "Bild:", "Puzzle.legs": "Been:", "Puzzle.legsChoose": "eraussichen...", "Puzzle.traits": "Eegenschaften:", "Puzzle.error0": "Perfekt!\nAll %1 Bléck si korrekt.", "Puzzle.error1": "Bal! Just ee Block ass net richteg.", "Puzzle.error2": "%1 Bléck si falsch.", "Puzzle.tryAgain": "De markéierte Block ass net richteg.\nProbéiert nach eng Kéier.", "Puzzle.checkAnswers": "Äntwerten nokucken", "Puzzle.helpText": "<PERSON><PERSON> <PERSON> (gréng), setzt säi Bild derbäi, sicht seng <PERSON> vu Been eraus a maacht e <PERSON> mat sengen Eegenschaften.", "Maze.moveForward": "virugoen", "Maze.turnLeft": "no lénks ofbéien", "Maze.turnRight": "no riets ofbéien", "Maze.doCode": "maachen", "Maze.repeatUntil": "widderhuele bis", "Maze.moveForwardTooltip": "<PERSON><PERSON><PERSON> de Spiller ee Feld no vir.", "Maze.capacity0": "Dir hutt nach %0 Bléck.", "Maze.capacity1": "Dir hutt nach %1 Block.", "Maze.capacity2": "Dir hutt nach %2 Bléck.", "Maze.helpRun": "Start Äre Programm fir ze kucke wat geschitt.", "Maze.helpSkins": "Wielt Äre Liblingsspiller aus dem Menü.", "Bird.noWorm": "huet kee <PERSON>", "Bird.heading": "Iwwerschrëft", "Turtle.turnLeft": "no lenks dréinen ëm", "Turtle.colourTooltip": "Ännert d'Faarf vum Stëft.", "Turtle.setColour": "d'<PERSON><PERSON><PERSON> op", "Turtle.hideTurtle": "Deckelsmouk verstoppen", "Turtle.showTurtle": "<PERSON>elsmouk weisen", "Turtle.print": "d<PERSON><PERSON>n", "Turtle.font": "Schrëftaart", "Turtle.fontSize": "Schrëftgréisst", "Turtle.fontNormal": "normal", "Turtle.fontBold": "fett", "Turtle.fontItalic": "<PERSON><PERSON><PERSON>", "Turtle.galleryMsg": "Galerie kucken", "Turtle.helpUseLoop": "Är Léisung funktionéiert, mee Dir kënnt et nach besser maachen.", "Movie.x": "x", "Movie.y": "y", "Movie.radius": "<PERSON><PERSON>", "Movie.width": "<PERSON><PERSON>", "Movie.height": "<PERSON><PERSON><PERSON><PERSON>", "Movie.circleDraw": "<PERSON><PERSON><PERSON>", "Movie.rectDraw": "<PERSON><PERSON><PERSON>", "Movie.lineDraw": "<PERSON><PERSON>", "Movie.galleryMsg": "Galerie kucken", "Movie.helpText5": "Déi mathematesch Formel fir den Aarm ass komplizéiert. Hei ass d'Äntwert:", "Movie.helpText6": "G<PERSON> der Per<PERSON>un e Puer Hänn.", "Music.firstPart": "<PERSON><PERSON><PERSON>", "Music.piano": "Piano", "Music.trumpet": "<PERSON><PERSON><PERSON><PERSON>", "Music.banjo": "<PERSON><PERSON>", "Music.violin": "<PERSON><PERSON>", "Music.guitar": "<PERSON><PERSON><PERSON>", "Music.flute": "<PERSON><PERSON><PERSON><PERSON>", "Music.drum": "<PERSON><PERSON><PERSON>", "Music.choir": "<PERSON><PERSON>", "Pond.swimTooltip": "No vir schwammen. Gitt eng Richtung un (0-360).", "Pond.stopTooltip": "<PERSON><PERSON> mat <PERSON>. <PERSON> Spiller gëtt méi lues bis e stoppt.", "Pond.docsTooltip": "Weist d'Sproochdokumentatioun.", "Pond.documentation": "Dokumentatioun", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "Zil", "Pond.pendulumName": "Pendel", "Pond.scaredName": "<PERSON><PERSON><PERSON>ngsch<PERSON><PERSON>", "Gallery": "Galerie"}