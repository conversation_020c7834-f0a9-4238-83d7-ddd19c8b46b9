{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Villuvilkur"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "pusle", "Games.maze": "<PERSON><PERSON><PERSON><PERSON>", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "Kilpkonn", "Games.movie": "Film", "Games.music": "Muusika", "Games.pondTutor": "<PERSON><PERSON><PERSON> lahing", "Games.pond": "Tiik", "Games.linesOfCode1": "<PERSON><PERSON><PERSON><PERSON> selle taseme kasutades ühe rea JavaScript koodi", "Games.linesOfCode2": "Lahendasid selle taseme kasutades %1 rida JavaScript koodi", "Games.nextLevel": "Oled sa valmis tasemeks %1 ?", "Games.finalLevel": "<PERSON><PERSON> oled järgmiseks väljakutseks valmis?", "Games.submitTitle": "<PERSON><PERSON><PERSON><PERSON>:", "Games.runTooltip": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> programm", "Games.runProgram": "Käivita programm", "Games.resetTooltip": "Peata programm ja mine algusesse.", "Games.resetProgram": "T<PERSON><PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON>", "Games.catLogic": "<PERSON><PERSON><PERSON>", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matemaatika", "Games.catText": "Tekst", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON><PERSON><PERSON>", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "Funktsioonid", "Games.httpRequestError": "<PERSON><PERSON><PERSON> tehniline probleem", "Games.linkAlert": "Jaga oma plokke selle lingiga: %1", "Games.hashError": "Kahjuks '%1' ei vasta ühelegi salvestatud programmile.", "Games.xmlError": "Ei saa laadida salvestatud faili. V<PERSON><PERSON>-<PERSON><PERSON> see on loodud er<PERSON>lo<PERSON>li versiooniga?", "Games.submitted": "Täname teid selle programmi eest! Kui meie koolitatud ahvide meeskonnale see meeld<PERSON>, avaldavad nad selle paari päeva jooksul galeriisse.", "Games.listVariable": "loend", "Games.textVariable": "tekst", "Games.breakLink": "<PERSON>i olete hakanud <PERSON> redigeerima, ei saa te plokkide redigeerimise juurde tagasi pöörduda. Kas see on OK?", "Games.blocks": "Plokid", "Games.congratulations": "Pa<PERSON>ju <PERSON>!", "Games.helpAbort": "See tase on väga raske. Kas soovid selle vahele j<PERSON>ta ja alustada järgmist mängu? Sa võid alati hiljem tagasi tulla.", "Index.clear": "Kustutan kõik sinu lahendused?", "Index.subTitle": "Mängud tulevastele programmeerijatele.", "Index.moreInfo": "<PERSON><PERSON><PERSON>...", "Index.startOver": "Kas soovid uuesti alustada?", "Index.clearData": "Kustuta andmed", "Puzzle.animal1": "Part", "Puzzle.animal1Trait1": "Suled", "Puzzle.animal1Trait2": "Nokk", "Puzzle.animal1HelpUrl": "https://et.wikipedia.org/wiki/Part", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "Karvkate", "Puzzle.animal2HelpUrl": "https://et.wikipedia.org/wiki/Kass", "Puzzle.animal3": "Mesilane", "Puzzle.animal3Trait1": "Me<PERSON>", "Puzzle.animal3Trait2": "Mürgiastel", "Puzzle.animal3HelpUrl": "https://et.wikipedia.org/wiki/Mesilane", "Puzzle.animal4": "Tigu", "Puzzle.animal4Trait1": "<PERSON><PERSON>", "Puzzle.animal4Trait2": "Lima", "Puzzle.animal4HelpUrl": "https://et.wikipedia.org/wiki/Teod", "Puzzle.picture": "pilt:", "Puzzle.legs": "jalad:", "Puzzle.legsChoose": "vali...", "Puzzle.traits": "tunnused:", "Puzzle.error0": "Täiuslik!\nKõik %1 plokid on õiged.", "Puzzle.error1": "Peaaegu! Üks plokk on valesti.", "Puzzle.error2": "%1 plokki on valesti.", "Puzzle.tryAgain": "Märgistatud plokk on valesti.\nProovi uuesti.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON>", "Puzzle.helpText": "<PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON>) lo<PERSON>le <PERSON> pilt, vali õige jalgade arv ja lisa <PERSON>d tunnused.", "Maze.moveForward": "liigu edasi", "Maze.turnLeft": "<PERSON><PERSON><PERSON><PERSON> vasakule", "Maze.turnRight": "<PERSON><PERSON><PERSON><PERSON> par<PERSON>", "Maze.doCode": "käivita", "Maze.helpIfElse": "If-else plokid lubavad valida kahe erineva tegevuse vahel.", "Maze.pathAhead": "kui rada on otse ees", "Maze.pathLeft": "kui rada l<PERSON><PERSON><PERSON>", "Maze.pathRight": "kui rada l<PERSON><PERSON><PERSON> par<PERSON>", "Maze.repeatUntil": "korda, kuni", "Maze.moveForwardTooltip": "Mängija liigub sammu võrra edasi.", "Maze.turnTooltip": "Mängija pöörab vasemale või paremale.", "Maze.ifTooltip": "<PERSON><PERSON> on olemas rada vajalikus suuna<PERSON>, siis tee järmised toim<PERSON><PERSON>.", "Maze.ifelseTooltip": "<PERSON>i rada lähe<PERSON> v<PERSON>, täida esimese rühma käsud. Vastasel juhul täida teise rühma käsud.", "Maze.whileTooltip": "<PERSON><PERSON> lisatud käsk<PERSON>id kuni j<PERSON>uad raja lõppu.", "Maze.capacity0": "Sul on %0 plokki järele jäänud.", "Maze.capacity1": "Sul on %1 plokk järel.", "Maze.capacity2": "Sul on %2 plokki järel.", "Maze.runTooltip": "Mängija täidab plokkides sisalduvaid käske.", "Maze.resetTooltip": "Pane mängija tagasi labürindi algusess<PERSON>.", "Maze.helpStack": "Eesmärgile jõudmiseks lisa mitu \"liigu edasi\" plokki.", "Maze.helpOneTopBlock": "<PERSON>l tasandil pead valgele alale kokku laduma kõik plokid.", "Maze.helpRun": "<PERSON><PERSON><PERSON><PERSON> oma programm ja vaata, mis juhtub.", "Maze.helpReset": "Sinu programm ei leidnud teed läbi labürindi. <PERSON><PERSON><PERSON> n<PERSON><PERSON> \"Reset\" ja proovi teisiti.", "Maze.helpRepeat": "Raja l<PERSON><PERSON><PERSON> jõud<PERSON>ks saad kasutada kahte plokki. <PERSON><PERSON><PERSON> \"kordus\" plok<PERSON>, et anda sama käsku mitu korda.", "Maze.helpCapacity": "Sul on ära kasutatud kõik selle taseme plokid. Uue ploki loomiseks pead sa kõigepealt mõne olemasoleva ära kustutama.", "Maze.helpRepeatMany": "\"korda\" ploki sisse saab panna ka rohkem, kui <PERSON>he ploki.", "Maze.helpSkins": "<PERSON>i menüüst oma lemmikmängija.", "Maze.helpIf": "\"If\" plokk täidab käsu ainult siis, kui seatud tingimus on tõene. <PERSON>ovi keerata vasemale, kui rada läheb vasemale.", "Maze.helpMenu": "Klõpsa %1, et muuta ploki olekut.", "Maze.helpWallFollow": "Kas saate selle keeruka labürindi lahendada? Proovige järgida vasakut seina. Ainult edasijõudnutele programmeerijatele!", "Bird.noWorm": "pole ussi", "Bird.heading": "Suund", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON>, kui lind pole ussi saanud", "Bird.headingTooltip": "Liigutage etteantud nurga suunas: 0 on paremale, 90 on otse üles jne", "Bird.positionTooltip": "x ja y tähistavad linnu asuk<PERSON>. Kui x = 0, on lind vasaku serva lähedal, kui x = 100 on see parema serva lähedal. Kui y = 0, on lind allosas, kui y = 100 on see ülaosas.", "Bird.helpHeading": "<PERSON><PERSON><PERSON>, et lind saaks ussi ja maanduks oma pesas", "Bird.helpElse": "<PERSON><PERSON><PERSON><PERSON><PERSON>, et muuta blokki 'if'.", "Bird.helpElseIf": "See tase vajab nii plokki “muidu kui” kui ka “muidu”.", "Bird.helpAnd": "<PERSON><PERSON><PERSON><PERSON> <PERSON>ja' on tõene ainult siis, kui m<PERSON><PERSON><PERSON> sisendid on tõesed.", "Bird.helpMutator": "Lohistage „muidu” plokk „kui” plokki", "Turtle.moveForward": "liigu edasi", "Turtle.moveBackward": "lii<PERSON>da tagasi", "Turtle.turnTooltip": "Pöörab kilpkonna vasakule või paremale määratud arvu kraadi võrra.", "Turtle.turnRight": "<PERSON><PERSON><PERSON><PERSON> par<PERSON>", "Turtle.turnLeft": "<PERSON><PERSON><PERSON><PERSON> vasakule", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON> pliiatsi laiust.", "Turtle.setWidth": "seada laius", "Turtle.colourTooltip": "Muudab pliiatsi värvi.", "Turtle.setColour": "sea värviks", "Turtle.penTooltip": "Tõstke või langetage pliiatsit, et peatada või alustada joonistamist.", "Turtle.penUp": "pliiats üles", "Turtle.penDown": "pliiats maha", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON> kil<PERSON> (ring ja nool) nähtavaks või nähtamatuks.", "Turtle.hideTurtle": "peita kilp<PERSON>n", "Turtle.showTurtle": "näita kilpkonn", "Turtle.printHelpUrl": "https://et.wikipedia.org/wiki/Tr%C3%BCkikunst", "Turtle.print": "prindi", "Turtle.fontTooltip": "<PERSON><PERSON><PERSON><PERSON> fondi, mida prindiblokk kasutab.", "Turtle.font": "šrift", "Turtle.fontSize": "kir<PERSON><PERSON><PERSON>", "Turtle.fontBold": "rasvane", "Turtle.fontItalic": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.submitDisabled": "K<PERSON>itage oma programmi, kuni see peatub. Seejärel võite oma joonistuse galeriisse esitada.", "Turtle.galleryTooltip": "<PERSON>ge jooniste galerii.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON><PERSON> gal<PERSON>", "Turtle.submitTooltip": "Esitage oma joonis galeri<PERSON>.", "Turtle.submitMsg": "<PERSON><PERSON><PERSON>", "Turtle.helpUseLoop": "<PERSON><PERSON>, kuid saate paremini hakkama.", "Turtle.helpUseLoop3": "Joonistage kuju kolme ploki abil.", "Turtle.helpUseLoop4": "Joonistage täht vaid nelja ploki abil.", "Turtle.helpText1": "<PERSON><PERSON><PERSON> programm, mis joonistab ruudu.", "Turtle.helpText2": "<PERSON><PERSON><PERSON> oma programmi nii, et ruudu asemel joon<PERSON>takse viisnurk.", "Turtle.helpText3a": "Seal on uus plokk, mis võimaldab teil värvi muuta:", "Turtle.helpText3b": "<PERSON><PERSON><PERSON> koll<PERSON> t<PERSON>ht.", "Turtle.helpText5": "Kas saate ühe tähe asemel joonistada neli tähte, mis moodustavad ruutu?", "Turtle.helpText6": "Joonista kolm kollast tähte ja üks valge joon.", "Turtle.helpText7": "Joonistage tähed ja seej<PERSON>rel joonistage neli valget joont.", "Turtle.helpText8": "360 valge joone joonistamine näeb välja nagu täiskuu.", "Turtle.helpText9": "Kas saate lisada musta ringi, et kuust saaks poolkuu?", "Turtle.helpText10": "Joonistage kõike, mida soovite. Teil on tohutult palju uusi plo<PERSON>ke, mida saate uurida. Lõbutsege!", "Turtle.helpText10Reddit": "Kasutage nuppu 'Vaata galeriid', et näha, mida teised inimesed joonistasid. <PERSON>i joonistate midagi huvitavat, kasutage selle avaldamiseks nuppu \"Edasta galeriisse\".", "Turtle.helpToolbox": "Plokkide nägemiseks valige kategooria.", "Movie.x": "x", "Movie.y": "y", "Movie.radius": "raadius", "Movie.width": "laius", "Movie.height": "kõrgus", "Movie.circleTooltip": "Joonistab ringi kindlaksmääratud asukohas ja määratud raadius<PERSON>a.", "Movie.circleDraw": "ring", "Movie.rectTooltip": "Joonistab ristküliku määratletud asukohas ning määratud laiuse ja kõ<PERSON>a.", "Movie.rectDraw": "ristkülik"}