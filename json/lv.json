{"@metadata": {"authors": ["Elomage", "<PERSON><PERSON>", "Papuass", "Peridot Nation", "<PERSON><PERSON><PERSON><PERSON>"]}, "Games.name": "Blockly Games", "Games.puzzle": "Saliekamattēls", "Games.maze": "Labirints", "Games.bird": "Putns", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Filma", "Games.music": "<PERSON><PERSON><PERSON><PERSON>", "Games.pondTutor": "Dīķis (t<PERSON><PERSON><PERSON><PERSON>)", "Games.pond": "Dīķis", "Games.linesOfCode1": "Tu atrisināji šo līmeni izmantojot 1 JavaScript rindiņu:", "Games.linesOfCode2": "Tu atrisināji šo līmeni izmantojot %1 JavaScript rindiņas:", "Games.nextLevel": "Vai esi gatavs %1 līmenim?", "Games.finalLevel": "Vai tu esi gatavs nākamajam izaicinājumam?", "Games.submitTitle": "Nosaukums:", "Games.linkTooltip": "Saglabāt un izveidot saiti uz blokiem.", "Games.runTooltip": "Palaist izveidoto programmu.", "Games.runProgram": "Izpildīt programmu", "Games.resetTooltip": "Apst<PERSON><PERSON>āt programmu un atiestatīt līmeni.", "Games.resetProgram": "<PERSON><PERSON><PERSON> no sākuma", "Games.help": "Palīdzī<PERSON>", "Games.catLogic": "Loģika", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catText": "Teksts", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON><PERSON><PERSON>", "Games.catVariables": "Mainīgie", "Games.catProcedures": "Funkcijas", "Games.httpRequestError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Games.linkAlert": "Dalies ar saviem blokiem ar šo saiti:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet '%1' neatbilst nevienai no saglabātajām programmām.", "Games.xmlError": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>t tavu saglabāto failu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tas tika izveidots ar citu Blockly versiju?", "Games.listVariable": "saraksts", "Games.textVariable": "teksts", "Games.breakLink": "Ja jūs sākat rediģēt JavaScript, bloku rediģēšana vairs nebūs pieej<PERSON>. Turpināt?", "Games.blocks": "Bloki", "Games.congratulations": "Apsveicam!", "Games.helpAbort": "<PERSON>is ir grūts līmenis. <PERSON>ai <PERSON> vēl<PERSON> to izlaist un doties uz nākamo spēli? J<PERSON><PERSON> vēlāk vienmēr varat atgriezties.", "Index.clear": "<PERSON>z<PERSON><PERSON>ēst visus savus risin<PERSON>jumus?", "Index.subTitle": "<PERSON><PERSON><PERSON><PERSON> rītdienas programmētājiem.", "Index.moreInfo": "Informācija pedagogiem...", "Index.startOver": "Vēlaties sākt no jauna?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus datus", "Puzzle.animal1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Knābis", "Puzzle.animal1HelpUrl": "https://lv.wikipedia.org/wiki/P%C4%ABle", "Puzzle.animal2": "Kaķis", "Puzzle.animal2Trait1": "Ūsas", "Puzzle.animal2Trait2": "Kažoks", "Puzzle.animal2HelpUrl": "https://lv.wikipedia.org/wiki/Kaķis", "Puzzle.animal3": "Bite", "Puzzle.animal3Trait1": "Medus", "Puzzle.animal3Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://lv.wikipedia.org/wiki/Bite", "Puzzle.animal4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "Čaula", "Puzzle.animal4Trait2": "Gļota", "Puzzle.animal4HelpUrl": "https://lv.wikipedia.org/wiki/G<PERSON><PERSON><PERSON>s", "Puzzle.picture": "attēls:", "Puzzle.legs": "kājas:", "Puzzle.legsChoose": "izvēlies...", "Puzzle.traits": "īpašības:", "Puzzle.error0": "Lieliski!\nVisi %1 bloki ir pareizi.", "Puzzle.error1": "Gandrīz! Viens bloks nav pareizs.", "Puzzle.error2": "%1 bloki nav pareizi.", "Puzzle.tryAgain": "Iezīmētais bloks nav pareizs.\nMēģini vēl.", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.helpText": "<PERSON><PERSON> (zaļais), <PERSON><PERSON><PERSON> tā <PERSON>t<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> un kāju skaitu.", "Maze.moveForward": "ej uz priekšu", "Maze.turnLeft": "pagriezies pa kreisi", "Maze.turnRight": "pagriezies pa labi", "Maze.doCode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Maze.helpIfElse": "<PERSON><PERSON><PERSON><PERSON><PERSON> komandas 'ja-cit<PERSON><PERSON>' i<PERSON><PERSON><PERSON>īs vai nu vienu, vai otru bloku atkarībā no p<PERSON>rbaud<PERSON>.", "Maze.pathAhead": "ja priek<PERSON><PERSON> ir brīvs ce<PERSON>", "Maze.pathLeft": "ja pa kreisi ir brīvs ce<PERSON>", "Maze.pathRight": "ja pa labi ir brīvs ce<PERSON>", "Maze.repeatUntil": "atk<PERSON><PERSON><PERSON> l<PERSON>z", "Maze.moveForwardTooltip": "Pārvieto cilvēciņu par vienu soli uz priekšu.", "Maze.turnTooltip": "<PERSON>g<PERSON>ž cilvēciņu pa kreisi vai pa labi par 90 grādiem.", "Maze.ifTooltip": "<PERSON>a attie<PERSON><PERSON><PERSON><PERSON> virzie<PERSON> ir brīvs ce<PERSON>, tad izpi<PERSON><PERSON>t attiec<PERSON><PERSON><PERSON><PERSON>.", "Maze.ifelseTooltip": "<PERSON>a at<PERSON><PERSON><PERSON><PERSON><PERSON> virzienā ir brīvs ce<PERSON>, tad izpildīt pirmo bloku ar darbībām. Citādi izpildīt otro bloku ar darbībām.", "Maze.whileTooltip": "Atkārt<PERSON> i<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON> kamēr sasniegts mērķis.", "Maze.capacity0": "Tev atlikuši %0 bloki.", "Maze.capacity1": "Tev atlicis %1 bloks.", "Maze.capacity2": "Tev atlikuši %2 bloki.", "Maze.runTooltip": "<PERSON><PERSON> spēl<PERSON><PERSON><PERSON><PERSON>m dar<PERSON>t to, ko saka bloki.", "Maze.resetTooltip": "Novietot spēlēt<PERSON><PERSON> atpakaļ labirinta sākumā.", "Maze.helpStack": "Saliec kopā dažus 'ej uz priekšu' blokus lai mēs varētu sasniegt mērķi.", "Maze.helpOneTopBlock": "<PERSON><PERSON><PERSON> lī<PERSON>ī tev bloki jāsaliek kopā baltajā lauku<PERSON>.", "Maze.helpRun": "<PERSON><PERSON><PERSON>, lai <PERSON>, kas notiek.", "Maze.helpReset": "Jūsu programma neatrisināja labirintu.  Nospiediet 'Atsākt' un mēģiniet vēlreiz.", "Maze.helpRepeat": "Datoriem ir ierobežota atmiņa. Sasniedz ceļa mērķi lietojot tikai divus blokus. Lieto atkārtošanas bloku lai varētu izpildīt darbību vairāk kā vienu reizi.", "Maze.helpCapacity": "<PERSON><PERSON>s esat izmantojis visus blokus šajā līmeni. <PERSON> izve<PERSON>tu jaunu bloku, jums vispirms nepieciešams izmest kādu esošu bloku.", "Maze.helpRepeatMany": "<PERSON><PERSON><PERSON> varat ievietot vairāk nekā vienu bloku \"atkārtot\" bloka iek<PERSON>.", "Maze.helpSkins": "Izvēlēties jūsu iec<PERSON>ītā<PERSON> spēlētāju no šī<PERSON> izv<PERSON>ln<PERSON>.", "Maze.helpIf": "P<PERSON><PERSON><PERSON><PERSON> komanda 'ja' dara kaut ko tikai tad, ja pārbaude ir patiesa. Pamēģini pagriezties pa kreisi, ja pa kreisi ir brīvs ceļš.", "Maze.helpMenu": "Noklikšķiniet uz %1 \"ja\" blokā lai mainītu tā nosacījumu.", "Maze.helpWallFollow": "Vai tu vari atrisināt šo sarežģīto labirintu? Mēģini sekot sienai kreisajā pusē. Šis ir pieredzējušiem programmētājiem!", "Bird.noWorm": "knābī nav tārpa", "Bird.heading": "vir<PERSON><PERSON>", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad putnam knābī nav tārps.", "Bird.headingTooltip": "Kustēties leņķa norādītajā virzienā. 0: pa labi, 90: uz au<PERSON><PERSON><PERSON>, 270: uz leju, utt...", "Bird.positionTooltip": "X un Y nosaka putna atrašanās vietu. Ja x = 0, putns ir pie kreisās malas, ja x = 100, tas ir pie labās malas. Ja y = 0, putns ir apakšā, ja y = 100, tas ir augšpusē.", "Bird.helpHeading": "<PERSON><PERSON>t putna virziena leņķi, lai palīdzētu tam iegūt tārpu un atgriezties ligzdā.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON>, lai izv<PERSON><PERSON><PERSON><PERSON> vir<PERSON> atkar<PERSON> no tā vai tārps ir paņemts vai nē.", "Bird.helpX": "'x' ir jūsu pašreizējā horizontālā pozīcija. <PERSON><PERSON><PERSON><PERSON><PERSON>o blo<PERSON>, lai ietu vienā virzienā, ja 'x' ir mazāks nekā dotais skaitlis, vai otrā virzienā, ja 'x' nav mazāks kā dotais skaitlis.", "Bird.helpElse": "Klikšķiniet uz ikonas, lai main<PERSON> 'ja' bloku.", "Bird.helpElseIf": "<PERSON><PERSON><PERSON> l<PERSON> ir vaja<PERSON>gi gan 'citādi, ja' un 'citādi' bloki.", "Bird.helpAnd": "'un' bloka vertība ir \"patiess\" tikai tad, ja abi argumenti ir patiesi.", "Bird.helpMutator": "Vel<PERSON>t \"citādi\" bloku zem \"ja\" bloka.", "Turtle.moveTooltip": "Pārvieto bruņurupuci uz priekšu vai atpakaļ par norādīto attālumu.", "Turtle.moveForward": "pārvieto uz priekšu par", "Turtle.moveBackward": "pā<PERSON><PERSON><PERSON> atpakaļ par", "Turtle.turnTooltip": "Pagriež bruņurupuci pa kreisi vai pa labi par norādīto leņķi, grā<PERSON>.", "Turtle.turnRight": "pagriezt pa labi par", "Turtle.turnLeft": "pagriezt pa kreisi par", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON><PERSON> plat<PERSON>u", "Turtle.setWidth": "iestatīt platumu, uz", "Turtle.colourTooltip": "<PERSON><PERSON>.", "Turtle.setColour": "iestatīt kr<PERSON>, uz", "Turtle.penTooltip": "<PERSON><PERSON> vai no<PERSON>, lai beigtu vai sāktu z<PERSON>.", "Turtle.penUp": "<PERSON><PERSON>", "Turtle.penDown": "nolaist z<PERSON><PERSON><PERSON>", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON> bru<PERSON><PERSON><PERSON> (apli un bultu) redzamu vai neredzamu.", "Turtle.hideTurtle": "pas<PERSON><PERSON><PERSON>", "Turtle.showTurtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Turtle.printHelpUrl": "https://lv.wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>a", "Turtle.printTooltip": "<PERSON><PERSON><PERSON><PERSON> tekstu bruņurupuča virzienā no tā atrašanās vietas.", "Turtle.print": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontTooltip": "<PERSON><PERSON><PERSON>, ko <PERSON><PERSON><PERSON> d<PERSON> blo<PERSON>.", "Turtle.font": "fonts", "Turtle.fontSize": "fonta lie<PERSON>", "Turtle.fontNormal": "<PERSON><PERSON><PERSON>", "Turtle.fontBold": "treknraksts (bold)", "Turtle.fontItalic": "sl<PERSON><PERSON><PERSON><PERSON> (italic)", "Turtle.submitDisabled": "<PERSON><PERSON><PERSON>, lī<PERSON><PERSON> tā apstājas. <PERSON> jūs varat iesniegt savu zīmējumu galerij<PERSON>.", "Turtle.galleryTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.submitTooltip": "<PERSON><PERSON><PERSON><PERSON> savu z<PERSON><PERSON><PERSON><PERSON>.", "Turtle.submitMsg": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON> da<PERSON>, bet jūs varat to i<PERSON><PERSON><PERSON><PERSON>.", "Turtle.helpUseLoop3": "Uzzīmējiet figūru tikai ar trīs bloku palī<PERSON>.", "Turtle.helpUseLoop4": "Uzzīmējiet z<PERSON> ar ne vairāk kā četru bloku palīdzību.", "Turtle.helpText1": "Izveidojiet programmu, kas uzzīmē k<PERSON>dr<PERSON>.", "Turtle.helpText2": "Izmainiet programmu tā, lai tā uzzīmētu piecstūri.", "Turtle.helpText3a": "<PERSON><PERSON><PERSON> b<PERSON>, ka<PERSON> <PERSON><PERSON><PERSON> j<PERSON><PERSON>t kr<PERSON>:", "Turtle.helpText3b": "Uzzī<PERSON>ē d<PERSON>u z<PERSON>z<PERSON>.", "Turtle.helpText4a": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> no papīra, lai pārvie<PERSON>tu citur:", "Turtle.helpText4b": "Uzzīmē<PERSON><PERSON> d<PERSON>, ar lī<PERSON><PERSON> virs tās.", "Turtle.helpText5": "Vai jūs varat uzzīmēt četras zvaigznes, kas izk<PERSON>ota<PERSON> kvadrātā?", "Turtle.helpText6": "<PERSON>zz<PERSON><PERSON><PERSON> trīs d<PERSON>, un vienu baltu lī<PERSON>.", "Turtle.helpText7": "Uzzīmējiet z<PERSON>, un pēc tam uzzīmējiet četras baltas līnijas.", "Turtle.helpText8": "360 baltas līnijas uzzīmētas izskatīsies kā pilnmēness.", "Turtle.helpText9": "<PERSON>ai <PERSON>s varat pievienot melnu apli tā, lai mēness kļūst par par pusmēnesi?", "Turtle.helpText10": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>, ko vē<PERSON><PERSON>. Jums ir pieejami daudzi jauni bloki. Gūstiet prieku tos izpētot!", "Turtle.helpText10Reddit": "Izmantojiet pogu \"<PERSON><PERSON><PERSON>\", lai <PERSON>, ko izveidojuši citi cilvēki. Ja jūs esat izveidojuši ko interesantu, i<PERSON><PERSON><PERSON><PERSON> pogu \"Pievienot <PERSON>\", lai to publicētu.", "Turtle.helpToolbox": "<PERSON>zv<PERSON><PERSON><PERSON> kate<PERSON>, lai <PERSON><PERSON><PERSON> b<PERSON>.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "s<PERSON><PERSON><PERSON> x", "Movie.y1": "s<PERSON><PERSON><PERSON> y", "Movie.x2": "beigu x", "Movie.y2": "beigu y", "Movie.radius": "<PERSON><PERSON><PERSON><PERSON>", "Movie.width": "platums", "Movie.height": "augstums", "Movie.circleTooltip": "<PERSON><PERSON><PERSON><PERSON> apli norādītajā vietā un ar doto rādiusu.", "Movie.circleDraw": "aplis", "Movie.rectTooltip": "<PERSON><PERSON><PERSON><PERSON> taisnstūri norādītajā vietā un dotajā augstumā un platumā.", "Movie.rectDraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movie.lineTooltip": "<PERSON><PERSON><PERSON><PERSON> līniju starp dotajiem punktiem norādītajā biezum<PERSON>.", "Movie.lineDraw": "lī<PERSON><PERSON>", "Movie.timeTooltip": "Atgriež pašreiz<PERSON>jo animācijas laiku (0-100).", "Movie.colourTooltip": "<PERSON><PERSON>.", "Movie.setColour": "iestatīt kr<PERSON>", "Movie.submitDisabled": "Jūsu filma nekustas. <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, lai izve<PERSON>tu kaut ko interesantu. <PERSON> jū<PERSON> varē<PERSON>t pievienot savu filmu galerijai.", "Movie.galleryTooltip": "Atvērt filmu galeriju.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON><PERSON>", "Movie.submitTooltip": "<PERSON><PERSON><PERSON><PERSON> savu filmu galerijā.", "Movie.submitMsg": "I<PERSON>niegt filmu galerijā", "Movie.helpText1": "<PERSON><PERSON><PERSON>, lai uzz<PERSON><PERSON><PERSON><PERSON> šo cilvēku.", "Movie.helpText2a": "<PERSON><PERSON> līmenis ir filma. <PERSON><PERSON><PERSON>, lai cilvēka roka kustas pāri ek<PERSON>. Nospiediet pogu 'atskaņot', lai redz<PERSON>tu p<PERSON>kšskatījumu.", "Movie.helpText2b": "<PERSON><PERSON><PERSON><PERSON> <PERSON>a darb<PERSON>, bloks 'time' s<PERSON><PERSON> kadrus no 0 līdz 100. T<PERSON> kā jūs vēlat<PERSON>, lai rokas 'y' pozī<PERSON>ja mainī<PERSON> no 0 līdz 100, tam vajadz<PERSON>tu būtu vien<PERSON>.", "Movie.helpText3": "Bloks 'time' s<PERSON><PERSON> kadrus no 0 līdz 100.\nBet šoreiz jūs vēlat<PERSON>, lai roka savu kustību sāk no 100 un iet līdz 0. <PERSON>ai varēsiet izdomāt vienkāršu matemātisku formulu, kas izmainīs rokas virzienu?", "Movie.helpText4": "<PERSON><PERSON><PERSON><PERSON><PERSON> to, ko jūs ap<PERSON>t i<PERSON>š<PERSON><PERSON> l<PERSON>, lai <PERSON>, ka kājas attiecīgi pārvieto<PERSON> visos k<PERSON>.", "Music.firstPart": "<PERSON><PERSON><PERSON><PERSON>", "Music.piano": "klavieres", "Music.trumpet": "trompete", "Music.banjo": "bandžo", "Music.violin": "vijole", "Music.guitar": "ģitāra", "Music.flute": "flauta", "Music.drum": "bungas", "Music.choir": "koris", "Music.galleryMsg": "<PERSON><PERSON><PERSON><PERSON>", "Music.submitMsg": "<PERSON><PERSON><PERSON><PERSON>", "Pond.swimTooltip": "Peldēt uz priekšu. <PERSON><PERSON><PERSON> (0-360).", "Pond.documentation": "Dokumentācija", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "Mērķis", "Pond.pendulumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pond.scaredName": "<PERSON><PERSON><PERSON><PERSON>", "Gallery": "<PERSON><PERSON><PERSON>"}