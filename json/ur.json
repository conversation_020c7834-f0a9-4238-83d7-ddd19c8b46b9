{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "عثم<PERSON> خان شاہ"]}, "Games.name": " بلوکلی گیم", "Games.puzzle": "پہیلی", "Games.maze": "بھول بھلیاں", "Games.bird": "پرندہ", "Games.turtle": "کچھوا", "Games.movie": "فلم", "Games.music": "موسیقی", "Games.pondTutor": "طالاب ٹیوٹر", "Games.pond": "طالاب", "Games.linesOfCode1": "آپ نے اس سطح کو جاوا اسکرپٹ کے 1 لائن کے ساتھ حل کیا:", "Games.linesOfCode2": "آپ نے اس سطح کو جاوا اسکرپٹ کے 1 فیصد (%1)  لائنوں کے ساتھ حل کیا", "Games.nextLevel": "کیا آپ 1 فیصد (%1) لیول کے لئے تیار ہیں؟", "Games.finalLevel": "کیا آپ اگلے چیلنج کے لئے تیار ہیں؟", "Games.submitTitle": ":عنوان", "Games.linkTooltip": "محفوظ کریں اور بلاکس سے لنک کریں.", "Games.runTooltip": "پروگرام جس میں آپ لکھا ہے چلائیں", "Games.runProgram": "پروگرام جس میں آپ نے لکھا. چلائیں", "Games.resetTooltip": "پروگرام بند کرو اور سطح کو دوبارہ ترتیب دیں.", "Games.resetProgram": "دوبارہ مرتب کریں", "Games.help": "مد<PERSON>", "Games.catLogic": "منطق", "Games.catLoops": "لوپیں", "Games.catMath": "ریاضی", "Games.catText": "متن", "Games.catLists": "فہرستیں", "Games.catColour": "رنگ", "Games.catVariables": "متغیرات", "Games.catProcedures": "افعال", "Games.httpRequestError": "درخواست میں مسئلہ ہے۔", "Games.linkAlert": "اپنے بلاک کو اس ربط سے شیئر کریں:\n\n١فیصد%1", "Games.hashError": "افسوس،'1فیصد'%1 کسی محفوظ کردہ پروگرام کے مطابق نہیں ہے.", "Games.xmlError": "آپ کی محفوظ کردہ فائل کو لوڈ نہیں مل سکا. شاید یہ بلاشبہ کے ایک مختلف ورژن سے پیدا ہوا تھا؟", "Games.submitted": "س پروگرام کے لئے شکریہ! اگر اس طرح تربیت یافتہ بندروں کے ہمارے عملے، وہ دو دن کے اندر اندر گیلری میں شائع کرے گا.", "Games.listVariable": "فہرست", "Games.textVariable": "ٹیکسٹ", "Games.breakLink": "ایک بار جب آپ جاوا اسکرپٹ میں ترمیم شروع کرتے ہیں تو، آپ کو بلاکس میں ترمیم کرنے میں واپس نہیں جا سکتے. کیا یہ ٹھیک ہے؟", "Games.blocks": "بلاکس", "Games.congratulations": "مبارک ہو!", "Games.helpAbort": "یہ سطح انتہائی مشکل ہے. کیا آپ اسے چھوڑ کر اگلے کھیل پر جائیں گے؟ آپ ہمیشہ بعد میں واپس آ سکتے ہیں.", "Index.clear": "اپنے سارے حل ختم کریں؟", "Index.subTitle": "کل کے پروگرامرز کے لئے کھیل.", "Index.moreInfo": "مزید معلومات...", "Index.startOver": "ایک بار پھر شروع کرنا چاہتے ہیں؟", "Index.clearData": "ڈیٹا صاف کریں", "Puzzle.animal1": "بطخ", "Puzzle.animal1Trait1": "پر", "Puzzle.animal1Trait2": "بیک", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "بلی", "Puzzle.animal2Trait1": "مونچھیں", "Puzzle.animal2Trait2": "بال", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "مکھی", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "ڈنک", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "سست", "Puzzle.animal4Trait1": "چھیلکہ", "Puzzle.animal4Trait2": "لعاب", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "تصویر:", "Puzzle.legs": ":ٹانگیں", "Puzzle.legsChoose": "...منتخب کریں", "Puzzle.traits": ":علامات", "Puzzle.error0": "بہترین!\nتمام 1فیصد%1  بلاکس درست ہیں.", "Puzzle.error1": "قریب! ایک بلاک درست نہیں۔", "Puzzle.error2": " (%1) فیصد1  بلاک درست نہیں", "Puzzle.tryAgain": "نمایاں کردہ بلاک صحیح نہیں ہے\nکوشش جاری رکھیں", "Puzzle.checkAnswers": "جوابات چیک کریں", "Puzzle.helpText": "ہر جانور کے لئے (سبز) اس کی تصویر سے منسلک کریں، اس کی تعداد میں ٹانگوں کا انتخاب کریں اور اس کی علامات کا ایک اسٹیک بنائیں", "Maze.moveForward": "آگے بڑھئے", "Maze.turnLeft": "دائیں موڑ جائے", "Maze.turnRight": "بائیں موڑ جائیں", "Maze.doCode": "کریں", "Maze.helpIfElse": "اگریآ", "Maze.pathAhead": "اگر آگے راستہ ہے", "Maze.pathLeft": "آگے بائیں راستہ ہے", "Maze.pathRight": "اگر دائیں راستہ ہے", "Maze.repeatUntil": "تک دہرائے", "Maze.moveForwardTooltip": "کھلاڑی آگے ایک جگہ منتقل کرتا ہے.", "Maze.turnTooltip": "نمایاں کردہ بلاک صحیح نہیں ہے\nکوشش جاری رکھیں", "Maze.ifTooltip": "اگر مخصوص سمت میں ایک راستہ ہے، تو پھر کچھ کام کریں.", "Maze.ifelseTooltip": "اگر مخصوص سمت میں ایک راستہ ہے، تو اعمال کے پہلے بلاک کو کریں. ورنہ، اعمال کا دوسرا بلاک کرو.", "Maze.whileTooltip": "جب تک ختم پوائنٹ تک پہنچ گئی ہے تک بند کردہ اعمال کو دوبارہ کریں.", "Maze.capacity0": "آپ کے پاس 0فیصد بلاک باقی ہیں.", "Maze.capacity1": "آپ کے پاس   (%1) 1فیصد بلاک باقی ہیں.", "Maze.capacity2": "آپ کے پاس %2فیصد بلاک باقی ہیں.", "Maze.runTooltip": "کھلاڑی بناتا ہے جو بلاکس کہتے ہیں.", "Maze.resetTooltip": "بھولبلییا کے آغاز پر کھلاڑی واپس رکھو.", "Maze.helpStack": "مقصد میں پہنچنے میں میری مدد کرنے کے لۓ ایک دوسرے کے ساتھ 'آگے آگے' بلاکس اسٹیک کریں.", "Maze.helpOneTopBlock": "اس سطح پر، آپ کو سفید کاری اسپیس میں تمام بلاکس کے ساتھ اسٹیک کرنے کی ضرورت ہے.", "Maze.helpRun": "اپنے پروگرام کو چلنے کے لۓ چلائیں.", "Maze.helpReset": "اپنے پروگرام کو چلائیں اپنے پروگرام بھولبلییا کو حل نہیں کیا. 'ری سیٹ کریں' پر دبائیں اور دوبارہ کوشش کریں.\nاے دیکھو کیا ہوتا ہے.", "Maze.helpRepeat": "صرف دو بلاکس کا استعمال کرتے ہوئے اس راستے کے اختتام تک پہنچیں. کسی بار سے زیادہ ایک بلاک کو چلانے کے لئے 'دوبارہ دوبارہ' کا استعمال کریں.", "Maze.helpCapacity": "آپ نے اس سطح کے لئے تمام بلاکس کو استعمال کیا ہے. ایک نیا بلاک بنانے کے لئے، آپ سب سے پہلے موجودہ بلاک کو ختم کرنے کی ضرورت ہے.", "Maze.helpRepeatMany": "آپ ایک 'دوبارہ' بلاک کے اندر ایک سے زائد بلاک فٹ کر سکتے ہیں.", "Maze.helpSkins": "اس مینو سے اپنے پسندیدہ پلیئر کا انتخاب کریں.", "Maze.helpIf": "ایک 'اگر' بلاک صرف اس صورت میں کچھ کرے گا جب حالت درست ہے. اگر بائیں طرف کا راستہ ہے تو بائیں باری کی کوشش کریں.", "Maze.helpMenu": "اس صورت میں 'اگر' بلاک کرنے کے لئے (%1) 1 فیصد\n پر کلک کریں.", "Maze.helpWallFollow": "کیا آپ یہ پیچیدہ بھولبلییا حل کر سکتے ہیں؟ بائیں ہاتھ دیوار کی پیروی کریں. اعلی درجے کی پروگرامرز صرف!", "Bird.noWorm": "کیڑا نہیں ہے", "Bird.heading": "سرخی", "Bird.noWormTooltip": "شرط جب پرندوں کو کیڑے نہیں ملتی ہے.", "Bird.headingTooltip": "دیئے گئے زاویہ کی سمت میں منتقل کریں: 0 صحیح ہے، 90 براہ راست ہے، وغیرہ.", "Bird.positionTooltip": "ایکس اور Y پرندوں کی پوزیشن کو نشان زد کرتے ہیں. جب ایکس = 0 بائیں بائیں کنارے کے قریب ہے، جب ایکس = 100 یہ دائیں کنارے کے قریب ہے. جب y = 0 پرندے نچلے حصے پر ہے، جب y = 100 یہ سب سے اوپر ہے.", "Bird.helpHeading": "پہلو کو زاویہ میں تبدیل کرنے کے لئے پہلو کو زاویہ میں تبدیل کریں.", "Bird.helpHasWorm": "اگر آپ کیڑا نہیں ہے اگر آپ کیڑا ہے، یا ایک مختلف سرنگ ایک سر میں جانے کے لئے اس بلاک کا استعمال کریں.", "Bird.helpX": "'x' آپ کا موجودہ افقی پوزیشن ہے. اگر یہ ایرر برقرار رہے تو ہمارے ہیلپ ڈیسک سے رابطہ کریں. غلط استعمال کی اطلاع دیتے ہوئے ایرر آ گیا ہے. براہ مہربانی دوبارہ کوشش کریں. اگر یہ ایرر برقرار رہے تو ہمارے ہیلپ ڈیسک سے رابطہ کریں. غلط استعمال کی اطلاع دیتے ہوئے ایرر آ گیا ہے.", "Bird.helpElse": "'اگر' بلاک کو ترمیم کرنے کیلئے آئکن پر کلک کریں.", "Bird.helpElseIf": "اس سطح کو 'اور' دونوں کی ضرورت ہوتی ہے اور ایک اور 'بلاک' کی ضرورت ہوتی ہے.", "Bird.helpAnd": "'اور' بلاک صرف اس صورت میں سچ ہے اگر اس کے آدانوں دونوں درست ہیں.", "Bird.helpMutator": "'اور' بلاک کو 'اگر' بلاک میں گھسیٹیں.", "Turtle.moveTooltip": "مخصوص رقم کی طرف سے کچھی اگلا یا پیچھے اگلا.", "Turtle.moveForward": "آگے بڑھو", "Turtle.moveBackward": "کی طرف سے پیچھے منتقل", "Turtle.turnTooltip": "مخصوص ڈگری کی طرف سے کچھی بائیں یا دائیں کو بدل دیتا ہے.", "Turtle.turnRight": "بائیں طرف", "Turtle.turnLeft": "بائیں مڑو", "Turtle.widthTooltip": "قلم کی چوڑائی میں تبدیلی", "Turtle.setWidth": "چوڑائی مقرر", "Turtle.colourTooltip": "قلم کا رنگ بدلتا ہے.", "Turtle.setColour": "رنگ مقرر", "Turtle.penTooltip": "ڈرائیو کو روکنے یا شروع کرنے کے لئے قلم لفٹیں یا قلم کو کم کرتی ہے.", "Turtle.penUp": "قلم اپ", "Turtle.penDown": "قلم نیچے", "Turtle.turtleVisibilityTooltip": "کچھی (دائرے اور تیر) دکھائی دیتا ہے یا پوشیدہ کرتا ہے.", "Turtle.hideTurtle": "کچھی چھپائیں", "Turtle.showTurtle": "کچھی دکھائیں", "Turtle.printTooltip": "اس جگہ پر کچھی کی سمت میں متن ڈرائیو.", "Turtle.print": "پرنٹ کریں", "Turtle.fontTooltip": "پرنٹ بلاک کی طرف سے استعمال کیا جاتا فونٹ سیٹ کرتا ہے.", "Turtle.font": "فونٹ", "Turtle.fontSize": "حرف کا سائز", "Turtle.fontNormal": "معمول", "Turtle.fontBold": "جرات مندانہ", "Turtle.fontItalic": "اٹلک", "Turtle.submitDisabled": "اپنے پروگرام کو چلائیں جب تک وہ روک نہیں پائے. پھر آپ اپنی ڈرائنگ گیلری میں جمع کر سکتے ہیں.", "Turtle.galleryTooltip": "ڈرائنگ کی گیلری، نگارخانہ کھولیں.", "Turtle.galleryMsg": "گیلری دیکھیں", "Turtle.submitTooltip": "گیلری، نگارخانہ میں اپنا ڈرائنگ جمع کرو.", "Turtle.submitMsg": "گیلری، نگارخانہ میں جمع", "Turtle.helpUseLoop": "آپ کا حل کام کرتا ہے، لیکن آپ بہتر کر سکتے ہیں.", "Turtle.helpUseLoop3": "صرف تین بلاکس سے شکل ڈرائیو.", "Turtle.helpUseLoop4": "صرف چار بلاکس کے ساتھ اسٹار ڈراؤ.", "Turtle.helpText1": "ایک ایسا پروگرام بنائیں جو ایک مربع ڈراؤ.", "Turtle.helpText2": "ایک مربع بجائے پینٹینگن کو اپنی پروگرام میں تبدیل کریں.", "Turtle.helpText3a": "ایک نیا بلاک ہے جو آپ کو رنگ تبدیل کرنے کی اجازت دیتا ہے.", "Turtle.helpText3b": "ایک پیلا اسٹار بناؤ", "Turtle.helpText4a": "ایک نیا بلاک ہے جو آپ کو اپنے قلم کو کاغذ سے دور کرنے کی اجازت دیتا ہے.", "Turtle.helpText4b": "ایک چھوٹا سا پیلا ستارہ بناؤ، پھر اس کے اوپر ایک قطار بناؤ", "Turtle.helpText5": "ایک ستارہ کے بجائے، کیا آپ چار ستاروں کو ایک مربع میں ترتیب دے سکتے ہیں؟", "Turtle.helpText6": " پیلے ستاروں اور ایک سفید لائن بنائیں.", "Turtle.helpText7": "ستارے بنائے، پھر چار سفید لائنوں کو ڈراؤ.", "Turtle.helpText8": "360 سفید لائنوں کو مکمل چاند کی طرح نظر آئے گا.", "Turtle.helpText9": "کیا آپ ایک سیاہ دائرے کو شامل کرسکتے ہیں تاکہ چاند ایک چراغ بن جائے؟", "Turtle.helpText10": "جو کچھ آپ چاہتے ہو اسے بنائیں آپ کو نئی بلاکس کی بڑی تعداد ملی ہے جو آپ تلاش کر سکتے ہیں. مزے کرو!", "Turtle.helpText10Reddit": "\"؛ گیلری، نگارخانہ \"؛ کا استعمال کریں. دوسرے لوگوں کو کیا کیا ہے دیکھنے کے لئے بٹن. اگر آپ کچھ دلچسپ ھیںچو تو، \"؛ گیلری، نگارخانہ میں جمع کریں \"؛ کا استعمال کریں. اسے شائع کرنے کیلئے بٹن.", "Turtle.helpToolbox": "بلاکس کو دیکھنے کے لئے ایک قسم کا انتخاب کریں.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x شروع کریں", "Movie.y1": "y شروع کریں", "Movie.x2": "x ختم کریں", "Movie.y2": "y ختم کریں", "Movie.radius": "من<PERSON><PERSON>", "Movie.width": "چوڑھائی", "Movie.height": "اونچھائی", "Movie.circleTooltip": "مخصوص مقام پر اور مخصوص ردعمل کے ساتھ ایک حلقہ بنائیں.", "Movie.circleDraw": "دا<PERSON>رہ", "Movie.rectTooltip": "مخصوص جگہ پر اور مخصوص چوڑائی اور اونچائی کے ساتھ ایک آئتاکار بنائیں.", "Movie.rectDraw": "مستطیل", "Movie.lineTooltip": "ایک چوڑائی سے مخصوص چوڑائی کے ساتھ ایک دوسرے کو ایک قطار ڈرا دیتا ہے.", "Movie.lineDraw": "لکیر", "Movie.timeTooltip": "موجودہ وقت حرکت پذیری (0-100) میں لوٹاتا ہے.", "Movie.colourTooltip": "قلم کا رنگ بدلتا ہے.", "Movie.setColour": "رنگ مقرر", "Movie.submitDisabled": "آپ کی فلم منتقل نہیں ہوتی. کچھ دلچسپ بنانے کے لئے بلاکس کا استعمال کریں. پھر آپ اپنی فلم کو گیلری، نگارخانہ میں جمع کر سکتے ہیں.", "Movie.galleryTooltip": "فلموں کی گیلری، نگارخانہ کھولیں.", "Movie.galleryMsg": "گیلری دیکھیں", "Movie.submitTooltip": "اپنی فلم گیلری، نگارخانہ میں جمع کرو.", "Movie.submitMsg": "اپنی فلم گیلری، نگارخانہ میں جمع کرو.", "Movie.helpLayer": "پس منظر کے حلقے کو اپنے پروگرام کے اوپر منتقل کریں. اس کے بعد یہ شخص کے پیچھے آتا ہے.", "Movie.helpText1": "اس شخص کو اپنی طرف متوجہ کرنے کے لئے سادہ شکلیں استعمال کریں.", "Movie.helpText2a": "یہ سطح ایک فلم ہے. آپ چاہتے ہیں کہ شخص کی بازو اسکرین میں منتقل ہوجائے. پیش نظارہ دیکھنے کیلئے کھیل بٹن دبائیں.", "Movie.helpText2b": "جیسا کہ فلم ادا کرتا ہے، 'وقت' بلاک کی قدر 0 سے 100 تک کی جاتی ہے. چونکہ آپ کو 'باز' کی بازی 0 شروع کرنے اور 100 پر جانے کے لئے اس سے آسان ہونا چاہئے.", "Movie.helpText3": "'وقت' بلاک 0 سے 100 تک شمار ہوتا ہے. لیکن اب آپ دوسرے بازو کی 'Y' پوزیشن چاہتے ہیں 100 سے شروع ہونے اور 0. پر جائیں. کیا آپ کو ایک سادہ ریاضیاتی فارمولا پتہ چلتا ہے جو سمت پھیلتا ہے؟", "Movie.helpText4": "پیرس کو کراس بنانے کے لئے پچھلے سطح میں آپ نے کیا سیکھا.", "Movie.helpText5": "بازو کے لئے ریاضیاتی فارمولا پیچیدہ ہے. یہاں جواب ہے:", "Movie.helpText6": "شخص کو دو ہاتھ  دو", "Movie.helpText7": "فلم کے پہلے نصف کے لئے ایک چھوٹا سا سر نکالنے کے لئے 'اگر' بلاک کا استعمال کریں. پھر فلم کے دوسرے نصف کے لئے ایک بڑا سر ڈراکرے.", "Movie.helpText8": "ٹانگوں کو فلم کے ذریعہ آدھے راستے سے ہٹائیں.", "Movie.helpText9": "شخص کے پیچھے ایک توسیع حلقہ ڈرا کرلو.", "Movie.helpText10": "جو کچھ آپ چاہتے ہیں اس کی فلم بنائیں. آپ کو نئی بلاکس کی بڑی تعداد ملی ہے جو آپ تلاش کر سکتے ہیں. مزے کرو!", "Movie.helpText10Reddit": "\"؛ گیلری، نگارخانہ \"؛ کا استعمال کریں. اس فلم کو دیکھنے کے لئے بٹن جسے دوسرے لوگوں نے بنایا ہے. اگر آپ ایک دلچسپ فلم بناتے ہیں تو، \"؛ گیلری، نگارخانہ میں جمع کریں \"؛ کا استعمال کریں. اسے شائع کرنے کیلئے بٹن.", "Music.playNoteTooltip": "مخصوص مدت اور پچ کے ایک موسیقی نوٹ ادا کرتا ہے.", "Music.playNote": "%1 نوٹ کو %2 ادا کریں", "Music.restTooltip": "مخصوص مدت کے لئے انتظار کرتا ہے.", "Music.restWholeTooltip": "ایک مکمل نوٹ کے لئے انتظار کرتا ہے.", "Music.rest": "باقی %1", "Music.setInstrumentTooltip": "مخصوص آلے پر سوئچ کرتا ہے جب بعد میں موسیقی کے نوٹوں کو کھیلنے.", "Music.setInstrument": " آلہ کو %1 میں سیٹ کرے", "Music.startTooltip": "'چلائیں پروگرام' کے بٹن پر کلک کیا جاتا ہے جب اندر بلاکس کو خارج کر دیتا ہے.", "Music.start": "جب%1 پر کلک کیا گیا", "Music.pitchTooltip": "ایک نوٹ (سی 4 7 ہے).", "Music.firstPart": "پہلا حصہ", "Music.piano": "پیانو", "Music.trumpet": "ترم", "Music.banjo": "بنجو", "Music.violin": "وایلن", "Music.guitar": "گٹار", "Music.flute": "بانسری", "Music.drum": "ڈھول", "Music.choir": "طائفہ", "Music.submitDisabled": "اپنا پروگرام چلائیں جب تک وہ روک نہیں پھر آپ اپنی موسیقی گیلری میں جمع کر سکتے ہیں.", "Music.galleryTooltip": "موسیقی کی گیلری، نگارخانہ کھولیں.", "Music.galleryMsg": "گیلری دیکھیں", "Music.submitTooltip": "اپنی موسیقی گیلری میں جمع کرو.", "Music.submitMsg": "گیلری، نگارخانہ میں جمع", "Music.helpUseFunctions": "آپ کا حل کام کرتا ہے، لیکن آپ بہتر کر سکتے ہیں. بار بار کوڈ کی رقم کو کم کرنے کے افعال کا استعمال کریں.", "Music.helpUseInstruments": "اگر آپ ہر ابتدائی بلاک میں ایک مختلف آلہ استعمال کرتے ہیں تو بہتر بہتر ہوگا.", "Music.helpText1": "'فریئر جیکس' کے پہلے چار نوٹوں کو تحریر کریں.", "Music.helpText2a": "ایک 'فنکشن' آپ کو بلاکس کو ایک ساتھ جمع کرنے کی اجازت دیتا ہے، پھر ان سے ایک بار سے زیادہ چلائیں.", "Music.helpText2b": "فریئر جیکس کے پہلے چار نوٹوں کو کھیلنے کے لئے ایک فنکشن بنائیں. اس تقریب کو دو بار چلائیں. کوئی نیا نوٹ بلاکس شامل نہ کریں.", "Music.helpText3": "فریئر جیکس کے اگلے حصے کے لئے ایک دوسری تقریب بنائیں. آخری نوٹ طویل ہے.", "Music.helpText4": "'فریئر جیکس' کے اگلے حصہ کے لئے ایک تیسری تقریب بنائیں. پہلا چار نوٹ کم ہیں.", "Music.helpText5": "فریئر جیکس کی مکمل دھن مکمل کریں.", "Music.helpText6a": "یہ نیا بلاک آپ کو ایک اور آلہ میں تبدیل کرنے کی اجازت دیتا ہے.", "Music.helpText6b": "وایلن کے ساتھ اپنی دھن کو کھیلیں.", "Music.helpText7a": "یہ نیا بلاک خاموش تاخیر میں اضافہ کرتا ہے.", "Music.helpText7b": "دوسرا آغاز بلاک بنائیں جس میں دو تاخیر بلاکس ہیں، پھر 'فریئر جیکس' بھی ادا کرتے ہیں.", "Music.helpText8": "ہر آغاز کے بلاک کو دو بار فریئر جیکس ادا کرنا چاہئے.", "Music.helpText9": "ہر کھیل 'فریئر جیکس' کو دو بار بلاکس بنائیں. تاخیر بلاکس کی صحیح تعداد میں شامل کریں.", "Music.helpText10": "جو کچھ آپ چاہتے ہو اسے لکھیں. آپ کو نئی بلاکس کی بڑی تعداد ملی ہے جو آپ تلاش کر سکتے ہیں. مزے کرو!", "Music.helpText10Reddit": "\"؛ گیلری، نگارخانہ \"؛ کا استعمال کریں. یہ دیکھنے کے لئے بٹن جسے دوسرے لوگوں نے تشکیل دیا ہے. اگر آپ دلچسپ بات کرتے ہیں تو، \"؛ گیلری، نگارخانہ میں جمع کریں \"؛ کا استعمال کریں. اسے شائع کرنے کیلئے بٹن.", "Pond.scanTooltip": "دشمنوں کے لئے سکین سمت کی وضاحت کریں (0-360). اس سمت میں قریبی دشمن کو فاصلہ واپس. اگر کوئی دشمن نہیں ملا تو انفینٹی واپس آتا ہے.", "Pond.cannonTooltip": "تپ آگ ایک سمت (0-360) اور ایک حد (0-70) کی وضاحت کریں.", "Pond.swimTooltip": "آگے بڑھو. سمت کی وضاحت کریں (0-360).", "Pond.stopTooltip": "سوئمنگ بند کرو پلیئر کو روکنے میں سست ہو جائے گا.", "Pond.healthTooltip": "کھلاڑی کی موجودہ صحت (0 مردہ ہے، 100 صحت مند ہے) واپس.", "Pond.speedTooltip": "پلیئر کی موجودہ رفتار (0 رکھی گئی ہے، 100 مکمل رفتار ہے) واپس.", "Pond.locXTooltip": "پلیئر کے X ہم آہنگی کو واپس دیتا ہے (0 بائیں جانب ہے، 100 دائیں کنارے ہے).", "Pond.locYTooltip": "کھلاڑی کے Y ہم آہنگی واپس دیتا ہے (0 سب سے نیچے کنارے ہے، 100 سب سے اوپر کنارہ ہے).", "Pond.docsTooltip": "زبان کی دستاویزات دکھائیں.", "Pond.documentation": "دستاویز کاری", "Pond.playerName": "کھلاڑی", "Pond.targetName": "ہدف", "Pond.pendulumName": "پینڈلم", "Pond.scaredName": "ڈرا ہوا", "Pond.helpUseScan": "آپ کا حل کام کرتا ہے، لیکن آپ بہتر کر سکتے ہیں. تپ کو بتانا کہ کتنا دور گولی مار کرنے کے لئے 'اسکین' کا استعمال کریں.", "Pond.helpText1": "ہدف کو مارنے کے لئے 'تپ' کمانڈ کا استعمال کریں. پہلا پیرامیٹر زاویہ ہے، دوسرا پیرامیٹر رینج ہے. صحیح مجموعہ تلاش کریں.", "Pond.helpText2": "اس ہدف کو کئی بار مارنے کی ضرورت ہے. غیر یقینی طور پر کچھ کرنے کے لئے 'جبکہ (سچ)' لوپ کا استعمال کریں.", "Pond.helpText3a": "یہ مخالف آگے پیچھے چلتا ہے، اسے ہٹانا مشکل بناتا ہے. 'اسکین' اظہار مخصوص سمت میں مخالف کو صحیح رینج واپس دیتا ہے.", "Pond.helpText3b": "یہ رینج بالکل وہی ہے جو 'تپ' کمانڈ درست طور پر آگ لگانے کی ضرورت ہے.", "Pond.helpText4": "یہ مخالف تپ کا استعمال کرنے کے لئے بہت دور ہے (جس میں 70 میٹر کی حد ہے). اس کے بجائے، مخالف اور حادثے کی طرف سوئمنگ شروع کرنے کے لئے 'تیر' کمانڈ استعمال کریں.", "Pond.helpText5": "یہ مخالف بھی تپ کا استعمال کرنے کے لئے بہت دور ہے. لیکن آپ ایک تصادم سے بچنے کے لئے بہت کمزور ہیں. مخالف کی طرف جھک جاؤ جبکہ آپ کے افقی مقام 50 سے زائد ہے. پھر 'روکے' اور تپ کا استعمال کریں.", "Pond.helpText6": "جب مخالف مارا جاتا ہے تو یہ دور ہوجائے گا. اس کی طرف رخ کرو اگر یہ حد (70 میٹر) سے باہر ہے.", "Gallery": "گیلری"}