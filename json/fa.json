{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Arash.pt", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hossein.safavi", "Lakzon", "<PERSON><PERSON><PERSON><PERSON>", "Nhzandi", "Reza1615", "TerranBoy"]}, "Games.name": "بازی‌های بستن", "Games.puzzle": "پازل", "Games.maze": "پیچ‌درپیچ", "Games.bird": "پرنده", "Games.turtle": "لاک‌پشت", "Games.movie": "فیلم", "Games.music": "آهنگ", "Games.pondTutor": "سرپرست تالاب", "Games.pond": "تالاب", "Games.linesOfCode1": "شما این مرحله را با یک خط کد جاوااسکریپت حل کردید:", "Games.linesOfCode2": "شما این مرحله را با %1 خط کد جاوااسکریپت حل کردید:", "Games.nextLevel": "آیا برای مرحله %1 آماده اید؟", "Games.finalLevel": "آیا تو آماده چالش بعدی هستی؟", "Games.submitTitle": "عنوان:", "Games.linkTooltip": "ذخیره و پیوند به بلوک‌ها.", "Games.runTooltip": "اجرا کردن برنامهٔ نوشته شده", "Games.runProgram": "اجرای برنامه", "Games.resetTooltip": "توقف برنامه و بازنشانی مرحله", "Games.resetProgram": "از نو", "Games.help": "راهنما", "Games.catLogic": "منطق", "Games.catLoops": "حلقه‌ها", "Games.catMath": "ریاضی", "Games.catText": "متن", "Games.catLists": "فهرست‌ها", "Games.catColour": "رنگ", "Games.catVariables": "متغییرها", "Games.catProcedures": "توابع", "Games.httpRequestError": "مشکلی با درخواست وجود داشت.", "Games.linkAlert": "اشتراک‌گذاری بلاک‌هایتان با این پیوند:\n\n%1", "Games.hashError": "شرمنده، «%1» با هیچ برنامهٔ ذخیره‌شده‌ای تطبیق پیدا نکرد.", "Games.xmlError": "نتوانست پروندهٔ ذخیرهٔ شما بارگیری شود.  احتمالاً با نسخهٔ متفاوتی از بلوکی درست شده است؟", "Games.listVariable": "فهرست", "Games.textVariable": "متن", "Games.breakLink": "یک بار که شما آغاز به ویرایش جاوااسکریپت می‌کنید، شما نمی‌توانید به ویرایش بلاک‌ها برگردید. آیا این موضوع را می‌پذیرید؟", "Games.blocks": "بلوک‌ها", "Games.congratulations": "تبریک می‌گوییم!", "Games.helpAbort": "این سطح از بازی بسیار سخت می‌باشد. آیا مایل هستید تا آن را رد کرده و به مرحلهٔ بعد بروید؟‌ بعداً می‌توانید به این مرحله بازگردید.", "Index.clear": "همهٔ راه‌حل‌ها را از بین میبری؟", "Index.subTitle": "بازی‌هایی برای برنامه‌نویسان آینده", "Index.moreInfo": "اطلاعات بیشتر...", "Index.startOver": "می خواید از اول شروع کنید؟", "Index.clearData": "خا<PERSON>ی کردن داده", "Puzzle.animal1": "مرغا<PERSON>ی", "Puzzle.animal1Trait1": "پر و بال", "Puzzle.animal1Trait2": "منق<PERSON>ر", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "گربه", "Puzzle.animal2Trait1": "ریش", "Puzzle.animal2Trait2": "پوستین", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "زنبور عسل", "Puzzle.animal3Trait1": "عسل", "Puzzle.animal3Trait2": "نیش حشرات", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "حلزون", "Puzzle.animal4Trait1": "پوسته", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON><PERSON>ن", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "تصویر:", "Puzzle.legs": "پاها:", "Puzzle.legsChoose": "انتخاب کنید...", "Puzzle.traits": "ویژگی‌ها:", "Puzzle.error0": "عالی!\nهر %1 بلوک درست است.", "Puzzle.error1": "تقریبا! یک بلوک نادرست است.", "Puzzle.error2": "%1 بلوک نادرست است.", "Puzzle.tryAgain": "بلوک پررنگ شده درست نیست.\nمجددا تلاش کنید.", "Puzzle.checkAnswers": "بررسی پاسخ", "Puzzle.helpText": "برای هر جانو<PERSON> (س<PERSON><PERSON>)، در کنار تصویرشان، تعدادپاها را مشخص کنید و ویژگی‌های آن را مشخص کنید.", "Maze.moveForward": "حرکت رو به جلو", "Maze.turnLeft": "بپیچ به چپ", "Maze.turnRight": "بپیچ به راست", "Maze.doCode": "انجام", "Maze.helpIfElse": "بلوک‌ها اگر-آنگاه یک کار را انجام می‌دهند یا یک کار دیگر.", "Maze.pathAhead": "اگر مسیر پیش رو", "Maze.pathLeft": "اگر مسیر به سمت چپ", "Maze.pathRight": "اگر مسیر به سمت راست", "Maze.repeatUntil": "تکرار تا", "Maze.moveForwardTooltip": "بازیکن را یک خانه جلو می‌برد.", "Maze.turnTooltip": "بازیکن ۹۰ درجه چپ یا راست می‌چرخاند.", "Maze.ifTooltip": "اگر مسیری در یک جهت وجود دارد، پس کارهایی انجام بده.", "Maze.ifelseTooltip": "اگر مسیری در جهت مشخص‌شده وجود دارد، اول اولین بلوک کارها را انجام بده. در غیر اینصورت بلوک دوم کارها را انجام بده.", "Maze.whileTooltip": "کارهای محصور را تا رسیدن به نقطهٔ پایان تکرار کن.", "Maze.capacity0": "شما %0 بلوک باقی‌مانده دارید.", "Maze.capacity1": "شما %1 بلوک مانده دارید.", "Maze.capacity2": "شما %2 بلوک باقی‌مانده دارید.", "Maze.runTooltip": "می‌گذارد کاربر آنچه که بلوک‌ها می‌گوید را انجام دهد.", "Maze.resetTooltip": "بازیکن را در اول شروع پیچ‌درپیچ قرار بده.", "Maze.helpStack": "تعدادی از بلوک‌های «حرکت به جلو» را با هم در پشته قرار بده که به رسیدن من به هدف کمک کند.", "Maze.helpOneTopBlock": "در این مرجله، شما نیازمند قرار دادن همهٔ بلوک‌ها در فضای کار سفید هستید.", "Maze.helpRun": "اجرای برنامهٔ شما برای دیدن اینکه چه رخ می‌دهد.", "Maze.helpReset": "برنامهٔ شما پیچ‌درپیچ را حل نکرد.  «از نو» را فشار دهید و دوباره تلاش کنید.", "Maze.helpRepeat": "رسیدن به انتهای مسیر با فقط دو بلوک. استفاده از «دوباره» برای اجرای یک بلوک بیشتر از یکی.", "Maze.helpCapacity": "شما همهٔ بلوک‌های این مرحله را استفاده کرده‌اید. برای ایجاد یک بلوک جدید، شما ابتدا نیازمند حذف یک بلوک موجود هستید.", "Maze.helpRepeatMany": "شما می‌توانید بیشتر از بلوک را در بلوک «تکرار» جای دهید.", "Maze.helpSkins": "انتخاب بازیکن مجبوب شما از این منو.", "Maze.helpIf": "یک بلوک «اگر» کاری انجام می‌دهد اگر شرایط صادق بود.  چرخش به چپ را آزمایش کنید اگر یک مسیر به چپ است.", "Maze.helpMenu": "بر %1 در بلوک «اگر» کلیک کنید که شرایط را تغییر دهید.", "Maze.helpWallFollow": "می‌توانید یک پیچ‌درپیچ پیچیده را جل کنید؟  از دیوار دست چپ استفاده کنید. برنامه‌نویسان حرفه‌ای فقط!", "Bird.noWorm": "کرم ندارد", "Bird.heading": "عنوان", "Bird.noWormTooltip": "شرط زمانی که پرنده کرم را نگرفته است", "Bird.headingTooltip": "در جهت زاویه مورد نظر حرکت کنید:‌ صفر به سمت راست، ۹۰ مسیر مستقیم و به همین ترتیب باقی زوایا.", "Bird.positionTooltip": "x و y مکان پرنده را مشخص می‌کنند. هنگامی که x = 0 باشد، پرنده نزدیک لبهٔ سمت چپ و هنگامی که x = 100 باشد، در نزدیکی لبهٔ سمت راست می‌باشد. هنگامی که y = 0 پرنده در پایین و هنگامی که y = 100 در بالای صفحه می‌باشد.", "Bird.helpHeading": "زاویه حرکت را تغییر دهید تا پرنده بتواند کرم را بگیرد و به آشیانهٔ خود بازگردد.", "Bird.helpElse": "'اگر' مسدود شده‌اید برای تغییر بر روی آیکون کلیک کنید.", "Bird.helpMutator": "بلاک 'else' را به داخل بلاک 'if' بکشید.", "Turtle.moveTooltip": "لاک پشت را به مقدار مشخص‌شده جلو یا عقب منتقل می‌کند.", "Turtle.moveForward": "انتقال به جلو تا", "Turtle.moveBackward": "انتقال به پشت تا", "Turtle.turnTooltip": "چرخاندن لاک پشت به چپ یا راست با عدد مشخص‌شدهٔ درجه.", "Turtle.turnRight": "چرخش به راست به مقدار", "Turtle.turnLeft": "چرخش به چپ به مقدار", "Turtle.widthTooltip": "پهنای قلم را تغییر می‌دهد.", "Turtle.setWidth": "تنظیم پهنا به", "Turtle.colourTooltip": "رنگ قلم را تغییر می‌دهد.", "Turtle.setColour": "تنظیم رنگ به", "Turtle.penTooltip": "پالا یا پایین‌بردن قلم، برای شروع یا پایان نقاشی.", "Turtle.penUp": "قلم تا", "Turtle.penDown": "قلم پایین", "Turtle.turtleVisibilityTooltip": "(دایره و فلش) لاک‌کپشت را ظاهر یا پنهان می‌کند.", "Turtle.hideTurtle": "پنهان کردن لاک‌‌پشت", "Turtle.showTurtle": "نمایش لاک‌پشت", "Turtle.printHelpUrl": "https://fa.wikipedia.org/wiki/%DA%86%D8%A7%D9%BE_%28%D8%B5%D9%86%D8%B9%D8%AA%29", "Turtle.printTooltip": "کشیدن متن در جهت لاک‌پشت و موقعیتش.", "Turtle.print": "چاپ", "Turtle.fontHelpUrl": "https://fa.wikipedia.org/wiki/%D9%82%D9%84%D9%85_%28%D8%B1%D8%A7%DB%8C%D8%A7%D9%86%D9%87%29", "Turtle.fontTooltip": "اندازهٔ قلم استفاده شده توسط چاپ بلوک را مشخص می‌کند.", "Turtle.font": "قلم", "Turtle.fontSize": "اندازهٔ قلم", "Turtle.fontNormal": "<PERSON><PERSON><PERSON>", "Turtle.fontBold": "پررنگ", "Turtle.fontItalic": "کج", "Turtle.galleryMsg": "نمایش نگارخانه", "Turtle.submitMsg": "افزودن به نگارخانه", "Turtle.helpUseLoop": "راه حل شما کار کرد، اما می‌توانید بهترش کنید.", "Turtle.helpUseLoop4": "تنها با استفاده از چهار بلوک ستاره را بکشید.", "Turtle.helpText1": "برنامه‌ای ایجاد کنید که یک مربع بکشد.", "Turtle.helpText2": "برنامه خود را تغییر دهید تا به جای مربع، یک پنج ضلعی بکشد.", "Turtle.helpText3a": "یک بلوک جدید وجود دارد که به شما اجازه می‌دهد تا رنگ را تغییر دهید:", "Turtle.helpText3b": "ترسیم یک ستاره زرد.", "Turtle.helpToolbox": "یک دسته‌بندی را انتخاب کنید تا بلوک‌ها را ببینید.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "شروع X", "Movie.y1": "شروع Y", "Movie.x2": "پایان X", "Movie.y2": "پ<PERSON><PERSON>ان Y", "Movie.radius": "شعاع", "Movie.width": "<PERSON><PERSON><PERSON>", "Movie.height": "ارتفاع", "Movie.circleTooltip": "در محل مشخص شده، یک دایره با شعاع مشخص شده می‌کشد.", "Movie.circleDraw": "دایره‌", "Movie.rectTooltip": "در محل مشخص شده، یک مستطیل با عرض و ارتفاع مشخص شده می‌کشد.", "Movie.rectDraw": "مستطیل", "Movie.lineTooltip": "از یک نقطه تا یک نقطهٔ دیگر، خطی با ضخامت مشخص شده می‌کشد.", "Movie.lineDraw": "خط", "Movie.colourTooltip": "رنگ قلم را تغییر دهید.", "Movie.setColour": "تنظیم رنگ به", "Movie.galleryTooltip": "گالری فیلم را باز کنید.", "Movie.galleryMsg": "نمایش نگارخانه", "Movie.submitTooltip": "فیلم خود را در گالری ثبت کنید.", "Movie.submitMsg": "افزودن به نگارخانه", "Movie.helpText1": "از شکل‌های ساده برای کشیدن این شخص استفاده کنید.", "Music.firstPart": "بخش اول", "Music.galleryMsg": "نمایش گالری", "Pond.locYTooltip": "مختصات Y بازیکن را برمی‌گرداند ( 0 لبهٔ پایینی، و 100 لبهٔ بالایی می‌باشد).", "Pond.docsTooltip": "نمایش مستندات زبان مورد نظر", "Pond.documentation": "مستندات", "Pond.playerName": "با<PERSON><PERSON>کن", "Pond.targetName": "هد<PERSON>", "Pond.pendulumName": "آونگ", "Pond.scaredName": "ترسیده", "Pond.helpUseScan": "راه حل شما درست است، اما می‌توانید بهتر از این هم کار کنید. از 'scan' استفاده کنید تا به توپ بگویید که در چه فاصله‌ای شلیک کند.", "Pond.helpText1": "از دستور 'cannon' استفاده کنید تا هدف را بزنید. پارامتر اول زاویه و پارامتر دوم فاصله می‌باشد. ترکیب مناسب را پیدا کنید.", "Pond.helpText2": "هدف مورد نظر بایستی چندین بار مورد اصابت قرار گیرد. از یک حلقهٔ 'while (true)' استفاده کنید تا یک عمل را به دفعات بینهایت انجام دهید.", "Gallery": "گالری"}