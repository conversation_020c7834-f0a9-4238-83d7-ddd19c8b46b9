{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Sabelöga", "<PERSON><PERSON><PERSON>", "Velg", "WikiPhoenix"]}, "Games.name": "Blocklyspel", "Games.puzzle": "<PERSON><PERSON><PERSON>", "Games.maze": "Labyrint", "Games.bird": "<PERSON><PERSON><PERSON>", "Games.turtle": "Sköldpadda", "Games.movie": "Film", "Games.music": "Mu<PERSON>", "Games.pondTutor": "Dammhandledare", "Games.pond": "<PERSON><PERSON>", "Games.linesOfCode1": "Du löste nivån med 1 rad JavaScript:", "Games.linesOfCode2": "Du löste nivån med %1 rader JavaScript:", "Games.nextLevel": "Är du redo för nivå %1?", "Games.finalLevel": "Är du redo för nä<PERSON> utman<PERSON>?", "Games.submitTitle": "Titel:", "Games.linkTooltip": "Spara och länka till block.", "Games.runTooltip": "K<PERSON>r programmet du skrev.", "Games.runProgram": "Kör program", "Games.resetTooltip": "Stoppa programmet och återställ nivån.", "Games.resetProgram": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Logik", "Games.catLoops": "Loopar", "Games.catMath": "Matematik", "Games.catText": "Text", "Games.catLists": "Listor", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "Variabler", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "Det uppstod ett problem med begäran.", "Games.linkAlert": "Dela dina block med länken: \n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON>, '%1' överensstämmer inte med något sparat program.", "Games.xmlError": "Kunde inte läsa din sparade fil. Den skapades kanske med en annan version av Blockly?", "Games.submitted": "Tack för programmet! Om vår personal som består av dresserade apor gillar det kommer de publicera det i galleriet inom några dagar.", "Games.listVariable": "lista", "Games.textVariable": "text", "Games.breakLink": "<PERSON><PERSON>r du väl börjar redigera JavaScript kan du inte gå tillbaka och redigera block. Är det okej med dig?", "Games.blocks": "Block", "Games.congratulations": "Grat<PERSON>rar!", "Games.helpAbort": "Den här nivån är extremt svår. Vill du hoppa över den och gå till nästa spel? Du kan alltid komma tillbaka senare.", "Index.clear": "Radera alla dina l<PERSON>ning<PERSON>?", "Index.subTitle": "Spel för morgondagens programmerare.", "Index.moreInfo": "Information för l<PERSON>...", "Index.startOver": "Vill du börja om?", "Index.clearData": "Rensa data", "Puzzle.animal1": "<PERSON><PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://sv.wikipedia.org/wiki/<PERSON>ka", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://sv.wikipedia.org/wiki/<PERSON>t", "Puzzle.animal3": "Bi", "Puzzle.animal3Trait1": "<PERSON><PERSON>", "Puzzle.animal3Trait2": "Gadd", "Puzzle.animal3HelpUrl": "https://sv.wikipedia.org/wiki/Bin", "Puzzle.animal4": "<PERSON><PERSON><PERSON>", "Puzzle.animal4Trait1": "Skal", "Puzzle.animal4Trait2": "Slem", "Puzzle.animal4HelpUrl": "https://sv.wikipedia.org/wiki/Snä<PERSON>or", "Puzzle.picture": "bild:", "Puzzle.legs": "ben:", "Puzzle.legsChoose": "välj...", "Puzzle.traits": "egenskaper:", "Puzzle.error0": "Perfekt!\nAlla %1 block är rätt.", "Puzzle.error1": "Nästan! Ett block är fel.", "Puzzle.error2": "%1 block är fel.", "Puzzle.tryAgain": "Det markerade blocket är inte rätt.\nFörsök igen.", "Puzzle.checkAnswers": "Kontrollera svar", "Puzzle.helpText": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>), l<PERSON><PERSON> till bilden, v<PERSON><PERSON><PERSON> antalet ben och skapa en lista med dess egenskaper.", "Maze.moveForward": "g<PERSON> framåt", "Maze.turnLeft": "sväng vänster", "Maze.turnRight": "sv<PERSON><PERSON> höger", "Maze.doCode": "utför", "Maze.helpIfElse": "Om-annars-block kommer att göra det ena eller det andra.", "Maze.pathAhead": "om det är en väg framåt", "Maze.pathLeft": "om det är en väg till vänster", "Maze.pathRight": "om det är en väg till höger", "Maze.repeatUntil": "upprepa tills", "Maze.moveForwardTooltip": "Flyttar spelaren framåt en ruta.", "Maze.turnTooltip": "Svänger spelaren 90 grader till vänster eller höger.", "Maze.ifTooltip": "Om det finns en väg i angiven riktning, utför vissa handlingar.", "Maze.ifelseTooltip": "Om det finns en väg i angiven rik<PERSON>, utför då det första blocket med åtgärder. Annars, utför det andra blocket med åtgärder.", "Maze.whileTooltip": "Upprepa de slutna kommandona till dess att slutpunkten nås.", "Maze.capacity0": "Du har %0 block kvar.", "Maze.capacity1": "Du har %1 block kvar.", "Maze.capacity2": "Du har %2 block kvar.", "Maze.runTooltip": "<PERSON><PERSON><PERSON> spelaren att göra vad blocken säger.", "Maze.resetTooltip": "Placerar spelaren i labyrintens början.", "Maze.helpStack": "Stapla ett par 'gå framåt'-block för att hjälpa mig att nå målet.", "Maze.helpOneTopBlock": "På den här nivån måste du sätta ihop blocken i det vita arbetsområdet.", "Maze.helpRun": "<PERSON><PERSON><PERSON> ditt program för att se vad som händer.", "Maze.helpReset": "Ditt program löste inte labyrinten. Tryck på 'Återställ' och försök igen.", "Maze.helpRepeat": "Nå slutet av vägen med hjälp av endast två block. Använd 'upprepa' för att använda ett block mer än en gång.", "Maze.helpCapacity": "Du har använt alla block för nivån. <PERSON><PERSON><PERSON> att skapa ett nytt block måste du först radera ett befintligt block.", "Maze.helpRepeatMany": "<PERSON> får plats med mer än ett block i ett upprepningsblock.", "Maze.helpSkins": "V<PERSON><PERSON>j din favoritspelare från menyn.", "Maze.helpIf": "Ett 'om'-block kommer endast att göra någonting om villkoret är sant. Pröva att svänga vänster om det finns en väg till vänster.", "Maze.helpMenu": "Klicka på %1 i \"om\"-blocket för att ändra dess tillstånd.", "Maze.helpWallFollow": "Kan du lösa den här komplicerade labyrinten? Försök att följa väggen till vänster. Endast för avancerade programmerare!", "Bird.noWorm": "har ingen mask", "Bird.heading": "riktning", "Bird.noWormTooltip": "Tillståndet när fågeln inte har fångat masken.", "Bird.headingTooltip": "Flyg i angiven riktning: 0 är till höger, 90 är rakt upp osv.", "Bird.positionTooltip": "x och y markerar fågelns position. När x = när fågeln nära den vänstra sidan, när x = 100 är den nära  den högra sidan. När y = 0 är fågel längst ned, när y = 100 är den längst upp.", "Bird.helpHeading": "Ändra riktningen för att fågeln ska få tag i masken och landa i sitt bo.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON>nd det här blocket för att flyga i en riktning när du har masken och i en annan riktning när du inte har masken.", "Bird.helpX": "'x' är din nuvarande horisontella position. Använd det här blocket för att flyga i en riktning när 'x' är mindre än en siffra och annars i en annan riktning.", "Bird.helpElse": "<PERSON><PERSON><PERSON> på ikonen för att ändra 'om'-blocket.", "Bird.helpElseIf": "Den här nivån kräver både ett 'annars om' och ett 'annars'-block.", "Bird.helpAnd": "'och'-blocket är endast sant om båda inmatningar är sanna.", "Bird.helpMutator": "Dra ett 'annars'-block till 'om'-blocket.", "Turtle.moveTooltip": "Flyttar sköldpaddan framåt eller bakåt enligt den angivna summan.", "Turtle.moveForward": "<PERSON>tta framåt med", "Turtle.moveBackward": "<PERSON><PERSON> bak<PERSON>t med", "Turtle.turnTooltip": "Vrider skö<PERSON>paddan vänster eller höger enligt det angivna antalet grader.", "Turtle.turnRight": "sväng höger med", "Turtle.turnLeft": "sväng vänster med", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON> bredden på pennan.", "Turtle.setWidth": "ställ in bredd till", "Turtle.colourTooltip": "<PERSON><PERSON><PERSON> f<PERSON>.", "Turtle.setColour": "ställ in färg till", "Turtle.penTooltip": "<PERSON>y<PERSON> eller sänk<PERSON>, för att sluta eller bör<PERSON> rita.", "Turtle.penUp": "penna upp", "Turtle.penDown": "penna ned", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON> (cirkel och pil) synlig eller osynlig.", "Turtle.hideTurtle": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.showTurtle": "visa sköldpadda", "Turtle.printHelpUrl": "https://sv.wikipedia.org/wiki/Tryckteknik", "Turtle.printTooltip": "Ritar text i sköldpaddans riktning på dess placering.", "Turtle.print": "visa", "Turtle.fontHelpUrl": "https://sv.wikipedia.org/wiki/<PERSON><PERSON><PERSON>tt", "Turtle.fontTooltip": "Anger vilket typsnitt som ska användas av textblocket.", "Turtle.font": "typsnitt", "Turtle.fontSize": "teckenstorlek", "Turtle.fontNormal": "normal", "Turtle.fontBold": "fet", "Turtle.fontItalic": "kursiv", "Turtle.submitDisabled": "<PERSON><PERSON><PERSON> ditt program tills det stannar. <PERSON><PERSON> kan du skicka din teckning till galleriet.", "Turtle.galleryTooltip": "Öppna teckningsgalleriet.", "Turtle.galleryMsg": "Se galleri", "Turtle.submitTooltip": "<PERSON><PERSON>a din teckning till galleriet.", "Turtle.submitMsg": "Skicka till galleriet", "Turtle.helpUseLoop": "<PERSON> fun<PERSON>, men du kan göra det b<PERSON>tre.", "Turtle.helpUseLoop3": "Rita formen med bara tre block.", "Turtle.helpUseLoop4": "Rita stjärnan med bara fyra block.", "Turtle.helpText1": "Skapa ett program som ritar en kvadrat.", "Turtle.helpText2": "Ändra ditt program för att rita en femhörning i stället för en kvadrat.", "Turtle.helpText3a": "Det finns ett nytt block som låter dig ändra färgen:", "Turtle.helpText3b": "<PERSON> en gul stjärna.", "Turtle.helpText4a": "Det finns ett nytt block som låter dig lyfta din penna från papperet när du flyttar den:", "Turtle.helpText4b": "Rita en liten gul stjärna och rita sedan en linje ovanför den.", "Turtle.helpText5": "Kan du rita fyra stjärnor ordnade i en kvadrat istället för en stjärna?", "Turtle.helpText6": "Rita tre gula stjärnor och en vit linje.", "Turtle.helpText7": "Rita stjärnorna och sedan fyra vita linjer.", "Turtle.helpText8": "Om en vit linje ritas i 360 grader ser den ut som en fullmåne.", "Turtle.helpText9": "Kan du lägga till en svart cirkel så att månen blir en halvmåne?", "Turtle.helpText10": "Rita vad du vill. Du har massa nya block att utforska. Ha kul!", "Turtle.helpText10Reddit": "<PERSON>v<PERSON>nd knappen \"Se galleriet\" för att se vad andra personer har ritat. Använd knappen \"Skicka till galleriet\" för att publicera din teckning om du ritar någonting intressant.", "Turtle.helpToolbox": "Välj en kategori för att se blocken.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "start x", "Movie.y1": "start y", "Movie.x2": "slut x", "Movie.y2": "slut y", "Movie.radius": "radie", "Movie.width": "bredd", "Movie.height": "<PERSON><PERSON><PERSON><PERSON>", "Movie.circleTooltip": "<PERSON>r en cirkel på angiven plats och med angiven radie.", "Movie.circleDraw": "cirkel", "Movie.rectTooltip": "<PERSON>r en cirkel på angiven plats och med angiven bredd och höjd.", "Movie.rectDraw": "rektang<PERSON>", "Movie.lineTooltip": "<PERSON>r en linje från en punkt till en annan med angiven bredd.", "Movie.lineDraw": "linje", "Movie.timeTooltip": "Returnerar den aktuella tiden i animationen (0-100).", "Movie.colourTooltip": "<PERSON><PERSON><PERSON> färgen på <PERSON>nan.", "Movie.setColour": "ställ in färg till", "Movie.submitDisabled": "Din film rör sig inte. Använd block för att göra någonting intressant. Då kan du sedan skicka din film till galleriet.", "Movie.galleryTooltip": "Öppna filmgalleriet.", "Movie.galleryMsg": "Se galleri", "Movie.submitTooltip": "<PERSON><PERSON><PERSON> din film till galleriet.", "Movie.submitMsg": "Skicka till galleriet", "Movie.helpLayer": "Flytta bakgrundscirkel längst upp i dit program. Den kommer sedan att dyka upp bakom personen.", "Movie.helpText1": "<PERSON><PERSON><PERSON><PERSON> enkla former för att rita personen.", "Movie.helpText2a": "Den här nivån är en film. Du ska förflytta personens arm tvärs över skärmen. Tryck på uppspelningsknappen för en förhandsvisning.", "Movie.helpText2b": "När filmen spelas upp kommer värdet för blocket 'time' räkna från 0 till 100. Eftersom du vill att 'y'-positionen för armen ska börja vid 0 och gå till 100 bör detta vara enkelt.", "Movie.helpText3": "Blocket 'time' r<PERSON><PERSON><PERSON> från 0 till 100. Men nu vill du att 'y'-positionen för den andra armen ska börja vid 100 och gå till 0. Kan du lista ut en enkel matematisk formel som vänder på riktningen?", "Movie.helpText4": "Använd vad du lärde dig i den tidigare nivån för att få benen att korsa varandra.", "Movie.helpText5": "Den matematiska formeln för armen är komplicerad. Här är svaret:", "Movie.helpText6": "Ge personen två händer.", "Movie.helpText7": "Använd blocket 'om' för att rita ett litet huvud i filmens första hälft. Rita sedan ett stort huvud i filmens andra hälft.", "Movie.helpText8": "Förflytta benen i motsatt riktning i filmens andra hälft.", "Movie.helpText9": "<PERSON> en utvidgande cirkel bakom personen.", "Movie.helpText10": "<PERSON><PERSON><PERSON> en film av vad du vill. Du har massor av nya block att utforska. Ha kul!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON><PERSON> knappen \"Se galleriet\" för att se filmer som andra personer har gjort. Om du gör en intressant film kan du använda knappen \"<PERSON>cka till galleriet\" för att publicera den.", "Music.playNoteTooltip": "<PERSON><PERSON><PERSON> upp en musiknot av vald varaktighet och tonhöjd.", "Music.playNote": "spela %1 not %2", "Music.restTooltip": "Väntar på vald varaktighet.", "Music.restWholeTooltip": "Väntar på en helnot.", "Music.rest": "vila %1", "Music.setInstrumentTooltip": "Växlar till valt instrument när flera musiknoter spelas.", "Music.setInstrument": "ändra instrument till %1", "Music.startTooltip": "Utför blocken inuti när knappen \"Kör program\" klickas.", "Music.start": "när %1 klickas", "Music.pitchTooltip": "En not (C4 är 7).", "Music.firstPart": "<PERSON><PERSON><PERSON><PERSON>", "Music.piano": "piano", "Music.trumpet": "trumpet", "Music.banjo": "banjo", "Music.violin": "violin", "Music.guitar": "gitarr", "Music.flute": "<PERSON><PERSON><PERSON><PERSON>", "Music.drum": "trumma", "Music.choir": "<PERSON><PERSON><PERSON>", "Music.submitDisabled": "<PERSON><PERSON><PERSON> ditt program tills det stannar. <PERSON>dan kan du skicka din musik till galleriet.", "Music.galleryTooltip": "Öppna musikgalleriet.", "Music.galleryMsg": "Se galleri", "Music.submitTooltip": "Skicka din musik till galleriet.", "Music.submitMsg": "Skicka till galleriet", "Music.helpUseFunctions": "<PERSON> fun<PERSON>, men du kan förbättra den. Använd funktioner för att reducera mängden upprepad kod.", "Music.helpUseInstruments": "Musiken låter bättre om du använder ett annorlunda instrument i varje startblock.", "Music.helpText1": "Komponera de första fyra noterna ur \"Broder Jakob\".", "Music.helpText2a": "En \"funktion\" lå<PERSON> dig gruppera block tillsammans och sedan köra dem mer än en gång.", "Music.helpText2b": "Skapa en funktion för att spela de fyra första noterna ur \"<PERSON><PERSON><PERSON> Jakob\". Kör funktionen två gånger. Lägg inte till några nya notblock.", "Music.helpText3": "<PERSON>ka<PERSON> en annan funktion för nästa del ur \"Broder Jakob\". Den sista noten är längre.", "Music.helpText4": "Skapa en tredje funktion för nä<PERSON> del ur \"Broder Jakob\". De fyra första noterna är kortare.", "Music.helpText5": "<PERSON>lutf<PERSON><PERSON> den fullständiga melodin ur \"Broder Jakob\".", "Music.helpText6a": "Detta nya block låter dig ändra till ett annat instrument.", "Music.helpText6b": "<PERSON><PERSON><PERSON> din ton med en violin.", "Music.helpText7a": "Detta nya block lägger till en tyst fördröjning.", "Music.helpText7b": "Skapa ett till startblock som har två fördröjningsblock och sedan spelar \"Broder Jakob\".", "Music.helpText8": "<PERSON><PERSON>je startblock b<PERSON><PERSON> <PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" två gånger.", "Music.helpText9": "Skapa fyra startblock som spel<PERSON> \"<PERSON><PERSON><PERSON>\" två gånger. Lägg till det rätta antalet fördröjningsblock.", "Music.helpText10": "Komponera vad du vill. Du har massa nya block att utforska. Ha kul!", "Music.helpText10Reddit": "Anv<PERSON>nd knappen \"Se galleriet\" för att se vad andra personer har komponerat. Använd knappen \"Skicka till galleriet\" för att publicera din musik om du komponerar någonting intressant.", "Pond.scanTooltip": "<PERSON><PERSON><PERSON> efter fiender. Ange en riktning (0-360). Returnerar avståndet till den närmaste fienden i den riktningen. Returnerar Oändligt om ingen fiende hittas.", "Pond.cannonTooltip": "Skjut med kanonen. Ange en riktning (0-360) och ett avstånd (0-70).", "Pond.swimTooltip": "Simma framåt. Ange en riktning (0-360).", "Pond.stopTooltip": "Sluta simma. <PERSON><PERSON><PERSON><PERSON> kommer att bromsa tills den stannar.", "Pond.healthTooltip": "<PERSON><PERSON>r spelarens nuvarande hälsa (0 är död, 100 är frisk).", "Pond.speedTooltip": "Returnerar spelarens aktuella hastigheten (0 är stopp, 100 är full fart).", "Pond.locXTooltip": "Returnerar spelarens X-koordinat (0 är den vänstra kanten, 100 är den högra kanten).", "Pond.locYTooltip": "Returnerar spelarens Y-koordinat (0 är den nedersta kanten, 100 är den översta kanten).", "Pond.logTooltip": "Skriver ett nummer till din webbläsares konsol.", "Pond.docsTooltip": "Visa språkets dokumentation.", "Pond.documentation": "Dokumentation", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Pendel", "Pond.scaredName": "<PERSON><PERSON>", "Pond.helpUseScan": "<PERSON> l<PERSON> fun<PERSON>, men du kan göra det bättre. Använd 'scan' för att tala om för kanonen hur långt den ska skjuta.", "Pond.helpText1": "<PERSON><PERSON><PERSON><PERSON> kommandot 'cannon' för att träffa kanonen. Den första parametern är vinkeln, den andra parametern är räckvidden. Hitta den rätta kombinationen.", "Pond.helpText2": "<PERSON><PERSON> mål behöver träffas flera gånger. Använd en 'while (true)'-loop för att göra någonting oändligt.", "Pond.helpText3a": "Den här motståndaren rör sig fram och tillbaka vilket gör det svårt att träffa. Kommandot 'scan' returnerar det exakta avståndet till en motståndare i den angivna riktningen.", "Pond.helpText3b": "<PERSON>na rä<PERSON>vidd är vad kommandot 'cannon' precis behöver för att skjuta träffsäkert.", "Pond.helpText4": "Denna användare är för långt borta för att använda kanonen (som har en räckvidd på 70 meter). Använd istället kommandot 'swim' för att börja simma mot motståndaren och krocka med den.", "Pond.helpText5": "<PERSON>na motståndare är för långt borta för att använda kanonen. Men du är för svag för att överleva en kollision. Simma fram till motståndaren medan din horisontala position är mindre än 50. Använd kommandot 'stop' sedan och använd kanonen.", "Pond.helpText6": "<PERSON><PERSON> mots<PERSON><PERSON><PERSON> förflyttar sig bortåt när den träffas. Simma framåt om den är utom rä<PERSON>.", "Gallery": "Galleri"}