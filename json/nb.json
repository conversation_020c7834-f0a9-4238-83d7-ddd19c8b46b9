{"@metadata": {"authors": ["<PERSON><PERSON>", "EdoAug", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SuperPotato"]}, "Games.name": "Blockly-spill", "Games.puzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.maze": "Labyrint", "Games.bird": "Fugl", "Games.turtle": "Skilpadde", "Games.movie": "Film", "Games.music": "Musikk", "Games.pondTutor": "<PERSON><PERSON><PERSON><PERSON>", "Games.pond": "Dam", "Games.linesOfCode1": "Du løste dette nivået med 1 linje JavaScript:", "Games.linesOfCode2": "Du løste dette nivået med %1 linjer JavaScript:", "Games.nextLevel": "<PERSON><PERSON> <PERSON> klar for nivå %1?", "Games.finalLevel": "Er du klar for neste u<PERSON>?", "Games.submitTitle": "Tittel:", "Games.linkTooltip": "Lagre og lenke til blokker.", "Games.runTooltip": "Kjør programmet du skrev.", "Games.runProgram": "Kjø<PERSON>", "Games.resetTooltip": "Stopp programmet og nullstill nivået.", "Games.resetProgram": "Nullstill", "Games.help": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLogic": "Logikk", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "<PERSON><PERSON>", "Games.catText": "Tekst", "Games.catLists": "Lister", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Variabler", "Games.catProcedures": "Funksjoner", "Games.httpRequestError": "Det oppsto et problem med forespørselen din", "Games.linkAlert": "Del dine blokker med denne lenken:\n\n%1", "Games.hashError": "<PERSON><PERSON><PERSON>, '%1' samsvarer ikke med noe lagret program.", "Games.xmlError": "Kunne ikke laste inn filen. Kanskje den ble laget med en annen versjon av Blockly?", "Games.submitted": "Takk for programmet! Om vårt personale bestående av trente aper liker det, blir det publisert til galleriet innen et par dager.", "Games.listVariable": "Liste", "Games.textVariable": "Tekst", "Games.breakLink": "<PERSON><PERSON>r du begynner å redigere JavaScript kan du ikke gå tilbake til redigeringsblokker. Er det greit?", "Games.blocks": "B<PERSON>kker", "Games.congratulations": "Gratulerer!", "Games.helpAbort": "Dette nivået er veldig vanskelig. Ønsker du å hoppe over det og fortsette til neste spill? Du kan alltids komme tilbake senere.", "Index.clear": "Slett alle løsningene dine?", "Index.subTitle": "Spill for morgendagens programmerere.", "Index.moreInfo": "Info for lærere …", "Index.startOver": "Ønsker du å starte på nytt?", "Index.clearData": "Fjern data", "Puzzle.animal1": "And", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Nebb", "Puzzle.animal1HelpUrl": "https://no.wikipedia.org/wiki/And", "Puzzle.animal2": "<PERSON><PERSON>", "Puzzle.animal2Trait1": "Værhår", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://no.wikipedia.org/wiki/<PERSON>t", "Puzzle.animal3": "<PERSON><PERSON>", "Puzzle.animal3Trait1": "Honning", "Puzzle.animal3Trait2": "Brodd", "Puzzle.animal3HelpUrl": "https://no.wikipedia.org/wiki/<PERSON>ier", "Puzzle.animal4": "<PERSON><PERSON>gle", "Puzzle.animal4Trait1": "Skall", "Puzzle.animal4Trait2": "<PERSON>", "Puzzle.animal4HelpUrl": "https://no.wikipedia.org/wiki/Snegler", "Puzzle.picture": "bilde:", "Puzzle.legs": "bein:", "Puzzle.legsChoose": "velg…", "Puzzle.traits": "trekk:", "Puzzle.error0": "Perfekt!\nAlle %1 blokker er riktig.", "Puzzle.error1": "Nesten! En blokk er feil.", "Puzzle.error2": "%1 blokker er feil.", "Puzzle.tryAgain": "Den merkede blokken er ikke riktig.\nFortsett å prøve.", "Puzzle.checkAnswers": "Sjekk svar", "Puzzle.helpText": "For hvert dyr (gr<PERSON><PERSON>), legg til dets bilde, velg antall bein, og legg en stabel med dets trekk.", "Maze.moveForward": "gå fremover", "Maze.turnLeft": "sving til venstre", "Maze.turnRight": "sving til høyre", "Maze.doCode": "ut<PERSON><PERSON><PERSON>", "Maze.helpIfElse": "If-else-blokker vil gjøre det ene eller det andre.", "Maze.pathAhead": "if-sti fremover", "Maze.pathLeft": "if-sti til venstre", "Maze.pathRight": "if-sti til høyre", "Maze.repeatUntil": "g<PERSON>nta inntil", "Maze.moveForwardTooltip": "Flytter spilleren ett felt fremover.", "Maze.turnTooltip": "<PERSON><PERSON><PERSON> spilleren 90 grader til høyre eller venstre.", "Maze.ifTooltip": "<PERSON><PERSON> det finnes en sti i angitt retning, gjør visse handlinger.", "Maze.ifelseTooltip": "<PERSON><PERSON> det finnes en sti i angitt retning, gjør den første blokken av handlinger. <PERSON><PERSON>, gjør den andre blokken av handlinger.", "Maze.whileTooltip": "Gjenta de sluttede kommandoene inntil sluttpunktet nås.", "Maze.capacity0": "Du har %0 blokker igjen.", "Maze.capacity1": "Du har %1 blokk igjen.", "Maze.capacity2": "Du har %2 blokker igjen.", "Maze.runTooltip": "Få spilleren til å gjøre det blokkene sier.", "Maze.resetTooltip": "<PERSON>tt spilleren tilbake til starten av labyrinten.", "Maze.helpStack": "<PERSON>t sammen et par 'gå fremover'-blokker for å hjelpe meg med å nå målet.", "Maze.helpOneTopBlock": "På dette nivået må du sette sammen alle blokkene i det hvite arbeidsområdet.", "Maze.helpRun": "<PERSON><PERSON><PERSON><PERSON> programmet for å se hva som skjer.", "Maze.helpReset": "Programmet ditt løste ikke labyrinten. Trykk 'Nullstill' og prøv igjen.", "Maze.helpRepeat": "<PERSON><PERSON> slutten av denne stien ved å bruke bare to blokker. Bruk 'repeter' for å kjøre en blokk mer enn én gang.", "Maze.helpCapacity": "Du har brukt opp alle blokkene for dette nivået. For å lage nye blokker, må du først slette en eksisterende blokk.", "Maze.helpRepeatMany": "Du kan plassere mer enn en blokk i en repeteringsblokk.", "Maze.helpSkins": "Velg din favorittspiller fra denne menyen.", "Maze.helpIf": "En 'if'-blokk vil bare gjøre noe dersom forholdene er sanne. Forsøk å svinge til venstre hvis det finnes en sti til venstre.", "Maze.helpMenu": "Klikk på %1 i 'if'-blokken for å endre tilstand.", "Maze.helpWallFollow": "Kan du løse denne kompliserte pyramiden? Forsøk å følge veggen på venstre side. Kun for avanserte programmerere.", "Bird.noWorm": "har ikke mark", "Bird.heading": "retning", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON>en for når fuglen ikke har fått marken.", "Bird.headingTooltip": "Flytt i retningen til den gitte vinkelen: 0 er høyre, 90 er rett opp, osv.", "Bird.positionTooltip": "x og y markerer fuglens posisjon. Når x = 0 er fuglen nær venstre kant, når x = 100 er den nær den høyre kanten. Når y = 0 er fuglen ved bunnen, mens y = 100 er den nær toppen.", "Bird.helpHeading": "<PERSON><PERSON> retningsvinkel<PERSON> for å få fuglen til å få marken og lande i redet sitt.", "Bird.helpHasWorm": "Bruk denne blokken for å gå i én retning om du har marken, eller en annen retning om du ikke har marken.", "Bird.helpX": "«x» er din nåværende horisontale posisjon. Bruk denne blokken for å gå i én retning om «x» er mindre en et nummer, eller en annen retning om «x» ikke er det.", "Bird.helpElse": "Klikk ikonet for å endre «if»-blokken.", "Bird.helpElseIf": "<PERSON>te nivået trenger både en «else if» og en «else»-blokk.", "Bird.helpAnd": "«and»-blokken er sann bare om begge inndataene er sanne.", "Bird.helpMutator": "<PERSON>a en «else»-blokk inn i «if»-blokken.", "Turtle.moveTooltip": "Flytter skilpadden fremover eller bakover med den angitte verdien.", "Turtle.moveForward": "flytt fremover med", "Turtle.moveBackward": "flytt bakover med", "Turtle.turnTooltip": "Sving skilpadden venstre eller høyre med angitt antall grader.", "Turtle.turnRight": "sving til høyre med", "Turtle.turnLeft": "sving til venstre med", "Turtle.widthTooltip": "<PERSON><PERSON> bredden på pennen.", "Turtle.setWidth": "sett bredden til", "Turtle.colourTooltip": "<PERSON><PERSON> fargen på pennen.", "Turtle.setColour": "sett fargen til", "Turtle.penTooltip": "<PERSON><PERSON> eller senker pennen, for å stoppe eller starte tegning.", "Turtle.penUp": "penn opp", "Turtle.penDown": "penn ned", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON><PERSON> skil<PERSON> (sirkel og pil) synlig eller usynlig.", "Turtle.hideTurtle": "skjul skilpadde", "Turtle.showTurtle": "vis skilpadde", "Turtle.printHelpUrl": "https://no.wikipedia.org/wiki/Trykking", "Turtle.printTooltip": "Skriver tekst i skilpaddens retning på dens plassering.", "Turtle.print": "skriv", "Turtle.fontHelpUrl": "https://no.wikipedia.org/wiki/Skrifttype", "Turtle.fontTooltip": "Set<PERSON> skrifttypen som brukes av skriveblokken.", "Turtle.font": "skrifttype", "Turtle.fontSize": "skriftstørrelse", "Turtle.fontNormal": "normal", "Turtle.fontBold": "fet", "Turtle.fontItalic": "kursiv", "Turtle.submitDisabled": "<PERSON><PERSON><PERSON><PERSON> programmet til det stopper. Etter det kan du levere tegningen din til galleriet.", "Turtle.galleryTooltip": "<PERSON><PERSON><PERSON> galler<PERSON> over tegni<PERSON>.", "Turtle.galleryMsg": "Se galleri", "Turtle.submitTooltip": "Send tegningen din til galleriet.", "Turtle.submitMsg": "Send til galleri", "Turtle.helpUseLoop": "Lø<PERSON>ningen din virker, men du kan gjøre det bedre.", "Turtle.helpUseLoop3": "Tegn formen med bare tre blokker.", "Turtle.helpUseLoop4": "<PERSON><PERSON> stjernen med bare fire blokker.", "Turtle.helpText1": "Lag et program som tegner et kvadrat.", "Turtle.helpText2": "<PERSON>re programmet til å tegne en femkant i stedet for en stjerne.", "Turtle.helpText3a": "Det er en ny blokk som lar deg endre fargen:", "Turtle.helpText3b": "Tegn en gul stjerne.", "Turtle.helpText4a": "Det er en ny blokk som lar deg løfte pennen av papiret når du flytter deg:", "Turtle.helpText4b": "Tegn en liten gul stjerne, og tegn en linje over den.", "Turtle.helpText5": "I stedet for én stjerne, kan du tegne fire stjerner i et firkantmønster?", "Turtle.helpText6": "Tegn tre gule stjerner og én hvit linje.", "Turtle.helpText7": "Tegn stjernene, og tegn så fire hvite linjer.", "Turtle.helpText8": "Å tegne 360 hvite linjer vil ligne på fullmånen.", "Turtle.helpText9": "Kan du legge til en svart sirkel så månen blir en halvmåne?", "Turtle.helpText10": "Tegn det du har lyst til. Du har et enormt antall nye blokker du kan utforske. Kos deg!", "Turtle.helpText10Reddit": "Bruk «Se galleri»-knappen for å se hva andre har tegnet. Om du tegner noe interessant, bruk «Send til galleri»-knappen for å publisere det.", "Turtle.helpToolbox": "Velg en kategori for å se blokkene.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "start x", "Movie.y1": "start y", "Movie.x2": "slutt x", "Movie.y2": "slutt y", "Movie.radius": "radius", "Movie.width": "bredde", "Movie.height": "<PERSON><PERSON><PERSON><PERSON>", "Movie.circleTooltip": "<PERSON><PERSON>r en sirkel ved den angitte lokasjonen med den angitte radiusen.", "Movie.circleDraw": "sir<PERSON>", "Movie.rectTooltip": "<PERSON><PERSON><PERSON> et rektangel ved den angitte lokasjonen med den angitte bredden og høyden.", "Movie.rectDraw": "rektang<PERSON>", "Movie.lineTooltip": "Tegner en linje fra ett punkt til et annet med den angitte bredden.", "Movie.lineDraw": "linje", "Movie.timeTooltip": "Returnerer nåværende tid i animasjonen (0–100).", "Movie.colourTooltip": "<PERSON><PERSON> fargen på pennen.", "Movie.setColour": "sett farge til", "Movie.submitDisabled": "Filmen din beveger seg ikke. Bruk blokker for å lage noe interessant. Så kan du sende filmen din til galleriet.", "Movie.galleryTooltip": "Å<PERSON>ne galleriet av filmer.", "Movie.galleryMsg": "Se galleri", "Movie.submitTooltip": "Send filmen din til galleriet.", "Movie.submitMsg": "Send til galleri", "Movie.helpLayer": "Flytt bakgrunnssirkelen til toppen av programmet ditt. Den vil da dukke opp bak personen.", "Movie.helpText1": "Bruk enkle former til å tegne denne personen.", "Movie.helpText2a": "Dette nivået er en film. Du vil at personens arm skal krysse skjermen. Trykk på avspillingsknappen for å se en forhåndsvisning.", "Movie.helpText2b": "Mens filmen spiller vil verdien til «time»-blokken telle fra 0 til 100. Siden du vil at «y»-posisjonen til armen skal starte på 0 og gå til 100 bør dette være enkelt.", "Movie.helpText3": "«time»-blokken teller fra 0 til 100. Men denne gangen vil du at «y»-posisjonen til den andre armen skal starte på 100 og gå til 0. Kan du finne en enkel matematisk formel som endrer retningen?", "Movie.helpText4": "Bruk det du har lært i tidligere nivåer for til å få kryssede bein.", "Movie.helpText5": "Den matematiske formelen for armen er komplisert. Her er svaret:", "Movie.helpText6": "Gi personen et par hender.", "Movie.helpText7": "Bruk «if»-blokken for å tegne et lite hode i første halvdel av filmen. Tegn så et stort hode i andre halvdel av filmen.", "Movie.helpText8": "Få beina til å krysses halvveis gjennom filmen.", "Movie.helpText9": "Tegn en ekspanderende sirkel bak personen.", "Movie.helpText10": "Lag en film med hva som helst. Du har et enormt antall nye blokker du kan utforske. Kos deg!", "Movie.helpText10Reddit": "Bruk «Se galleri»-knappen for å se filmer som andre har laget. Om du lager en interessant film, bruk «Send til galleri»-knappen for å publisere den.", "Music.playNoteTooltip": "Spiller én musikknote med den gitte varigheten og tonehøyden.", "Music.playNote": "spill %1 note %2", "Music.restTooltip": "Venter i den gitte varigheten.", "Music.restWholeTooltip": "Venter én hel note.", "Music.rest": "hviler %1", "Music.setInstrumentTooltip": "Bytter til det gitte instrumentet når etterfølgende noter spilles.", "Music.setInstrument": "sett instrument til %1", "Music.startTooltip": "<PERSON><PERSON><PERSON><PERSON> blokkene inni når knappen «Kjør program» klikkes.", "Music.start": "når %1 klikkes", "Music.pitchTooltip": "Én note (C4 er 7).", "Music.firstPart": "f<PERSON><PERSON><PERSON>", "Music.piano": "piano", "Music.trumpet": "trompet", "Music.banjo": "banjo", "Music.violin": "<PERSON><PERSON>", "Music.guitar": "gitar", "Music.flute": "<PERSON><PERSON><PERSON>", "Music.drum": "tromme", "Music.choir": "kor", "Music.submitDisabled": "<PERSON><PERSON><PERSON><PERSON> <PERSON>t til det stopper. Du kan sende musikken din til galleriet.", "Music.galleryTooltip": "Åpne musikkgalleriet.", "Music.galleryMsg": "Se galleri", "Music.submitTooltip": "Send musikken din til galleriet.", "Music.submitMsg": "Send til galleriet", "Music.helpUseFunctions": "Løsningen din virker, men du kan gjøre det bedre. Bruk funksjoner for å redusere mengden gjentatt kode.", "Music.helpUseInstruments": "Musikken vil høres bedre ut om du bruker et forskjellig instrument i hver startblokk.", "Music.helpText1": "Komponer de første fire notene fra «Fader Jakob».", "Music.helpText2a": "En «funksjon» lar deg gruppere blokker sammen, og så kjøre dem mer enn én gang.", "Music.helpText2b": "Opprett en funksjon for å spille de første fire notene i «Fader Jakob». <PERSON>j<PERSON><PERSON> den funksjonen to ganger. Ikke legg til nye noteblokker.", "Music.helpText3": "Opprett en annen funksjon for neste del av «Fader Jakob». Den siste noten er lengre.", "Music.helpText4": "Opprett en tredje funksjon for neste del av «Fader Jakob». de første fire notene er kortere.", "Music.helpText5": "<PERSON>før hele melodien til «Fader Jakob».", "Music.helpText6a": "Denne nye lokka lar deg bytte til et annet instrument.", "Music.helpText6b": "Spill melodien din med fiolin.", "Music.helpText7a": "<PERSON>ne nye blokka legger til en taus forsinkelse.", "Music.helpText7b": "Opprett en annen startblokk som har to forsinkelsesblokker, og så spiller «Fader Jakob».", "Music.helpText8": "<PERSON><PERSON> startblokk bør spille «Fader Jakob» to ganger.", "Music.helpText9": "Opprett fire startblokker som hver spiller «<PERSON><PERSON> Jakob» to ganger. Legg til riktig antall forsinkelsesblokker.", "Music.helpText10": "Komponer det du har lyst til. Du har et enormt antall nye blokker du kan utforske. Kos deg!", "Music.helpText10Reddit": "Bruk knappen «Se galleri» for å se hva andre har komponert. Om du komponerer noe interessant, bruk knappen «Send til galleri» for å publisere det.", "Pond.scanTooltip": "<PERSON><PERSON><PERSON> etter fiender. <PERSON><PERSON> en retning (0–360). Returnerer avstanden til nærmeste fiende i den retningen. Returnerer uendelighet om ingen fiender finnes.", "Pond.cannonTooltip": "Fyr av kanonen. <PERSON><PERSON> en retning (0–360) og en avstand (0–70).", "Pond.swimTooltip": "Svøm forover. <PERSON><PERSON> en retning (0–360).", "Pond.stopTooltip": "Slutt å svømme. Spilleren vil sakke ned til den stopper.", "Pond.healthTooltip": "Returnerer spillerens nåværende helse (0 er død, 100 er sunn).", "Pond.speedTooltip": "Returnerer spillerens nåværende fart (0 er stoppet, 100 er full fart).", "Pond.locXTooltip": "Returnerer spillerens X-koordinat (0 er venstre kant, 100 er høyre kant).", "Pond.locYTooltip": "Returnerer spillerens Y-koordinat (0 er bunnen, 100 er toppen).", "Pond.logTooltip": "Printer et tall til nettleserkonsollen.", "Pond.docsTooltip": "Viser språkdokumentasjonen.", "Pond.documentation": "Dokumentasjon", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Pendel", "Pond.scaredName": "Redd", "Pond.helpUseScan": "Løsningen din virker, men du kan gjøre det bedre. Bruk «scan» for å fortelle kanonen hvor langt den skal skyte.", "Pond.helpText1": "Bruk kommandoen «cannon» for å treffe målet. Den første parameteren er vinkelen, den andre er avstanden. Finn riktig kombinasjon.", "Pond.helpText2": "<PERSON><PERSON> målet må treffes mange ganger. Bruk en «while (true)»-sløyfe for å gjøre noe uendelig.", "Pond.helpText3a": "Denn motstanderen flytter seg fram og tilbake, og er vanskelig å treffe. «scan»-uttrykket returnerer den eksakte avstanden til motstanderen i den angitte retningen.", "Pond.helpText3b": "Denne avstanden er akkurat det «cannon»-kommandoen trenger for å skyte treffsikkert.", "Pond.helpText4": "<PERSON>ne motstanderen er for langt unna til å bruke kanonen (som har en grense på 70 meter). Bruk i stedet kommandoen «swim» for å svømme mot motstanderen og kræsje inn i den.", "Pond.helpText5": "Denne motstanderen er også for langt unna til å bruke kanonen. Men du er for svak til å overleve et kræsj. Svøm mot motstanderen mens din horisontale lokasjon er mindre enn 50. Bruk så «stop» og bruk kanonen.", "Pond.helpText6": "<PERSON>ne motstanderen vil flytte seg vekk når den blir truffet. Svøm mot den om den er for langt unna (70 meter).", "Gallery": "Galleri"}