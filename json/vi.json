{"@metadata": {"authors": ["Baonguyen21022003", "Dstream", "Leducthn", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Qneutron", "SierraNguyen", "Withoutaname"]}, "Games.name": "<PERSON><PERSON>", "Games.puzzle": "Đố vui", "Games.maze": "<PERSON> trận", "Games.bird": "<PERSON><PERSON>", "Games.turtle": "<PERSON><PERSON><PERSON>", "Games.movie": "<PERSON><PERSON>", "Games.music": "<PERSON><PERSON>", "Games.pondTutor": "Hướng dẫn chơi Ao hồ", "Games.pond": "<PERSON><PERSON> <PERSON>", "Games.linesOfCode1": "Bạn đã xử lý level này với 1 dòng của JavaScript:", "Games.linesOfCode2": "Bạn đã xử lý level này với %1 dòng của JavaScript:", "Games.nextLevel": "Bạn có sẵn sàng cho level %1 chưa?", "Games.finalLevel": "Bạn có sẵn sàng cho level tiếp theo chưa?", "Games.submitTitle": "Tên:", "Games.linkTooltip": "<PERSON><PERSON><PERSON> và lấy địa chỉ liên kết.", "Games.runTooltip": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>ng trình bạn viết.", "Games.runProgram": "<PERSON><PERSON><PERSON> ch<PERSON> trình", "Games.resetTooltip": "Dừng chương trình và thiết lập lại level.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON> g<PERSON>", "Games.catLogic": "Logic", "Games.catLoops": "Vòng lặp", "Games.catMath": "<PERSON><PERSON><PERSON> thức to<PERSON>", "Games.catText": "<PERSON><PERSON><PERSON>", "Games.catLists": "<PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON><PERSON>", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON>", "Games.httpRequestError": "<PERSON><PERSON><PERSON> động bị trục trặc, kh<PERSON><PERSON> thực hiện đượ<PERSON> yêu cầu của bạn.", "Games.linkAlert": "Chia sẻ chương trình của bạn với liên kết sau:\n\n %1", "Games.hashError": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chương trình đư<PERSON><PERSON> lưu ở '%1'.", "Games.xmlError": "Không mở được chương trình của bạn.  <PERSON><PERSON> thể nó nằm trong một phiên bản khác của <PERSON>ly?", "Games.submitted": "Cảm ơn phần mềm của bạn! <PERSON>ếu dàn \"khỉ viên\" đã qua huấn luyện của chúng tôi thích phần mềm này, phần mềm sẽ được xuất bản vào thư viện trong vài ngày thôi.", "Games.listVariable": "danh s<PERSON>ch", "Games.textVariable": "v<PERSON><PERSON> b<PERSON>n", "Games.breakLink": "Một khi bạn bắt đầu chỉnh sửa JavaScript, bạn không thể quay về chỉnh sửa các mảnh ghép. Bạn có đồng ý không?", "Games.blocks": "<PERSON><PERSON><PERSON>", "Games.congratulations": "<PERSON><PERSON>c mừng!", "Games.helpAbort": "Level này rất khó. Bạn có muốn bỏ qua và chuyển sang trò chơi tiếp theo? Bạn có thể quay lại level này sau.", "Index.clear": "<PERSON><PERSON><PERSON> cách gi<PERSON>i quyết của bạn?", "Index.subTitle": "<PERSON><PERSON><PERSON> ch<PERSON>i cho ngư<PERSON>i lập trình của ngày mai.", "Index.moreInfo": "Thông tin dành cho người giáo dục...", "Index.startOver": "Bạn có muốn bắt đầu lại từ đầu?", "Index.clearData": "<PERSON><PERSON><PERSON> dữ liệu", "Puzzle.animal1": "<PERSON> v<PERSON>", "Puzzle.animal1Trait1": "Lông vịt", "Puzzle.animal1Trait2": "Mỏ", "Puzzle.animal1HelpUrl": "https://vi.wikipedia.org/wiki/V%E1%BB%8Bt", "Puzzle.animal2": "Con mèo", "Puzzle.animal2Trait1": "<PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON><PERSON> m<PERSON>o", "Puzzle.animal2HelpUrl": "https://vi.wikipedia.org/wiki/M%C3%A8o", "Puzzle.animal3": "<PERSON> ong", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON> ong", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://vi.wikipedia.org/wiki/Ong", "Puzzle.animal4": "Ốc sên", "Puzzle.animal4Trait1": "Vỏ ốc", "Puzzle.animal4Trait2": "<PERSON>", "Puzzle.animal4HelpUrl": "https://vi.wikipedia.org/wiki/%E1%BB%90c", "Puzzle.picture": "hình <PERSON>nh:", "Puzzle.legs": "chân:", "Puzzle.legsChoose": "chọn...", "Puzzle.traits": "đặc điểm:", "Puzzle.error0": "Rất hoàn chỉnh!\nTất cả %1 mảnh đều ch<PERSON>h xác.", "Puzzle.error1": "Gần đúng rồi! Một mảnh còn chưa ch<PERSON>h xác.", "Puzzle.error2": "%1 mảnh còn chưa ch<PERSON>h xác.", "Puzzle.tryAgain": "<PERSON><PERSON><PERSON> mảnh đư<PERSON><PERSON> đánh dấu là không đúng.\n<PERSON><PERSON> lên!", "Puzzle.checkAnswers": "<PERSON><PERSON><PERSON> tra đ<PERSON>p <PERSON>n", "Puzzle.helpText": "Với mỗi con vật (màu xanh lá cây), hãy gắn vào hình ảnh đúng với mỗi con vật đó, chọn số chân của chúng, và gắn đặc điểm thích hợp của chúng.", "Maze.moveForward": "đi tới", "Maze.turnLeft": "quay qua trái", "Maze.turnRight": "quay qua phải", "Maze.doCode": "<PERSON><PERSON><PERSON>", "Maze.helpIfElse": "<PERSON><PERSON><PERSON> <PERSON>nếu-nếu không' sẽ thực hiện một trong hai nhóm lệnh đưa ra.", "Maze.pathAhead": "nếu có đường phía trước", "Maze.pathLeft": "nếu có đường bên trái", "Maze.pathRight": "nếu có đường bên ph<PERSON>i", "Maze.repeatUntil": "lặp lại cho đến", "Maze.moveForwardTooltip": "<PERSON><PERSON>n <PERSON> tới một bước.", "Maze.turnTooltip": "Quay Pegman qua bên trái hoặc bên phải 90 độ.", "Maze.ifTooltip": "<PERSON><PERSON><PERSON> có đường ở hướng đấy, hã<PERSON> thực hiện các lệnh đưa ra.", "Maze.ifelseTooltip": "<PERSON><PERSON><PERSON> có đường ở hướng đấy, h<PERSON><PERSON> thực hiện các lệnh thứ nhất. <PERSON><PERSON><PERSON>, thực hiện các lệnh thứ hai.", "Maze.whileTooltip": "<PERSON><PERSON><PERSON><PERSON> hiện các lệnh đư<PERSON><PERSON> bao gồm cho đến khi đến đích", "Maze.capacity0": "Bạn còn %0 mảnh.", "Maze.capacity1": "Bạn còn %1 mảnh.", "Maze.capacity2": "Bạn còn %2 mảnh.", "Maze.runTooltip": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON>i chơi làm theo lệnh trong các mảnh.", "Maze.resetTooltip": "<PERSON><PERSON>a người chơi trở lại vị trí khởi đầu của mê cung.", "Maze.helpStack": "Chồng hai mảnh 'đi tới' lên nhau để giúp tớ đến đích.", "Maze.helpOneTopBlock": "Ở vòng nà<PERSON>, bạn cần gắn chồng các mảnh lệnh lên nhau trong khoảng trắng (còn được gọi là sân chơi) để tạo chuỗi mệnh lệnh cần thiết.", "Maze.helpRun": "<PERSON><PERSON><PERSON> chương trình của bạn để xem điều gì sẽ xảy ra.", "Maze.helpReset": "Chương trình của bạn chưa giải mê cung.  Nhấn 'Trở lại' và thử lại.", "Maze.helpRepeat": "Đến cuối đường chỉ bằng hai mảnh.  Sử dụng 'lặp lại' để chạy một mảnh hơn một lần.", "Maze.helpCapacity": "Bạn đã sử dụng hết tất mảnh trong cấp này.  <PERSON><PERSON> tạo mảnh mới, tr<PERSON><PERSON><PERSON> tiên bạn cần xóa một mảnh hiện tại.", "Maze.helpRepeatMany": "<PERSON>ạn có thể đặt nhiều mảnh vào trong một mảnh 'lặp lại'.", "Maze.helpSkins": "L<PERSON>a chọn nhân vật yêu thích của bạn từ trình đơn này.", "Maze.helpIf": "<PERSON><PERSON><PERSON> 'nếu [điều kiện]' nhìn vào điều kiện rồi chỉ thực hiện lệnh nếu có điều kiện ấy. Hãy thử rẽ trái nếu có đường bên trái.", "Maze.helpMenu": "Ấn vào %1 trong mảnh 'nếu' để thay đổi hình thể của nó.", "Maze.helpWallFollow": "Bạn có thể giải cái ma trận rắc rối này? Gợi ý: Thử đi sát theo một bức tườ<PERSON>, y như bạn đang giữ một tay theo suốt bức tường <PERSON>y, dù nó rẻ phải hay trái.", "Bird.noWorm": "không có sâu", "Bird.heading": "bay đi", "Bird.noWormTooltip": "<PERSON><PERSON><PERSON> chim chưa có sâu.", "Bird.headingTooltip": "<PERSON> chuyển theo hướng của góc đượ<PERSON> chọn: 0 là sang phải, 90 là đi thẳng đứng lên.", "Bird.positionTooltip": "x và y đánh dấu vị trí của chim. Khi x = 0 chim gần với mép trái, khi x = 100 nó gần với mép phải. Khi y = 0 chim ở đáy trang, khi y = 100 nó ở đầu trang.", "Bird.helpHeading": "Thay đổi hướng bay để chim lấy đ<PERSON><PERSON><PERSON> sâu và tha về tổ của nó.", "Bird.helpHasWorm": "Sử dụng mảnh này để chim có thể bay theo một hướng nếu đã lấy đư<PERSON><PERSON> sâu, còn nếu không thì bay theo hướng khác.", "Bird.helpX": "'x' là vị trí nằm ngang hiện tại của con chim. Sử dụng mảnh này để con chim của bạn bay theo một hướng nếu 'x' nhỏ hơn một số nào đó, hoặc nếu không thì bay theo hướng khác.", "Bird.helpElse": "<PERSON><PERSON><PERSON><PERSON> vào kí tự này để sửa đổi mảnh 'nếu'.", "Bird.helpElseIf": "<PERSON><PERSON><PERSON> độ này cần cả hai mảnh 'nếu không nếu' và 'nếu không'.", "Bird.helpAnd": "<PERSON><PERSON><PERSON> 'và' chỉ đúng nếu cả hai điều kiện nhập vào đều đúng.", "Bird.helpMutator": "<PERSON><PERSON><PERSON> mảnh 'nếu không' cho vào mảnh 'nếu'.", "Turtle.moveTooltip": "<PERSON> chuyển con rùa tới hoặc lui một đoạn tùy ý.", "Turtle.moveForward": "đi tới", "Turtle.moveBackward": "đi lui", "Turtle.turnTooltip": "Quay con rùa qua tay trái hoặc tay phải một số độ tùy ý.", "Turtle.turnRight": "quay qua tay phải", "Turtle.turnLeft": "quay qua tay trái", "Turtle.widthTooltip": "Thay đổi độ rộng của bút vẽ.", "Turtle.setWidth": "đặt chiều rộng", "Turtle.colourTooltip": "<PERSON>hay màu của bút vẽ.", "Turtle.setColour": "g<PERSON>", "Turtle.penTooltip": "<PERSON><PERSON><PERSON><PERSON> bút vẽ lên hoặc đặt xuống, để bắt đầu hoặc ngưng nét vẽ.", "Turtle.penUp": "b<PERSON>t vẽ nhấc lên", "Turtle.penDown": "bút vẽ đặt xuống", "Turtle.turtleVisibilityTooltip": "<PERSON><PERSON><PERSON> cho con rùa (vòng tròn và mũi tên) hiện hình hoặc vô hình.", "Turtle.hideTurtle": "<PERSON>", "Turtle.showTurtle": "show <PERSON><PERSON><PERSON>", "Turtle.printTooltip": "In văn bản theo h<PERSON>ớng và vị trí của con rùa.", "Turtle.print": "In", "Turtle.fontTooltip": "<PERSON><PERSON> phông chữ được sử dụng bởi mảnh in.", "Turtle.font": "phông chữ", "Turtle.fontSize": "<PERSON><PERSON><PERSON> th<PERSON> chữ", "Turtle.fontNormal": "th<PERSON><PERSON><PERSON>", "Turtle.fontBold": "<PERSON><PERSON><PERSON>", "Turtle.fontItalic": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.submitDisabled": "Chạy chương trình của bạn cho đến khi nó dừng. Sau đó bạn có thể gửi bản vẽ của bạn vào trong phòng trưng bày.", "Turtle.galleryTooltip": "Mở phòng trưng bày của các bản vẽ.", "Turtle.galleryMsg": "<PERSON><PERSON> trưng bày", "Turtle.submitTooltip": "<PERSON><PERSON><PERSON> bản vẽ của bạn đến phòng trưng bày.", "Turtle.submitMsg": "<PERSON><PERSON><PERSON> đến phòng trưng bày", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON> gi<PERSON>i quyết của bạn hoạt động, nh<PERSON><PERSON> bạn có thể thử cách khác hiệu quả hơn không?", "Turtle.helpUseLoop3": "<PERSON><PERSON><PERSON> vẽ theo hình dạng này chỉ với ba mảnh.", "Turtle.helpUseLoop4": "H<PERSON>y vẽ ngôi sao chỉ với bốn mảnh.", "Turtle.helpText1": "<PERSON><PERSON><PERSON> nên một chương trình có thể vẽ một hình vuông.", "Turtle.helpText2": "Thay đổi chương trình của bạn để vẽ một hình ngũ giác thay vì một hình vuông.", "Turtle.helpText3a": "<PERSON><PERSON>t mảnh mới có thể cho phép bạn thay đổi màu:", "Turtle.helpText3b": "Hãy vẽ một ngôi sao vàng.", "Turtle.helpText4a": "Một mảnh mới có thể cho phép bạn nhấc bút khỏi trang giấy khi bạn dịch chuyển:", "Turtle.helpText4b": "H<PERSON>y vẽ một ngôi sao nhỏ màu vàng, sau đó vẽ một đường thẳng ở trên nó.", "Turtle.helpText5": "Thay vì một ngôi sao, bạn có thể vẽ bốn ngôi sao được xếp ở bốn góc của một hình vuông không?", "Turtle.helpText6": "<PERSON><PERSON>y vẽ ba ngôi sao màu vàng, và một đường thẳng màu trắng.", "Turtle.helpText7": "Hãy vẽ những ngôi sao và vẽ bốn đường thẳng màu trắng.", "Turtle.helpText8": "Vẽ 360 đường thẳng màu trắng sẽ trông giống như mặt trăng tròn.", "Turtle.helpText9": "Bạn có thể thêm một hình tròn màu đen để cho trăng tròn thành  trăng khuyết không?", "Turtle.helpText10": "H<PERSON>y vẽ bất cứ thứ gì bạn muốn. Bạn có thể khám phá với một số lượng lớn của những mảnh mới. Chúc bạn vui vẻ!", "Turtle.helpText10Reddit": "<PERSON><PERSON> dụng \"Xem <PERSON>òng trưng bày\" nhấn để xem những người khác đã vẽ. Nếu bạn vẽ nên thứ gì đặc sắc, h<PERSON><PERSON> dùng \"G<PERSON>i đến Phòng trưng bày\" nhấn để trưng bày nó.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON> chọn một chuyên mục để xem những mảnh.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "b<PERSON><PERSON> đ<PERSON>u x", "Movie.y1": "b<PERSON><PERSON> đ<PERSON> y", "Movie.x2": "kết thúc x", "Movie.y2": "kết th<PERSON>c y", "Movie.radius": "b<PERSON>", "Movie.width": "ch<PERSON><PERSON><PERSON> ngang", "Movie.height": "ch<PERSON><PERSON><PERSON> cao", "Movie.circleTooltip": "Hãy vẽ một hình tròn tại một vị trí cụ thể và với một bán k<PERSON>h cụ thể.", "Movie.circleDraw": "hình tròn", "Movie.rectTooltip": "Hãy vẽ một hình chữ nhật tại một vị trí cụ thể với chiều ngang và chiều cao cụ thể.", "Movie.rectDraw": "h<PERSON>nh chữ nhật", "Movie.lineTooltip": "Hãy vẽ một đường thẳng từ một điểm đến điểm còn lại với một chiều ngang cụ thể.", "Movie.lineDraw": "dòng", "Movie.timeTooltip": "<PERSON><PERSON><PERSON> tr<PERSON> thời điểm hiện tại ở phim (0-100).", "Movie.colourTooltip": "<PERSON>hay màu của bút vẽ.", "Movie.setColour": "g<PERSON>", "Movie.submitDisabled": "<PERSON><PERSON> của bạn không dịch chuyển. Sử dụng các mảnh để tạo vật đặc sắc. <PERSON>u đó bạn có thể gửi phim của bạn đến phòng trưng bày.", "Movie.galleryTooltip": "Mở phòng trưng bày của các phim.", "Movie.galleryMsg": "<PERSON><PERSON> trưng bày", "Movie.submitTooltip": "<PERSON><PERSON><PERSON> phim của bạn đến phòng trưng bày.", "Movie.submitMsg": "<PERSON><PERSON><PERSON> đến phòng trưng bày", "Movie.helpLayer": "<PERSON> chuyển nền hình tròn lên phần đầu của chương trình. Sau đó nó sẽ xuất hiện phía sau nhân vật.", "Movie.helpText1": "<PERSON><PERSON> dụng các khối hình đơn giản để vẽ người này.", "Movie.helpText2a": "<PERSON><PERSON><PERSON> độ này là một đoạn phim. Bạn muốn cánh tay của nhân vật di chuyển qua màn hình. Nhấn nút chơi để xem chương trình chạy.", "Movie.helpText2b": "<PERSON><PERSON> đoạn phim đư<PERSON><PERSON>, g<PERSON><PERSON> trị của mảnh 'thời gian' đếm từ 0 đến 100.\nBạn muốn cho vị trí nằm ngang của cánh tay bắt đầu tại 0 và đi đến 100.", "Movie.helpText3": "<PERSON><PERSON><PERSON> <PERSON>thời gian' đ<PERSON><PERSON> từ 0 đến 100. <PERSON><PERSON><PERSON><PERSON> bây giờ bạn muốn vị trí nằm ngang của cánh tay bắt đầu tại 100 và đi đến 0. <PERSON><PERSON><PERSON> hãy tìm ra công thức toán đơn giản để đổi hướng đếm.", "Movie.helpText4": "Sử dụng những gì bạn đã học trong những cấp độ trước để làm sao cho những cái chân bắt chéo nhau.", "Movie.helpText5": "<PERSON>é<PERSON> to<PERSON> cho cánh tay khá phức tạp. <PERSON><PERSON><PERSON> là câu trả lời:", "Movie.helpText6": "<PERSON><PERSON><PERSON> cho nhân vật hai bàn tay.", "Movie.helpText7": "Sử dụng mảnh 'if' để vẽ một cái đầu nhỏ cho nửa đoạn đầu trình chiếu của phim. Sau đó vẽ một cái đầu lớn cho nửa đoạn sau của phim.", "Movie.helpText8": "<PERSON><PERSON><PERSON> cho những cái chân thay đổi hướng ở giữa chặng của đoạn phim.", "Movie.helpText9": "Vẽ một vòng tròn mở rộng đằng sau nhân vật.", "Movie.helpText10": "Tạo ra phim về mọi thứ bạn muốn. Bạn có thể khám phá cùng với một số lượng lớn mảnh mới. Chúc bạn vui vẻ.", "Movie.helpText10Reddit": "<PERSON><PERSON> dụng \"Xem Phòng trưng bày\" nhấn để xem những người khác đã thiết kế. Nếu bạn vẽ nên đoạn phim đặc sắc, h<PERSON><PERSON> dùng \"Gửi đến Phòng trưng bày\" nhấn để trưng bày nó.", "Music.playNoteTooltip": "<PERSON><PERSON>i một nốt nhạc trong một khoảng thời gian và cao độ nhất định.", "Music.playNote": "Chơi %1 nốt %2", "Music.restTooltip": "Chờ trong một kho<PERSON>ng thời gian cụ thể.", "Music.restWholeTooltip": "<PERSON><PERSON> cho một nốt tròn.", "Music.rest": "Nghỉ %1", "Music.setInstrumentTooltip": "<PERSON><PERSON> đ<PERSON> sang một nhạc cụ cụ thể khi chơi những nốt tiếp theo.", "Music.setInstrument": "Chọn n<PERSON> cụ %1", "Music.startTooltip": "<PERSON><PERSON>y nh<PERSON><PERSON> mảnh gh<PERSON>p khi 'Run Program' đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>.", "Music.start": "khi %1 đ<PERSON><PERSON><PERSON> chọn", "Music.pitchTooltip": "<PERSON><PERSON><PERSON> (C4 là 7).", "Music.firstPart": "ph<PERSON>n đầu tiên", "Music.piano": "piano", "Music.trumpet": "k<PERSON>n", "Music.banjo": "b<PERSON><PERSON> c<PERSON>m", "Music.violin": "v<PERSON>", "Music.guitar": "ghi-ta", "Music.flute": "sáo", "Music.drum": "tr<PERSON><PERSON>", "Music.choir": "<PERSON><PERSON><PERSON>", "Music.submitDisabled": "Chạy chương trình của bạn cho đến khi nó dừng. <PERSON>u đó bạn có thể gửi bản nhạc của bạn vào phòng trưng bày.", "Music.galleryTooltip": "Mở phòng trưng bày của các bản nh<PERSON>c.", "Music.galleryMsg": "<PERSON><PERSON> trưng bày", "Music.submitTooltip": "<PERSON><PERSON><PERSON> bản nhạc của bạn đến phòng trưng bày.", "Music.submitMsg": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> viện", "Music.helpUseFunctions": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> tố<PERSON>, nhưng vẫn có thể được cải thiện. Sử dụng các hàm để giảm số lượng các phần lặp lại.", "Music.helpUseInstruments": "<PERSON>ản nhạc sẽ trở nên tốt hơn nếu bạn sử dụng một nhạc cụ khác cho phần khởi đầu của mỗi khối ghép.", "Music.helpText1": "<PERSON><PERSON><PERSON><PERSON> soạn bốn nốt đầu tiên của \"K<PERSON>a con bướm vàng\".", "Music.helpText2a": "Một 'function' cho phép bạn nhóm các mảnh ghép lại với nhau, sau đó chạy các mảnh đó nhiều lần.", "Music.helpText2b": "Tạo một hàm để chơi bốn nốt đầu của \"<PERSON><PERSON>a con bướm vàng\". Ch<PERSON>y hàm vừa tạo hai lần. <PERSON><PERSON><PERSON> thêm bất cứ khối nốt nào.", "Music.helpText3": "<PERSON><PERSON><PERSON> hàm thứ hai cho phần tiếp theo củ<PERSON> \"<PERSON><PERSON>a con bướm vàng\". <PERSON><PERSON>t cuối cùng dài hơn.", "Music.helpText4": "T<PERSON><PERSON> một hàm thứ ba cho phần tiếp theo củ<PERSON> \"<PERSON><PERSON>a con bướm vàng\". B<PERSON>n nốt đầu tiên ngắn hơn.", "Music.helpText5": "Hoàn chỉnh toàn bộ âm điệu của \"Kìa con bướm vàng\".", "Music.helpText6a": "<PERSON><PERSON><PERSON><PERSON> ghép này cho phép bạn thay đổi sang một nhạc cụ khác.", "Music.helpText6b": "<PERSON><PERSON><PERSON> bản nh<PERSON>c của bạn với một cây vi-ô-lông.", "Music.helpText7a": "<PERSON><PERSON><PERSON><PERSON> gh<PERSON>p này tạo ra một đoạn im lặng.", "Music.helpText7b": "<PERSON><PERSON><PERSON> kh<PERSON>i bắt đầu thứ hai bao gồm hai mảnh tạm dừng, sau đ<PERSON> chơ<PERSON> \"<PERSON><PERSON><PERSON> con bướm vàng\".", "Music.helpText8": "Mỗi mảnh ghép khởi đầu nên chơi \"<PERSON><PERSON><PERSON> con bướm vàng\" hai lần.", "Music.helpText9": "<PERSON><PERSON><PERSON> bốn mảnh khởi đầu, mỗi mảnh chơi \"<PERSON><PERSON><PERSON> con bướm vàng\" hai lần. Th<PERSON><PERSON> ch<PERSON>h xác số mảnh tạm dừng.", "Music.helpText10": "<PERSON><PERSON><PERSON> tác bất cứ thứ gì bạn muốn. Bạn có thể khám phá với một số lượng lớn của những mảnh ghép mới. <PERSON><PERSON>c bạn vui vẻ!", "Music.helpText10Reddit": "<PERSON><PERSON> dụng nút \"<PERSON>em <PERSON>ộ sưu tập\" để xem tác phẩm của những người đã sáng tác. Nếu bạn đã sáng tác nên một thứ thú vị, h<PERSON><PERSON> nh<PERSON> \"<PERSON><PERSON><PERSON> đến Bộ sưu tập\" để công bố nó.", "Pond.scanTooltip": "<PERSON><PERSON><PERSON><PERSON> dò địch thủ. <PERSON><PERSON><PERSON> hướ<PERSON> (0-360). <PERSON><PERSON><PERSON> lại khoảng cách đến địch thủ gần nhất theo hướng đó. <PERSON><PERSON><PERSON> lại vô tận nếu không có địch thủ nào bị ngắm.", "Pond.cannonTooltip": "Bắn súng. <PERSON><PERSON><PERSON> (0-360) và phạm vi (0-70).", "Pond.swimTooltip": "Bơi về phía trước. <PERSON><PERSON><PERSON> (0-360).", "Pond.stopTooltip": "<PERSON><PERSON><PERSON> bơi. <PERSON><PERSON><PERSON><PERSON> chơi sẽ chậm dần đến khi dừng hẳn.", "Pond.healthTooltip": "<PERSON><PERSON><PERSON> trả năng lượng hiện tại của người chơi ( 0 là chết, 100 là khỏe mạnh).", "Pond.speedTooltip": "Tr<PERSON> về tốc độ hiện tại của trình phát (0 là dừng lại, 100 là tốc độ cao nhất).", "Pond.locXTooltip": "Tr<PERSON> về tọa độ X của trình phát (0 là cạnh bên trái, 100 là cạnh bên phải).", "Pond.locYTooltip": "Tr<PERSON> về tọa độ Y của trình phát (0 là cạnh dư<PERSON>i cùng, 100 là cạnh trên cùng).", "Pond.logTooltip": "In một số vào bảng điều khiển của trình duyệt của bạn.", "Pond.docsTooltip": "<PERSON><PERSON><PERSON> thị tài liệu ngôn ngữ.", "Pond.documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>i", "Pond.targetName": "<PERSON><PERSON><PERSON> ti<PERSON>", "Pond.pendulumName": "<PERSON><PERSON><PERSON> lắc đồng hồ", "Pond.scaredName": "<PERSON><PERSON>i", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>p của bạn hoạt động, nh<PERSON><PERSON> bạn có thể làm tốt hơn. Sử dụng 'scan' để khẩu pháo biết đ<PERSON><PERSON><PERSON> kho<PERSON>ng cách để bắn.", "Pond.helpText1": "Sử dụng lệnh 'cannon' đ<PERSON> đánh trúng mục tiêu. Tham số đầu tiên là góc, tham số thứ hai là phạm vi. T<PERSON>m sự kết hợp phù hợp.", "Pond.helpText2": "<PERSON><PERSON><PERSON> tiêu này cần bị đánh trúng nhiều lần. Sử dụng vòng lặp 'while (true)' để thực hiện điều gì đó vô thời hạn.", "Pond.helpText3a": "<PERSON><PERSON><PERSON> thủ này di chuyển qua lại, khi<PERSON><PERSON> việc đánh trúng trở nên khó khăn hơn. 'Scan' tr<PERSON> về phạm vi chính xác của đối thủ trong một hướng nhất định.", "Pond.helpText3b": "<PERSON><PERSON><PERSON> ch<PERSON>h là phạm vi lệnh 'cannon' cần để bắn một cách chính xác.", "Pond.helpText4": "<PERSON><PERSON>i thủ này quá xa để sử dụng khẩu pháo (có giới hạn 70 mét). <PERSON><PERSON> và<PERSON> đó, sử dụng lệnh 'swim' để bắt đầu bơi về phía đối thủ và đâm vào nó.", "Pond.helpText5": "<PERSON><PERSON>i thủ này cũng quá xa để sử dụng khẩu pháo. Nhưng bạn quá yếu để tồn tại một vụ va chạm. Bơi về phía đối thủ trong khi vị trí nằm ngang của bạn nhỏ hơn 50. <PERSON><PERSON> đ<PERSON> 'stop' và sử dụng khẩu pháo.", "Pond.helpText6": "<PERSON><PERSON><PERSON> thủ này sẽ di chuyển đi khi nó bị bắn trúng. Bơi về phía nó nếu nó nằm ngoài phạm vi (70 mét).", "Gallery": "Phòng trưng bày"}