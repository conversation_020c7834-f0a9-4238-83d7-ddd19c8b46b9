{"@metadata": {"lastupdated": "2022-09-06 03:50:28.039245", "locale": "en", "messagedocumentation": "qqq"}, "Games.name": "Blockly Games", "Games.puzzle": "Puzzle", "Games.maze": "<PERSON><PERSON>", "Games.bird": "<PERSON>", "Games.turtle": "Turtle", "Games.movie": "Movie", "Games.music": "Music", "Games.pondTutor": "Pond Tutor", "Games.pond": "Pond", "Games.linesOfCode1": "You solved this level with 1 line of JavaScript:", "Games.linesOfCode2": "You solved this level with %1 lines of JavaScript:", "Games.nextLevel": "Are you ready for level %1?", "Games.finalLevel": "Are you ready for the next challenge?", "Games.submitTitle": "Title:", "Games.linkTooltip": "Save and link to blocks.", "Games.runTooltip": "Run the program you wrote.", "Games.runProgram": "Run Program", "Games.resetTooltip": "Stop the program and reset the level.", "Games.resetProgram": "Reset", "Games.help": "Help", "Games.catLogic": "Logic", "Games.catLoops": "Loops", "Games.catMath": "Math", "Games.catText": "Text", "Games.catLists": "Lists", "Games.catColour": "Colour", "Games.catVariables": "Variables", "Games.catProcedures": "Functions", "Games.httpRequestError": "There was a problem with the request.", "Games.linkAlert": "Share your blocks with this link:\n\n%1", "Games.hashError": "Sorry, '%1' doesn't correspond with any saved program.", "Games.xmlError": "Could not load your saved file. Perhaps it was created with a different version of <PERSON><PERSON>?", "Games.submitted": "Thank you for this program!  If our staff of trained monkeys like it, they will publish it to the gallery within a couple of days.", "Games.listVariable": "list", "Games.textVariable": "text", "Games.breakLink": "Once you start editing JavaScript, you can't go back to editing blocks. Is this OK?", "Games.blocks": "Blocks", "Games.congratulations": "Congratulations!", "Games.helpAbort": "This level is extremely difficult. Would you like to skip it and go onto the next game? You can always come back later.", "Index.clear": "Delete all your solutions?", "Index.subTitle": "Games for tomorrow's programmers.", "Index.moreInfo": "Info for educators...", "Index.startOver": "Want to start over?", "Index.clearData": "Clear data", "Puzzle.animal1": "<PERSON>", "Puzzle.animal1Trait1": "Feathers", "Puzzle.animal1Trait2": "Beak", "Puzzle.animal1HelpUrl": "https://en.wikipedia.org/wiki/Duck", "Puzzle.animal2": "Cat", "Puzzle.animal2Trait1": "Whiskers", "Puzzle.animal2Trait2": "Fur", "Puzzle.animal2HelpUrl": "https://en.wikipedia.org/wiki/Cat", "Puzzle.animal3": "Bee", "Puzzle.animal3Trait1": "Honey", "Puzzle.animal3Trait2": "<PERSON><PERSON>", "Puzzle.animal3HelpUrl": "https://en.wikipedia.org/wiki/Bee", "Puzzle.animal4": "Snail", "Puzzle.animal4Trait1": "Shell", "Puzzle.animal4Trait2": "Slime", "Puzzle.animal4HelpUrl": "https://en.wikipedia.org/wiki/Snail", "Puzzle.picture": "picture:", "Puzzle.legs": "legs:", "Puzzle.legsChoose": "choose...", "Puzzle.traits": "traits:", "Puzzle.error0": "Perfect!\nAll %1 blocks are correct.", "Puzzle.error1": "Almost! One block is incorrect.", "Puzzle.error2": "%1 blocks are incorrect.", "Puzzle.tryAgain": "The highlighted block is not correct.\nKeep trying.", "Puzzle.checkAnswers": "Check Answers", "Puzzle.helpText": "For each animal (green), attach its picture, choose its number of legs, and make a stack of its traits.", "Maze.moveForward": "move forward", "Maze.turnLeft": "turn left", "Maze.turnRight": "turn right", "Maze.doCode": "do", "Maze.helpIfElse": "If-else blocks will do one thing or the other.", "Maze.pathAhead": "if path ahead", "Maze.pathLeft": "if path to the left", "Maze.pathRight": "if path to the right", "Maze.repeatUntil": "repeat until", "Maze.moveForwardTooltip": "Moves the player forward one space.", "Maze.turnTooltip": "Turns the player left or right by 90 degrees.", "Maze.ifTooltip": "If there is a path in the specified direction, then do some actions.", "Maze.ifelseTooltip": "If there is a path in the specified direction, then do the first block of actions. Otherwise, do the second block of actions.", "Maze.whileTooltip": "Repeat the enclosed actions until finish point is reached.", "Maze.capacity0": "You have %0 blocks left.", "Maze.capacity1": "You have %1 block left.", "Maze.capacity2": "You have %2 blocks left.", "Maze.runTooltip": "Makes the player do what the blocks say.", "Maze.resetTooltip": "Put the player back at the start of the maze.", "Maze.helpStack": "Stack a couple of 'move forward' blocks together to help me reach the goal.", "Maze.helpOneTopBlock": "On this level, you need to stack together all of the blocks in the white workspace.", "Maze.helpRun": "Run your program to see what happens.", "Maze.helpReset": "Your program didn't solve the maze. Press 'Reset' and try again.", "Maze.helpRepeat": "Reach the end of this path using only two blocks. Use 'repeat' to run a block more than once.", "Maze.helpCapacity": "You have used up all the blocks for this level. To create a new block, you first need to delete an existing block.", "Maze.helpRepeatMany": "You can fit more than one block inside a 'repeat' block.", "Maze.helpSkins": "Choose your favourite player from this menu.", "Maze.helpIf": "An 'if' block will do something only if the condition is true. Try turning left if there is a path to the left.", "Maze.helpMenu": "Click on %1 in the 'if' block to change its condition.", "Maze.helpWallFollow": "Can you solve this complicated maze? Try following the left-hand wall. Advanced programmers only!", "Bird.noWorm": "does not have worm", "Bird.heading": "heading", "Bird.noWormTooltip": "The condition when the bird has not gotten the worm.", "Bird.headingTooltip": "Move in the direction of the given angle: 0 is to the right, 90 is straight up, etc.", "Bird.positionTooltip": "x and y mark the bird's position. When x = 0 the bird is near the left edge, when x = 100 it's near the right edge. When y = 0 the bird is at the bottom, when y = 100 it's at the top.", "Bird.helpHeading": "Change the heading angle to make the bird get the worm and land in her nest.", "Bird.helpHasWorm": "Use this block to go in one heading if you have the worm, or a different heading if you don't have the worm.", "Bird.helpX": "'x' is your current horizontal position. Use this block to go in one heading if 'x' is less than a number, or a different heading otherwise.", "Bird.helpElse": "Click the icon to modify the 'if' block.", "Bird.helpElseIf": "This level needs both an 'else if' and an 'else' block.", "Bird.helpAnd": "The 'and' block is true only if both its inputs are true.", "Bird.helpMutator": "Drag an 'else' block into the 'if' block.", "Turtle.moveTooltip": "Moves the turtle forward or backward by the specified amount.", "Turtle.moveForward": "move forward by", "Turtle.moveBackward": "move backward by", "Turtle.turnTooltip": "Turns the turtle left or right by the specified number of degrees.", "Turtle.turnRight": "turn right by", "Turtle.turnLeft": "turn left by", "Turtle.widthTooltip": "Changes the width of the pen.", "Turtle.setWidth": "set width to", "Turtle.colourTooltip": "Changes the colour of the pen.", "Turtle.setColour": "set colour to", "Turtle.penTooltip": "Lifts or lowers the pen, to stop or start drawing.", "Turtle.penUp": "pen up", "Turtle.penDown": "pen down", "Turtle.turtleVisibilityTooltip": "Makes the turtle (circle and arrow) visible or invisible.", "Turtle.hideTurtle": "hide turtle", "Turtle.showTurtle": "show turtle", "Turtle.printHelpUrl": "https://en.wikipedia.org/wiki/Printing", "Turtle.printTooltip": "Draws text in the turtle's direction at its location.", "Turtle.print": "print", "Turtle.fontHelpUrl": "https://en.wikipedia.org/wiki/Font", "Turtle.fontTooltip": "Sets the font used by the print block.", "Turtle.font": "font", "Turtle.fontSize": "font size", "Turtle.fontNormal": "normal", "Turtle.fontBold": "bold", "Turtle.fontItalic": "italic", "Turtle.submitDisabled": "Run your program until it stops. Then you may submit your drawing to the gallery.", "Turtle.galleryTooltip": "Open the gallery of drawings.", "Turtle.galleryMsg": "See Gallery", "Turtle.submitTooltip": "Submit your drawing to the gallery.", "Turtle.submitMsg": "Submit to Gallery", "Turtle.helpUseLoop": "Your solution works, but you can do better.", "Turtle.helpUseLoop3": "Draw the shape with just three blocks.", "Turtle.helpUseLoop4": "Draw the star with just four blocks.", "Turtle.helpText1": "Create a program that draws a square.", "Turtle.helpText2": "Change your program to draw a pentagon instead of a square.", "Turtle.helpText3a": "There's a new block that allows you to change the colour:", "Turtle.helpText3b": "Draw a yellow star.", "Turtle.helpText4a": "There's a new block that allows you to lift your pen off the paper when you move:", "Turtle.helpText4b": "Draw a small yellow star, then draw a line above it.", "Turtle.helpText5": "Instead of one star, can you draw four stars arranged in a square?", "Turtle.helpText6": "Draw three yellow stars, and one white line.", "Turtle.helpText7": "Draw the stars, then draw four white lines.", "Turtle.helpText8": "Drawing 360 white lines will look like the full moon.", "Turtle.helpText9": "Can you add a black circle so that the moon becomes a crescent?", "Turtle.helpText10": "Draw anything you want. You've got a huge number of new blocks you can explore. Have fun!", "Turtle.helpText10Reddit": "Use the 'See Gallery' button to see what other people have drawn. If you draw something interesting, use the 'Submit to Gallery' button to publish it.", "Turtle.helpToolbox": "Choose a category to see the blocks.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "start x", "Movie.y1": "start y", "Movie.x2": "end x", "Movie.y2": "end y", "Movie.radius": "radius", "Movie.width": "width", "Movie.height": "height", "Movie.circleTooltip": "Draws a circle at the specified location and with the specified radius.", "Movie.circleDraw": "circle", "Movie.rectTooltip": "Draws a rectangle at the specified location and with the specified width and height.", "Movie.rectDraw": "rectangle", "Movie.lineTooltip": "Draws a line from one point to another with the specified width.", "Movie.lineDraw": "line", "Movie.timeTooltip": "Returns the current time in the animation (0-100).", "Movie.colourTooltip": "Changes the colour of the pen.", "Movie.setColour": "set colour to", "Movie.submitDisabled": "Your movie doesn't move. Use blocks to make something interesting. Then you may submit your movie to the gallery.", "Movie.galleryTooltip": "Open the gallery of movies.", "Movie.galleryMsg": "See Gallery", "Movie.submitTooltip": "Submit your movie to the gallery.", "Movie.submitMsg": "Submit to Gallery", "Movie.helpLayer": "Move the background circle to the top of your program.  Then it will appear behind the person.", "Movie.helpText1": "Use simple shapes to draw this person.", "Movie.helpText2a": "This level is a movie. You want the person's arm to move across the screen. Press the play button to see a preview.", "Movie.helpText2b": "As the movie plays, the value of the 'time' block counts from 0 to 100. Since you want the 'y' position of the arm to start at 0 and go to 100 this should be easy.", "Movie.helpText3": "The 'time' block counts from 0 to 100. But now you want the 'y' position of the other arm to start at 100 and go to 0. Can you figure out a simple mathematical formula that flips the direction?", "Movie.helpText4": "Use what you learned in the previous level to make legs that cross.", "Movie.helpText5": "The mathematical formula for the arm is complicated. Here's the answer:", "Movie.helpText6": "Give the person a couple of hands.", "Movie.helpText7": "Use the 'if' block to draw a small head for the first half of the movie. Then draw a big head for the second half of the movie.", "Movie.helpText8": "Make the legs reverse direction half way through the movie.", "Movie.helpText9": "Draw an expanding circle behind the person.", "Movie.helpText10": "Make a movie of anything you want. You've got a huge number of new blocks you can explore. Have fun!", "Movie.helpText10Reddit": "Use the 'See Gallery' button to see movies that other people have made. If you make an interesting movie, use the 'Submit to Gallery' button to publish it.", "Music.playNoteTooltip": "Plays one musical note of the specified duration and pitch.", "Music.playNote": "play %1 note %2", "Music.restTooltip": "Waits for the specified duration.", "Music.restWholeTooltip": "Waits for one whole note.", "Music.rest": "rest %1", "Music.setInstrumentTooltip": "Switches to the specified instrument when playing subsequent musical notes.", "Music.setInstrument": "set instrument to %1", "Music.startTooltip": "Executes the blocks inside when the 'Run Program' button is clicked.", "Music.start": "when %1 clicked", "Music.pitchTooltip": "One note (C4 is 7).", "Music.firstPart": "first part", "Music.piano": "piano", "Music.trumpet": "trumpet", "Music.banjo": "banjo", "Music.violin": "violin", "Music.guitar": "guitar", "Music.flute": "flute", "Music.drum": "drum", "Music.choir": "choir", "Music.submitDisabled": "Run your program until it stops. Then you may submit your music to the gallery.", "Music.galleryTooltip": "Open the gallery of music.", "Music.galleryMsg": "See Gallery", "Music.submitTooltip": "Submit your music to the gallery.", "Music.submitMsg": "Submit to Gallery", "Music.helpUseFunctions": "Your solution works, but you can do better.  Use functions to reduce the amount of repeated code.", "Music.helpUseInstruments": "The music will sound better if you use a different instrument in each start block.", "Music.helpText1": "Compose the first four notes of '<PERSON><PERSON> Jacques'.", "Music.helpText2a": "A 'function' allows you to group blocks together, then run them more than once.", "Music.helpText2b": "Create a function to play the first four notes of '<PERSON><PERSON> Jacques'. Run that function twice.  Don't add any new note blocks.", "Music.helpText3": "Create a second function for the next part of 'Frère Jacques'. The last note is longer.", "Music.helpText4": "Create a third function for the next part of 'Frère Jacques'. The first four notes are shorter.", "Music.helpText5": "Complete the full tune of '<PERSON><PERSON> Jacques'.", "Music.helpText6a": "This new block lets you change to another instrument.", "Music.helpText6b": "Play your tune with a violin.", "Music.helpText7a": "This new block adds a silent delay.", "Music.helpText7b": "Create a second start block that has two delay blocks, then also plays 'Frère Jacques'.", "Music.helpText8": "Each start block should play '<PERSON><PERSON>' twice.", "Music.helpText9": "Create four start blocks that each play '<PERSON><PERSON> Jacques' twice. Add the correct number of delay blocks.", "Music.helpText10": "Compose anything you want. You've got a huge number of new blocks you can explore. Have fun!", "Music.helpText10Reddit": "Use the 'See Gallery' button to see what other people have composed. If you compose something interesting, use the 'Submit to Gallery' button to publish it.", "Pond.scanTooltip": "Scan for enemies. Specify a direction (0-360). Returns the distance to the closest enemy in that direction. Returns Infinity if no enemy found.", "Pond.cannonTooltip": "Fire the cannon. Specify a direction (0-360) and a range (0-70).", "Pond.swimTooltip": "Swim forward. Specify a direction (0-360).", "Pond.stopTooltip": "Stop swimming. Player will slow to a stop.", "Pond.healthTooltip": "Returns the player's current health (0 is dead, 100 is healthy).", "Pond.speedTooltip": "Returns the current speed of the player (0 is stopped, 100 is full speed).", "Pond.locXTooltip": "Returns the X coordinate of the player (0 is the left edge, 100 is the right edge).", "Pond.locYTooltip": "Returns the Y coordinate of the player (0 is the bottom edge, 100 is the top edge).", "Pond.logTooltip": "Prints a number to your browser's console.", "Pond.docsTooltip": "Display the language documentation.", "Pond.documentation": "Documentation", "Pond.playerName": "Player", "Pond.targetName": "Target", "Pond.pendulumName": "Pen<PERSON><PERSON>", "Pond.scaredName": "Scared", "Pond.helpUseScan": "Your solution works, but you can do better. Use 'scan' to tell the cannon how far to shoot.", "Pond.helpText1": "Use the 'cannon' command to hit the target. The first parameter is the angle, the second parameter is the range. Find the right combination.", "Pond.helpText2": "This target needs to be hit many times. Use a 'while (true)' loop to do something indefinitely.", "Pond.helpText3a": "This opponent moves back and forth, making it hard to hit. The 'scan' expression returns the exact range to the opponent in the specified direction.", "Pond.helpText3b": "This range is exactly what the 'cannon' command needs to fire accurately.", "Pond.helpText4": "This opponent is too far away to use the cannon (which has a limit of 70 meters). Instead, use the 'swim' command to start swimming towards the opponent and crash into it.", "Pond.helpText5": "This opponent is also too far away to use the cannon. But you are too weak to survive a collision. Swim towards the opponent while your horizontal location is less than than 50. Then 'stop' and use the cannon.", "Pond.helpText6": "This opponent will move away when it is hit. Swim towards it if it is out of range (70 meters).", "Gallery": "Gallery"}