{"@metadata": {"authors": ["Amire80", "<PERSON><PERSON>", "Dorongol", "<PERSON><PERSON><PERSON>", "Erelon", "Inkbug", "<PERSON><PERSON><PERSON><PERSON>", "Orsa", "Steeve815", "YaronSh", "moshfeu", "מזל<PERSON> שביר"]}, "Games.name": "מש<PERSON><PERSON><PERSON> בלוקלי", "Games.puzzle": "פאזל", "Games.maze": "מבוך", "Games.bird": "ציפור", "Games.turtle": "צב", "Games.movie": "סרט", "Games.music": "מוזיקה", "Games.pondTutor": "מדריך האגם", "Games.pond": "אגם", "Games.linesOfCode1": "פתרת את השלב עם שורה אחת של JavaScript:", "Games.linesOfCode2": "פתרת את השלב עם %1 שורות של JavaScript:", "Games.nextLevel": "אתה מוכן לשלב %1?", "Games.finalLevel": "אתה מוכן לאתגר הבא?", "Games.submitTitle": "כותרת:", "Games.linkTooltip": "שמירה וקישור לקוביות.", "Games.runTooltip": "הרץ את התוכנית שכתבת.", "Games.runProgram": "הרץ תכנית", "Games.resetTooltip": "הפסקת התכנית ואיפוס השלב", "Games.resetProgram": "איפוס", "Games.help": "עזרה", "Games.catLogic": "לוגיקה", "Games.catLoops": "לולאות", "Games.catMath": "מתמטיקה", "Games.catText": "<PERSON><PERSON><PERSON><PERSON>", "Games.catLists": "רשימות", "Games.catColour": "צבע", "Games.catVariables": "משתנים", "Games.catProcedures": "פונקציות", "Games.httpRequestError": "הבקשה נכשלה.", "Games.linkAlert": "ניתן לשתף את הקוביות שלך באמצעות קישור זה:\n\n%1", "Games.hashError": "לצערנו, '%1' איננו מתאים לאף אחת מהתוכניות השמורות", "Games.xmlError": "נסיון הטעינה של הקובץ השמור שלך נכשל. האם ייתכן שהוא נוצר בגרסה שונה של בלוקלי?", "Games.submitted": "תודה ששלחת את התכנית! אם צוות הקופים המאולפים שלנו יאהב אותה, הם יפרסמו אותה בגלריית התכניות בתוך כמה ימים", "Games.listVariable": "רשימה", "Games.textVariable": "<PERSON><PERSON><PERSON><PERSON>", "Games.breakLink": "לאחר התחלת עריכה ב־JavaScript, לא ניתן יהיה לחזור לעריכת קוביות. האם זה בסדר?", "Games.blocks": "קט<PERSON>י קוד", "Games.congratulations": "מזל טוב!", "Games.helpAbort": "שלב זה הוא קשה למדי. האם תרצה לדלג עליו ולעבור למשחק הבא? תוכל לחזור מאוחר יותר.", "Index.clear": "למחוק את כל הפתרונות שלך?", "Index.subTitle": "משחקים למתכנתים של מחר.", "Index.moreInfo": "מידע למורים…", "Index.startOver": "רוצה להתחיל מחדש?", "Index.clearData": "ניקוי נתונים", "Puzzle.animal1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait1": "נוצות", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://he.wikipedia.org/wiki/ברווז", "Puzzle.animal2": "חתול", "Puzzle.animal2Trait1": "שפם", "Puzzle.animal2Trait2": "פרווה", "Puzzle.animal2HelpUrl": "https://he.wikipedia.org/wiki/חתול", "Puzzle.animal3": "דבורה", "Puzzle.animal3Trait1": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait2": "עוקץ", "Puzzle.animal3HelpUrl": "https://he.wikipedia.org/wiki/דבורה", "Puzzle.animal4": "חיל<PERSON><PERSON>ן", "Puzzle.animal4Trait1": "קונכייה", "Puzzle.animal4Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal4HelpUrl": "https://he.wikipedia.org/wiki/חילזון", "Puzzle.picture": "תמונה:", "Puzzle.legs": "רגליים:", "Puzzle.legsChoose": "בחירה…", "Puzzle.traits": "מאפיינים:", "Puzzle.error0": "מושלם!\nכל %1 הקוביות נכונות.", "Puzzle.error1": "כמעט! קטע קוד אחד לא נכון.", "Puzzle.error2": "%1 קוביות אינן נכונות.", "Puzzle.tryAgain": "הקוביה המודגשת שגויה.\nנא לנסות שוב.", "Puzzle.checkAnswers": "בדו<PERSON> את התשובות", "Puzzle.helpText": "בשביל כל חיה (ב<PERSON><PERSON><PERSON><PERSON>), צרפו את התמונה, בחרו את מספר הרגליים שלה, ועשו ערמה של המאפיינים שלה.", "Maze.moveForward": "נוע קדימה", "Maze.turnLeft": "פנה שמאלה", "Maze.turnRight": "פנה ימינה", "Maze.doCode": "תעשה", "Maze.helpIfElse": "קוביות אם-אז תבצענה אחד משני דברים.", "Maze.pathAhead": "נתיב אם בהמשך", "Maze.pathLeft": "נתיב אם מש<PERSON>ל", "Maze.pathRight": "נתיב אם מימין", "Maze.repeatUntil": "המשך עד ש", "Maze.moveForwardTooltip": "הזז את השחקן קדימה מרווח אחד", "Maze.turnTooltip": "סובב את השחקן שמאלה או ימינה ב-90 מעלות", "Maze.ifTooltip": "אם יש אפשרות תנועה בכיוון הנוכחי, עשה מספר פעולות", "Maze.ifelseTooltip": "אם יש אפשרות תנועה בכיוון הנוכחי אזי לבצע את קוביית הפעולות הראשונה. אחרת, לבצע את קוביית הפעולות השניה.", "Maze.whileTooltip": "חזור על הפעולות האלה עד הגעה לנקודת הסיום", "Maze.capacity0": "נותרו לך %0 קוביות.", "Maze.capacity1": "נותרה לך פעולת קוד אחת", "Maze.capacity2": "נותרו לך %2 קוביות.", "Maze.runTooltip": "גורם לשחקן לעשות את מה שהקוביות אומרות.", "Maze.resetTooltip": "שים את השחקן בחזרה בנקודת ההתחלה של המבוך", "Maze.helpStack": "יש לערום כמה קוביות ‚לנוע קדימה’ כדי לעזור להגיע אל המטרה.", "Maze.helpOneTopBlock": "בשלב זה, נדרש ממך לערום את כל הקוביות שבמשטח העבודה הלבן.", "Maze.helpRun": "עליך להריץ את התוכנית בכדי לראות מה קורה", "Maze.helpReset": "התוכנית שלך לא פתרה את המבוך. לחץ על 'אפס' בכדי לנסות שנית", "Maze.helpRepeat": "השתמש בשני קטעי קוד בלבד בכדי להגיע לקצה הנתיב הזה. השתמש ב-'חזור' בכדי להריץ קטע קוד יותר מפעם אחת.", "Maze.helpCapacity": "עשית שימוש בכל קטעי הקוד הזמינים לשלב זה. בכדי ליצור קטע קוד חדש, אתה צריך קודם לכן למחוק קטע קוד קיים.", "Maze.helpRepeatMany": "אתה יכול להשתמש ביותר מקטע קוד אחד בתוך קטע קוד 'חזור'.", "Maze.helpSkins": "בחר את השחקן המועדף עליך מתוך תפריט זה.", "Maze.helpIf": "קטע קוד 'אם' יבצע פעולה רק אם התנאי הוא נכון. נסה לפנות שמאלה אם ישנו נתיב פנוי לכיוון צד שמאל.", "Maze.helpMenu": "יש ללחוץ על %1 בקוביה ‚אם’ כדי לשנות את התנאי.", "Maze.helpWallFollow": "האם תוכל לפתור את המבוך המסובך הזה? נסה לעקוב אחרי הקיר השמאלי. מיועד לתוכניתנים מנוסים בלבד!", "Bird.noWorm": "לא מחזיקה תולעת", "Bird.heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Bird.noWormTooltip": "התנאי כאשר הציפור לא קיבלה את התולעת.", "Bird.headingTooltip": "נוע בכיוו<PERSON> הזווית הנתונה, 0 לכיוו<PERSON> ימין, 90 להמשיך ישר, וכן הלאה", "Bird.positionTooltip": "x ו - y מסמנים את מיקום הציפור. כאשר x = 0 הציפור ליד הקצה השמאלי. כאשר x = 100 הציפור ליד הקצה הימני. כאשר y = 0 הציפור בקצה התחתון, כאשר y = 100 הציפור בקצה העליון", "Bird.helpHeading": "שנה את זווית הכיוון כדי לגרום לציפור לתפוס את התולעת ולנחות בקן שלה", "Bird.helpHasWorm": "השת<PERSON><PERSON> בבלוק הזה כדי ללכת בכיוון אחד אם יש לציפור תולעת, או בכיוון אחר אם אין לה תולעת", "Bird.helpX": "\"x\" הוא המיקום האופקי שלך. השתמש בבלוק הזה כדי ללכת בכיוון אחד אם הערך של \"x\" קטן ממספר, אחרת, לך בכיוון אחר", "Bird.helpElse": "לחץ על הסמליל כדי לשנות את בלוק ה־\"if\".", "Bird.helpElseIf": "השלב הזה דורש גם 'else if' וגם 'else'", "Bird.helpAnd": "ערכו של הבלוק 'and' הוא true רק אם שני הקלטים שלו הם true", "Bird.helpMutator": "גרור בלוק 'else' לתוך בלוק ה-'if'.", "Turtle.moveTooltip": "מזיז את הצב קדימה או אחורה על פי המספר המוגדר.", "Turtle.moveForward": "זוז קדימה כ", "Turtle.moveBackward": "זוז אחורה כ", "Turtle.turnTooltip": "מסובב את הצב שמאלה או ימינה כמספר המעלות המוגדר.", "Turtle.turnRight": "סובב ימינה כ", "Turtle.turnLeft": "סובב שמאלה כ", "Turtle.widthTooltip": "שינוי עובי העט.", "Turtle.setWidth": "קבע עובי ל", "Turtle.colourTooltip": "משנה את צבע העט", "Turtle.setColour": "קבע את הצבע ל", "Turtle.penTooltip": "מרים או מוריד את העט, בכ<PERSON>י לעצור או להתחיל לצייר.", "Turtle.penUp": "עט למעלה", "Turtle.penDown": "עט למטה", "Turtle.turtleVisibilityTooltip": "מציג או מסתיר את הצב (מעגל או חץ)", "Turtle.hideTurtle": "הסתרת הצב", "Turtle.showTurtle": "הצגת הצב", "Turtle.printTooltip": "מצייר את הטקסט בכיוון הצב במיקו<PERSON><PERSON> הנוכחי", "Turtle.print": "הדפס", "Turtle.fontTooltip": "קובע את הגופן שבשימוש קטע קוד ההדפסה", "Turtle.font": "גו<PERSON>ן", "Turtle.fontSize": "גודל הגופן", "Turtle.fontNormal": "רגיל", "Turtle.fontBold": "מוד<PERSON>ש", "Turtle.fontItalic": "נטוי", "Turtle.submitDisabled": "הרץ את התכנית שלך עד אשר היא עוצרת. לאחר מכן באפשרותך לשלוח את הציור שלך לגלריה.", "Turtle.galleryTooltip": "פתיחת גלריית הציורים.", "Turtle.galleryMsg": "ראה גלריה", "Turtle.submitTooltip": "שלח את הציור שלך לגלריה", "Turtle.submitMsg": "שליחה לגלריה", "Turtle.helpUseLoop": "הפתרון שלך עובד, אך את/ה יכול/ה להשתפר.", "Turtle.helpUseLoop3": "צייר את הצורה עם שלושה בלוקים בלבד.", "Turtle.helpUseLoop4": "צייר את הכוכב עם ארבעה בלוקים בלבד.", "Turtle.helpText1": "<PERSON>ור תכנית אשר תצייר ריבוע.", "Turtle.helpText2": "בצע שינויים בתכנית שלך כדי לצייר מחומש במקום ריבוע.", "Turtle.helpText3a": "ישנו בלו<PERSON> חדש המאפשר לך לשנות את הצבע:", "Turtle.helpText3b": "ציירו כוכב צהוב.", "Turtle.helpText4a": "קיים בלוק חדש שמאפשר לך להרים את העט מהנייר כאשר אתה זז", "Turtle.helpText4b": "ציירו כו<PERSON><PERSON> קטן בצבע צהוב, לאחר מכן ציירו קו מעליו.", "Turtle.helpText5": "במקום כוכב אחד, האם תוכל לצייר 4 כוכבים מסודרים בריבוע?", "Turtle.helpText6": "צייר שלושה כוכבים צהובים וקו לבן אחד.", "Turtle.helpText7": "צייר את הכוכבים, לאחר מכן צייר ארבעה קווים לבנים.", "Turtle.helpText8": "ציור 360 קווים לבנים ייראה כמו הירח המלא.", "Turtle.helpText9": "האם אתה יכול להוסיף עיגול שחור באופן בו הירח הופך לסהרון?", "Turtle.helpText10": "צייר מה שתרצה. יש לך מספר רב של בלוקים חדשים שאתה יכול לחקור. תהנה!", "Turtle.helpText10Reddit": "השתמש בכפתור 'צ<PERSON><PERSON>ה בגלריה' כד<PERSON> לצפות בציורים של משתמשים אחרים. אם ציירת משהו מעניין, השתמש בכפתור 'שליחה לגלריה' לפרסם אותו", "Turtle.helpToolbox": "יש לבחור קטגוריה כדי לראות את הקוביות.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "התחלה x", "Movie.y1": "התחלה y", "Movie.x2": "סיום x", "Movie.y2": "סיום y", "Movie.radius": "רדיוס", "Movie.width": "רו<PERSON><PERSON>", "Movie.height": "גובה", "Movie.circleTooltip": "צייר עיגול במיקום מסויים וברדיוס מסויים", "Movie.circleDraw": "עיגול", "Movie.rectTooltip": "צייר מלבן במיקום מסויים ועם אורך ורוחב מסויימים.", "Movie.rectDraw": "<PERSON>ל<PERSON><PERSON>", "Movie.lineTooltip": "מצייר קו מנקודה אחת לאחרת עם העובי שנקבע.", "Movie.lineDraw": "קו", "Movie.timeTooltip": "מחז<PERSON>ר את הזמן הנו<PERSON><PERSON>י של ההנפשה (0-100).", "Movie.colourTooltip": "משנה את צבע העט.", "Movie.setColour": "קבע את הצבע ל", "Movie.submitDisabled": "הסרט שלך אינו נע. יש להשתמש בקוביות כדי ליצור משהו מעניין. לאחר מכן ניתן לשלוח את הסרט לגלריה.", "Movie.galleryTooltip": "פתיחת גלריית הסרטים.", "Movie.galleryMsg": "ראה גלריה", "Movie.submitTooltip": "שלח את הסרט שלך לגלריה.", "Movie.submitMsg": "שליחה לגלריה", "Movie.helpLayer": "הזז את עיגול הרקע לתחילת התכנית, כך שהוא יופיע מאחורי האיש", "Movie.helpText1": "השתמש בצורות פשוטות בשביל לצייר איש.", "Movie.helpText2a": "השלב הזה הוא סרט. המטרה שלך היא להזיז את היד של האיש לרוחב המסך. לחץ על כפתור ה־play כדי לצפות בו.", "Movie.helpText2b": "כאשר הסרט מתנגן, הערך של הבלוק ‚time’ עולה מ־0 ל־100. מאחר שהמטרה שמיקום ה־y של היד יתחיל מ־0 וימשיך עד 100, זה אמור להיות קל.", "Movie.helpText3": "ערך הבלוק 'time' עולה מ 0 ל 100. אבל הפעם המטרה שמיקום ה y של היד השניה יתחיל מ 100 וירד ל 0. האם תוכל למצוא שיטה מתמטית פשוטה שמחליפה את הכיוונים?", "Movie.helpText4": "השתמש במה שלמדת בשלב הקודם כדי ליצור רגלים מצטלבות", "Movie.helpText5": "הנוסחה המתמטית עבור היד מורכבת. הינה התשובה:", "Movie.helpText6": "תן לאיש מספר ידיים.", "Movie.helpText7": "השתמש בבלוק 'if' כדי לצייר ראש קטן לחצי הראשון של הסרט. לאחר מכן, צייר ראש גדול לחצי השני של הסרט.", "Movie.helpText8": "הפוך את כיוון הרגליים באמצע הסרט", "Movie.helpText9": "צייר עיגול מתרחב מאחורי האיש", "Movie.helpText10": "צור סרט של מה שתרצה. יש לך הרבה בלוקים חדשים לחקור. תהנה!", "Movie.helpText10Reddit": "השתמש בכפתור 'צ<PERSON><PERSON>ה בגלריה' כדי לצפות בסרטים של משתמשים אחרים. אם יצרת סרט מעניין, השתמש בכפתור 'שליחה לגלריה' לפרסם אותו", "Music.playNoteTooltip": "מנגן תו אחד לאורך זמן מסויים ובגובה מסויים", "Music.playNote": "נגן %1 מהתו %2", "Music.restTooltip": "ממתין למשך הזמן שנקבע.", "Music.restWholeTooltip": "חכה אורך של תו שלם.", "Music.rest": "תמתין %1", "Music.setInstrumentTooltip": "החלף לכלי מסויים כאשר מתנגנים התווים הבאים.", "Music.setInstrument": "קביעת כלי נגינה עבור %1", "Music.startTooltip": "הרץ את הבלוקים שבתוך כאשר לוחצים על הכפתור 'Run Program'.", "Music.start": "כאשר %1 נלחץ", "Music.pitchTooltip": "תו אחד (C4 מיוצג בתור 7).", "Music.firstPart": "המסלול הראשון", "Music.piano": "פסנתר", "Music.trumpet": "חצוצרה", "Music.banjo": "בנג'ו", "Music.violin": "כינ<PERSON>ר", "Music.guitar": "גיטרה", "Music.flute": "חליל", "Music.drum": "תוף", "Music.choir": "מקחלה", "Music.submitDisabled": "הרץ את התכנית שלך עד אשר היא עוצרת. לאחר מכן באפשרותך לשלוח את השיר שלך לגלריה.", "Music.galleryTooltip": "פתיחת גלריית המוזיקה.", "Music.galleryMsg": "ראה גלריה", "Music.submitTooltip": "שלח את השיר שלך לגלריה.", "Music.submitMsg": "שליחה לגלריה", "Music.helpUseFunctions": "הפתרון שלך עובד, אבל אפשר להשתפר. יש השתמש בפונקציות כדי לצמצם את כמות הקוד החוזר על עצמו.", "Music.helpUseInstruments": "המוזיקה תשמע טוב יותר אם תשתמש בכלים שונים בכל בלוק", "Music.helpText1": "נגן את 4 התווים הראשונים של אחינו הנהג", "Music.helpText2a": "'פונקציה' מאפשרת לך ליצור קבוצה מכמה בלוקים ביחד, כך שהם רצים אחד אחרי השני.", "Music.helpText2b": "צור פונקציה כדי לנגן את 4 התווים הראשונים של אחינו הנהג. הרץ את הפונקציה הזאת פעמיים. אל תוסיף בלוקים חדשים של תווים.", "Music.helpText3": "צור פונקציה שניה לחלק הבא של השיר אחינו הנהג. התו האחרון ארוך יותר", "Music.helpText4": "צור פונקציה שלישית לחלק הבא של השיר אחינו הנהג. 4 התווים הראשונים הם קצרים יותר", "Music.helpText5": "להשלים את הנעימה ‚אחינו הנהג’.", "Music.helpText6a": "הלבנה החדשה הזאת מאפשרת לך להחליף כלי נגינה.", "Music.helpText6b": "נגן את המנגינה שלך עם כינור.", "Music.helpText7a": "לבנה חדשה זו מוסיפה השהיה שקטה.", "Music.helpText7b": "ליצור לבנת התחלה שנייה שבה שתי לבני השהיה, לאחר מכן יתנגן ‚אחינו הנהג’.", "Music.helpText8": "כל לבנת התחלה אמורה לנגן את ‚אחינו הנהג’ פעמיים.", "Music.helpText9": "עליך ליצור ארבע קוביות התחלה שכל אחת מהן מנגנת את ‚אחינו הנהג’ פעמיים. יש להוסיף את המספר הנכון של קוביות ההשהיה.", "Music.helpText10": "באפשרותך להלחין כל מה שבא לך. יש לך מגוון ענק של קוביות חדשות שמחכות לך לחקור אותן. באנו לבלות!", "Music.helpText10Reddit": "השתמש בכפתור 'צ<PERSON><PERSON>ה בגלריה' כד<PERSON> לצפות בשירים של משתמש אחרים. אם הלחנת משהו מעניין, השתמש בכפתור 'שליחה לגלריה' לפרסם אותו", "Pond.scanTooltip": "סרוק אם ישנם אויבים. ציי<PERSON> כיוון (0-360). קבל בחזרה את המרחק לאויב הקרוב ביותר בכיוון המבוקש. מחזיר Infinity אם אין אויב בכיוון המבוקש", "Pond.cannonTooltip": "בצע יריה מהתותח. ציי<PERSON> כיוון (0-360) וטווח (0-70).", "Pond.swimTooltip": "לשח<PERSON><PERSON> קדימה. נא לציין כיוון (0-360).", "Pond.stopTooltip": "לה<PERSON><PERSON>יק לשחות. השח<PERSON>ן יאט עד לעצירה.", "Pond.healthTooltip": "החזרת מצב הבריאות הנוכ<PERSON>י של השחקן (0 - מת, 100 - בריא).", "Pond.speedTooltip": "החזרת המהירות הנוכחית של השחקן (0 - עוצר, 100 - מהירות מלאה).", "Pond.locXTooltip": "החזרת נקודת הציון של השחקן בציר X‏ (0 - קצה שמאלי, 100 - קצה ימני).", "Pond.locYTooltip": "החזרת נקודת הציון של השחקן בציר Y‏ (0 - תחתית, 100 - ראש).", "Pond.logTooltip": "מדפיס מספר למסוף של הדפדפן שלך.", "Pond.docsTooltip": "הצגת התיעוד עבור השפה.", "Pond.documentation": "תיעוד", "Pond.playerName": "<PERSON><PERSON><PERSON><PERSON>", "Pond.targetName": "מטרה", "Pond.pendulumName": "מטוטלת", "Pond.scaredName": "מבוהל", "Pond.helpUseScan": "הפתרון שלך עובד, אבל אפשר להשתפר. נא להשתמש ב‚סריקה’ כדי להגיד לתותח כמה רחוק לירות.", "Pond.helpText1": "השתמש בפקודה 'cannon' כדי לפגוע במטרה. הפרמטר הראשון זו הזווית, השני זה הטווח. מצא את השילוב הנכון", "Pond.helpText2": "המטרה צריכה להיפגע כמה פעמים. השתמש בלולאת 'while (true)' כדי לבצע את הפעולה עד אין סוף.", "Pond.helpText3a": "היריב הזה זז מצד לצד מה שמקשה על פגיעה זו. הביטוי 'scan' מחזיר את הטווח המדוייק ליריב בכיוון מסויים", "Pond.helpText3b": "הטווח הזה הוא בדיוק מה שהפקודה 'canon' צריכה כדי לירות באופן מדוייק", "Pond.helpText4": "היריב הזה הוא רחוק מידי מכדי להשתמש בתותח (שהטוו<PERSON> שלו מוגבל ל 70 מטר). במקום זה, השתמש בפקודה 'swim' כדי להתחיל לשחות לכיוון היריב ולהתנגש בו.", "Pond.helpText5": "גם היריב הזה רחוק מדי מכדי להשתמש בתותח. אבל אתה חלש מידי כדי לשרוד התנגשות. תשחה לכיוון היריב כאשר המיקום האופקי שלך קטן מ 50. לאחר מכן השתמש ב stop ואז השתמש בתותח.", "Pond.helpText6": "היריב כבר יזוז עד הפגיעה. תשחה לכיוון שלו אם הוא מחוץ לטווח (70 מטר).", "Gallery": "גלריה"}