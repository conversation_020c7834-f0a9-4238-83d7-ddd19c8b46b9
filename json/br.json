{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Y-M D"]}, "Games.name": "<PERSON><PERSON>ho<PERSON><PERSON>", "Games.puzzle": "<PERSON><PERSON><PERSON>", "Games.maze": "Milendall", "Games.bird": "Labous", "Games.turtle": "<PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Sonerezh", "Games.pondTutor": "Tutorial Stank", "Games.pond": "Stank", "Games.linesOfCode1": "Diskoulmet ho peus al live-mañ gant 1 linenn JavaScript :", "Games.linesOfCode2": "Diskoulmet ho peus al live-mañ gant %1 linenn JavaScript :", "Games.nextLevel": "Ha prest oc'h evit al live %1 ?", "Games.finalLevel": "Ha prest oc'h evit an dae da zont ?", "Games.submitTitle": "Titl :", "Games.linkTooltip": "Enrollañ ha liammañ ouzh ar bloc'hadoù.", "Games.runTooltip": "Lañsañ ar programm ho peus skrivet.", "Games.runProgram": "Lañsañ ar programm", "Games.resetTooltip": "<PERSON><PERSON><PERSON> ar programm hag adderaouekaat al live.", "Games.resetProgram": "Adderaouekaat", "Games.help": "Skoazell", "Games.catLogic": "<PERSON><PERSON>", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematik", "Games.catText": "<PERSON><PERSON><PERSON>", "Games.catLists": "<PERSON><PERSON><PERSON>", "Games.catColour": "Liv", "Games.catVariables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.catProcedures": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.httpRequestError": "<PERSON>r gudenn zo gant ar reked.", "Games.linkAlert": "Rannañ ho ploc'hoù gant al liamm-mañ :\n\n%1", "Games.hashError": "Digarezit. \"%1\" ne glot gant programm enrollet ebet.", "Games.xmlError": "Ne c'haller ket kargañ ho restr enrollet. <PERSON><PERSON><PERSON> e oa bet krouet gant ur stumm disheñvel eus <PERSON> ?", "Games.submitted": "Trugarez evit ar programm-mañ. Ma plij d'hor skipailh marmouzed  doñvaet e vo embannet ganto er palier ac'hann da zaou zevezh.", "Games.listVariable": "roll", "Games.textVariable": "testenn", "Games.breakLink": "Ur wech m'ho po kroget da zazkemmañ ar JavaScript ne c'hallit ket mui distreiñ d'an daskemmañ bloc'hoù. Ha mat eo evidoc'h ?", "Games.blocks": "Bloc'hoù", "Games.congratulations": "Gour<PERSON>'hemennoù !", "Games.helpAbort": "Diaes-kenañ eo al live-mañ. Ha c'hoant ho peus da lammat anezhañ ha da dremen d'ar c'hoari war-lerc'h ? Gallout a rit atav distreiñ dezhañ diwezhatoc'h.", "Index.clear": "<PERSON><PERSON> holl ho tiskoulmoù ?", "Index.subTitle": "C'hoarioù evit programmerien warc'hoazh.", "Index.moreInfo": "<PERSON><PERSON><PERSON> pedagogel...", "Index.startOver": "Ha c'hoant ho peus da adkregiñ ?", "Index.clearData": "<PERSON><PERSON><PERSON><PERSON> ar roaden<PERSON>", "Puzzle.animal1": "Houad", "Puzzle.animal1Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal1Trait2": "Beg", "Puzzle.animal1HelpUrl": "https://br.wikipedia.org/wiki/Houad", "Puzzle.animal2": "Kazh", "Puzzle.animal2Trait1": "<PERSON><PERSON><PERSON><PERSON>", "Puzzle.animal2Trait2": "<PERSON><PERSON>", "Puzzle.animal2HelpUrl": "https://br.wikipedia.org/wiki/Kazh", "Puzzle.animal3": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "<PERSON>", "Puzzle.animal3Trait2": "Flemm", "Puzzle.animal3HelpUrl": "https://br.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "Puzzle.animal4": "Maligorn", "Puzzle.animal4Trait1": "Krogenn", "Puzzle.animal4Trait2": "Babouz", "Puzzle.animal4HelpUrl": "https://br.wikipedia.org/wiki/Maligorn", "Puzzle.picture": "Skeudenn :", "Puzzle.legs": "Pavioù :", "Puzzle.legsChoose": "dibab...", "Puzzle.traits": "perzhioù :", "Puzzle.error0": "Dispar !\nReizh eo an/ar %1 bloc'h.", "Puzzle.error1": "Tost ! Chom a ra ur bloc'h direizh.", "Puzzle.error2": "%1 bloc'h zo direizh.", "Puzzle.tryAgain": "<PERSON><PERSON><PERSON>h eo ar bloc'h usskedet.\nKendalc'hit da bleustriñ.", "Puzzle.checkAnswers": "<PERSON>wi<PERSON><PERSON> ar responto<PERSON>", "Puzzle.helpText": "<PERSON><PERSON>t kement loen(e gwer), staga<PERSON> he skeudenn, dibab pet pav zo de<PERSON>, ha berniañ he ferzhioù.", "Maze.moveForward": "mont war-raok", "Maze.turnLeft": "treiñ a-gleiz", "Maze.turnRight": "treiñ a-zehoù", "Maze.doCode": "ober", "Maze.helpIfElse": "Seveniñ a raio ar bloc'hoù \"Ma/A-hend-all\" an eil tra pe egile.", "Maze.pathAhead": "Ma'z eus un hent dirak", "Maze.pathLeft": "Ma'z eus un hent a-gleiz", "Maze.pathRight": "Ma'z eus un hent a-zehoù", "Maze.repeatUntil": "adober betek", "Maze.moveForwardTooltip": "<PERSON><PERSON>t ar c'hoarier da vont war-raok eus un esaouenn.", "Maze.turnTooltip": "Lakait ar c'hoarier da dreiñ a-gleiz pe a-zehoù a 90 derez.", "Maze.ifTooltip": "Ma'z eus un hent war an tu spisaet, grit an oberoù-se neuze.", "Maze.ifelseTooltip": "Ma'z eus un hent war an tu spisaet, grit ar c'hentañ bloc'had oberoù. A-hend-all, grit an eil bloc'had oberoù.", "Maze.whileTooltip": "Adober ar bloc'ho<PERSON> zo en diabarzh betek tizhout ar pal.", "Maze.capacity0": "Ne chom bloc'h ebet (%0) ganeoc'h.", "Maze.capacity1": "Chom a ra %1 bloc'h ganeoc'h.", "Maze.capacity2": "Chom a ra %2 bloc'h ganeoc'h.", "Maze.runTooltip": "Lakaat ar c'hoarier d'ober ar pezh a lavar ar bloc'hoù.", "Maze.resetTooltip": "<PERSON><PERSON><PERSON><PERSON> an dudenn e deroù ar milendall.", "Maze.helpStack": "Berniañ asambles daou vloc'h kemen<PERSON><PERSON> \"mont war-raok\" evit sikour ac'hanon da dizhout ar pal.", "Maze.helpOneTopBlock": "El live-se az peus ezhomm da verniañ bloc'hoù an eil war egile en un takad labour gwenn.", "Maze.helpRun": "Sevenit ho programm evit gwelet petra a c'hoarvez.", "Maze.helpReset": "N'eo ket bet diskoulmet ar milendall gant ho programm. Pouezit war \"Adderaouekaat\" ha klaskit en-dro.", "Maze.helpRepeat": "N'eus ket nemeur a vemor gant an urzhiataerioù. Implijit daou vloc'h hepken evit tizhout ar pal. Implijit ar bloc'h \"adober\" evit seveniñ ur bloc'h meur a wech.", "Maze.helpCapacity": "Implijet eo bet ganeoc'h holl vloc'hoù al live-mañ. <PERSON><PERSON>t krouiñ ur bloc'h nevez e rankit dilemel ur bloc'h zo anezhañ da gentañ.", "Maze.helpRepeatMany": "Meur a vloc'h a c'haller lakaat en ur bloc'h 'adober'.", "Maze.helpSkins": "Dibabit ho c'hoarier karetañ el lañser.", "Maze.helpIf": "Ne raio ur bloc'h \"ma\" un dra bennak ken nemet m'eo gwir an amplegad. Klaskit treiñ a-gleiz ma'z eus un hent war an tu kleiz.", "Maze.helpMenu": "Klikit war %1 er bloc'h \"ma\" evit cheñch e amplegad.", "Maze.helpWallFollow": "Daoust ha gouest oc'h da ziskoulmañ ar milendall luziet-mañ ? Klaskit mont a-hed ar voger gleiz. Evit ar brogram<PERSON>ien arroutet-mat hepken !", "Bird.noWorm": "n'eus preñv ebet gantañ", "Bird.heading": "Penn", "Bird.noWormTooltip": "Ar stad m'emañ al labous pa n'eo ket deuet a-benn da gaout ar preñv.", "Bird.headingTooltip": "Mont war-zu ur c'horn roet  : 0 zo war an tu-dehou, 90 zo war-eeun, ha kement zo", "Bird.positionTooltip": "x hag y a verk pelec'h emañ al labous. Pa'z eo par x da 0 emañ al labous tost d'ar vord kleiz, pa'z eo par x da 100 emañ al labous tost d'ar vord dehou. Pa'z eo par y da 0 emañ al labous en traoñ, pa'z eo par y da 100 emañ e krec'h.", "Bird.helpHeading": "Kit d'un tu all evit ma c'hallo al labous tapout ar preñv ha mont d'e neizh.", "Bird.helpHasWorm": "Implijit ar bloc'h-mañ evit mont war un tu m'emañ ar preñv ganeoc'h, ha d'un tu all ma n'emañ ket ganeoc'h.", "Bird.helpX": "\"X\" zo ho lec'hiadur a-blaen. Implijit ar bloc'h-mañ evit mont d'un tu ma'z eo bihanoc'h \"X\" eget un niver, anez-se d'un tu all ?", "Bird.helpElse": "Klikit war an arlun evit kemmañ ar bloc'h 'ma'", "Bird.helpElseIf": "Ezhomm en deus al live-mañ eus ur bloc'h \"ma\" hag unan \"anez\" war un dro.", "Bird.helpAnd": "Gwir eo ar bloc'h \"ha\" mar ha nemet mard eo gwir an daou voned.", "Bird.helpMutator": "<PERSON><PERSON><PERSON> ur bloc'h \"anez\" er bloc'h \"ma\".", "Turtle.moveTooltip": "Lakaat ar vaot da vont war-raok pe da vont war-gil hervez ar c'hementad merket.", "Turtle.moveForward": "Mont war-raok eus", "Turtle.moveBackward": "Mont war-gil eus", "Turtle.turnTooltip": "Lakaat ar vaot da dreiñ a-gleiz pe a-zehoù hervez an niver a zerezioù merket.", "Turtle.turnRight": "treiñ a-zehoù da", "Turtle.turnLeft": "<PERSON>re<PERSON><PERSON> a-gleiz da", "Turtle.widthTooltip": "<PERSON><PERSON><PERSON><PERSON> ledander ar stilo.", "Turtle.setWidth": "laka<PERSON> al ledander da", "Turtle.colourTooltip": "Cheñch liv ar stilo.", "Turtle.setColour": "lakaat al liv da", "Turtle.penTooltip": "<PERSON>vel pe diskenn ar stilo, evit paouez pe kregiñ da dresañ.", "Turtle.penUp": "sevel ar stilo", "Turtle.penDown": "lakaat ar stilo d'an traoñ", "Turtle.turtleVisibilityTooltip": "Lakaat a ra ar vaot (kelc'h ha biroù) war wel pe diwar wel.", "Turtle.hideTurtle": "kuzhat ar vaot", "Turtle.showTurtle": "disk<PERSON>ez ar vaot", "Turtle.printTooltip": "Tresañ an destenn war-zu ar vaot el lec'h m'emañ.", "Turtle.print": "mou<PERSON><PERSON>", "Turtle.fontTooltip": "Termeniñ a ra ar font implijet gant ar bloc'h skrivañ.", "Turtle.font": "font", "Turtle.fontSize": "ment ar font", "Turtle.fontNormal": "reizh", "Turtle.fontBold": "tev", "Turtle.fontItalic": "italek", "Turtle.submitDisabled": "Luskañ ho programm betek ma paouezo. Goude-se e c'hallit embann ho tresadenn er palier.", "Turtle.galleryTooltip": "<PERSON><PERSON><PERSON><PERSON> palier an tresadennoù.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON> ar palier", "Turtle.submitTooltip": "Embannit ho tresadenn er palier.", "Turtle.submitMsg": "Embann er Palier", "Turtle.helpUseLoop": "<PERSON> tiskoulm a zo vont en-dro, met gallout a rit ober gwelloc'h.", "Turtle.helpUseLoop3": "<PERSON><PERSON><PERSON><PERSON> ar stumm gant tri bloc'h hepken.", "Turtle.helpUseLoop4": "Tresa<PERSON> ar steredenn gant pevar bloc'h hepken.", "Turtle.helpText1": "<PERSON><PERSON><PERSON> ur programm evit tresañ ur garrezenn.", "Turtle.helpText2": "Kemmit ho programm evit tresañ ur pempkorneg kentoc'h eget ur garrezenn.", "Turtle.helpText3a": "Setu ur bloc'h nevez ha gantañ e c'hallit kemmañ al liv :", "Turtle.helpText3b": "<PERSON><PERSON><PERSON><PERSON> ur steredenn velen", "Turtle.helpText4a": "Setu ur bloc'h nevez ha gantañ e c'hallit lemel ho kreion diwar ar follenn pa vezit o vont gant ho hent :", "Turtle.helpText4b": "<PERSON><PERSON><PERSON><PERSON> ur steredenn vihan, ha goude-se ul linenn a-us dezhi.", "Turtle.helpText5": "E-lec'h ur steredenn hep<PERSON>, ha gallout a rit tresañ teir steredenn kempennet e doare ur garrezenn ?", "Turtle.helpText6": "Tresit teir steredenn velen, hag ul <PERSON>n wenn.", "Turtle.helpText7": "Tresit ar stered, ha peder linenn wenn da c'houde.", "Turtle.helpText8": "Tresit 360 linenn wenn hag a denn d'ul loargann", "Turtle.helpText9": "Gallout a rit ouzhpennañ ur c'helc'h du evit ma teuio al loar da vezañ ur c'hresk-loar ?", "Turtle.helpText10": "Tresit ar pezh ho peus c'hoant. Un niver bras a vloc'hoù a c'hallit ergerzhet ho peus. Kemerit plijadur !", "Turtle.helpText10Reddit": "Implijit ar bouton %quot;gwelet ar palier; evit gwelet ar pezh zo bet treset gant ar re all. Ma tresit un dra bennak deden<PERSON>, implijit ar bouton %quot;Embann er palier%quot; evit embann ho tresadenn.", "Turtle.helpToolbox": "<PERSON><PERSON><PERSON> ur rummad evit gwelet ar bloc'ho<PERSON>.", "Movie.x": "x", "Movie.y": "y", "Movie.x1": "x deroù", "Movie.y1": "y deroù", "Movie.x2": "x dibenn", "Movie.y2": "y dibenn", "Movie.radius": "skin", "Movie.width": "<PERSON><PERSON>", "Movie.height": "<PERSON><PERSON><PERSON>", "Movie.circleTooltip": "Dr<PERSON><PERSON>ñ a ra ur c'helc'h el lec'h spisaet gant ur skin spisaet.", "Movie.circleDraw": "Kelc'h", "Movie.rectTooltip": "Tresañ a ra ur skouergorneg en ul lec'h spisaet gant al ledander hag an uhelder spisaet.", "Movie.rectDraw": "Skouergorneg", "Movie.lineTooltip": "Tresañ a ra ul linenn eus ur poent d'egile gant ul ledander spisaet.", "Movie.lineDraw": "linenn", "Movie.timeTooltip": "Di<PERSON><PERSON><PERSON><PERSON> pad a vremañ ar skeudenn-vev (0-100)", "Movie.colourTooltip": "Cheñch liv ar stilo.", "Movie.setColour": "lakaat al liv da", "Movie.submitDisabled": "N'emañ ket ho skeudenn-vev o fiñval. Implijout ar bloc'hoù evit ober un dra bennak dedennus. Goude-se e c'hallot embann ho fskeudenn-vev er palier.", "Movie.galleryTooltip": "<PERSON><PERSON><PERSON><PERSON> palier ar skeudennoù-bev.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON> ar palier", "Movie.submitTooltip": "<PERSON>ban<PERSON>t ho skeudenn-vev er palier.", "Movie.submitMsg": "Embann er Palier", "Movie.helpLayer": "<PERSON><PERSON><PERSON>'hiit ar c'helc'h en drekleur e krec'h ho programm. Dont a ray war wel a-dreñv an den.", "Movie.helpText1": "<PERSON><PERSON><PERSON><PERSON> stum<PERSON> eeun evit tresañ an den-mañ.", "Movie.helpText2a": "Ur film eo al live-se. C'hoant ho peus da zilec'hiañ brec'h an den a-dreuz ar skramm. Pouezit war ar bouton c'hoari evit gwelet un alberz.", "Movie.helpText2b": "Keit ha m'emañ ar skeudenn-vev o c'hoari emañ ar bloc'h \"amzer\" o vont war-raok eus 0 da 100. <PERSON><PERSON> m'ho peus c'hoant e krogo lechiadur \"y\" ar vrec'h  e 0 ha ma'z ay betek 100 e tlefe bezañ aes.", "Movie.helpText3": "Emañ ar bloc'h \"amzer\" o vont eus 0 da 100. Bremañ avat ho peus c'hoant ma krogo lec'hiadur \"y\" ar vrec'h all e 100 ha ma'z ay betek 0. Ha c'hoant ho peus da gavout ar formulenn vatematik a cheñcho tu ?", "Movie.helpText4": "Implijit ar pezh ho peus desket el live kent evit kroaziañ e zivesker.", "Movie.helpText5": "Luziet eo ar formulenn vatematik evit ar vrec'h. Setu ar respont :", "Movie.helpText6": "<PERSON><PERSON> daou zorn d'an den.", "Movie.helpText7": "Implijit ar bloc'h \"if\" evit tresañ ur penn bihan evit an hanterenn gentañ eus ar film. Tresit goude-se ur penn bras evit an eil hanterenn eus ar film.", "Movie.helpText8": "Lakait an divesker da vont en tu all eus hanterenn ar film.", "Movie.helpText9": "Tresit ur c'helc'h hag a zo o vrasaat a-dreñv an den.", "Movie.helpText10": "Savit ar skeudenn-vev ho peus c'hoant. Un niver bras a vloc'hoù a c'hallit ergerzhet ho peus. Kemerit plijadur !", "Movie.helpText10Reddit": "Implijit ar bouton %quot;gwelet ar palier; evit gwelet fiskeudennoù-bev ar re all. Ma rit ur skeudenn-vev dedennus, implijit ar bouton %quot;Embann er palier%quot; evit embann anezhi.", "Music.playNoteTooltip": "Seniñ a ra un notenn sonerezh eus padalezh hag uhelder ar roadennoù.", "Music.playNote": "c'hoari %1 notenn %2", "Music.restTooltip": "O c'hortoz ar pad spisaet.", "Music.restWholeTooltip": "O c'hortoz un notenn anterin.", "Music.rest": "gortoz %1", "Music.setInstrumentTooltip": "A wint war-zu ar benveg spisaet en ur seniñ an notennoù sonerezh war-lerc'h.", "Music.setInstrument": "Termenañ ar benveg-seniñ e %1", "Music.startTooltip": "a seven ar bloc'hoù en diabarzh pa vez kliket ar bouton \"Lañsañ ar programm\".", "Music.start": "Pa z'eo kliket %1", "Music.pitchTooltip": "Un notenn (C4 zo 7).", "Music.firstPart": "kenta<PERSON> lodenn", "Music.piano": "piano", "Music.trumpet": "trompilh", "Music.banjo": "banjo", "Music.violin": "violoñs", "Music.guitar": "gitar", "Music.flute": "<PERSON><PERSON><PERSON><PERSON>", "Music.drum": "taboulin", "Music.choir": "laz-kanañ", "Music.submitDisabled": "Roit lañs d'ho programm betek ma paouezo. Gallout a rit embann ho sonerezh er palier neuze.", "Music.galleryTooltip": "Digeriñ ar palier sonerezh.", "Music.galleryMsg": "<PERSON><PERSON><PERSON> ar palier", "Music.submitTooltip": "Embannit ho sonerezh er palier.", "Music.submitMsg": "Embann er Palier", "Music.helpUseFunctions": "<PERSON> a ra ho tiskoulm en-dro, met gallout a reot ober gwelloc'h. Implijit an arc'hwelio<PERSON> evit digreskiñ ar c'hementad kod arreet.", "Music.helpUseInstruments": "Gwelloc'h e vo ar sonerezh ma'z implijit ur benveg disheñvel e pep bloc'h loc'hañ.", "Music.helpText1": "<PERSON>vit ar peder notenn gentañ eus \"Breur Jakez\".", "Music.helpText2a": "Gant un \"arc'hwel\" e challit kenstrollañ bloc'hoù, ha goude-se seveniñ anezho meur a wech.", "Music.helpText2b": "<PERSON><PERSON>it un arc'hwel evit seniñ ar peder notenn gentañ eus \"Breur Jakez\". Lañsit an arc'hwel-se div wech. Arabat eo deoc'h ouzhpennañ bloc'ho<PERSON> notennoù all.", "Music.helpText3": "<PERSON><PERSON>it un eil arc'hwel evit al lodenn da-heul eus \"Breur Jakez\". Hiroc'h eo an notenn diwez<PERSON>ñ.", "Music.helpText4": "<PERSON><PERSON>it un trede arc'hwel evit al lodenn da-heul \"Breur Jakez\". Berroc'h eo ar peder notenn gentañ.", "Music.helpText5": "Echuit an ton \"Breur Jakez\" en e bezh.", "Music.helpText6a": "Gant ar bloc'h nevez e c'hallit tremen d'ur benveg all.", "Music.helpText6b": "Seniñ ho ton gant ar violoñs", "Music.helpText7a": "Ar b<PERSON><PERSON><PERSON> ne<PERSON>-mañ a ouzhp<PERSON>n pred ur bao<PERSON><PERSON>n.", "Music.helpText7b": "<PERSON><PERSON><PERSON> un eil bloc'h en deus daou vloc'h paoue<PERSON>n, ha son \"<PERSON><PERSON><PERSON>\" i<PERSON> goude-se .", "Music.helpText8": "Pep bloc'h derao<PERSON>ñ a rank seniñ \"B<PERSON>ur Jakez\" div wech.", "Music.helpText9": "<PERSON><PERSON><PERSON> pevar bloc'h loc'hañ hag a c'hall seniñ \"<PERSON><PERSON><PERSON>\" div wech. Ouzhpennit an niver mat a vloc'h paouezenn.", "Music.helpText10": "Savit ar pezh ho peus c'hoant. Un niver bras a vloc'hoù a c'hallit ergezhet ho peus. Kemerit plijadur !", "Music.helpText10Reddit": "Implijit ar bouton 'gwelet ar palier' evit gwelet ar pezh a zo bet sonaozet gant tud all. Ma savit un ton dedennus bennak, implijit ar bouton 'Embann er palier' evit embann anezha<PERSON>.", "Pond.scanTooltip": "Klaskit enebourien. Spisait un tu (0-360). Distreiñ a ra an hed d'an enebour tostañ diouzh an tu-mañ. Distreiñ a ra \"anvevenn\" ma ne vez kavet enebour ebet.", "Pond.cannonTooltip": "<PERSON><PERSON><PERSON> gant ar c'hanol. <PERSON><PERSON>ait un tu (0-360) hag un hed-tenn (0-70).", "Pond.swimTooltip": "Neuial. Spisait un tu (0-360).", "Pond.stopTooltip": "Paouez<PERSON> da neuial. Ar c'hoarier a grenno e dizh evit chom a-sav.", "Pond.healthTooltip": "Di<PERSON><PERSON><PERSON><PERSON> yec'hed a vremañ ar c'hoarier (0 zo par da \"marv\", 100 zo par da \"yac'h-pesk\")", "Pond.speedTooltip": "Distreiñ tizh a vremañ ar c'hoarier (0 zo par da \"ehanet\", 100 zo par \"diwar dizh\").", "Pond.locXTooltip": "Dist<PERSON><PERSON><PERSON> daveenn X ar c'hoarier (0 zo ar bord kleiz, 100 zo par d'ar vord dehou)", "Pond.locYTooltip": "Retorn a ra daveenn Y ar c'hoarier (0 zo an traoñ, 100 zo ar c'hrec'h).", "Pond.logTooltip": "<PERSON><PERSON><PERSON>ñ a ra un niver war penell ho merdeer.", "Pond.docsTooltip": "<PERSON><PERSON><PERSON><PERSON> an teuliadur diwar-benn ar yezh.", "Pond.documentation": "<PERSON><PERSON><PERSON><PERSON>", "Pond.playerName": "<PERSON><PERSON>ho<PERSON>er", "Pond.targetName": "Pal", "Pond.pendulumName": "<PERSON><PERSON><PERSON>", "Pond.scaredName": "S<PERSON>et", "Pond.helpUseScan": "Mat eo ho tiskoulm, met gallout a reot ober gwelloc'h.\n<PERSON><PERSON><PERSON><PERSON> \"scan\" evit lavaret d'ar c'hanol betek pegeit tennañ.", "Pond.helpText1": "Implijit an urzhiad \"cannon\" evit tizhout ar gwenn. An arventenn gentañ eo ar \"c'horn\", an eil eo ar \"c'heit\". Kavit ar c'henaozad mat.", "Pond.helpText2": "Ar c'horn-mañ a zle bezañ tizhet meur a wech. <PERSON><PERSON><PERSON><PERSON> ur rodell \"while (true)\" evit un ober un dra dizehan.", "Pond.helpText3a": "An enebour-mañ a zo o vont war-raok ha war-gil. Gant-se ez eo diaes tizhout anezhañ. Ar bomm \"scan\" a zistro ar c'heit resis d'an enebour war an tu spisaet.", "Pond.helpText3b": "An hed-tenn-mañ zo tre-ha-tre ar pezh en deus ezhomm an urzhiad \"cannon\" evit tennañ en un doare resis.", "Pond.helpText4": "Re bell eo an eneber-mañ evit implijout ar c'hanol (a c'hall tennañ betek 70 m d'ar muiañ). En e lec'h, implijit an urzhiad \"swim\" evit kregiñ da neuial war-zu an eneber ha skeiñ outañ.", "Pond.helpText5": "Re bell eo an enebour-se evit implijout ar c'hanol. Re wan oc'h avat evit chom bev goude ur stokadenn. Neuiit davet an eneber keit ha ma'z eo bihanoc'h ho lec'hiadur a-blaen eget 50. \"stop\" da c'houde hag implijit ar c'hanol.", "Pond.helpText6": "Pellaat a ray an eneber-mañ pa vo skoet outañ. Neuit davetañ ma'z eo dreist diraez (70 metr).", "Gallery": "Palier"}