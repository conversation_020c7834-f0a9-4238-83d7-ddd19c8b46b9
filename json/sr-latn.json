{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Zoranzoki21"]}, "Games.name": "Blokli igre", "Games.puzzle": "Slagalica", "Games.maze": "<PERSON><PERSON><PERSON>", "Games.bird": "Ptica", "Games.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Games.movie": "Film", "Games.music": "Muzika", "Games.pondTutor": "<PERSON><PERSON> z<PERSON> Ribnjak", "Games.pond": "Ribnjak", "Games.linesOfCode1": "<PERSON><PERSON><PERSON><PERSON> rešiti ovaj nivo jednom linijom javaskript koda:", "Games.linesOfCode2": "Možeš rešiti ovaj nivo sa %1 linija javaskript koda:", "Games.nextLevel": "Da li si spreman za nivo %1?", "Games.finalLevel": "Da li si spreman za sledeći izazov?", "Games.linkTooltip": "Snima i pravi vezu ka blokovima.", "Games.runTooltip": "Pokreće program koji si napisao.", "Games.runProgram": "Pokreni program", "Games.resetTooltip": "Zaustavlja program i ponovo postavlja nivo.", "Games.resetProgram": "<PERSON><PERSON><PERSON>", "Games.help": "<PERSON><PERSON><PERSON>", "Games.catLogic": "Logika", "Games.catLoops": "<PERSON><PERSON><PERSON>", "Games.catMath": "Matematika", "Games.catText": "Tekst", "Games.catLists": "<PERSON><PERSON><PERSON><PERSON>", "Games.catColour": "<PERSON><PERSON>", "Games.catVariables": "Promenljive", "Games.catProcedures": "Funkcije", "Games.httpRequestError": "Došlo je do problema u zahtevu.", "Games.linkAlert": "Delite svoje blokove ovom vezom:\n\n%1", "Games.hashError": "„%1“ ne odgovara nijednom sačuvanom programu.", "Games.xmlError": "Ne mogu da učitam sačuvanu datoteku. Možda je napravljena drugom verzijom Blockly-ja.", "Games.listVariable": "spisak", "Games.textVariable": "tekst", "Games.breakLink": "Jednom kada počnete uređivanje JavaScript, ne možete se vratiti na uređivanje blokova. Da li je to u redu?", "Games.blocks": "B<PERSON><PERSON><PERSON>", "Games.congratulations": "Čestitamo!", "Games.helpAbort": "O<PERSON>j nivo je veoma teško. Želite da ga preskočite i idite na sledeću igru? Uvek možete da se vratite kasnije.", "Index.clear": "Uklonite sve vaše odluke?", "Index.subTitle": "Igre za buduće programere.", "Index.moreInfo": "Više informacija...", "Index.startOver": "<PERSON><PERSON><PERSON> da počnete ispočetka?", "Index.clearData": "Obriš<PERSON> pod<PERSON>", "Puzzle.animal1": "Pat<PERSON>", "Puzzle.animal1Trait1": "<PERSON><PERSON>", "Puzzle.animal1Trait2": "<PERSON><PERSON><PERSON>", "Puzzle.animal1HelpUrl": "https://sr.wikipedia.org/sr-ec/Patka", "Puzzle.animal2": "Mač<PERSON>", "Puzzle.animal2Trait1": "Brkovi", "Puzzle.animal2Trait2": "Krzno", "Puzzle.animal2HelpUrl": "https://sr.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "Puzzle.animal3": "<PERSON><PERSON><PERSON>", "Puzzle.animal3Trait1": "Med", "Puzzle.animal3Trait2": "Žaoka", "Puzzle.animal3HelpUrl": "https://sr.wikipedia.org/wiki/<PERSON><PERSON><PERSON>", "Puzzle.animal4": "P<PERSON>ž", "Puzzle.animal4Trait1": "Ljuštura", "Puzzle.animal4Trait2": "Sluz", "Puzzle.animal4HelpUrl": "https://sr.wikipedia.org/wiki/<PERSON>u<PERSON>", "Puzzle.picture": "slika:", "Puzzle.legs": "noge:", "Puzzle.legsChoose": "<PERSON><PERSON><PERSON><PERSON>…", "Puzzle.traits": "osobine:", "Puzzle.error0": "Savršeno!\nSvih %1 blokova je ispravno postavljeno.", "Puzzle.error1": "Zamalo! Jedan blok je neispravno postavljen.", "Puzzle.error2": "%1 blokova je neispravno postavljeno.", "Puzzle.tryAgain": "Označeni blok nije ispravan.\nNastavi da pokušavaš.", "Puzzle.checkAnswers": "Proveri odgovore", "Puzzle.helpText": "Za svaku životinju (zeleno), pril<PERSON><PERSON><PERSON> nje<PERSON> slik<PERSON>, i<PERSON><PERSON>i njen broj nogu, i naslaži njene osobine.", "Maze.moveForward": "idi napred", "Maze.turnLeft": "okreni se levo", "Maze.turnRight": "okreni se desno", "Maze.doCode": "<PERSON>z<PERSON>i", "Maze.helpIfElse": "B<PERSON><PERSON>i „ako-inače“ izvršiće ili jedno ili drugo.", "Maze.pathAhead": "a<PERSON> put napred", "Maze.pathLeft": "ako post<PERSON> put levo", "Maze.pathRight": "a<PERSON> post<PERSON> put desno", "Maze.repeatUntil": "ponavljaj do", "Maze.moveForwardTooltip": "Pomera igrača napred za jedno mesto.", "Maze.turnTooltip": "Okreće igrača ulevo ili udesno za 90 stepeni.", "Maze.ifTooltip": "<PERSON><PERSON> put u datom pravcu, onda izvrši neke radnje.", "Maze.ifelseTooltip": "<PERSON><PERSON> put u datom pravcu, onda izvrši prvi blok radnji. U suprotnom, izvrši drugi.", "Maze.whileTooltip": "Ponavlja zadate radnje dok ne dođe do kraja.", "Maze.capacity0": "Preostalo ti je %0 blokova.", "Maze.capacity1": "Preostao ti je %1 blok.", "Maze.capacity2": "Preostalo ti je %2 blokova.", "Maze.runTooltip": "Određuje šta će igrač uraditi na osnovu blokova.", "Maze.resetTooltip": "Vratite igrača na početak lavirinta.", "Maze.helpStack": "Naređajte dva bloka „idi napred“ da biste mi pomogli da dođem do cilja.", "Maze.helpOneTopBlock": "U ovom nivou treba da naređate sve blokove u beli radni prostor.", "Maze.helpRun": "Pokrenite program i vidite šta se dešava.", "Maze.helpReset": "Program nije rešio lavirint. Kliknite na „Ponovo“ da biste ponovo pokušali.", "Maze.helpRepeat": "Dođite do kraja puta upotrebivši samo dva bloka. Koristite „ponovi“ da biste izvršili blok više puta.", "Maze.helpCapacity": "Ste koristili sve blokove na ovom nivou. Da napravite novi blok, potrebno je da prvo uklonite postojeći blok.", "Maze.helpRepeatMany": "Dostigni cilj upotrebom samo pet blokova.", "Maze.helpSkins": "Izaberite Svoj omiljeni igrač iz ovog menija.", "Maze.helpIf": "'ako' blok će uraditi nešto jedino ako je uslov ispunjen. Pokušaj okret u levo ako postoji put sa leve strane.", "Maze.helpMenu": "Kliknite na %1 'ako' blok, da promeni svoj status.", "Maze.helpWallFollow": "<PERSON><PERSON><PERSON><PERSON> li da rešiš ovaj složeni lavirint? Pokšaj da se krećeš uz levi zid.", "Bird.noWorm": "nema crva", "Bird.heading": "pravac", "Bird.noWormTooltip": "Stanje u kom ptica nije dobila crva.", "Bird.headingTooltip": "Kreću u pravcu kojim uglom: 0 je na desnoj strani, 90 je uspravno, itd", "Bird.positionTooltip": "X i Y marka ptica pozicije. Kada je X = 0 ptica je od leve ivice, ako je X = 100 je u blizini desne ivice. Kada je Y = 0 ova ptica je na dnu, kada je Y = 100 je na samom vrhu.", "Bird.helpHeading": "Promenite kategoriju pod takvim uglom da ptica je bila na crva i zemlju u svoje gnezdo.", "Bird.helpHasWorm": "<PERSON><PERSON><PERSON> ovaj blok u jednoj kategoriji, ako imate crv, ili drug<PERSON>, ako nemate crva.", "Bird.helpX": "'X' - to je vaš trenutni vodoravni položaj. Koristite ovaj blok u jednoj kategoriji, ako je 'X'  je manji od broja, ili drugi Naslov u suprotnom.", "Bird.helpElse": "Kliknite na ikonu da promenite blok 'if'.", "Bird.helpElseIf": "<PERSON><PERSON>j nivo potreba kao 'else if' i 'else' blok.", "Bird.helpAnd": "U 'I' blo<PERSON> je <PERSON>, samo ako su oba skupa ulaznih podataka istinite.", "Bird.helpMutator": "Prevucite 'else' jedinicu 'if' blok.", "Turtle.moveTooltip": "Pomera čerepašku napred ili nazad na pomenuti broj.", "Turtle.moveForward": "idi napred", "Turtle.moveBackward": "da se kreće nazad", "Turtle.turnTooltip": "Kornjača se okreće levo ili desno na određeno broja stepeni.", "Turtle.turnRight": "skrenite desno na", "Turtle.turnLeft": "skrenite levo na", "Turtle.widthTooltip": "Changes the width of the pen.", "Turtle.setWidth": "podesite širinu na", "Turtle.colourTooltip": "Menja boju o<PERSON>.", "Turtle.setColour": "podesite boju za", "Turtle.penTooltip": "Podiže ili izostavlja olovku, da se zaustavi ili počne da se skrene.", "Turtle.penUp": "digni olov<PERSON>", "Turtle.penDown": "spusti olovku", "Turtle.turtleVisibilityTooltip": "Čini kornjaču (krug i strelica) vidljivom ili nevidljivom.", "Turtle.hideTurtle": "sa<PERSON><PERSON><PERSON>", "Turtle.showTurtle": "p<PERSON><PERSON><PERSON> k<PERSON>", "Turtle.printHelpUrl": "https://sr.wikipedia.org/wiki/Štamparstvo", "Turtle.printTooltip": "Iscrtava tekst u pravcu kornjače na njenom mestu.", "Turtle.print": "<PERSON><PERSON><PERSON><PERSON>", "Turtle.fontTooltip": "Postavlja font koji koristi blok za pisanje.", "Turtle.font": "font", "Turtle.fontSize": "ve<PERSON><PERSON><PERSON> fonta", "Turtle.fontNormal": "normalan", "Turtle.fontBold": "podeb<PERSON>jan<PERSON>", "Turtle.fontItalic": "iskošeni", "Turtle.submitDisabled": "Pratite program do kraja. Onda možete da pošaljete svoj crtež u galeriji.", "Turtle.galleryTooltip": "Otvori galeriju crteža na Redditu.", "Turtle.galleryMsg": "<PERSON><PERSON><PERSON>", "Turtle.submitTooltip": "Pošalji svoj crtež na Reddit.", "Turtle.submitMsg": "Postavi u Galeriju", "Turtle.helpUseLoop": "<PERSON><PERSON><PERSON> re<PERSON>enje radi, ali ti to mo<PERSON><PERSON><PERSON> još bolje.", "Turtle.helpUseLoop3": "Nacrtaj ovaj oblik samo sa tri bloka.", "Turtle.helpUseLoop4": "Nacrtaj zvezdu samo sa četiri bloka.", "Turtle.helpText1": "Napravi program koji crta kvadrat.", "Turtle.helpText2": "Izmeni svoj program tako da crta petougao umesto kvadrata.", "Turtle.helpText3a": "Evo novog bloka koji ti omogućava da promeniš boju:", "Turtle.helpText3b": "Nacrtaj žutu <PERSON>.", "Turtle.helpText4a": "Evo novog bloka koji ti omogućava da podigneš olovku sa papira kad se krećeš:", "Turtle.helpText4b": "Nacrtaj malu <PERSON>, potom nacrtaj liniju iznad nje.", "Turtle.helpText5": "<PERSON>esto jedne zvezde, m<PERSON><PERSON><PERSON><PERSON> li nacrtati četiri zvezde raspoređene u uglove kvadrata?", "Turtle.helpText6": "Nacrtaj tri žute zvezde, i jednu belu liniju.", "Turtle.helpText7": "Nacrtaj zvezde, potom nacrtaj četiri bele linije.", "Turtle.helpText8": "Crtanje 360 belih linija izgledaće kao pun mesec.", "Turtle.helpText9": "<PERSON>ž<PERSON>š li dodati crni krug tako da ti mesec postane mladi mesec?", "Turtle.helpText10": "Crtaj šta želiš. <PERSON><PERSON><PERSON> ogroman broj novih blokova koje možeš da istražuješ. Zabavljaj se!", "Turtle.helpText10Reddit": "<PERSON><PERSON><PERSON> dug<PERSON> \"Pogledaj Galeriju\" da biste videli šta su drugi nacrtali. <PERSON><PERSON> ste nacrtali ne<PERSON>, k<PERSON><PERSON> dugme \"Postavi u Galeriju\" da biste to ob<PERSON><PERSON>.", "Turtle.helpToolbox": "Odaberite kategoriju da bidite blokove.", "Movie.x": "X", "Movie.y": "Y", "Movie.x1": "početak x", "Movie.y1": "početak y", "Movie.x2": "kraj x", "Movie.y2": "kraj y", "Movie.radius": "poluprečnik", "Movie.width": "<PERSON><PERSON><PERSON>", "Movie.height": "visina", "Movie.circleTooltip": "Crta krug u tom mestu i sa određenom radijusu.", "Movie.circleDraw": "krug", "Movie.rectTooltip": "Crta pravougaonik na tom mestu i sa datom širine i visine.", "Movie.rectDraw": "pravougaonik", "Movie.lineTooltip": "Crtanje linije od jedne tačke do druge sa određenom širinom.", "Movie.lineDraw": "linija", "Movie.timeTooltip": "Vraća trenutno vreme u filmu (0–100).", "Movie.colourTooltip": "Menja boju o<PERSON>.", "Movie.setColour": "promeni boju", "Movie.submitDisabled": "Vaš film nije pokretan. Koristite blokove da napravite nešto zanimljivo. Potom možete svoj film objaviti u galeriji.", "Movie.galleryTooltip": "Otvorite galeriju filmova na Reddit-u.", "Movie.galleryMsg": "<PERSON><PERSON><PERSON>", "Movie.submitTooltip": "Postavite svoj film na Reddit.", "Movie.submitMsg": "Postavi u Galeriju", "Movie.helpText1": "Koristite jednostavne oblike da biste nacrtali ovu osobu.", "Movie.helpText2a": "Ovaj nivo je film. <PERSON><PERSON><PERSON> da se crvena lopta kotrlja preko ekrana. <PERSON><PERSON><PERSON><PERSON><PERSON> play da biste videli pretpregled.", "Movie.helpText2b": "<PERSON><PERSON> film igra, vrednost bloka 'time' ide od 0 do 100. <PERSON><PERSON><PERSON> da horizontalna pozicija crvene lopte počne na 0 i ide do 100 ovo bi trebalo da je lako.", "Movie.helpText3": "Blok 'time' broji od 0 do 100. <PERSON> ovog puta želite da horizontalna pozicija crvene lopte počne na 100 i ide do 0. Možete li shvatiti jednostavnu matematičku formulu koja obrće smer?", "Movie.helpText4": "Koristite šta ste naučili na prethodnim nivoima da biste napravili četiri zelene loptice koje će se kretati u sve četiri smera.", "Movie.helpText5": "Napraviti da se glava miša pomera je lako. Možete li odgonetnuti matematiku kojom bi se i uši takođe pomerale?", "Movie.helpText6": "Dve jednostavne linije. Samo shvatite šta krajnje linije rade.", "Movie.helpText7": "Matematička formula za ovu loptu koja pada je komplikovana. Evo odgovora:", "Movie.helpText8": "Korist<PERSON> blok 'if' da biste nacrtali crvenu i plavu loptu kako padaju prvu polovinu filma. Potom nacrtajte zelenu loptu za drugu polovinu filma.", "Movie.helpText9": "Možete li napraviti loptu koja prati žicu? Žica je već nartana za Vas. Kada ovo budete mogli, onda možete sve.", "Movie.helpText10": "Napravite film za <PERSON>ta god želite. Imate ogroman broj novih blokova koje možete da istražujete. Zabavite se!", "Movie.helpText10Reddit": "<PERSON><PERSON><PERSON> dug<PERSON> \"Pogledaj Galeriju\" da biste pogledali filmove koji su drugi napravili. <PERSON><PERSON> napravite interesantan film, k<PERSON><PERSON> dugme \"Postavi u Galeriju\" da ga objavite.", "Music.firstPart": "prvi deo", "Music.piano": "klavir", "Music.violin": "violina", "Music.guitar": "gitara", "Music.galleryTooltip": "Otvori galeriju muzike na Reddit-u.", "Music.galleryMsg": "<PERSON><PERSON><PERSON>", "Music.submitTooltip": "Pošalji svoju muziku na Reddit.", "Music.submitMsg": "Postavi u Galeriju", "Pond.scanTooltip": "Pretraga neprijatelja. Određivanje smera (0-360). Vraćanje udaljenosti najbliže mete u tom smeru. Vraćanje beskonačnosti ako nijedna meta nije pronađena.", "Pond.cannonTooltip": "Ispaljivanje iz topa. Određivanje smera (0-360) i dometa (0-70).", "Pond.swimTooltip": "Plivanje napred. Određivanje smera (0-360).", "Pond.stopTooltip": "Prestanak plivanja. Igrač će usporavati dok se ne zaustavi.", "Pond.healthTooltip": "Vraća igračevo trenutno zdravlje (0 je mrtav, 100 je zdrav).", "Pond.speedTooltip": "Vraća trenutnu brzinu igrača (0 je zaustavljen, 100 je maksimalna brzina).", "Pond.locXTooltip": "Vraća X koordinatu igrača (0 je leva ivica, 100 je desna ivica).", "Pond.locYTooltip": "Vraća Y koordinatu igrača (0 je donja ivica, 100 je gornja ivica).", "Pond.docsTooltip": "Prikazuje jezik dokumentacije.", "Pond.documentation": "Dokumentacija", "Pond.playerName": "<PERSON><PERSON><PERSON>", "Pond.targetName": "<PERSON><PERSON><PERSON>", "Pond.pendulumName": "Klatno", "Pond.scaredName": "Uplašeni", "Pond.helpUseScan": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>, ali mož<PERSON> i bolje. Koristite 'scan' da biste videli koliko daleko pucati iz topa.", "Pond.helpText1": "Korist<PERSON> komandu 'cannon' da biste pogodili metu. Prvi parametar je ugao, drugi parametar je domet. Otkrijte pravu kombinaciju.", "Pond.helpText2": "<PERSON>va meta mora da se pogodi više puta. Koristite petlju 'while (true)' da biste nešto radi<PERSON> be<PERSON>.", "Pond.helpText3a": "Protivnik se kreće napred i nazad, zbog čega ga je teško pogoditi. Izraz 'scan' vraća tačan domet do protivnika u određenom smeru.", "Pond.helpText3b": "<PERSON>t je tačno ono što komanda 'cannon' treba da bi se pucalo precizno.", "Pond.helpText4": "Ovaj protivnik je predaleko da bi se koristio top (koji ima domet od 70 metara). <PERSON><PERSON><PERSON>, koris<PERSON> komandu 'swim' da biste počeli plivati prema protivniku i zabili se u njega.", "Pond.helpText5": "Ovaj protivnik je takođe predaleko da bi se koristio top. <PERSON> Vi ste preslabi da preživite sudar. Plivajte prema protivniku dok Vam je horizontalna lokacija manja od 50. <PERSON><PERSON> koristite 'stop' i upotrebite top.", "Pond.helpText6": "Ovaj protivnik će se udaljiti kada je pogođen. Plivajte prema njemu ako je van dometa (70 metara)."}